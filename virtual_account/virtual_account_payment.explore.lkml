include: "/virtual_account/**/*.view.lkml"
include: "/transactions/invoices/ag_invoices.view.lkml"
include: "/businesses/ag_businessinternalinformations.view.lkml"

explore: virtual_account_payment {
  from: ag_integrated_payment
  view_label: "Bank Callback"
  group_label: "Virtual Account"
  label: "VA Payment"

  join: va_payment_method_mapping {
    view_label: "VA Payment Method Mapping"
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_account_payment.bank_code} = ${va_payment_method_mapping.bank}
            and (
              ${virtual_account_payment.payment_method} = ${va_payment_method_mapping.terminal_id}
              or ${virtual_account_payment.payment_method} = ''
            );;
  }


  join: ag_payment_statements {
    view_label: "VA Detailed Payment"
    type: left_outer
    relationship: one_to_one
    sql_on: ${virtual_account_payment.id} = ${ag_payment_statements.id} ;;
  }

  join: ag_integrated_deposit {
    view_label: "VA Deposit"
    type: left_outer
    relationship: one_to_one
    sql_on: ${virtual_account_payment.payment_identifier} = ${ag_integrated_deposit.payment_id} ;;
  }

  join: ag_integrated_callback {
    view_label: "Callback to Merchant"
    type: left_outer
    relationship: one_to_one
    sql_on: ${ag_integrated_deposit.callback_id} = ${ag_integrated_callback.payload_id} ;;
  }

  join: virtual_accounts {
    from: ndt_virtual_account
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_account_payment.virtual_account_number} = ${virtual_accounts.full_payment_code} ;;
  }

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_accounts.master_business_id} = ${business_internal_informations.business_id} ;;
  }
}

explore: va_payment_processing_timeline {
  from: ndt_va_payment_processing_timeline
  view_label: "VA Payment Processing"
  group_label: "Virtual Account"
  label: "VA Payment Processing Timeline"

  join: virtual_accounts {
    from: ndt_virtual_account
    type: left_outer
    relationship: many_to_one
    sql_on: ${va_payment_processing_timeline.virtual_account_number} = ${virtual_accounts.full_payment_code} ;;
  }

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_accounts.master_business_id} = ${business_internal_informations.business_id} ;;
  }
}

explore: va_payment_processing_timeline_2 {
  from: ag_payment_processing_timeline
  view_label: "Payment Processing Timeline"
  group_label: "Virtual Account"
  label: "VA Payment Processing Timeline 2"

  join: virtual_accounts {
    from: ndt_virtual_account
    type: left_outer
    relationship: many_to_one
    sql_on: ${va_payment_processing_timeline_2.virtual_account_number} = ${virtual_accounts.full_payment_code} ;;
  }

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_accounts.master_business_id} = ${business_internal_informations.business_id} ;;
  }
}
