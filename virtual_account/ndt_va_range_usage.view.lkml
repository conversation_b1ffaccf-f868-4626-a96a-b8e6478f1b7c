# The name of this view in Looker is "Va Range Usage"
view: ag_va_range_usage {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__virtual_accounts.va_range_usage ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Bid Count" in Explore.

  dimension: bid_count {
    type: number
    sql: ${TABLE}.bid_count ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_bid_count {
    type: sum
    sql: ${bid_count} ;;  }
  measure: average_bid_count {
    type: average
    sql: ${bid_count} ;;  }

  dimension: bids {
    type: string
    sql: ${TABLE}.bids ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: date {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}."date" ;;
  }

  dimension_group: max_va_created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.max_va_created_at ;;
  }

  dimension_group: max_va_updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.max_va_updated_at ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: provider_code {
    type: string
    sql: ${TABLE}.provider_code ;;
  }

  dimension: range {
    type: number
    sql: ${TABLE}."range" ;;
  }

  dimension: smc_and_range_end {
    type: string
    sql: ${TABLE}.smc_and_range_end ;;
  }

  dimension: smc_and_range_start {
    type: string
    sql: ${TABLE}.smc_and_range_start ;;
  }

  dimension: usage_percentage {
    type: number
    sql: ${TABLE}.usage_percentage ;;
  }

  dimension: va_count {
    type: number
    sql: ${TABLE}.va_count ;;
  }
  measure: count {
    type: count
  }
}

view: ndt_va_range_usage {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  derived_table: {
    sql:
      with newest_date as (
        select max(date) as newest_date
        from ${ag_va_range_usage.SQL_TABLE_NAME}
      )
      select r.*, b.bid, n.newest_date
      from newest_date n, ${ag_va_range_usage.SQL_TABLE_NAME} r
      cross join unnest(r.bids) as b(bid)
    ;;
  }
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Bid Count" in Explore.

  dimension: bid_count {
    type: number
    sql: ${TABLE}.bid_count ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_bid_count {
    type: sum
    sql: ${bid_count} ;;  }
  measure: average_bid_count {
    type: average
    sql: ${bid_count} ;;  }

  dimension: bid {
    type: string
    sql: ${TABLE}.bid ;;
  }
  dimension: bids {
    type: string
    sql: ${TABLE}.bids ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: date {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}."date" ;;
  }

  dimension_group: newest_date {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}."newest_date" ;;
  }

  dimension_group: max_va_created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.max_va_created_at ;;
  }

  dimension_group: max_va_updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.max_va_updated_at ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: provider_code {
    type: string
    sql: ${TABLE}.provider_code ;;
  }

  dimension: range {
    type: number
    sql: ${TABLE}."range" ;;
  }

  dimension: smc_and_range_end {
    type: string
    sql: ${TABLE}.smc_and_range_end ;;
  }

  dimension: smc_and_range_start {
    type: string
    sql: ${TABLE}.smc_and_range_start ;;
  }

  dimension: usage_percentage {
    type: number
    sql: ${TABLE}.usage_percentage ;;
  }

  dimension: va_count {
    type: number
    sql: ${TABLE}.va_count ;;
  }
  measure: count {
    type: count
  }
}
