# The name of this view in Looker is "Integrated Payment"
view: ag_integrated_payment {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__virtual_accounts.integrated_payment ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Bank Code" in Explore.

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: bank_code_display {
    type: string
    sql: ${TABLE}.bank_code_display ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  dimension: is_new_arch {
    type: yesno
    sql: ${TABLE}.is_new_arch ;;
  }

  dimension: payment_amount {
    type: number
    sql: ${TABLE}.payment_amount ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_payment_amount {
    type: sum
    sql: ${payment_amount} ;;  }
  measure: average_payment_amount {
    type: average
    sql: ${payment_amount} ;;  }

  dimension: payment_identifier {
    type: string
    sql: ${TABLE}.payment_identifier ;;
  }

  dimension: payment_interface {
    type: string
    sql: ${TABLE}.payment_interface ;;
  }

  dimension: payment_method {
    type: string
    sql: ${TABLE}.payment_method ;;
  }

  dimension_group: payment_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.payment_timestamp ;;
  }

  dimension: reference {
    type: string
    sql: ${TABLE}.reference ;;
  }

  dimension: remark {
    type: string
    sql: ${TABLE}.remark ;;
  }

  dimension: sender_account_number {
    type: string
    sql: ${TABLE}.sender_account_number ;;
  }

  dimension: sender_channel_code {
    type: string
    sql: ${TABLE}.sender_channel_code ;;
  }

  dimension: sender_name {
    type: string
    sql: ${TABLE}.sender_name ;;
  }

  dimension: transfer_method {
    type: string
    sql: ${TABLE}.transfer_method ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }
  measure: count {
    type: count
    drill_fields: [id, sender_name]
  }
}
