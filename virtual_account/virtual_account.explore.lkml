include: "//central-models-dbr/businesses/*.view.lkml"

include: "/virtual_account/**/*.view.lkml"
include: "/businesses/ag_businessinternalinformations.view.lkml"
include: "/xenPlatform/ag_xenplatformrelationships.view.lkml"

explore: virtual_account {
  from: ndt_virtual_account
  group_label: "Virtual Account"
  view_label: "Virtual Account"
  label: "Virtual Account"

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_account.master_business_id} = ${business_internal_informations.business_id} ;;
  }

  join: businesses {
    sql_on: ${virtual_account.business_id} = ${businesses.business_id} ;;
    relationship: many_to_one
    type :  inner
  }

  join: business_marketing_attribution {
    view_label: "Businesses - Marketing Attribution"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_marketing_attribution.business_id};;
    relationship: one_to_one
  }
  join: xp_businesses {
    type: full_outer
    view_label: "xenPlatform Master Account"
    sql_on: ${businesses.master_account_id} = ${xp_businesses.business_id} ;;
    relationship: many_to_one
  }

  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }
}

explore: ndt_virtual_account {
  hidden: yes
  from: ag_cash_payment_service_virtual_account

  join: ag_xenplatformrelationships {
    type: left_outer
    sql_on: ${ndt_virtual_account.business_id} = ${ag_xenplatformrelationships.sub_acc_business} ;;
    relationship: many_to_one
  }
}
