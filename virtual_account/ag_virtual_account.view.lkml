view: ag_cash_payment_service_virtual_account {
  sql_table_name: clean__cash_payment_service.virtual_account ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension_group: deleted {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.deleted_at ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: expected_amount {
    type: number
    sql: ${TABLE}.expected_amount ;;
  }

  dimension_group: expiration {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.expiration_date ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: from_old_arch {
    type: yesno
    sql: ${TABLE}.from_old_arch ;;
  }

  dimension: full_payment_code {
    type: string
    sql: ${TABLE}.full_payment_code ;;
  }

  dimension: is_closed {
    type: yesno
    sql: ${TABLE}.is_closed ;;
  }

  dimension: is_single_use {
    type: yesno
    sql: ${TABLE}.is_single_use ;;
  }

  dimension: is_specify_va_number {
    type: yesno
    sql: ${TABLE}.is_specify_va_number ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: payment_code {
    type: string
    sql: ${TABLE}.payment_code ;;
  }

  dimension: payment_code_id {
    type: string
    sql: ${TABLE}.payment_code_id ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: provider_code {
    type: string
    sql: ${TABLE}.provider_code ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: sub_merchant_code {
    type: string
    sql: ${TABLE}.sub_merchant_code ;;
  }

  dimension: suggested_amount {
    type: number
    sql: ${TABLE}.suggested_amount ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated_at ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name]
  }
}
