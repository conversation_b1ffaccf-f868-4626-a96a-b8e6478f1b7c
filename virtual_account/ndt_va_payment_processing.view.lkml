view: ndt_va_payment_processing {
  derived_table: {
    explore_source: virtual_account_payment {
      column: id {
        field: virtual_account_payment.id
      }
      column: bank_code {
        field: virtual_account_payment.bank_code
      }
      column: bank_code_display {
        field: virtual_account_payment.bank_code_display
      }
      column: type {
        field: ag_integrated_deposit.type
      }
      column: is_new_arch {
        field: virtual_account_payment.is_new_arch
      }
      column: is_switching {
        field: ag_integrated_deposit.is_switching
      }
      column: ledger_account_id {
        field: ag_integrated_deposit.ledger_account_id
      }
      column: payment_amount {
        field: virtual_account_payment.payment_amount
      }
      column: payment_timestamp {
        field: virtual_account_payment.payment_timestamp_raw
      }
      column: va_payment_created {
        field: virtual_account_payment.created_raw
      }
      column: deposit_created {
        field: ag_integrated_deposit.created_raw
      }
      column: callback_to_merchant_created {
        field: ag_integrated_callback.created_raw
      }
      column: callback_to_merchant_first_delivery_timestamp {
        field: ag_integrated_callback.first_delivery_timestamp_raw
      }
      column: deposit_id {
        field: ag_integrated_deposit.id
      }
      column: callback_to_merchant_id {
        field: ag_integrated_callback.id
      }
      column: virtual_account_number {
        field: virtual_account_payment.virtual_account_number
      }
    }
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: deposit_id {
    type: string
    sql: ${TABLE}.deposit_id ;;
  }

  dimension: callback_to_merchant_id {
    type: string
    sql: ${TABLE}.callback_to_merchant_id ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: bank_code_display {
    type: string
    sql: ${TABLE}.bank_code_display ;;
  }

  dimension_group: callback_to_merchant_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_created ;;
  }

  dimension_group: deposit_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.deposit_created ;;
  }

  dimension_group: callback_to_merchant_first_delivery_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_first_delivery_timestamp ;;
  }

  dimension: is_new_arch {
    type: yesno
    sql: ${TABLE}.is_new_arch ;;
  }

  dimension: is_switching {
    type: yesno
    sql: ${TABLE}.is_switching ;;
  }

  dimension: ledger_account_id {
    type: string
    sql: ${TABLE}.ledger_account_id ;;
  }

  dimension: payment_amount {
    type: number
    sql: ${TABLE}.payment_amount ;;
  }

  dimension_group: payment_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_timestamp ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  dimension_group: va_payment_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.va_payment_created ;;
  }

  dimension: partner_processing_time {
    type: number
    sql: date_diff(second, ${TABLE}.payment_timestamp, ${TABLE}.va_payment_created) ;;
  }

  dimension: bank_callback_processing_time {
    type: number
    sql: date_diff(second, ${TABLE}.va_payment_created, ${TABLE}.deposit_created) ;;
  }

  dimension: deposit_processing_time {
    type: number
    sql: date_diff(second, ${TABLE}.deposit_created, ${TABLE}.callback_to_merchant_created) ;;
  }

  dimension: xendit_callback_processing_time {
    type: number
    sql: date_diff(second, ${TABLE}.callback_to_merchant_created, ${TABLE}.callback_to_merchant_first_delivery_timestamp) ;;
  }

  dimension: total_processing_time {
    type: number
    sql: date_diff(second, ${TABLE}.payment_timestamp, ${TABLE}.callback_to_merchant_first_delivery_timestamp) ;;
  }

  measure: partner_processing_p99 {
    type: percentile
    percentile: 99
    sql: ${partner_processing_time} ;;
  }

  measure: bank_callback_processing_p99 {
    type: percentile
    percentile: 99
    sql: ${bank_callback_processing_time} ;;
  }

  measure: deposit_processing_p99 {
    type: percentile
    percentile: 99
    sql: ${deposit_processing_time} ;;
  }

  measure: xendit_callback_processing_p99 {
    type: percentile
    percentile: 99
    sql: ${xendit_callback_processing_time} ;;
  }

  measure: total_processing_p99 {
    type: percentile
    percentile: 99
    sql: ${total_processing_time} ;;
  }
}

view: ndt_va_payment_processing_timeline {
  derived_table: {
    sql:
      select
        *,
        'Partner' as step,
        0 as p1,
        0 as p2,
        date_diff(second, payment_timestamp, va_payment_created) as p3
      from ${ndt_va_payment_processing.SQL_TABLE_NAME}
        where callback_to_merchant_id is not null
        and payment_timestamp <= va_payment_created
        and va_payment_created <= coalesce(deposit_created, va_payment_created)
        and coalesce(deposit_created, va_payment_created) <= callback_to_merchant_created
        and date_diff(second, payment_timestamp, va_payment_created) <= 180
      union all
      select
        *,
        'Bank callback',
        0,
        date_diff(second, payment_timestamp, va_payment_created),
        date_diff(second, va_payment_created, coalesce(deposit_created, va_payment_created))
      from ${ndt_va_payment_processing.SQL_TABLE_NAME}
        where callback_to_merchant_id is not null
        and payment_timestamp <= va_payment_created
        and va_payment_created <= coalesce(deposit_created, va_payment_created)
        and coalesce(deposit_created, va_payment_created) <= callback_to_merchant_created
        and date_diff(second, payment_timestamp, va_payment_created) <= 180
      union all
      select
        *,
        'Deposit',
        date_diff(second, payment_timestamp, va_payment_created),
        date_diff(second, va_payment_created, coalesce(deposit_created, va_payment_created)),
        date_diff(second, coalesce(deposit_created, va_payment_created), callback_to_merchant_created)
      from ${ndt_va_payment_processing.SQL_TABLE_NAME}
        where callback_to_merchant_id is not null
        and payment_timestamp <= va_payment_created
        and va_payment_created <= coalesce(deposit_created, va_payment_created)
        and coalesce(deposit_created, va_payment_created) <= callback_to_merchant_created
        and date_diff(second, payment_timestamp, va_payment_created) <= 180
    ;;
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: bank_code_display {
    type: string
    sql: ${TABLE}.bank_code_display ;;
  }

  dimension_group: callback_to_merchant_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_created ;;
  }

  dimension_group: callback_to_merchant_first_delivery_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_first_delivery_timestamp ;;
  }

  dimension_group: deposit_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.deposit_created ;;
  }

  dimension: is_new_arch {
    type: yesno
    sql: ${TABLE}.is_new_arch ;;
  }

  dimension: is_switching {
    type: yesno
    sql: ${TABLE}.is_switching ;;
  }

  dimension: ledger_account_id {
    type: string
    sql: ${TABLE}.ledger_account_id ;;
  }

  dimension: payment_amount {
    type: number
    sql: ${TABLE}.payment_amount ;;
  }

  dimension_group: payment_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_timestamp ;;
  }

  dimension: step {
    type: string
    sql: ${TABLE}.step ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  dimension_group: va_payment_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.va_payment_created ;;
  }

  measure: p90_1 {
    hidden: yes
    type: percentile
    percentile: 90
    sql: ${TABLE}.p1 ;;
  }

  measure: p90_2 {
    hidden: yes
    type: percentile
    percentile: 90
    sql: ${TABLE}.p2 ;;
  }

  measure: p90_3 {
    hidden: yes
    type: percentile
    percentile: 90
    sql: ${TABLE}.p3 ;;
  }

  measure: processing_p90_start {
    type: number
    sql: ${p90_1} + ${p90_2}  ;;
  }

  measure: processing_p90_end {
    type: number
    sql: ${processing_p90_start} + ${p90_3} ;;
  }

  measure: p95_1 {
    hidden: yes
    type: percentile
    percentile: 95
    sql: ${TABLE}.p1 ;;
  }

  measure: p95_2 {
    hidden: yes
    type: percentile
    percentile: 95
    sql: ${TABLE}.p2 ;;
  }

  measure: p95_3 {
    hidden: yes
    type: percentile
    percentile: 95
    sql: ${TABLE}.p3 ;;
  }

  measure: processing_p95_start {
    type: number
    sql: ${p95_1} + ${p95_2}  ;;
  }

  measure: processing_p95_end {
    type: number
    sql: ${processing_p95_start} + ${p95_3} ;;
  }

  measure: p99_1 {
    hidden: yes
    type: percentile
    percentile: 99
    sql: ${TABLE}.p1 ;;
  }

  measure: p99_2 {
    hidden: yes
    type: percentile
    percentile: 99
    sql: ${TABLE}.p2 ;;
  }

  measure: p99_3 {
    hidden: yes
    type: percentile
    percentile: 99
    sql: ${TABLE}.p3 ;;
  }

  measure: processing_p99_start {
    type: number
    sql: ${p99_1} + ${p99_2}  ;;
  }

  measure: processing_p99_end {
    type: number
    sql: ${processing_p99_start} + ${p99_3} ;;
  }

  measure: avg_1 {
    hidden: yes
    type: average
    precision: 0
    sql: ${TABLE}.p1 ;;
  }

  measure: avg_2 {
    hidden: yes
    type: average
    precision: 0
    sql: ${TABLE}.p2 ;;
  }

  measure: avg_3 {
    hidden: yes
    type: average
    precision: 0
    sql: ${TABLE}.p3 ;;
  }

  measure: processing_avg_start {
    type: number
    sql: ${avg_1} + ${avg_2}  ;;
  }

  measure: processing_avg_end {
    type: number
    sql: ${processing_avg_start} + ${avg_3} ;;
  }
}
