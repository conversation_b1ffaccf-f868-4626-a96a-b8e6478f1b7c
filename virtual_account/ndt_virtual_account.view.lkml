view: ndt_virtual_account {
  derived_table: {
    explore_source: ndt_virtual_account {
      column: id {}
      column: business_id {}
      column: master_acc_business {
        field: ag_xenplatformrelationships.master_acc_business
      }
      derived_column: master_business_id {
        sql: coalesce(master_acc_business, business_id) ;;
      }
      column: created {
        field: ndt_virtual_account.created_raw
      }
      column: currency {}
      column: deleted {
        field: ndt_virtual_account.deleted_raw
      }
      column: dt {
        field: ndt_virtual_account.dt_raw
      }
      column: description {}
      column: expected_amount {}
      column: expiration {
        field: ndt_virtual_account.expiration_raw
      }
      column: external_id {}
      column: from_old_arch {}
      column: full_payment_code {}
      column: is_closed {}
      column: is_single_use {}
      column: is_specify_va_number {}
      column: merchant_code {}
      column: metadata {}
      column: name {}
      column: payment_code {}
      column: payment_code_id {}
      column: product_type {}
      column: provider_code {}
      column: status {}
      column: sub_merchant_code {}
      column: suggested_amount {}
      column: type {}
      column: updated {
        field: ndt_virtual_account.updated_raw
      }
    }
  }

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: master_business_id {
    type: string
    sql: ${TABLE}.master_business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension_group: deleted {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.deleted ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
    convert_tz: no
    datatype: date
    group_label: "Created Date (UTC)"
    description: "Created Date of transaction in UTC time. Partitioned column, highly recommended to use as filter"
  }

  dimension: expected_amount {
    type: number
    sql: ${TABLE}.expected_amount ;;
  }

  dimension_group: expiration {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.expiration ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: from_old_arch {
    type: yesno
    sql: ${TABLE}.from_old_arch ;;
  }

  dimension: full_payment_code {
    type: string
    sql: ${TABLE}.full_payment_code ;;
  }

  dimension: is_closed {
    type: yesno
    sql: ${TABLE}.is_closed ;;
  }

  dimension: is_single_use {
    type: yesno
    sql: ${TABLE}.is_single_use ;;
  }

  dimension: is_specify_va_number {
    type: yesno
    sql: ${TABLE}.is_specify_va_number ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: payment_code {
    type: string
    sql: ${TABLE}.payment_code ;;
  }

  dimension: payment_code_id {
    type: string
    sql: ${TABLE}.payment_code_id ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: provider_code {
    type: string
    sql: ${TABLE}.provider_code ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: sub_merchant_code {
    type: string
    sql: ${TABLE}.sub_merchant_code ;;
  }

  dimension: suggested_amount {
    type: number
    sql: ${TABLE}.suggested_amount ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name]
  }
}
