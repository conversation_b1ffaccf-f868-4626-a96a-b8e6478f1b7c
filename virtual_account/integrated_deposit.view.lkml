view: ag_integrated_deposit {
  sql_table_name: transform__virtual_accounts.integrated_deposit ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: callback_id {
    type: string
    sql: ${TABLE}.callback_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: fee_transaction_id {
    type: string
    sql: ${TABLE}.fee_transaction_id ;;
  }

  dimension: fees_paid_amount {
    type: number
    sql: ${TABLE}.fees_paid_amount ;;
  }

  dimension: holding_transaction_id {
    type: string
    sql: ${TABLE}.holding_transaction_id ;;
  }

  dimension: invoice_id {
    type: string
    sql: ${TABLE}.invoice_id ;;
  }

  dimension: is_switching {
    type: yesno
    sql: ${TABLE}.is_switching ;;
  }

  dimension: ledger_account_id {
    type: string
    sql: ${TABLE}.ledger_account_id ;;
  }

  dimension: payment_id {
    type: string
    sql: ${TABLE}.payment_id ;;
  }

  dimension_group: payment_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_timestamp ;;
  }

  dimension: settlement_transaction_id {
    type: string
    sql: ${TABLE}.settlement_transaction_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: vat_paid_amount {
    type: number
    sql: ${TABLE}.vat_paid_amount ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
