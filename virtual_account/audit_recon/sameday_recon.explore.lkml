include: "/virtual_account/**/*.view.lkml"

explore: sameday_recon {
  from: ndt_sameday_healthcheck_va
  view_label: "Unreconciled Transactions"
  group_label: "Virtual Account"
  label: "Sameday Recon"

    join: ndt_sameday_healthcheck_va {
      view_label: "Recon Candidates"
      type: inner
      relationship: one_to_many
      sql_on:
        ${ndt_sameday_healthcheck_va.report_id} is not null
        and ${sameday_recon.recon_timestamp_original} = ${ndt_sameday_healthcheck_va.recon_timestamp_original}
        and ${sameday_recon.report_va_number} = ${ndt_sameday_healthcheck_va.report_va_number}
        and ${sameday_recon.report_amount} = ${ndt_sameday_healthcheck_va.report_amount}
        and ${sameday_recon.report_channel} = ${ndt_sameday_healthcheck_va.report_channel};;
    }
  }
