include: "/virtual_account/audit_recon/ndt_combined_reconciliation_payments.view.lkml"
include: "//central-models-dbr/transactions/transaction_backbone.view.lkml"
include: "/transactions/cashpay/virtual_account/virtual_accounts.view.lkml"

view: ndt_audit_with_transaction2 {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql:
      select
        id,
        bank_code,
        created,
        recon_method,
        remarks,
        'AUDIT' as source,
        virtual_account_number
      from ${ndt_combined_reconciliation_payments.SQL_TABLE_NAME}
        where reconciled = true
      union all
      select
        reference as id,
        channel_name as bank_code,
        t.created as created,
        null,
        '' as remarks,
        'BANK' as source,
        va.account_number as virtual_account_number
      from ${transaction_backbone.SQL_TABLE_NAME} t
        left join ${virtual_accounts.SQL_TABLE_NAME} va
          on t.product_type = 'VIRTUAL_ACCOUNT'
          and t.reference = va.id
      where t.product_type = 'VIRTUAL_ACCOUNT'
    ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: recon_method {
    type: string
    sql: ${TABLE}.recon_method ;;
  }

  dimension: remarks {
    type: string
    sql: ${TABLE}.remarks ;;
  }

  dimension: source {
    type: string
    sql: ${TABLE}.source ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }

  measure: raw_audit_callback_count {
    hidden:  yes
    type: count
    filters: [source: "AUDIT"]
  }

  measure: raw_manual_audit_callback_count {
    hidden:  yes
    type: count
    filters: [source: "AUDIT", recon_method: "MANUAL"]
  }

  measure: raw_bank_callback_count {
    hidden:  yes
    type: count
    filters: [source: "BANK"]
  }

  measure: all_callback_count {
    type: number
    sql: ${raw_bank_callback_count} ;;
  }

  measure: audit_callback_count {
    type: number
    sql: ${raw_audit_callback_count} ;;
  }

  measure: bank_callback_count {
    type: number
    sql: ${raw_bank_callback_count} - ${raw_audit_callback_count} ;;
  }

  measure: xendit_callback_count {
    type: number
    sql: ${raw_bank_callback_count} - ${raw_manual_audit_callback_count} ;;
  }

  measure: bank_callback_percentage {
    type: number
    value_format_name: percent_3
    sql: cast(${bank_callback_count} as double) / cast(${all_callback_count} as double) ;;
  }

  measure: xendit_callback_percentage {
    type: number
    value_format_name: percent_3
    sql: cast(${xendit_callback_count} as double) / cast(${all_callback_count} as double) ;;
  }
}