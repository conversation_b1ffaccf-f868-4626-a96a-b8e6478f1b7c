include: "/virtual_account/audit_recon/ag_healthcheck_va.view.lkml"
include: "/virtual_account/audit_recon/ag_partner_channel_statement_rows.view.lkml"

view: ndt_healthcheck_va {
  derived_table: {
    sql:
      select
        h.*,
        r.row_hash,
        row_number() OVER (partition by h.report_va_number, h.report_channel, h.report_amount order by h.report_timestamp asc) as trx_order
      from ${ag_healthcheck_va.SQL_TABLE_NAME} h
        inner join ${ag_partner_channel_statement_rows.SQL_TABLE_NAME} r
          on h.report_id = r.id;;
  }
  suggestions: no

  dimension: ledger_amount {
    type: number
    sql: ${TABLE}.ledger_amount ;;
  }

  dimension: ledger_channel {
    type: string
    sql: ${TABLE}.ledger_channel ;;
  }

  dimension: ledger_id {
    type: string
    sql: ${TABLE}.ledger_id ;;
  }

  dimension_group: ledger_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.ledger_timestamp ;;
  }

  dimension: ledger_va_number {
    type: string
    sql: ${TABLE}.ledger_va_number ;;
  }

  dimension: recon_reason {
    type: string
    sql: ${TABLE}.recon_reason ;;
  }

  dimension: recon_status {
    type: string
    sql: ${TABLE}.recon_status ;;
  }

  dimension: report_amount {
    type: number
    sql: ${TABLE}.report_amount ;;
  }

  dimension: report_channel {
    type: string
    sql: ${TABLE}.report_channel ;;
  }

  dimension: report_id {
    type: string
    sql: ${TABLE}.report_id ;;
  }

  dimension_group: report_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.report_timestamp ;;
  }

  dimension: report_va_number {
    type: string
    sql: ${TABLE}.report_va_number ;;
  }

  dimension: row_hash {
    type: string
    sql: ${TABLE}.row_hash ;;
  }

  dimension: trx_order {
    type: number
    sql: ${TABLE}.trx_order ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }

  measure: report_timestamp_list {
    sql: array_join(array_agg(${TABLE}.report_timestamp order by ${TABLE}.report_timestamp asc), '|') ;;
  }

  measure: row_hash_list {
    sql: array_join(array_agg(${TABLE}.row_hash order by ${TABLE}.report_timestamp asc), '|') ;;
  }
}
