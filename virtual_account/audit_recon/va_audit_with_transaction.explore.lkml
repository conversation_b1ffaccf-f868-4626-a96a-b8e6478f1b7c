include: "/virtual_account/**/*.view.lkml"
include: "/businesses/ag_businessinternalinformations.view.lkml"

explore: va_audit_with_transaction {
  from: "ndt_audit_with_transaction"
  group_label: "Virtual Account"
  label: "VA Audit with Transaction"

  join: virtual_accounts {
    from: ndt_virtual_account
    type: left_outer
    relationship: many_to_one
    sql_on: ${va_audit_with_transaction.virtual_account_number} = ${virtual_accounts.full_payment_code} ;;
  }

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_accounts.master_business_id} = ${business_internal_informations.business_id} ;;
  }
}

explore: va_audit_with_transaction2 {
  from: "ndt_audit_with_transaction2"
  group_label: "Virtual Account"
  label: "VA Audit with Transaction 2"
  persist_with: views_updated_based_on_time

  join: virtual_accounts {
    from: ndt_virtual_account
    type: left_outer
    relationship: many_to_one
    sql_on: ${va_audit_with_transaction2.virtual_account_number} = ${virtual_accounts.full_payment_code} ;;
  }

  join: business_internal_informations {
    from: ag_businessinternalinformations
    type: left_outer
    relationship: many_to_one
    sql_on: ${virtual_accounts.master_business_id} = ${business_internal_informations.business_id} ;;
  }
}
