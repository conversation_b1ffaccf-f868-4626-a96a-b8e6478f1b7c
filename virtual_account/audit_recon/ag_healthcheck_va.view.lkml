view: ag_healthcheck_va {
  sql_table_name: clean__product_healthcheck.virtual_account ;;
  suggestions: no

  dimension: ledger_amount {
    type: number
    sql: ${TABLE}.ledger_amount ;;
  }

  dimension: ledger_channel {
    type: string
    sql: ${TABLE}.ledger_channel ;;
  }

  dimension: ledger_id {
    type: string
    sql: ${TABLE}.ledger_id ;;
  }

  dimension_group: ledger_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.ledger_timestamp ;;
  }

  dimension: ledger_va_number {
    type: string
    sql: ${TABLE}.ledger_va_number ;;
  }

  dimension: recon_reason {
    type: string
    sql: ${TABLE}.recon_reason ;;
  }

  dimension: recon_status {
    type: string
    sql: ${TABLE}.recon_status ;;
  }

  dimension: report_account_number {
    type: string
    sql: ${TABLE}.report_account_number ;;
  }

  dimension: report_amount {
    type: number
    sql: ${TABLE}.report_amount ;;
  }

  dimension: report_channel {
    type: string
    sql: ${TABLE}.report_channel ;;
  }

  dimension: report_channel_statement_id {
    type: string
    sql: ${TABLE}.report_channel_statement_id ;;
  }

  dimension: report_id {
    type: string
    sql: ${TABLE}.report_id ;;
  }

  dimension: report_row_type {
    type: string
    sql: ${TABLE}.report_row_type ;;
  }

  dimension_group: report_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.report_timestamp ;;
  }

  dimension: report_va_number {
    type: string
    sql: ${TABLE}.report_va_number ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
