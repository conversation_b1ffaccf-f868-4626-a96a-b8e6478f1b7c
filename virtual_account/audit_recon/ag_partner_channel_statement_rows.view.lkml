view: ag_partner_channel_statement_rows {
  sql_table_name: clean__xendit_reconciliation_service_live.partner_channel_statement_rows ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: channel_statement_id {
    type: string
    sql: ${TABLE}.channel_statement_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: credit_amount {
    type: number
    sql: ${TABLE}.credit_amount ;;
  }

  dimension: debit_amount {
    type: number
    sql: ${TABLE}.debit_amount ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: md__channel_reference {
    type: string
    sql: ${TABLE}.md__channel_reference ;;
  }

  dimension: md__debt_virtual_account_number {
    type: string
    sql: ${TABLE}.md__debt_virtual_account_number ;;
  }

  dimension: md__destination_account_number {
    type: string
    sql: ${TABLE}.md__destination_account_number ;;
  }

  dimension: md__masked_card_number {
    type: string
    sql: ${TABLE}.md__masked_card_number ;;
  }

  dimension: md__payment_identifier {
    type: string
    sql: ${TABLE}.md__payment_identifier ;;
  }

  dimension: md__receiver_account_number {
    type: string
    sql: ${TABLE}.md__receiver_account_number ;;
  }

  dimension: md__secondary_channel_reference {
    type: string
    sql: ${TABLE}.md__secondary_channel_reference ;;
  }

  dimension: md__source_account_number {
    type: string
    sql: ${TABLE}.md__source_account_number ;;
  }

  dimension: md__virtual_account_number {
    type: string
    sql: ${TABLE}.md__virtual_account_number ;;
  }

  dimension: row_hash {
    type: string
    sql: ${TABLE}.row_hash ;;
  }

  dimension: row_type {
    type: string
    sql: ${TABLE}.row_type ;;
  }

  dimension: sequence {
    type: number
    sql: ${TABLE}.sequence ;;
  }

  dimension_group: transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.transaction_timestamp ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id, channel_name]
  }
}
