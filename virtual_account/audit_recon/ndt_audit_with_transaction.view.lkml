include: "/virtual_account/audit_recon/ndt_combined_reconciliation_payments.view.lkml"
include: "//central-models-dbr/transactions/transaction_backbone.view.lkml"
include: "/transactions/cashpay/virtual_account/virtual_accounts.view.lkml"

view: ndt_audit_with_transaction {
  derived_table: {
    sql:
      with transactions as (
        select
          reference as id,
          channel_name as bank_code,
          t.created as created,
          'BANK' as source,
          '' as remarks,
          va.account_number as virtual_account_number
        from ${transaction_backbone.SQL_TABLE_NAME} t
          left join ${virtual_accounts.SQL_TABLE_NAME} va
            on t.product_type = 'VIRTUAL_ACCOUNT'
            and t.reference = va.id
        where t.product_type = 'VIRTUAL_ACCOUNT'
      )
      select
        id,
        bank_code,
        'XENDIT' as callback,
        created,
        'AUDIT' as source,
        remarks,
        virtual_account_number
      from ${ndt_combined_reconciliation_payments.SQL_TABLE_NAME}
        where reconciled = true
        and recon_method = 'AUTOMATED'
      union all
      select
        id,
        bank_code,
        'XENDIT',
        created,
        source,
        remarks,
        virtual_account_number
      from transactions
      union all
      select
        id,
        bank_code,
        'BANK',
        created,
        source,
        remarks,
        virtual_account_number
      from transactions
      union all
      select
        id,
        bank_code,
        'ALL',
        created,
        'AUDIT',
        remarks,
        virtual_account_number
      from ${ndt_combined_reconciliation_payments.SQL_TABLE_NAME}
        where reconciled = true
      union all
      select
        id,
        bank_code,
        'ALL',
        created,
        source,
        remarks,
        virtual_account_number
      from transactions
    ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: callback {
    type: string
    sql: ${TABLE}.callback ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: remarks {
    type: string
    sql: ${TABLE}.remarks ;;
  }

  dimension: source {
    type: string
    sql: ${TABLE}.source ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}