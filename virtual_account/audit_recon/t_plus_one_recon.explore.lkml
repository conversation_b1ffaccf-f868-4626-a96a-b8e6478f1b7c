include: "/virtual_account/**/*.view.lkml"

explore: t_plus_one_recon {
  from: ndt_healthcheck_va
  view_label: "Unreconciled Transactions"
  group_label: "Virtual Account"
  label: "T+1 Recon"

  join: ndt_healthcheck_va {
    view_label: "Recon Candidates"
    type: inner
    relationship: one_to_many
    sql_on:
        ${ndt_healthcheck_va.report_id} is not null
        and ${t_plus_one_recon.report_va_number} = ${ndt_healthcheck_va.report_va_number}
        and ${t_plus_one_recon.report_amount} = ${ndt_healthcheck_va.report_amount}
        and ${t_plus_one_recon.report_channel} = ${ndt_healthcheck_va.report_channel};;
  }
}
