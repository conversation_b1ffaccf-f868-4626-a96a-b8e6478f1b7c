view: ndt_combined_reconciliation_payments {
  derived_table: {
    sql:
      select
        rp.id,
        rp.amount,
        case
          when rp.bank_code = 'BCA' and bcm.connector_code = 'ALTO' then 'BCA_ALTOPAY'
          when rp.bank_code = 'BCA' and bcm.connector_code = 'DIRECT' then 'BCA_DIRECT'
          when (rp.bank_code = 'BCA' and (bcm.connector_code = 'SPRINT' or bcm.id is null)) then 'BCA_SPRINT'
          else rp.bank_code
        end as bank_code,
        rp.created,
        'AUTOMATED' as recon_method,
        rp.remarks,
        rp.reconciled,
        rp.transaction_timestamp,
        rp.virtual_account_number
      from clean__va_audit_recon.reconciliationpayments rp
        left join clean__cash_payment_settings.bank_connector_mapping bcm
          on substr(rp.virtual_account_number, 1, length(bcm.merchant_code)) = bcm.merchant_code
          and rp.bank_code = bcm.provider_code
      where
        (rp.recon_schedule is null or rp.recon_schedule != 'AD_HOC')
          or (rp.recon_source is null or rp.recon_source != 'TRANSACTION_INQUIRY_RECORDER')
      union all
      select
        mp.id,
        mp.amount,
        case
          when mp.bank_code like 'BCA%' and bcm.connector_code = 'ALTO' then 'BCA_ALTOPAY'
          when mp.bank_code like 'BCA%' and bcm.connector_code = 'DIRECT' then 'BCA_DIRECT'
          when (mp.bank_code like 'BCA%' and (bcm.connector_code = 'SPRINT' or bcm.id is null)) then 'BCA_SPRINT'
          else mp.bank_code
        end as bank_code,
        mp.created,
        'MANUAL',
        null,
        true as reconciled,
        mp.transaction_timestamp,
        mp.virtual_account_number
      from clean__xendit_admin_dashboard.manualpayments mp
        left join clean__cash_payment_settings.bank_connector_mapping bcm
          on substr(mp.virtual_account_number, 1, length(bcm.merchant_code)) = bcm.merchant_code
          and mp.bank_code like 'BCA%'
      where mp.is_approved = true
      and (mp.invoice_id is null or mp.invoice_id != 'sth1')
      and mp.maker_name not in (
        'test',
        '<EMAIL>'
      )
    ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: recon_method {
    type: string
    sql: ${TABLE}.recon_method ;;
  }

  dimension: reconciled {
    type: yesno
    sql: ${TABLE}.reconciled ;;
  }

  dimension: remarks {
    type: string
    sql: ${TABLE}.remarks ;;
  }

  dimension_group: transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.transaction_timestamp ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
