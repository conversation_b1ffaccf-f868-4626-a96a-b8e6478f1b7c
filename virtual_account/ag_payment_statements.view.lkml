# The name of this view in Looker is "Payment Statements"
view: ag_payment_statements {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__payment_statement_service.payment_statements ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Amount" in Explore.

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_amount {
    type: sum
    sql: ${amount} ;;  }
  measure: average_amount {
    type: average
    sql: ${amount} ;;  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: channel_id {
    type: string
    sql: ${TABLE}.channel_id ;;
  }

  dimension: client_type {
    type: string
    sql: ${TABLE}.client_type ;;
  }

  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: full_payment_code {
    type: string
    sql: ${TABLE}.full_payment_code ;;
  }

  dimension: internal_metadata {
    type: string
    sql: ${TABLE}.internal_metadata ;;
  }

  dimension: is_force_match {
    type: yesno
    sql: ${TABLE}.is_force_match ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension_group: notification_received_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.notification_received_timestamp ;;
  }

  dimension: payment_detail {
    type: string
    sql: ${TABLE}.payment_detail ;;
  }

  dimension: payment_source {
    type: string
    sql: ${TABLE}.payment_source ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: provider_code {
    type: string
    sql: ${TABLE}.provider_code ;;
  }

  dimension: raw_payload {
    type: string
    sql: ${TABLE}.raw_payload ;;
  }

  dimension: sender_name {
    type: string
    sql: ${TABLE}.sender_name ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: transaction {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.transaction_time ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: unique_transaction_id {
    type: string
    sql: ${TABLE}.unique_transaction_id ;;
  }

  dimension: payment_detail_reference {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.reference') ;;
  }

  dimension: payment_detail_remark {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.remark') ;;
  }

  dimension: payment_detail_sender_name {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.sender_name') ;;
  }

  dimension: payment_detail_sender_account_number {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.sender_account_number') ;;
  }

  dimension: payment_detail_sender_channel_code {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.sender_channel_code') ;;
  }

  dimension: payment_detail_payment_interface {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.payment_interface') ;;
  }

  dimension: payment_detail_transfer_method {
    type:  string
    sql:  json_extract_scalar(${TABLE}.payment_detail, '$.transfer_method') ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  measure: count {
    type: count
    drill_fields: [id, name, sender_name]
  }
}
