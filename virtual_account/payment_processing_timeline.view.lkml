view: ag_payment_processing_timeline {
  sql_table_name: transform__virtual_accounts.payment_processing_timeline ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: bank_code_display {
    type: string
    sql: ${TABLE}.bank_code_display ;;
  }

  dimension_group: callback_to_merchant_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_created ;;
  }

  dimension_group: callback_to_merchant_first_delivery_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.callback_to_merchant_first_delivery_timestamp ;;
  }

  dimension: callback_to_merchant_id {
    type: string
    sql: ${TABLE}.callback_to_merchant_id ;;
  }

  dimension_group: deposit_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.deposit_created ;;
  }

  dimension: deposit_id {
    type: string
    sql: ${TABLE}.deposit_id ;;
  }

  dimension: is_new_arch {
    type: yesno
    sql: ${TABLE}.is_new_arch ;;
  }

  dimension: is_switching {
    type: yesno
    sql: ${TABLE}.is_switching ;;
  }

  dimension: ledger_account_id {
    type: string
    sql: ${TABLE}.ledger_account_id ;;
  }

  dimension: payment_amount {
    type: number
    sql: ${TABLE}.payment_amount ;;
  }

  dimension_group: payment_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_timestamp ;;
  }

  dimension: step_label {
    type: string
    sql: ${TABLE}.step_label ;;
  }

  dimension: step_order {
    type: number
    sql: ${TABLE}.step_order ;;
  }

  dimension: step_time {
    type: number
    sql: ${TABLE}.step_time ;;
  }

  measure: step_time_p90 {
    type: percentile
    percentile: 90
    sql: ${TABLE}.step_time ;;
  }

  measure: step_time_p95 {
    type: percentile
    percentile: 95
    sql: ${TABLE}.step_time ;;
  }

  measure: step_time_p99 {
    type: percentile
    percentile: 99
    sql: ${TABLE}.step_time ;;
  }

  measure: step_time_avg {
    type: average
    precision: 0
    sql: ${TABLE}.step_time ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: va_payment_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.va_payment_created ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
