view: va_payment_method_mapping {
  sql_table_name: clean__google_sheets.va_payment_method_mapping ;;
  suggestions: no

  dimension: bank {
    type: string
    sql: ${TABLE}.bank ;;
  }

  dimension: bank_merged {
    type: string
    sql: ${TABLE}.bank_merged ;;
  }

  dimension: bank_terminal_name {
    type: string
    sql: ${TABLE}.bank_terminal_name ;;
  }

  dimension: terminal_id {
    type: string
    sql: ${TABLE}.terminal_id ;;
  }

  dimension: terminal_name {
    type: string
    sql: ${TABLE}.terminal_name ;;
  }

  dimension: xd_on_us {
    type: string
    sql: ${TABLE}.xd_on_us ;;
  }

  dimension: xd_payment_channel {
    type: string
    sql: ${TABLE}.xd_payment_channel ;;
  }

  dimension: xd_payment_method {
    type: string
    sql: ${TABLE}.xd_payment_method ;;
  }

  measure: count {
    type: count
    drill_fields: [terminal_name, bank_terminal_name]
  }
}
