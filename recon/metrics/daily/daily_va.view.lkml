view: daily_va {
  derived_table: {
    sql: with data as (
            select *
            , coalesce(date(ledger_timestamp), date(report_timestamp)) as date
            from hive.transform__product_healthcheck.virtual_account
        )
        --------------------------------------------------------------------------------
        , with_report_id as (
            select *
            , row_number() over(partition by report_id order by ledger_va_number desc, date desc) as row
            from data
            where report_id is not null
        )

        , clean_report_id as (
            select *
            from with_report_id
            where row = 1
        )
        --------------------------------------------------------------------------------
        , with_ledger_va_number as (
            select *
            , row_number() over(partition by ledger_va_number order by date desc) as row
            from data
            where report_id is null
        )

        , clean_ledger_va_number as (
            select *
            from with_ledger_va_number
            where row = 1
        )
        --------------------------------------------------------------------------------
        , final as (
            select *
            from clean_ledger_va_number
            UNION ALL
            select *
            from clean_report_id
        )
        --------------------------------------------------------------------------------
        , healthcheck_raw as (
          select
          date
          , coalesce(coalesce(ledger_channel,'Unknown Bank Code'),'none') as recon_channel
          , coalesce(recon_status, 'none') as recon_status
          , coalesce(recon_reason, 'none') as recon_reason
          , ledger_amount
          , report_amount

          from final
        )

        --------------------------------------------------------------------------------
        , healthcheck as (
          SELECT
            recon_channel as product_channel,
            'virtual_account' as product_type,
            date as recon_date,
            CAST(
              COUNT(
                CASE
                  WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') THEN 1
                END
              ) AS DECIMAL (21, 2)
            ) AS unrecon_count,
            CAST(
              COUNT(
                CASE
                  WHEN recon_status = 'RECONCILED' THEN 1
                END
              ) AS DECIMAL(21, 2)
            ) as reconciled_count,
            CAST(COUNT(*) AS DECIMAL(21, 2)) as total_count,
            CAST(
              SUM(
                CASE
                  WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') AND ledger_amount > 0 THEN ledger_amount
                  WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') AND report_amount > 0 THEN report_amount
                  ELSE 0
                END
              ) AS DECIMAL(21, 2)
            ) as unrecon_volume,
            CAST(
              SUM(
                CASE
                  WHEN recon_status = 'RECONCILED' THEN report_amount
                  ELSE 0
                END
              ) AS DECIMAL(21, 2)
            ) as reconciled_volume,
            CAST(
              SUM(
                CASE
                  WHEN recon_reason = 'Unreconciled No Report' THEN ledger_amount
                  ELSE report_amount
                END
              ) AS DECIMAL(21, 2)
            ) as total_volume
          FROM
            healthcheck_raw
          GROUP BY
            date, recon_channel
        )

        SELECT
          product_channel
          , product_type
          , CAST(recon_date AS timestamp) as recon_date
          , total_count as total_transactions
          , reconciled_count as total_reconciled
          , unrecon_count as total_unreconciled
          , total_volume as volume_transactions
          , reconciled_volume as volume_reconciled
          , unrecon_volume as volume_unreconciled
          , CASE WHEN total_count = 0 THEN 0 ELSE CAST((reconciled_count * 100) / total_count AS DECIMAL(21,4)) END AS p_of_reconciled_count
          , CASE WHEN total_count = 0 THEN 0 ELSE CAST((unrecon_count * 100) / total_count AS DECIMAL(21,4)) END AS p_of_unreconciled_count
          , CASE WHEN total_volume = 0 THEN 0 ELSE CAST((reconciled_volume * 100) / total_volume AS DECIMAL(21,4)) END AS p_of_reconciled_volume
          , CASE WHEN total_volume = 0 THEN 0 ELSE CAST((unrecon_volume * 100) / total_volume AS DECIMAL(21,4)) END AS p_of_unreconciled_volume
        FROM healthcheck
        WHERE recon_date is NOT NULL
      ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: product_channel {
    type: string
    sql: ${TABLE}.product_channel ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension_group: recon_date {
    type: time
    sql: ${TABLE}.recon_date ;;
  }

  dimension: total_transactions {
    type: number
    sql: ${TABLE}.total_transactions ;;
  }

  dimension: total_reconciled {
    type: number
    sql: ${TABLE}.total_reconciled ;;
  }

  dimension: total_unreconciled {
    type: number
    sql: ${TABLE}.total_unreconciled ;;
  }

  dimension: volume_transactions {
    type: number
    sql: ${TABLE}.volume_transactions ;;
  }

  dimension: volume_reconciled {
    type: number
    sql: ${TABLE}.volume_reconciled ;;
  }

  dimension: volume_unreconciled {
    type: number
    sql: ${TABLE}.volume_unreconciled ;;
  }

  dimension: p_of_reconciled_count {
    type: number
    sql: ${TABLE}.p_of_reconciled_count ;;
  }

  dimension: p_of_unreconciled_count {
    type: number
    sql: ${TABLE}.p_of_unreconciled_count ;;
  }

  dimension: p_of_reconciled_volume {
    type: number
    sql: ${TABLE}.p_of_reconciled_volume ;;
  }

  dimension: p_of_unreconciled_volume {
    type: number
    sql: ${TABLE}.p_of_unreconciled_volume ;;
  }

  set: detail {
    fields: [
      product_channel,
      product_type,
      recon_date_time,
      total_transactions,
      total_reconciled,
      total_unreconciled,
      volume_transactions,
      volume_reconciled,
      volume_unreconciled,
      p_of_reconciled_count,
      p_of_unreconciled_count,
      p_of_reconciled_volume,
      p_of_unreconciled_volume
    ]
  }
}
