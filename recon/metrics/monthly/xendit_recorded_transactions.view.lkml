view: xendit_recorded_transactions {
  derived_table: {
    sql: with raw_transactions as (
      SELECT
        CASE
          WHEN transactions.product_type = 'CREDIT_CARD'  THEN 'credit_card'
          WHEN (transactions.product_type = 'DIRECT_DEBIT' and transactions.currency = 'PHP')  THEN 'ph_direct_debit'
          WHEN (transactions.product_type = 'DIRECT_DEBIT' and transactions.currency != 'PHP')  THEN 'direct_debit'
          WHEN transactions.product_type = 'DISBURSEMENT'  THEN 'disbursement'
          WHEN transactions.product_type = 'EWALLET'  THEN 'ewallet'
          WHEN transactions.product_type = 'RETAIL_OUTLET'  THEN 'retail_outlet'
          WHEN transactions.product_type = 'IN-HOUSE TRANSACTION'  THEN 'inhouse_transaction'
          WHEN transactions.product_type = 'VIRTUAL_ACCOUNT'  THEN 'virtual_account'
          WHEN transactions.product_type = 'QR_CODE'  THEN 'qr_codes'
          WHEN transactions.product_type = 'PAYLATER'  THEN 'paylater'
          ELSE 'Uncategorised'
        END AS `product_type`,
        transactions.amount / case
          when transactions.currency = 'IDR' then fx_rates.idr_usd
          when transactions.currency = 'PHP' then fx_rates.php_usd
          when transactions.currency = 'MYR' then fx_rates.myr_usd
          when transactions.currency = 'SGD' then fx_rates.sgd_usd
          when transactions.currency = 'VND' then fx_rates.vnd_usd
          when transactions.currency = 'THB' then fx_rates.thb_usd
          else 1 end  AS amount_usd,
        (DATE_FORMAT(from_utc_timestamp(transactions.updated,'Asia/Jakarta'), 'yyyy')) AS `transaction_year`,
        (DATE_FORMAT(from_utc_timestamp(transactions.updated,'Asia/Jakarta'), 'MM')) AS `transaction_month`,
        transactions.amount  AS `amount`,
        transactions.currency  AS `currency`
        FROM transform__transaction_volumes.transaction_backbone  AS transactions
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON case when false then year(CURRENT_DATE) else (YEAR(from_utc_timestamp(transactions.created,'Asia/Jakarta'))) end = fx_rates.year
        WHERE year(transactions.updated) >= 2022
        AND (commercial_model != 'SWITCHER' OR commercial_model IS NULL)
      )
      , agg_transactions as (
        SELECT
          product_type,
          transaction_year,
          transaction_month,
          SUM(amount_usd) as sum_transactions,
          count(amount) as count_transactions
        FROM raw_transactions
        GROUP BY 1, 2, 3
      )
      select * from agg_transactions
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: transaction_year {
    type: string
    sql: ${TABLE}.transaction_year ;;
  }

  dimension: transaction_month {
    type: string
    sql: ${TABLE}.transaction_month ;;
  }

  dimension: sum_transactions {
    type: number
    sql: ${TABLE}.sum_transactions ;;
  }

  dimension: count_transactions {
    type: number
    sql: ${TABLE}.count_transactions ;;
  }

  set: detail {
    fields: [product_type, transaction_year, transaction_month, sum_transactions, count_transactions]
  }
}