include: "/recon/metrics/monthly/xendit_recorded_transactions.view.lkml"

view: reconciled_ro {
  derived_table: {
    sql: with agg_transactions as (
          SELECT * FROM ${xendit_recorded_transactions.SQL_TABLE_NAME}
      )
      , report_data AS (
        SELECT *, row_number() over(partition by report_id order by recon_executed desc) as row
        FROM hive.transform__product_healthcheck.retail_outlet_monthly
        WHERE year(recon_executed) >= 2023
      )
      , clean_report_data as (
        SELECT *
        FROM report_data
        WHERE row = 1
      )
      , ledger_data AS (
        SELECT *, row_number() over(partition by product_id order by recon_executed desc) as row
        FROM hive.transform__product_healthcheck.retail_outlet_monthly
        WHERE year(recon_executed) >= 2023
        AND report_id IS NULL
      )
      , clean_ledger_data as (
        SELECT *
        FROM ledger_data
        WHERE row = 1
      )
      , cleaned_data as (
        SELECT * FROM clean_report_data
        UNION ALL
        SELECT * FROM clean_ledger_data
      )
      , raw_healthcheck_monthly_result AS (
      SELECT
        recon_month,
        CAST(SUM(
          CASE
          WHEN recon_status = 'RECONCILED' THEN amount
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS reconciled_volume,
        CAST(SUM(
          CASE
          WHEN recon_status != 'RECONCILED' AND amount IS NOT NULL THEN amount
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS unreconciled_volume_ledger,
        CAST(SUM(
          CASE
          WHEN recon_status != 'RECONCILED' AND report_amount IS NOT NULL THEN report_amount
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS unreconciled_volume_report,
        CAST(SUM(
          CASE
          WHEN recon_status = 'RECONCILED' THEN 1
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS reconciled_count,
        CAST(SUM(
          CASE
          WHEN recon_status != 'RECONCILED' AND amount IS NOT NULL THEN 1
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS unreconciled_count_ledger,
        CAST(SUM(
          CASE
          WHEN recon_status != 'RECONCILED' AND report_amount IS NOT NULL THEN 1
          ELSE 0
          END) AS DECIMAL(21,2)
          ) AS unreconciled_count_report
        FROM
          cleaned_data
        GROUP BY
          recon_month
        ORDER BY
          1 DESC
      )
      , healthcheck AS (
      SELECT
        'retail_outlet' as product_type,
        substr(recon_month, 1, 4) as recon_year,
        CONCAT(CASE WHEN length(substr(recon_month, 6, 2)) = 1 THEN '0' ELSE '' END, substr(recon_month, 6, 2)) as recon_month,
        reconciled_count + unreconciled_count_ledger + unreconciled_count_report as total_transactions,
        reconciled_count as total_reconciled,
        (reconciled_volume + unreconciled_volume_ledger + unreconciled_volume_report) / fx_rates.idr_usd as volume_transactions,
        reconciled_volume / fx_rates.idr_usd as volume_reconciled
      FROM
        raw_healthcheck_monthly_result
      LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON substr(recon_month, 1, 4) = cast(fx_rates.year as string)
      )

      select
        at.product_type as product_type,
        at.transaction_year as year,
        at.transaction_month as month,
        at.sum_transactions as volume_transactions,
        h.volume_transactions as recon_volume_transactions,
        h.volume_reconciled as recon_volume_reconciled,
        at.count_transactions as total_transactions,
        h.total_transactions as recon_total_transactions,
        h.total_reconciled as recon_total_reconciled,
        (h.total_reconciled / at.count_transactions)*100 as p_total_reconciled,
        (h.volume_reconciled / at.sum_transactions)*100 as p_volume_reconciled
      from agg_transactions at
        full outer join healthcheck h on
        at.product_type = h.product_type
        and at.transaction_month = h.recon_month
        and at.transaction_year = h.recon_year
      where at.product_type = 'retail_outlet'
      order by transaction_year, transaction_month ASC
      ;;
    }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: year {
    type: string
    sql: ${TABLE}."year" ;;
  }

  dimension: month {
    type: string
    sql: ${TABLE}."month" ;;
  }

  dimension: volume_transactions {
    type: number
    sql: ${TABLE}.volume_transactions ;;
  }

  dimension: recon_volume_transactions {
    type: number
    sql: ${TABLE}.recon_volume_transactions ;;
  }

  dimension: recon_volume_reconciled {
    type: number
    sql: ${TABLE}.recon_volume_reconciled ;;
  }

  dimension: total_transactions {
    type: number
    sql: ${TABLE}.total_transactions ;;
  }

  dimension: recon_total_transactions {
    type: number
    sql: ${TABLE}.recon_total_transactions ;;
  }

  dimension: recon_total_reconciled {
    type: number
    sql: ${TABLE}.recon_total_reconciled ;;
  }

  dimension: p_total_reconciled {
    type: number
    sql: ${TABLE}.p_total_reconciled ;;
  }

  dimension: p_volume_reconciled {
    type: number
    sql: ${TABLE}.p_volume_reconciled ;;
  }

  set: detail {
    fields: [
      product_type,
      year,
      month,
      volume_transactions,
      recon_volume_transactions,
      recon_volume_reconciled,
      total_transactions,
      recon_total_transactions,
      recon_total_reconciled,
      p_total_reconciled,
      p_volume_reconciled
    ]
  }
}