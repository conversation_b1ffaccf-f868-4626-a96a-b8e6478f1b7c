view: agg_all_reconciled {
  derived_table: {
    sql: with raw_transactions as (
        SELECT
          CASE
            WHEN transactions.product_type = 'CREDIT_CARD'  THEN 'credit_card'
            WHEN (transactions.product_type = 'DIRECT_DEBIT' and transactions.currency = 'PHP')  THEN 'ph_direct_debit'
            WHEN (transactions.product_type = 'DIRECT_DEBIT' and transactions.currency != 'PHP')  THEN 'direct_debit'
            WHEN transactions.product_type = 'DISBURSEMENT'  THEN 'disbursement'
            WHEN transactions.product_type = 'EWALLET'  THEN 'ewallet'
            WHEN transactions.product_type = 'RETAIL_OUTLET'  THEN 'retail_outlet'
            WHEN transactions.product_type = 'IN-HOUSE TRANSACTION'  THEN 'inhouse_transaction'
            WHEN transactions.product_type = 'VIRTUAL_ACCOUNT'  THEN 'virtual_account'
            WHEN transactions.product_type = 'QR_CODE'  THEN 'qr_codes'
            WHEN transactions.product_type = 'PAYLATER'  THEN 'paylater'
            ELSE 'Uncategorised'
          END AS `product_type`,
          transactions.amount / case
            when transactions.currency = 'IDR' then fx_rates.idr_usd
            when transactions.currency = 'PHP' then fx_rates.php_usd
            when transactions.currency = 'MYR' then fx_rates.myr_usd
            when transactions.currency = 'SGD' then fx_rates.sgd_usd
            when transactions.currency = 'VND' then fx_rates.vnd_usd
            when transactions.currency = 'THB' then fx_rates.thb_usd
            else 1 end  AS amount_usd,
          (DATE_FORMAT(from_utc_timestamp(transactions.updated,'Asia/Jakarta'), 'yyyy')) AS `transaction_year`,
          (DATE_FORMAT(from_utc_timestamp(transactions.updated,'Asia/Jakarta'), 'MM')) AS `transaction_month`,
          transactions.amount  AS `amount`
        FROM transform__transaction_volumes.transaction_backbone  AS transactions
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON YEAR(from_utc_timestamp(transactions.created,'Asia/Jakarta')) = fx_rates.year
        WHERE year(transactions.created) >= 2022 or year(transactions.updated) >= 2022 or year(transactions.dt) >= 2022 and year(transactions.updated) <= 2200 and year(transactions.created) <= 2200
        AND (commercial_model != 'SWITCHER' OR commercial_model IS NULL)
      )
      , agg_transactions as (
        SELECT
          product_type,
          transaction_year,
          transaction_month,
          SUM(amount_usd) as sum_transactions,
          count(*) as count_transactions
        FROM raw_transactions
        GROUP BY 1, 2, 3
      )
      , disb_report_data AS (
        SELECT *, row_number() over(partition by report_row_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.disbursement_monthly
        WHERE year(recon_executed) >= 2023
        AND recon_executed IN (
          SELECT recon_executed
          FROM (
            SELECT max(recon_executed) as recon_executed, year(recon_executed) as year_executed
            FROM transform__product_healthcheck.disbursement_monthly
            group by 2
          ) t1
        )
      )
      , disb_clean_report_data as (
        SELECT *
        FROM disb_report_data
        WHERE row = 1
      )
      , disb_ledger_data AS (
        SELECT *, row_number() over(partition by product_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.disbursement_monthly
        WHERE year(recon_executed) >= 2023
        AND report_row_id IS NULL
        AND recon_executed IN (
          SELECT recon_executed
          FROM (
            SELECT max(recon_executed) as recon_executed, year(recon_executed) as year_executed
            FROM transform__product_healthcheck.disbursement_monthly
            group by 2
          ) t1
        )
      )
      , disb_clean_ledger_data as (
        SELECT *
        FROM disb_ledger_data
        WHERE row = 1
      )
      , disb_cleaned_data as (
        SELECT * FROM disb_clean_report_data
        UNION ALL
        SELECT * FROM disb_clean_ledger_data
      )
      , disb_statistic AS (
        SELECT
          'disbursement' as product_type,
          substr(recon_month, 1, 4) as recon_year,
          CONCAT(CASE WHEN length(substr(recon_month, 6, 2)) = 1 THEN '0' ELSE '' END, substr(recon_month, 6, 2)) as recon_month,
          CAST(
            COUNT(
              CASE
                WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') THEN 1
              END
            ) AS DECIMAL (21, 2)
          ) AS unrecon_count,
          CAST(
            COUNT(
              CASE
                WHEN recon_status = 'RECONCILED' THEN 1
              END
            ) AS DECIMAL(21, 2)
          ) as reconciled_count,
          CAST(COUNT(*) AS DECIMAL(21, 2)) as total_count,
          CAST(
            SUM(
              CASE
                WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') AND report_debit > 0 THEN report_debit
                WHEN recon_status IN ('UNRECONCILED', 'UNRECONCILED_CONFIRMED') AND amount > 0 THEN amount
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) as unrecon_volume,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'RECONCILED' THEN report_debit
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) as reconciled_volume,
          CAST(
            SUM(
              CASE
                WHEN recon_reason = 'Unreconciled No Report' THEN amount
                ELSE report_debit
              END
            ) AS DECIMAL(21, 2)
          ) as total_volume
        FROM
          disb_cleaned_data
        GROUP BY
          recon_month
      ),
      disb_healthcheck as (
        SELECT
          product_type
          , recon_year
          , recon_month
          , total_count as total_transactions
          , reconciled_count as total_reconciled
          , total_volume / fx_rates.idr_usd as volume_transactions
          , reconciled_volume / fx_rates.idr_usd as volume_reconciled
        FROM disb_statistic
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON recon_year = cast(fx_rates.year as string)
      ),
      va_raw_healthcheck_ch as (
        SELECT *
        FROM transform__product_healthcheck.monthly_virtual_account
        WHERE month > '2022'
      ),
      va_raw_healthcheck as (
        SELECT
          month,
          CAST(SUM(
            CASE
              WHEN recon_status = 'RECONCILED' THEN CAST(amount AS DECIMAL(21,2))
              ELSE 0 END
          ) AS DECIMAL(21,2)) AS reconciled_volume,
          CAST(SUM(
            CASE
              WHEN report_row_id IS NULL AND amount IS NOT NULL THEN CAST(amount AS DECIMAL(21,2))
              WHEN product_id IS NULL AND report_credit IS NOT NULL THEN CAST(report_credit AS DECIMAL(21,2))
              ELSE 0 END
          ) AS DECIMAL(21,2)) as unreconciled_volume,
          CAST(SUM(
            CASE
              WHEN recon_status = 'RECONCILED' THEN 1
              ELSE 0 END
          ) AS DECIMAL(21,2)) as reconciled_count,
          CAST(SUM(
            CASE
              WHEN recon_status = 'UNRECONCILED' OR recon_status = 'UNRECONCILED_CONFIRMED' THEN 1
              ELSE 0 END
          ) AS DECIMAL(21,2)) as unreconciled_count,
          SUM(
            CASE WHEN product_id IS NULL THEN 1
            ELSE 0 END
          ) AS no_ledger_rows,
          SUM(
            CASE WHEN report_row_id IS NULL THEN 1
            ELSE 0 END
          ) AS no_report_rows,
          SUM (
            CASE WHEN product_id IS NOT NULL THEN 1
            ELSE 0 END
          ) AS total_ledger,
          SUM (
            CASE WHEN report_row_id IS NOT NULL THEN 1
            ELSE 0 END
          ) AS total_report
        FROM
          va_raw_healthcheck_ch
        GROUP BY month
        ORDER BY month DESC
      ),
      va_healthcheck as (
        SELECT
          'virtual_account' as product_type,
          substr(month, 1, 4)  as recon_year,
          substr(month, 6, 2)  as recon_month,
          reconciled_count + unreconciled_count as total_transactions,
          reconciled_count as total_reconciled,
          (coalesce(reconciled_volume, 0) + coalesce(unreconciled_volume, 0)) / fx_rates.idr_usd as volume_transactions,
          coalesce(reconciled_volume, 0) / fx_rates.idr_usd as volume_reconciled
        FROM
          va_raw_healthcheck
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON substr(month, 1, 4) = cast(fx_rates.year as string)
      ),
      ro_report_data AS (
        SELECT *, row_number() over(partition by report_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.retail_outlet_monthly
        WHERE year(recon_executed) >= 2023
      )
      , ro_clean_report_data as (
        SELECT *
        FROM ro_report_data
        WHERE row = 1
      )
      , ro_ledger_data AS (
        SELECT *, row_number() over(partition by product_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.retail_outlet_monthly
        WHERE year(recon_executed) >= 2023
        AND report_id IS NULL
      )
      , ro_clean_ledger_data as (
        SELECT *
        FROM ro_ledger_data
        WHERE row = 1
      )
      , ro_cleaned_data as (
        SELECT * FROM ro_clean_report_data
        UNION ALL
        SELECT * FROM ro_clean_ledger_data
      )
      , ro_raw_healthcheck_monthly_result AS (
        SELECT
          recon_month,
          CAST(SUM(
            CASE
            WHEN recon_status = 'RECONCILED' THEN amount
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS reconciled_volume,
          CAST(SUM(
            CASE
            WHEN recon_status != 'RECONCILED' AND amount IS NOT NULL THEN amount
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS unreconciled_volume_ledger,
          CAST(SUM(
            CASE
            WHEN recon_status != 'RECONCILED' AND report_amount IS NOT NULL THEN report_amount
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS unreconciled_volume_report,
          CAST(SUM(
            CASE
            WHEN recon_status = 'RECONCILED' THEN 1
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS reconciled_count,
          CAST(SUM(
            CASE
            WHEN recon_status != 'RECONCILED' AND amount IS NOT NULL THEN 1
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS unreconciled_count_ledger,
          CAST(SUM(
            CASE
            WHEN recon_status != 'RECONCILED' AND report_amount IS NOT NULL THEN 1
            ELSE 0
            END) AS DECIMAL(21,2)
            ) AS unreconciled_count_report
        FROM
          ro_cleaned_data
        GROUP BY
          recon_month
        ORDER BY
          1 DESC
      )
      , ro_healthcheck AS (
        SELECT
          'retail_outlet' as product_type,
          substr(recon_month, 1, 4) as recon_year,
          CONCAT(CASE WHEN length(substr(recon_month, 6, 2)) = 1 THEN '0' ELSE '' END, substr(recon_month, 6, 2)) as recon_month,
          reconciled_count + unreconciled_count_ledger + unreconciled_count_report as total_transactions,
          reconciled_count as total_reconciled,
          (reconciled_volume + unreconciled_volume_ledger + unreconciled_volume_report) / fx_rates.idr_usd as volume_transactions,
          reconciled_volume / fx_rates.idr_usd as volume_reconciled
        FROM
          ro_raw_healthcheck_monthly_result
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON substr(recon_month, 1, 4) = cast(fx_rates.year as string)
      ),
      cc_healthcheck_src AS (
        SELECT *,
        CASE
          WHEN report_channel_code IS NOT NULL THEN report_channel_code
          ELSE channel_code
        END AS channel
        FROM transform__product_healthcheck.monthly_credit_card
      ),
      cc_raw_healthcheck AS (
        SELECT
          month,
          channel,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'RECONCILED' THEN amount
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS reconciled_volume,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'UNRECONCILED'
                AND amount IS NOT NULL THEN amount
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS unreconciled_v_ledger,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'UNRECONCILED'
                AND report_credit IS NOT NULL THEN report_credit
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS unreconciled_v_report,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'RECONCILED' THEN 1
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS reconciled_count,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'UNRECONCILED'
                AND product_id IS NOT NULL THEN 1
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS unreconciled_c_ledger,
          CAST(
            SUM(
              CASE
                WHEN recon_status = 'UNRECONCILED'
                AND report_row_id IS NOT NULL THEN 1
                ELSE 0
              END
            ) AS DECIMAL(21, 2)
          ) AS unreconciled_c_report
          FROM cc_healthcheck_src
          GROUP BY month, channel ORDER BY month, channel
        ),
      cc_four AS (
        SELECT
          month,
          channel,
          reconciled_count + unreconciled_c_ledger + unreconciled_c_report as total_transactions,
          reconciled_count as total_reconciled,
          unreconciled_c_ledger + unreconciled_c_report as total_unreconciled,
          reconciled_volume + unreconciled_v_ledger + unreconciled_v_report as volume_transactions,
          reconciled_volume as volume_reconciled,
          unreconciled_v_ledger + unreconciled_v_report as volume_unreconciled,
          CAST(
            reconciled_count * 100 /(
              reconciled_count + unreconciled_c_ledger + unreconciled_c_report
            ) AS DECIMAL(21, 2)
          ) AS `p_of_reconciled_count`,
          CAST(
            (unreconciled_c_ledger + unreconciled_c_report) * 100 /(
              reconciled_count + unreconciled_c_ledger + unreconciled_c_report
            ) AS DECIMAL(21, 2)
          ) AS `p_of_unreconciled_count`,
          CAST(
            reconciled_volume * 100 /(
              reconciled_volume + unreconciled_v_ledger + unreconciled_v_report
            ) AS DECIMAL(21, 2)
          ) AS `p_of_reconciled_volume`,
          CAST(
            (unreconciled_v_ledger + unreconciled_v_report) * 100 /(
              reconciled_volume + unreconciled_v_ledger + unreconciled_v_report
            ) AS DECIMAL(21, 2)
          ) AS `p_of_unreconciled_volume`
        FROM
          cc_raw_healthcheck
        ORDER BY
          month, channel
      ),
      cc_invalid_ref_number AS (
        SELECT count(1) AS cnt, month, channel
        FROM cc_healthcheck_src WHERE report_row_id IS NOT NULL
        AND (
          report_merchant_ref_number IS NULL OR report_merchant_ref_number = ''
          OR report_merchant_ref_number = '-'
        ) GROUP BY month, channel
      ),
      cc_total_cc_inquiry_rows AS (
        SELECT count(1) AS cnt, month, channel FROM cc_healthcheck_src
        WHERE report_row_id IS NOT NULL GROUP BY month, channel
      ),
      cc_invalid_ref_number_percentage AS (
        SELECT
          tcir.month,
          tcir.channel,
          CAST(
            CASE
              WHEN irn.cnt IS NULL THEN 0.00
              ELSE (irn.cnt / tcir.cnt * 100)
            END AS DECIMAL(21, 2)
          ) AS invalid_ref_number_percentage
        FROM cc_invalid_ref_number AS irn FULL OUTER JOIN
        cc_total_cc_inquiry_rows AS tcir
        ON irn.month = tcir.month
        AND irn.channel = tcir.channel
      ),
      cc_healthcheck AS (
        SELECT
          'credit_card' as product_type,
          substr(four.month, 1, 4)  as recon_year,
          substr(four.month, 6, 2)  as recon_month,
          sum(four.total_transactions) as total_transactions,
          sum(four.total_reconciled) as total_reconciled,
          sum(four.volume_transactions) as volume_transactions,
          sum(four.volume_reconciled) as volume_reconciled
        FROM
          cc_four four FULL
        OUTER JOIN cc_invalid_ref_number_percentage AS irnp
          ON irnp.month = four.month AND irnp.channel = four.channel
        GROUP BY 1, 2, 3
      ),
      cc_healthcheck_usd AS (
        SELECT
          product_type,
          recon_year,
          recon_month,
          total_transactions,
          total_reconciled,
          volume_transactions / fx_rates.idr_usd as volume_transactions,
          volume_reconciled / fx_rates.idr_usd as volume_reconciled
        FROM
          cc_healthcheck
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON recon_year = cast(fx_rates.year as string)
      )
      , ewallet_report_data AS (
        SELECT *, row_number() over(partition by report_row_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.monthly_ewallet
        WHERE year(recon_executed) >= 2023
        and recon_executed IN (
          SELECT recon_executed
          FROM (
            SELECT max(recon_executed) as recon_executed, year(recon_executed) as year_executed
            FROM transform__product_healthcheck.monthly_ewallet
            group by 2
          ) t1
        )
      )
      , ewallet_clean_report_data as (
        SELECT *
        FROM ewallet_report_data
      )
      , ewallet_ledger_data AS (
        SELECT *, row_number() over(partition by product_id order by recon_executed desc) as row
        FROM transform__product_healthcheck.monthly_ewallet
        WHERE year(recon_executed) >= 2023
        AND report_row_id IS NULL
        and recon_executed IN (
          SELECT recon_executed
          FROM (
            SELECT max(recon_executed) as recon_executed, year(recon_executed) as year_executed
            FROM transform__product_healthcheck.monthly_ewallet
            group by 2
          ) t1
        )
      )
      , ewallet_clean_ledger_data as (
        SELECT *
        FROM ewallet_ledger_data
        WHERE row = 1
      )
      , ewallet_cleaned_data as (
        SELECT * FROM ewallet_clean_report_data
        UNION ALL
        SELECT * FROM ewallet_clean_ledger_data
      )
      , ewallet_raw_healthcheck_ch as (
        SELECT *,
        CASE
          WHEN channel_name IS NOT NULL THEN channel_name
          ELSE report_bank_code
        END as channel
        FROM ewallet_cleaned_data
      ),
      ewallet_raw_healthcheck as (
        SELECT
          recon_month,
          CASE
            WHEN (channel = 'ID_DANA_QRIS') THEN 'qr_codes'
            WHEN (channel = 'ID_LINKAJA_QRIS') THEN 'qr_codes'
            WHEN (channel = 'ID_DANA_EWALLET') THEN 'ewallet'
            WHEN (channel = 'ID_LINKAJA_EWALLET') THEN 'ewallet'
            WHEN (channel = 'ID_OVO_EWALLET') THEN 'ewallet'
            WHEN (channel = 'ID_SHOPEEPAY_EWALLET') THEN 'ewallet'
          END AS product_type,
          CAST(SUM(
            CASE
              WHEN recon_status = 'RECONCILED' THEN amount
              ELSE 0 END
          ) AS DECIMAL(21,2)) AS reconciled_volume,
          CAST(SUM(
            CASE
              WHEN recon_status = 'UNRECONCILED' AND amount IS NOT NULL THEN amount
              WHEN recon_status = 'UNRECONCILED' AND report_credit IS NOT NULL THEN report_credit
              WHEN recon_status = 'UNRECONCILED CONFIRM' AND amount IS NOT NULL THEN amount
              WHEN recon_status = 'UNRECONCILED CONFIRM' AND report_credit IS NOT NULL THEN report_credit
              ELSE 0 END
          ) AS DECIMAL(21,2)) as unreconciled_volume,
          CAST(SUM(
            CASE
              WHEN recon_status = 'RECONCILED' THEN 1
              ELSE 0 END
          ) AS DECIMAL(21,2)) as reconciled_count,
          CAST(SUM(
            CASE
              WHEN recon_status = 'UNRECONCILED' THEN 1
              WHEN recon_status = 'UNRECONCILED CONFIRM' THEN 1
              ELSE 0 END
          ) AS DECIMAL(21,2)) as unreconciled_count,
          SUM(
            CASE WHEN recon_reason = 'Unreconciled No Ledger' THEN 1
            ELSE 0 END
          ) AS no_ledger_rows,
          SUM(
            CASE WHEN recon_reason = 'Unreconciled No Report' THEN 1
            ELSE 0 END
          ) AS no_report_rows,
          SUM (
            CASE WHEN product_id IS NOT NULL THEN 1
            ELSE 0 END
          ) AS total_ledger,
          SUM (
            CASE WHEN report_row_id IS NOT NULL THEN 1
            ELSE 0 END
          ) AS total_report
        FROM
          ewallet_raw_healthcheck_ch
        GROUP BY 1, 2
      ),
      ewallet_healthcheck as (
        SELECT
          product_type,
          substr(recon_month, 1, 4) as recon_year,
          substr(recon_month, 6, 2) as recon_month,
          reconciled_count + unreconciled_count as total_transactions,
          reconciled_count as total_reconciled,
          (reconciled_volume + unreconciled_volume) / fx_rates.idr_usd as volume_transactions,
          reconciled_volume / fx_rates.idr_usd as volume_reconciled
        FROM
          ewallet_raw_healthcheck
        LEFT JOIN transform__fpna.fx_rates_by_year   AS fx_rates ON substr(recon_month, 1, 4) = cast(fx_rates.year as string)
      ),


      healthcheck as (
      SELECT * FROM disb_healthcheck
      UNION ALL
      SELECT * FROM va_healthcheck
      UNION ALL
      SELECT * FROM ro_healthcheck
      UNION ALL
      SELECT * FROM cc_healthcheck_usd
      UNION ALL
      SELECT * FROM ewallet_healthcheck
      )

      select
      at.product_type as product_type,
      at.transaction_year as year,
      at.transaction_month as month,
      coalesce(at.sum_transactions, 0) as volume_transactions,
      coalesce(h.volume_transactions, 0) as recon_volume_transactions,
      coalesce(h.volume_reconciled, 0) as recon_volume_reconciled,
      coalesce(at.count_transactions, 0) as total_transactions,
      coalesce(h.total_transactions, 0) as recon_total_transactions,
      coalesce(h.total_reconciled, 0) as recon_total_reconciled,
      (h.total_reconciled / at.count_transactions)*100 as p_total_reconciled,
      (h.volume_reconciled / at.sum_transactions)*100 as p_volume_reconciled
      from agg_transactions at
      full outer join healthcheck h on
      at.product_type = h.product_type
      and at.transaction_month = h.recon_month
      and at.transaction_year = h.recon_year
      where at.product_type is not null
      order by transaction_year, transaction_month ASC
      ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: year {
    type: string
    sql: ${TABLE}.year ;;
  }

  dimension: month {
    type: string
    sql: ${TABLE}."month" ;;
  }

  dimension: volume_transactions {
    type: number
    sql: ${TABLE}.volume_transactions ;;
  }

  dimension: recon_volume_transactions {
    type: number
    sql: ${TABLE}.recon_volume_transactions ;;
  }

  dimension: recon_volume_reconciled {
    type: number
    sql: ${TABLE}.recon_volume_reconciled ;;
  }

  dimension: total_transactions {
    type: number
    sql: ${TABLE}.total_transactions ;;
  }

  dimension: recon_total_transactions {
    type: number
    sql: ${TABLE}.recon_total_transactions ;;
  }

  dimension: recon_total_reconciled {
    type: number
    sql: ${TABLE}.recon_total_reconciled ;;
  }

  measure: sum_transactions_count {
    description: "Total filtered transactions count"
    type: sum
    sql: ${total_transactions} ;;
  }

  measure: sum_internal_count {
    description: "Total filtered internal transactions count"
    type: sum
    sql: ${recon_total_transactions} ;;
  }

  measure: sum_reconciled_count {
    description: "Total filtered reconciled transactions count"
    type: sum
    sql: ${recon_total_reconciled} ;;
  }

  measure: sum_transactions_vol {
    description: "Total filtered transactions volume"
    type: sum
    sql: ${volume_transactions} ;;
  }

  measure: sum_internal_vol {
    description: "Total filtered internal transactions volume"
    type: sum
    sql: ${recon_total_transactions} ;;
  }

  measure: sum_reconciled_vol {
    description: "Total filtered reconciled transactions volume"
    type: sum
    sql: ${recon_volume_reconciled} ;;
  }

  set: detail {
    fields: [
      product_type,
      year,
      month,
      volume_transactions,
      recon_volume_transactions,
      recon_volume_reconciled,
      total_transactions,
      recon_total_transactions,
      recon_total_reconciled
    ]
  }
}