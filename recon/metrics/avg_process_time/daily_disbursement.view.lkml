view: daily_disbursement_datediff {
  derived_table: {
    sql: with data as (
          select *
          from transform__product_healthcheck.disbursement_v4
      )
      --------------------------------------------------------------------------------
      , with_product_id_recon as (
          select *
          , row_number() over(partition by product_id order by report_row_id desc, recon_finished_at asc) as row
          from data
          where product_id is not null and recon_status = 'RECONCILED'
      )

      , clean_product_id_recon as (
      select *
      from with_product_id_recon
      where row = 1
      )
      --------------------------------------------------------------------------------
      , with_product_id_unrecon as (
      select *
      , row_number() over(partition by product_id order by report_row_id desc, recon_finished_at asc) as row
      from data
      where product_id is not null and recon_status != 'RECONCILED'
      )

      , clean_product_id_unrecon as (
      select *
      from with_product_id_unrecon
      where row = 1
      )
      --------------------------------------------------------------------------------
      , combined as (
          select * from clean_product_id_recon
          UNION ALL
          select * from clean_product_id_unrecon
      )
      , with_take_latest_status as (
          select *
          , row_number() over(partition by product_id order by recon_finished_at desc) as row_latest
          from combined
      )
      , final as (
        select * from with_take_latest_status where row_latest = 1
      )

      --------------------------------------------------------------------------------
      select
        datediff(hour, completed_timestamp, recon_finished_at) as hours_difference,
        'disbursement' as product_type,
        recon_bank as product_channel,
        completed_timestamp as product_settlement_timestamp,
        recon_finished_at,
        report_row_id as report_id,
        product_id,
        recon_status
      from final
      ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: hours_difference {
    type: number
    sql: ${TABLE}.hours_difference ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_channel {
    type: string
    sql: ${TABLE}.product_channel ;;
  }

  dimension_group: product_settlement_timestamp {
    type: time
    sql: ${TABLE}.product_settlement_timestamp ;;
  }

  dimension_group: recon_finished_at {
    type: time
    sql: ${TABLE}.recon_finished_at ;;
  }

  dimension: report_id {
    type: string
    sql: ${TABLE}.report_id ;;
  }

  dimension: product_id {
    type: string
    sql: ${TABLE}.product_id ;;
  }

  dimension: recon_status {
    type: string
    sql: ${TABLE}.recon_status ;;
  }

  set: detail {
    fields: [
      hours_difference,
      product_type,
      product_channel,
      product_settlement_timestamp_time,
      recon_finished_at_time,
      report_id,
      product_id,
      recon_status
    ]
  }
}
