view: domestic_remittance_payouts_metrics {

  sql_table_name: transform__remittance.domestic_remittance_payouts_metrics   ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }
  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }
  dimension: amount {
    type: string
    sql: ${TABLE}.amount ;;
  }
  dimension: channel_code {
    type:  string
    sql: ${TABLE}.channel_code ;;
  }
  dimension: flow_type {
    type: string
    sql: ${TABLE}.flow_type ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension_group: product_creation_timestamp {
    type: time
    timeframes:[raw,
      time,
      time_of_day,
      hour_of_day,
      date,
      week,
      day_of_week,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  dimension: end_to_end_time {
    type: number
    sql: ${TABLE}.end_to_end_time / 1000;;
  }

  dimension: end_to_end_time_without_compliance {
    type: number
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000 ;;
  }

  dimension: time_to_perform_initial_processing {
    type: number
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
  }

  dimension: time_to_perform_compliance_verification {
    type: number
    sql: ${TABLE}.time_to_perform_compliance_verification/ 1000 ;;
  }

  dimension: time_to_perform_ledger_creation {
    type: number
    sql: ${TABLE}.time_to_perform_ledger_creation / 1000;;
  }

  dimension: time_to_perform_disbursement_creation {
    type: number
    sql: ${TABLE}.time_to_perform_disbursement_creation / 1000;;
  }

  dimension: time_to_perform_disbursement {
    type: number
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
  }
  dimension: time_to_perform_disbursing {
    type: number
    sql: ${TABLE}.time_to_perform_disbursing / 1000;;
  }

  dimension: time_to_perform_ledger_updating {
    type: number
    sql: ${TABLE}.time_to_perform_ledger_updating / 1000;;
  }

  dimension: time_to_process_customer_request {
    type: number
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
  }

  dimension: time_to_process_customer_request_without_compliance {
    type: number
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
  }

  dimension: time_to_process_on_remittance_side {
    type: number
    sql: ${TABLE}.time_to_process_on_remittance_side / 1000 ;;
  }

  dimension: time_to_create_webhook {
    type: number
    sql: ${TABLE}.time_to_create_webhook / 1000;;
  }

  dimension: time_to_send_first_webhook {
    type: number
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
  }

  dimension: slow_sla_reason {
    type:  string
    case:  {
      when: {
        sql:  ${time_to_perform_disbursing} >= 13 ;;
        label: "stuck on disbursing"
      }
      when: {
        sql:  ${time_to_perform_disbursement_creation} > 3 ;;
        label: "stuck on creating disbursement"
      }
      when: {
        sql:  ${time_to_perform_initial_processing} > 3 ;;
        label: "stuck on checking name blacklist"
      }
      when: {
        sql:  ${time_to_perform_ledger_creation} > 3 ;;
        label: "stuck on creating ledger "
      }
      when: {
        sql:  ${time_to_perform_ledger_updating} > 3 ;;
        label: "stuck on updating ledger"
      }
      when: {
        sql:  ${time_to_create_webhook} > 2 ;;
        label: "failed on creating webhook"
      }
      when: {
        sql:  ${time_to_send_first_webhook} >= 600 ;;
        label: "failed on sending webhook (auto retry)"
      }
      when: {
        sql:  ${time_to_send_first_webhook} > 2 ;;
        label: "slow on sending webhook"
      }
      else: "others"
    }
  }

  dimension: ID_Speed_SLA { # https://docs.xendit.co/xendisburse/limits-and-processing-times#processing-time
    type:  string
    case: {
      when: {
        sql:channel_code = 'BCA' and (amount < 10000 or amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '20:00' or ${product_creation_timestamp_time_of_day} <= '06:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'BCA' and (amount < 10000 or amount > ********* ) and (${product_creation_timestamp_time_of_day} < '20:00' and ${product_creation_timestamp_time_of_day} > '06:59') ;;
        label: "instant"
      }
      when: {
        sql:(channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '03:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:(channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day}<= '23:00' and ${product_creation_timestamp_time_of_day} > '03:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '01:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '01:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'SAHABAT_SAMPOERNA' and (amount < *********0) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '02:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'SAHABAT_SAMPOERNA' and (amount < *********0) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '02:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '00:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '00:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'CIMB' and  (amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '21:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'CIMB' and  (amount > ********* ) and (${product_creation_timestamp_time_of_day} < '21:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'SINARMAS' and ( amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '14:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'SINARMAS' and ( amount > ********* ) and (${product_creation_timestamp_time_of_day} < '14:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "instant"
      }
      when: {
        sql: (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (amount >= 10000 or amount < ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql: (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (amount >= 10000 or amount < ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "instant"
      }
      when: {
        sql: (amount <= ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql: (amount <= ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "instant"
      }
      else: "others"
    }
  }

  dimension: ID_general_downtime {
    type:  string
    case: {
      when: {
        sql: ${product_creation_timestamp_time_of_day} >= '05:01' and ${product_creation_timestamp_time_of_day} <= '23:00' ;;
        label: "general instant"
      }
      else: "general downtime"
    }
  }

  # Measure end_to_end_time
  measure:end_to_end_time_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time / 1000;;
  }

  measure:end_to_end_time_elapsed_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time / 1000;;
  }

  measure:end_to_end_time_elapsed_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time / 1000;;
  }

  # Measure end_to_end_time_without_compliance
  measure:end_to_end_time_without_compliance_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000;;
  }

  measure:end_to_end_time_without_compliance_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time_without_compliance/ 1000 ;;
  }

  measure:end_to_end_time_without_compliance_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000;;
  }

  # Measure time_to_perform_initial_processing
  measure:time_to_perform_initial_processing_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
  }

  measure:time_to_perform_initial_processing_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
  }

  measure:time_to_perform_initial_processing_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
  }

  # Measure time_to_perform_compliance_verification
  measure:time_to_perform_compliance_verification_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
  }

  measure:time_to_perform_compliance_verification_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
  }

  measure:time_to_perform_compliance_verification_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
  }

  # Measure time_to_perform_ledger_creation
  measure:time_to_perform_ledger_creation_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_ledger_creation / 1000;;
  }

  measure:time_to_perform_ledger_creation_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_ledger_creation / 1000;;
  }

  measure:time_to_perform_ledger_creation_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_ledger_creation / 1000;;
  }

  # Measure time_to_perform_disbursement
  measure:time_to_perform_disbursement_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
  }

  measure:time_to_perform_disbursement_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
  }

  measure:time_to_perform_disbursement_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
  }

  # Measure time_to_process_customer_request
  measure:time_to_process_customer_request_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
  }

  measure:time_to_process_customer_request_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
  }

  measure:time_to_process_customer_request_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
  }

  # Measure time_to_process_customer_request_without_compliance
  measure:time_to_process_customer_request_without_compliance_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
  }

  measure:time_to_process_customer_request_without_compliance_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
  }

  measure:time_to_process_customer_request_without_compliance_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
  }

  # Measure time_to_send_first_webhook
  measure:time_to_send_first_webhook_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
  }

  measure:time_to_send_first_webhook_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
  }

  measure:time_to_send_first_webhook_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
  }
}
