view: remittance_payouts_metrics {
  derived_table: {
    # datagroup_trigger: dashboard_update_time
    sql:
    select * from (SELECT
      id,
      reference_id,
      business_id,
      destination_amount,
      disbursement_id,
      status,
      flow_type,
      product_creation_timestamp,
      end_to_end_time,
      time_to_process_customer_request,
      time_to_perform_initial_processing,
      time_to_perform_risk_assessment,
      null as time_to_perform_ledger_creation,
      null as time_to_perform_ledger_updating,
      time_to_perform_compliance_verification,
      time_to_perform_disbursement,
      time_to_disbursement_service_perform_disbursement,
      time_to_receive_disbursement_callback,
      time_to_send_first_webhook,
      destination_currency,
      channel_code,
      failure_code,
      channel_type,
      'regional-remittance-money-out-service' as service_id
       FROM transform__remittance.regional_remittance_payouts_metrics
       UNION
       SELECT
      id,
      external_id as reference_id,
      user_id as business_id,
      amount as destination_amount,
      disbursement_id,
      status,
      flow_type,
      product_creation_timestamp,
      end_to_end_time,
      time_to_process_customer_request,
      time_to_perform_initial_processing,
      time_to_perform_risk_assessment,
      time_to_perform_ledger_creation,
      time_to_perform_ledger_updating,
      time_to_perform_compliance_verification,
      time_to_perform_disbursing as time_to_perform_disbursement,
      time_to_disbursement_service_perform_disbursement,
      time_to_receive_disbursement_callback,
      time_to_send_first_webhook,
      destination_currency,
      channel_code,
      failure_code,
      null as channel_type,
      'domestic-remittance-money-out-service' as service_id
       FROM transform__remittance.domestic_remittance_payouts_metrics) ;;
  }

  suggestions: no
  drill_fields: [id,destination_currency]

  # Define your dimensions and measures here, like this:
  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: flow_type {
    type: string
    sql: ${TABLE}.flow_type ;;
  }
  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  dimension: destination_amount {
    type: number
    sql: ${TABLE}.destination_amount ;;
  }
  dimension: channel_type {
    type: string
    sql:  case when ${TABLE}.channel_type is not null then ${TABLE}.channel_type
          when ${channel_code} in ('ID_GOPAY', 'ID_OVO', 'ID_LINKAJA', 'ID_SHOPEEPAY', 'ID_DANA', 'GOPAY','OVO','LINKAJA','SHOPEEPAY','DANA') then 'EWALLET'
          else 'BANK_ACCOUNT' END;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }
  dimension: failure_code {
    type:  string
    sql:  ${TABLE}.failure_code;;
  }
  dimension: reference_id {
    type:  string
    sql:  ${TABLE}.reference_id;;
  }
  dimension: disbursement_id {
    type:  string
    sql:  ${TABLE}.disbursement_id;;
  }
  dimension: end_to_end_time {
    type: number
    sql: ${TABLE}.end_to_end_time / 1000.0;;
  }
  dimension: time_to_process_customer_request {
    type: number
    sql: ${TABLE}.time_to_process_customer_request / 1000.0;;
  }

  dimension: time_to_send_first_webhook {
    type: number
    sql: ${TABLE}.time_to_send_first_webhook / 1000.0 ;;
  }

  dimension: time_to_perform_compliance_verification {
    type: number
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000.0;;
  }

  dimension: time_to_perform_initial_processing {
    type: number
    sql: ${TABLE}.time_to_perform_initial_processing / 1000.0;;
  }

  dimension: time_to_perform_risk_assessment {
    type: number
    sql: ${TABLE}.time_to_perform_risk_assessment / 1000.0;;
  }

  dimension: time_to_perform_ledger_creation {
    type: number
    sql: ${TABLE}.time_to_perform_ledger_creation / 1000.0;;
  }

  dimension: time_to_perform_ledger_updating {
    type: number
    sql: ${TABLE}.time_to_perform_ledger_updating / 1000.0;;
  }

  dimension: time_to_perform_disbursement {
    type: number
    sql: ${TABLE}.time_to_perform_disbursement / 1000.0;;
  }

  dimension: time_to_disbursement_service_perform_disbursement {
    type: number
    sql: ${TABLE}.time_to_disbursement_service_perform_disbursement / 1000.0;;
  }

  dimension: time_to_receive_disbursement_callback {
    type: number
    sql: ${TABLE}.time_to_receive_disbursement_callback / 1000.0;;
  }

  dimension: service_id {
    type: string
    sql: ${TABLE}.service_id ;;
  }

  dimension_group: product_creation_timestamp {
    type: time
    timeframes:[raw,
      time,
      time_of_day,
      hour_of_day,
      date,
      week,
      day_of_week,
      day_of_week_index,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
    drill_fields: [product_creation_timestamp_month,product_creation_timestamp_week,product_creation_timestamp_date, product_creation_timestamp_time_of_day, product_creation_timestamp_hour_of_day]
  }

  dimension_group: product_creation_timestamp_ph {
    type: time
    timeframes:[raw,
      time,
      time_of_day,
      hour_of_day,
      date,
      week,
      day_of_week,
      day_of_week_index,
      month,
      quarter,
      year]
    convert_tz: no
    sql: from_utc_timestamp(${TABLE}.product_creation_timestamp,'Etc/GMT-8') ;;
  }

  dimension: PH_Connector_type {
    type:  string
    case: {
      when: {
        # to update https://docs.xendit.co/multicurrency-crossborder-payouts/corridors
        sql:  ${channel_code} in ('PH_AIB','PH_ANZ','PH_BCH','PH_BKB','PH_BKI','PH_BNSP','PH_BOA','PH_BOF','PH_BOM','PH_CAB','PH_CBB','PH_CITI','PH_DEUTSCHE','PH_DCDB','PH_FBC','PH_GRB','PH_HSBC','PH_HSBI','PH_IBI','PH_IBK','PH_ICBC','PH_JPM','PH_KEB','PH_KHB','PH_MEGA','PH_MIZUHO','PH_MUFG','PH_RABI','PH_RBD','PH_RBMI','PH_SHB','PH_SMBC','PH_TYSB','PH_UOB','PH_MAYA');;
        label: "Cut-off Processing only"
      }
      else: "Instant Processing"
    }
  }

  dimension: speed_SLA { # https://docs.xendit.co/xendisburse/limits-and-processing-times#processing-time
    type:  string
    case: {
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'BCA' and (destination_amount < 10000 or destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '20:00' or ${product_creation_timestamp_time_of_day} <= '06:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'BCA' and (destination_amount < 10000 or destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '20:00' and ${product_creation_timestamp_time_of_day} > '06:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '03:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day}<= '23:00' and ${product_creation_timestamp_time_of_day} > '03:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '01:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '01:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'SAHABAT_SAMPOERNA' and (destination_amount < *********0) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '02:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'SAHABAT_SAMPOERNA' and (destination_amount < *********0) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '02:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '00:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '00:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'CIMB' and  (destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '21:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'CIMB' and  (destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '21:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'SINARMAS' and ( destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '14:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and channel_code = 'SINARMAS' and ( destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '14:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (destination_amount >= 10000 or destination_amount < ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (destination_amount >= 10000 or destination_amount < ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "IDR instant"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (destination_amount <= ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "IDR bank downtimes"
      }
      when: {
        sql:${destination_currency} = 'IDR' and (destination_amount <= ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "IDR instant"
      }
      when: {
        sql: ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= 50000 ;;
        label: "PHP channels with instant processing"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${product_creation_timestamp_day_of_week_index} < 5 and ${TABLE}.destination_amount > 50000 and  ${TABLE}.destination_amount <= ********* and ${product_creation_timestamp_ph_time_of_day} >= '06:00' and ${product_creation_timestamp_ph_time_of_day} <= '15:00'  ;;
        label: "PHP channels with instant and cut off processing and with > 50.000"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount > 50000 and  ${TABLE}.destination_amount <= ********* and ((${product_creation_timestamp_day_of_week_index} < 5 and (${product_creation_timestamp_ph_time_of_day}  >= '15:01' or ${product_creation_timestamp_ph_time_of_day} <= '05:59')) or ${product_creation_timestamp_day_of_week_index} >=5 ) ;;
        label: "PHP channels with instant cut off processing and with > 50.000 and after cut off time"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Cut-off Processing only' and ${destination_currency} = 'PHP' and ${product_creation_timestamp_day_of_week_index} < 5 and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= ********* and ${product_creation_timestamp_ph_time_of_day} >= '06:00' and ${product_creation_timestamp_ph_time_of_day} <= '15:00'  ;;
        label: "PHP channels with cut off processing only"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Cut-off Processing only' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= ********* and ((${product_creation_timestamp_day_of_week_index} < 5 and (${product_creation_timestamp_ph_time_of_day}  >= '15:01' or ${product_creation_timestamp_ph_time_of_day} <= '05:59')) or ${product_creation_timestamp_day_of_week_index} >=5 ) ;;
        label: "PHP channels with cut off processing only and after cut off time"
      }
      when: {
        sql:  ${destination_currency} = 'SGD'  ;;
        label: "SGD"
      }
      when: {
        sql:  ${destination_currency} = 'THB'  ;;
        label: "THB"
      }
      else: "others"
    }
  }

  dimension: sla_type {
    type: string
    case: {
      when: {
        sql:  ${speed_SLA} = 'PHP channels with instant processing' or ${speed_SLA} = 'IDR instant';;
        label: "instant processing"
      }
      else: "cut-off time"
    }
  }

  dimension: below_threshold_e2e {
    type: string
    case: {
      when: {
        sql:  ${end_to_end_time} < 20;;
        label: "< 20 s"
      }
      when: {
        sql:  ${end_to_end_time} < 60;;
        label: "< 60 s"
      }
      when: {
        sql:  ${end_to_end_time} < 300;;
        label: "< 5 m"
      }
      when: {
        sql:  ${end_to_end_time} < 900;;
        label: "< 15 m"
      }
      when: {
        sql:  ${end_to_end_time} < 3600;;
        label: "< 1 h"
      }
      when: {
        sql:  ${end_to_end_time} < 10800;;
        label: "< 3 h"
      }
      when: {
        sql:  ${end_to_end_time} < 86400;;
        label: "< 24 h"
      }
      else: "others"
    }
  }


  dimension: is_below_20_secs_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 20;;
  }

  dimension: is_below_60_secs_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 60;;
  }

  dimension: is_below_5_mins_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 300;;
  }

  dimension: is_below_5_mins_time_to_perform_compliance_verification {
    type: yesno
    sql:  ${time_to_perform_compliance_verification} < 300;;
  }

  dimension: is_below_15_mins_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 900;;
  }

  dimension: is_below_1_hours_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 3600;;
  }

  dimension: is_below_3_hours_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 10800;;
  }

  dimension: is_below_24_hours_e2e {
    type: yesno
    sql:  ${end_to_end_time} < 86400;;
  }

  dimension: is_below_15_mins_time_to_process_customer_request {
    type: yesno
    sql:  ${time_to_process_customer_request} < 900;;
  }


  dimension: slow_sla_reason {
    type:  string
    case:  {
      when: {
        sql:  ${time_to_perform_compliance_verification} > 0 ;;
        label: "manual_compliance_check"
      }
      when: {
        sql:  ${time_to_disbursement_service_perform_disbursement} >= 15 ;;
        label: "slow on disbursing"
      }
      when: {
        sql:  ${time_to_send_first_webhook} >= 600 ;;
        label: "failed on sending webhook (auto retry)"
      }
      when: {
        sql:  ${time_to_send_first_webhook} > 2 ;;
        label: "slow on sending webhook"
      }
      when: {
        sql:  ${time_to_perform_risk_assessment} > 2 ;;
        label: "slow on checking name blacklist"
      }
      when: {
        sql:  ${time_to_receive_disbursement_callback} >= 1 ;;
        label: "slow on receive disbursement callback"
      }
      # when: {
      #   sql:  ${time_to_perform_disbursement_creation} > 3 ;;
      #   label: "stuck on creating disbursement"
      # }
      when: {
        sql:  ${time_to_perform_ledger_creation} > 1 ;;
        label: "slow on creating ledger "
      }
      # when: {
      #   sql:  ${time_to_create_webhook} > 2 ;;
      #   label: "failed on creating webhook"
      # }


      else: "others"
    }
  }

  measure: count {
    type:  count_distinct
    sql:  ${id} ;;
    drill_fields: [id, disbursement_id, flow_type, status, destination_currency, slow_sla_reason,product_creation_timestamp_time,business_id,end_to_end_time,time_to_perform_initial_processing,time_to_perform_compliance_verification,time_to_perform_risk_assessment,time_to_perform_ledger_creation,time_to_perform_disbursement,time_to_disbursement_service_perform_disbursement,time_to_receive_disbursement_callback , time_to_perform_ledger_updating,time_to_send_first_webhook]
  }

  measure: count_below_20_secs_e2e {
    type:  sum
    sql:  case when ${is_below_20_secs_e2e} then 1 else 0 end ;;
  }

  measure: percentage_20_secs_e2e{
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_20_secs_e2e} as double) / ${count} ;;
  }

  measure: count_below_60_secs_e2e {
    type:  sum
    sql:  case when ${is_below_60_secs_e2e} then 1 else 0 end ;;
  }

  measure: percentage_60_secs_e2e{
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_60_secs_e2e} as double) / ${count} ;;
  }

  measure: count_below_5_mins_e2e {
    type:  sum
    sql:  case when ${is_below_5_mins_e2e} then 1 else 0 end ;;
  }

  measure: percentage_5_mins_e2e{
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_5_mins_e2e} as double) / ${count} ;;
  }

  measure: count_below_5_mins_time_to_perform_compliance_verification {
    type:  sum
    sql:  case when ${is_below_5_mins_time_to_perform_compliance_verification} then 1 else 0 end ;;
  }

  measure: percentage_5_mins_time_to_perform_compliance_verification{
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_5_mins_time_to_perform_compliance_verification} as double) / ${count} ;;
  }

  measure: count_below_15_mins_e2e {
    type:  sum
    sql:  case when ${is_below_15_mins_e2e} then 1 else 0 end ;;
  }

  measure: percentage_15_mins_e2e {
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_15_mins_e2e} as double) / ${count} ;;
  }

  measure: count_below_1_hours_e2e {
    type:  sum
    sql:  case when ${is_below_1_hours_e2e} then 1 else 0 end ;;
  }

  measure: percentage_1_hours_e2e {
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_1_hours_e2e} as double) / ${count} ;;
  }

  measure: count_below_3_hours_e2e {
    type:  sum
    sql:  case when ${is_below_3_hours_e2e} then 1 else 0 end ;;
  }

  measure: percentage_3_hours_e2e {
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_3_hours_e2e} as double) / ${count} ;;
  }

  measure: count_below_24_hours_e2e {
    type:  sum
    sql:  case when ${is_below_24_hours_e2e} then 1 else 0 end ;;
  }

  measure: percentage_24_hours_e2e {
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_24_hours_e2e} as double) / ${count} ;;
  }

  measure: count_below_15_mins_time_to_process_customer_request  {
    type:  sum
    sql:  case when ${is_below_15_mins_time_to_process_customer_request} then 1 else 0 end ;;
  }

  measure: percentage_15_mins_time_to_process_customer_request {
    type: number
    value_format_name: percent_2
    sql: cast(${count_below_15_mins_time_to_process_customer_request} as double) / ${count} ;;
  }

  # Measure end_to_end_time
  measure:end_to_end_time_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${end_to_end_time};;
    value_format: "0.##"
  }

  measure:end_to_end_time_elapsed_95pct  {
    type: percentile
    percentile: 95
    sql: ${end_to_end_time};;
    value_format: "0.##"
  }

  measure:end_to_end_time_elapsed_90pct  {
    type: percentile
    percentile: 90
    sql: ${end_to_end_time};;
    value_format: "0.##"
  }

  # Measure time_to_send_first_webhook
  measure:time_to_send_first_webhook_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_send_first_webhook};;
    value_format: "0.##"
  }

  measure:time_to_send_first_webhook_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_send_first_webhook};;
    value_format: "0.##"
  }

  measure:time_to_send_first_webhook_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_send_first_webhook};;
    value_format: "0.##"
  }

  # Measure time_to_perform_compliance_verification
  measure:time_to_perform_compliance_verification_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_perform_compliance_verification};;
    value_format: "0.##"
  }

  measure:time_to_perform_compliance_verification_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_perform_compliance_verification};;
    value_format: "0.##"
  }

  measure:time_to_perform_compliance_verification_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_perform_compliance_verification};;
    value_format: "0.##"
  }

  # Measure time_to_perform_disbursement
  measure:time_to_perform_disbursement_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_perform_disbursement};;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_perform_disbursement};;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_perform_disbursement};;
    value_format: "0.##"
  }

  # Measure time_to_perform_risk_assessment
  measure:time_to_perform_risk_assessment_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_perform_risk_assessment};;
    value_format: "0.##"
  }

  measure:time_to_perform_risk_assessment_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_perform_risk_assessment};;
    value_format: "0.##"
  }

  measure:time_to_perform_risk_assessment_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_perform_risk_assessment};;
    value_format: "0.##"
  }


  # Measure time_to_disbursement_service_perform_disbursement
  measure:time_to_disbursement_service_perform_disbursement_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_disbursement_service_perform_disbursement};;
    value_format: "0.##"
  }

  measure:time_to_disbursement_service_perform_disbursement_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_disbursement_service_perform_disbursement};;
    value_format: "0.##"
  }

  measure:time_to_disbursement_service_perform_disbursement_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_disbursement_service_perform_disbursement};;
    value_format: "0.##"
  }

  # Measure time_to_receive_disbursement_callback
  measure:time_to_receive_disbursement_callback_99_pct  {
    type: percentile
    percentile: 99
    sql: ${time_to_receive_disbursement_callback};;
    value_format: "0.##"
  }

  measure:time_to_receive_disbursement_callback_95pct  {
    type: percentile
    percentile: 95
    sql: ${time_to_receive_disbursement_callback};;
    value_format: "0.##"
  }

  measure:time_to_receive_disbursement_callback_90pct  {
    type: percentile
    percentile: 90
    sql: ${time_to_receive_disbursement_callback};;
    value_format: "0.##"
  }
}

# view: remittance_payouts_metrics_breakdown {
#   derived_table: {
#     datagroup_trigger: dashboard_update_time
#     sql:${remittance_payouts_metrics}
#   }
#   }
