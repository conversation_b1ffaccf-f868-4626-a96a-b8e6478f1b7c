- dashboard: regional_remittance_payouts_metrics
  title: "[Metric] Regional Remittance Payouts"
  layout: newspaper
  preferred_viewer: dashboards-next
  description: ''
  preferred_slug: 9Z78rdAuYkGeeiNnFVlq5g
  elements:
  - name: E2E elapsed (s)
    type: text
    title_text: E2E elapsed (s)
    subtitle_text: all client without callback excluded from here
    body_text: ''
    row: 0
    col: 1
    width: 22
    height: 2
  - title: 90 percentile (s)
    name: 90 percentile (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_elapsed_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 6
    col: 9
    width: 6
    height: 2
  - title: 95 Percentile (s)
    name: 95 Percentile (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_elapsed_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 4
    col: 9
    width: 6
    height: 2
  - name: Time to process customer request (s)
    type: text
    title_text: Time to process customer request (s)
    subtitle_text: ''
    body_text: ''
    row: 16
    col: 1
    width: 22
    height: 1
  - title: Mean (s)
    name: Mean (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_time_to_process_customer_request]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_process_customer_request, based_on: regional_remittance_payouts_metrics.time_to_process_customer_request,
        expression: '', label: Average of Time to Process Customer Request, type: average,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 17
    col: 1
    width: 8
    height: 6
  - title: 99 Percentile (s)
    name: 99 Percentile (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 17
    col: 9
    width: 6
    height: 2
  - title: 90 Percentile (s)
    name: 90 Percentile (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 21
    col: 9
    width: 6
    height: 2
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 19
    col: 9
    width: 6
    height: 2
  - title: Min (s)
    name: Min (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_time_to_process_customer_request]
    limit: 500
    dynamic_fields: [{measure: min_of_time_to_process_customer_request, based_on: regional_remittance_payouts_metrics.time_to_process_customer_request,
        expression: '', label: Min of Time to Process Customer Request, type: min,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 20
    col: 15
    width: 8
    height: 3
  - name: Disbursement Creation elapsed (s)
    type: text
    title_text: Disbursement Creation elapsed (s)
    subtitle_text: ''
    body_text: ''
    row: 42
    col: 1
    width: 22
    height: 1
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_creation_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 45
    col: 9
    width: 6
    height: 2
  - title: 99 Percentile (s)
    name: 99 Percentile (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_creation_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 43
    col: 9
    width: 6
    height: 2
  - title: 90 Percentile (s)
    name: 90 Percentile (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_creation_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    limit_displayed_rows: false
    defaults_version: 1
    series_types: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 47
    col: 9
    width: 6
    height: 2
  - title: Mean (s)
    name: Mean (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_time_to_perform_disbursement_creation]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_perform_disbursement_creation, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement_creation,
        expression: '', label: Average of Time to Perform Ledger Creation, type: average,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 43
    col: 1
    width: 8
    height: 6
  - title: Min (s)
    name: Min (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_time_to_perform_disbursement_creation]
    limit: 500
    dynamic_fields: [{measure: min_of_time_to_perform_disbursement_creation, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement_creation,
        expression: '', label: Min of Time to Perform Ledger Creation, type: min,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 46
    col: 15
    width: 8
    height: 3
  - title: Max (s)
    name: Max (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_time_to_perform_disbursement_creation]
    limit: 500
    dynamic_fields: [{measure: max_of_time_to_perform_disbursement_creation, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement_creation,
        expression: '', label: Max of Time to Perform Ledger Creation, type: max,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 43
    col: 15
    width: 8
    height: 3
  - name: Time to process customer request without compliance  elapsed (s)
    type: text
    title_text: Time to process customer request without compliance  elapsed (s)
    subtitle_text: ''
    body_text: ''
    row: 23
    col: 1
    width: 22
    height: 1
  - title: Mean (s)
    name: Mean (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_time_to_process_customer_request_without_compliance]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_process_customer_request_without_compliance,
        based_on: regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance,
        expression: '', label: Average of Time to Process Customer Request Without
          Compliance, type: average, _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 24
    col: 1
    width: 8
    height: 6
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 26
    col: 9
    width: 6
    height: 2
  - title: 99 Percentile (s)
    name: 99 Percentile (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 24
    col: 9
    width: 6
    height: 2
  - title: Min (s)
    name: Min (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_time_to_process_customer_request_without_compliance]
    limit: 500
    dynamic_fields: [{measure: min_of_time_to_process_customer_request_without_compliance,
        based_on: regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance,
        expression: '', label: Min of Time to Process Customer Request Without Compliance,
        type: min, _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 27
    col: 15
    width: 8
    height: 3
  - title: Max (s)
    name: Max (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_time_to_process_customer_request]
    limit: 500
    dynamic_fields: [{measure: max_of_time_to_process_customer_request, based_on: regional_remittance_payouts_metrics.time_to_process_customer_request,
        expression: '', label: Max of Time to Process Customer Request, type: max,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 17
    col: 15
    width: 8
    height: 3
  - title: 90 Percentile (s)
    name: 90 Percentile (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    limit_displayed_rows: false
    defaults_version: 1
    series_types: {}
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 28
    col: 9
    width: 6
    height: 2
  - name: Disbursing elapsed (s)
    type: text
    title_text: Disbursing elapsed (s)
    subtitle_text: ''
    body_text: ''
    row: 49
    col: 1
    width: 22
    height: 1
  - title: Mean (s)
    name: Mean (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_time_to_perform_disbursement]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_perform_disbursement, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement,
        expression: '', label: Average of Time to Perform Disbursement, type: average,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 50
    col: 1
    width: 8
    height: 6
  - title: 99 Percentile (s)
    name: 99 Percentile (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 50
    col: 9
    width: 6
    height: 2
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 52
    col: 9
    width: 6
    height: 2
  - title: 90 Percentile (s)
    name: 90 Percentile (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_disbursement_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    limit_displayed_rows: false
    defaults_version: 1
    series_types: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 54
    col: 9
    width: 6
    height: 2
  - title: Min (s)
    name: Min (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_time_to_perform_disbursement]
    limit: 500
    dynamic_fields: [{measure: min_of_time_to_perform_disbursement, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement,
        expression: '', label: Min of Time to Perform Disbursement, type: min, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 53
    col: 15
    width: 8
    height: 3
  - name: Compliance elapsed (s)
    type: text
    title_text: Compliance elapsed (s)
    subtitle_text: ''
    body_text: ''
    row: 56
    col: 1
    width: 22
    height: 1
  - title: Max (s)
    name: Max (s) (3)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_time_to_perform_disbursement]
    limit: 500
    dynamic_fields: [{measure: max_of_time_to_perform_disbursement, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement,
        expression: '', label: Max of Time to Perform Disbursement, type: max, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 50
    col: 15
    width: 8
    height: 3
  - title: Mean (s)
    name: Mean (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_time_to_perform_compliance_verification]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_perform_compliance_verification,
        based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification, expression: '',
        label: Average of Time to Perform Compliance Verification, type: average,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 57
    col: 1
    width: 8
    height: 6
  - title: 99 Percentile (s)
    name: 99 Percentile (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_compliance_verification_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 57
    col: 9
    width: 6
    height: 2
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (6)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_compliance_verification_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 59
    col: 9
    width: 6
    height: 2
  - title: 90 Percentile (s)
    name: 90 Percentile (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.time_to_perform_compliance_verification_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    limit_displayed_rows: false
    defaults_version: 1
    series_types: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 61
    col: 9
    width: 6
    height: 2
  - title: Max (s)
    name: Max (s) (4)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_time_to_perform_compliance_verification]
    limit: 500
    dynamic_fields: [{measure: max_of_time_to_perform_compliance_verification, based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification,
        expression: '', label: Max of Time to Perform Compliance Verification, type: max,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 57
    col: 15
    width: 8
    height: 3
  - title: Min (s)
    name: Min (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_time_to_perform_compliance_verification]
    limit: 500
    dynamic_fields: [{measure: min_of_time_to_perform_compliance_verification, based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification,
        expression: '', label: Min of Time to Perform Compliance Verification, type: min,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 60
    col: 15
    width: 8
    height: 3
  - title: Average Time to process customer request without compliance (s) / hour
    name: Average Time to process customer request without compliance (s) / hour
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [average_of_time_to_process_customer_request_without_compliance, regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day]
    fill_fields: [regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day]
    sorts: [regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_process_customer_request_without_compliance,
        based_on: regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance,
        expression: '', label: Average of Time to Process Customer Request Without
          Compliance, type: average, _kind_hint: measure, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    y_axes: [{label: '', orientation: left, series: [{axisId: average_of_drmo_e2e_internal_without_compliance_ut_elapsed,
            id: average_of_drmo_e2e_internal_without_compliance_ut_elapsed, name: Average
              of Drmo E2e Internal Without Compliance Ut Elapsed}], showLabels: true,
        showValues: true, unpinAxis: false, tickDensity: default, tickDensityCustom: 5,
        type: log}]
    x_axis_zoom: true
    y_axis_zoom: true
    label_value_format: 0.##
    series_types: {}
    show_null_points: true
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 30
    col: 1
    width: 22
    height: 6
  - title: txn count/ hour
    name: txn count/ hour
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day, count_of_id]
    fill_fields: [regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day]
    sorts: [regional_remittance_payouts_metrics.product_creation_timestamp_hour_of_day]
    limit: 500
    dynamic_fields: [{measure: count_of_id, based_on: regional_remittance_payouts_metrics.id, expression: '',
        label: Count of ID, type: count_distinct, _kind_hint: measure, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 36
    col: 1
    width: 22
    height: 6
  - title: Compliance txn count (s) / day
    name: Compliance txn count (s) / day
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [count_of_id, regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week]
    fill_fields: [regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week]
    filters:
      flow_type: '"with_compliance_assessment"'
    sorts: [regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week]
    limit: 500
    dynamic_fields: [{category: dimension, expression: "${regional_remittance_payouts_metrics.flow_type}",
        label: flow type, value_format: !!null '', value_format_name: !!null '', dimension: flow_type,
        _kind_hint: dimension, _type_hint: string}, {measure: count_of_id, based_on: regional_remittance_payouts_metrics.id,
        expression: '', label: Count of ID, type: count_distinct, _kind_hint: measure,
        _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 63
    col: 1
    width: 11
    height: 5
  - title: Max (s)
    name: Max (s) (5)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_end_to_end_time_2]
    limit: 500
    dynamic_fields: [{measure: max_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Max of End to End Time, type: max, _kind_hint: measure,
        _type_hint: number}, {measure: max_of_end_to_end_time_2, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Max of End to End Time, type: max, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 2
    col: 15
    width: 8
    height: 3
  - title: Mean (s)
    name: Mean (s) (6)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_end_to_end_time_2]
    limit: 500
    dynamic_fields: [{measure: median_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Median of End to End Time, type: median, _kind_hint: measure,
        _type_hint: number}, {measure: average_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Average of End to End Time, type: average, _kind_hint: measure,
        _type_hint: number}, {measure: average_of_end_to_end_time_2, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Average of End to End Time, type: average, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    hidden_pivots: {}
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    show_null_points: true
    interpolation: linear
    defaults_version: 1
    series_types: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 2
    col: 1
    width: 8
    height: 6
  - title: Min (s)
    name: Min (s) (6)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_end_to_end_time]
    limit: 500
    dynamic_fields: [{measure: min_of_end_to_end_time_2, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Min of End to End Time, type: min, _kind_hint: measure,
        _type_hint: number}, {measure: min_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Min of End to End Time, type: min, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    limit_displayed_rows: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 5
    col: 15
    width: 8
    height: 3
  - name: E2E elapsed without compliance time(s)
    type: text
    title_text: E2E elapsed without compliance time(s)
    subtitle_text: all client without callback excluded from here
    body_text: ''
    row: 8
    col: 1
    width: 22
    height: 2
  - title: 99 percentile (s)
    name: 99 percentile (s)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_elapsed_99_pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 2
    col: 9
    width: 6
    height: 2
  - title: 99 percentile (s)
    name: 99 percentile (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_without_compliance_99_pct, regional_remittance_payouts_metrics.end_to_end_time_without_compliance_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 10
    col: 9
    width: 6
    height: 2
  - title: Mean (s)
    name: Mean (s) (7)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [average_of_end_to_end_time_without_compliance_2]
    limit: 500
    dynamic_fields: [{measure: median_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Median of End to End Time, type: median, _kind_hint: measure,
        _type_hint: number}, {measure: average_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Average of End to End Time, type: average, _kind_hint: measure,
        _type_hint: number}, {measure: average_of_end_to_end_time_without_compliance,
        based_on: regional_remittance_payouts_metrics.end_to_end_time_without_compliance, expression: '',
        label: Average of End to End Time Without Compliance, type: average, _kind_hint: measure,
        _type_hint: number}, {measure: average_of_end_to_end_time_without_compliance_2,
        based_on: regional_remittance_payouts_metrics.end_to_end_time_without_compliance, expression: '',
        label: Average of End to End Time Without Compliance, type: average, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    value_format: 0.##
    hidden_pivots: {}
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    show_null_points: true
    interpolation: linear
    defaults_version: 1
    series_types: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 10
    col: 1
    width: 8
    height: 6
  - title: 95 Percentile (s)
    name: 95 Percentile (s) (7)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_without_compliance_95pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 12
    col: 9
    width: 6
    height: 2
  - title: 90 percentile (s)
    name: 90 percentile (s) (2)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [regional_remittance_payouts_metrics.end_to_end_time_without_compliance_90pct]
    limit: 500
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 14
    col: 9
    width: 6
    height: 2
  - title: Max (s)
    name: Max (s) (6)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_end_to_end_time_without_compliance_2]
    limit: 500
    dynamic_fields: [{measure: max_of_end_to_end_time, based_on: regional_remittance_payouts_metrics.end_to_end_time,
        expression: '', label: Max of End to End Time, type: max, _kind_hint: measure,
        _type_hint: number}, {measure: max_of_end_to_end_time_without_compliance,
        based_on: regional_remittance_payouts_metrics.end_to_end_time_without_compliance, expression: '',
        label: Max of End to End Time Without Compliance, type: max, _kind_hint: measure,
        _type_hint: number}, {measure: max_of_end_to_end_time_without_compliance_2,
        based_on: regional_remittance_payouts_metrics.end_to_end_time_without_compliance, expression: '',
        label: Max of End to End Time Without Compliance, type: max, _kind_hint: measure,
        _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 10
    col: 15
    width: 8
    height: 3
  - title: Min (s)
    name: Min (s) (7)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [min_of_end_to_end_time_without_compliance]
    limit: 500
    dynamic_fields: [{measure: min_of_end_to_end_time_without_compliance, based_on: regional_remittance_payouts_metrics.end_to_end_time_without_compliance,
        expression: '', label: Min of End to End Time Without Compliance, type: min,
        _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    limit_displayed_rows: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 13
    col: 15
    width: 8
    height: 3
  - title: Max (s)
    name: Max (s) (7)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: single_value
    fields: [max_of_time_to_process_customer_request_without_compliance]
    limit: 500
    dynamic_fields: [{measure: max_of_time_to_process_customer_request_without_compliance,
        based_on: regional_remittance_payouts_metrics.time_to_process_customer_request_without_compliance,
        expression: '', label: Max of Time to Process Customer Request Without Compliance,
        type: max, _kind_hint: measure, _type_hint: number}]
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    series_types: {}
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 24
    col: 15
    width: 8
    height: 3
  - title: Average Compliance verification time / day
    name: Average Compliance verification time / day
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week, average_of_time_to_perform_compliance_verification]
    fill_fields: [regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week]
    sorts: [regional_remittance_payouts_metrics.product_creation_timestamp_day_of_week]
    limit: 500
    dynamic_fields: [{measure: average_of_time_to_perform_compliance_verification,
        based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification, expression: '',
        label: Average of Time to Perform Compliance Verification, type: average,
        _kind_hint: measure, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 63
    col: 12
    width: 11
    height: 5
  - title: Compliance verification count
    name: Compliance verification count
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_pie
    fields: [flow_type, count_of_id]
    sorts: [count_of_id desc]
    limit: 500
    dynamic_fields: [{category: dimension, expression: "${regional_remittance_payouts_metrics.flow_type}",
        label: flow type, value_format: !!null '', value_format_name: !!null '', dimension: flow_type,
        _kind_hint: dimension, _type_hint: string}, {measure: count_of_id, based_on: regional_remittance_payouts_metrics.id,
        expression: '', label: Count of ID, type: count_distinct, _kind_hint: measure,
        _type_hint: number}]
    value_labels: legend
    label_type: labPer
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    x_axis_zoom: true
    y_axis_zoom: true
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    series_types: {}
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_dropoff: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    defaults_version: 1
    show_null_points: true
    interpolation: linear
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 68
    col: 1
    width: 11
    height: 5
  - title: E2E Without compliance completion time
    name: E2E Without compliance completion time
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [30_seconds, count_of_id, 15_minutes, 7_days, 24_hours]
    filters:
      regional_remittance_payouts_metrics.end_to_end_time: NOT NULL
      regional_remittance_payouts_metrics.flow_type: '"without_compliance_assessment"'
    limit: 500
    dynamic_fields: [{category: table_calculation, expression: "${30_seconds} / ${count_of_id}",
        label: "< 30 Second percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 30_second_percent, _type_hint: number},
      {category: table_calculation, expression: "${15_minutes} / ${count_of_id}",
        label: "< 15 Minutes percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 15_minutes_percent, _type_hint: number},
      {category: table_calculation, expression: "${24_hours} / ${count_of_id}", label: "<\
          \ 24 Hours percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 24_hours_percent, _type_hint: number},
      {category: table_calculation, expression: "${7_days} / ${count_of_id}", label: "<\
          \ 7 days percentage", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 7_days_percentage, _type_hint: number},
      {category: measure, expression: !!null '', label: "< 30 Seconds", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 30_seconds, type: count_distinct, _type_hint: number, filters: {
          regional_remittance_payouts_metrics.end_to_end_time_without_compliance: "<30"}}, {category: measure,
        expression: !!null '', label: "< 15 Minutes", value_format: !!null '', value_format_name: !!null '',
        based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure, measure: 15_minutes, type: count_distinct,
        _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time_without_compliance: "<900"}},
      {category: measure, expression: !!null '', label: "< 24 Hours", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 24_hours, type: count_distinct, _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time_without_compliance: "<86400"}},
      {category: measure, expression: !!null '', label: "< 7 days", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 7_days, type: count_distinct, _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time_without_compliance: "<604800"}},
      {category: measure, expression: '', label: Count of ID, value_format: !!null '',
        value_format_name: decimal_0, based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: count_of_id, type: count_distinct, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: false
    show_x_axis_ticks: false
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    series_types: {}
    series_labels: {}
    defaults_version: 1
    hidden_pivots: {}
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    hidden_fields: [30_seconds, count_of_id, 15_minutes, 7_days, 24_hours]
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    show_null_points: true
    interpolation: linear
    value_labels: legend
    label_type: labPer
    hidden_points_if_no: []
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 73
    col: 1
    width: 11
    height: 6
  - title: E2E Completion time
    name: E2E Completion time
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [30_seconds, count_of_id, 15_minutes, 7_days, 24_hours]
    filters:
      regional_remittance_payouts_metrics.end_to_end_time: NOT NULL
    limit: 500
    dynamic_fields: [{category: table_calculation, expression: "${30_seconds} / ${count_of_id}",
        label: "< 30 Second percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 30_second_percent, _type_hint: number},
      {category: table_calculation, expression: "${15_minutes} / ${count_of_id}",
        label: "< 15 Minutes percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 15_minutes_percent, _type_hint: number},
      {category: table_calculation, expression: "${24_hours} / ${count_of_id}", label: "<\
          \ 24 Hours percent", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 24_hours_percent, _type_hint: number},
      {category: table_calculation, expression: "${7_days} / ${count_of_id}", label: "<\
          \ 7 days percentage", value_format: !!null '', value_format_name: percent_2,
        _kind_hint: measure, table_calculation: 7_days_percentage, _type_hint: number},
      {category: measure, expression: !!null '', label: "< 30 Seconds", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 30_seconds, type: count_distinct, _type_hint: number, filters: {
          regional_remittance_payouts_metrics.end_to_end_time: "<30"}}, {category: measure, expression: !!null '',
        label: "< 15 Minutes", value_format: !!null '', value_format_name: !!null '',
        based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure, measure: 15_minutes, type: count_distinct,
        _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time: "<900"}},
      {category: measure, expression: !!null '', label: "< 24 Hours", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 24_hours, type: count_distinct, _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time: "<86400"}},
      {category: measure, expression: !!null '', label: "< 7 days", value_format: !!null '',
        value_format_name: !!null '', based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: 7_days, type: count_distinct, _type_hint: number, filters: {regional_remittance_payouts_metrics.end_to_end_time: "<604800"}},
      {category: measure, expression: '', label: Count of ID, value_format: !!null '',
        value_format_name: decimal_0, based_on: regional_remittance_payouts_metrics.id, _kind_hint: measure,
        measure: count_of_id, type: count_distinct, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: false
    show_x_axis_ticks: false
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    series_types: {}
    series_labels: {}
    defaults_version: 1
    hidden_pivots: {}
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    hidden_fields: [30_seconds, count_of_id, 15_minutes, 7_days, 24_hours]
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    show_null_points: true
    interpolation: linear
    value_labels: legend
    label_type: labPer
    hidden_points_if_no: []
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 73
    col: 12
    width: 11
    height: 6
  - title: Average time (s) / state
    name: Average time (s) / state
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [average_of_time_to_perform_initial_processing, average_of_time_to_perform_compliance_verification_1,
      average_of_time_to_perform_disbursement_creation, average_of_time_to_perform_disbursement,
      average_of_time_to_send_first_webhook]
    limit: 500
    dynamic_fields: [{category: measure, expression: '', label: Average of Time to
          Perform Initial Processing, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_perform_initial_processing, _kind_hint: measure,
        measure: average_of_time_to_perform_initial_processing, type: average, _type_hint: number},
      {measure: average_of_time_to_perform_compliance_verification, based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification,
        expression: '', label: Average of Time to Perform Compliance Verification,
        type: average, _kind_hint: measure, _type_hint: number}, {category: measure,
        expression: '', label: Average of Time to Perform Ledger Creation, value_format: !!null '',
        value_format_name: decimal_1, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement_creation,
        _kind_hint: measure, measure: average_of_time_to_perform_disbursement_creation,
        type: average, _type_hint: number}, {category: measure, expression: '', label: Average
          of Time to Perform Compliance Verification, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification, _kind_hint: measure,
        measure: average_of_time_to_perform_compliance_verification_1, type: average,
        _type_hint: number}, {category: measure, expression: '', label: Average of
          Time to Send First Webhook, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_send_first_webhook, _kind_hint: measure,
        measure: average_of_time_to_send_first_webhook, type: average, _type_hint: number},
      {category: measure, expression: '', label: Average of Time to Perform Disbursement,
        value_format: !!null '', value_format_name: decimal_1, based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement,
        _kind_hint: measure, measure: average_of_time_to_perform_disbursement, type: average,
        _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: false
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    series_types: {}
    reference_lines: []
    trend_lines: []
    defaults_version: 1
    hidden_pivots: {}
    show_null_points: true
    interpolation: linear
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 79
    col: 1
    width: 11
    height: 7
  - title: Success rate
    name: Success rate
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_pie
    fields: [regional_remittance_payouts_metrics.status, count_of_id]
    sorts: [count_of_id desc]
    limit: 500
    dynamic_fields: [{measure: count_of_id, based_on: regional_remittance_payouts_metrics.id, expression: '',
        label: Count of ID, type: count_distinct, _kind_hint: measure, _type_hint: number}]
    value_labels: legend
    label_type: labPer
    series_types: {}
    defaults_version: 1
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 68
    col: 12
    width: 11
    height: 5
  - title: Average time (s) / state for abnormal cases ( > 1 day)
    name: Average time (s) / state for abnormal cases ( > 1 day)
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    type: looker_column
    fields: [average_of_time_to_perform_initial_processing, average_of_time_to_perform_compliance_verification,
      average_of_time_to_perform_disbursement_creation, average_of_time_to_perform_disbursement,
      average_of_time_to_send_first_webhook]
    filters:
      regional_remittance_payouts_metrics.end_to_end_time: ">=86400,NOT NULL"
    limit: 500
    dynamic_fields: [{category: measure, expression: '', label: Average of Time to
          Perform Initial Processing, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_perform_initial_processing, _kind_hint: measure,
        measure: average_of_time_to_perform_initial_processing, type: average, _type_hint: number},
      {category: measure, expression: '', label: Average of Time to Perform Compliance
          Verification, value_format: !!null '', value_format_name: decimal_1, based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification,
        _kind_hint: measure, measure: average_of_time_to_perform_compliance_verification,
        type: average, _type_hint: number}, {category: measure, expression: '', label: Average
          of Time to Perform Ledger Creation, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement_creation, _kind_hint: measure,
        measure: average_of_time_to_perform_disbursement_creation, type: average, _type_hint: number},
      {measure: average_of_time_to_perform_compliance_verification_2, based_on: regional_remittance_payouts_metrics.time_to_perform_compliance_verification,
        expression: '', label: Average of Time to Perform Compliance Verification,
        type: average, _kind_hint: measure, _type_hint: number}, {category: measure,
        expression: '', label: Average of Time to Send First Webhook, value_format: !!null '',
        value_format_name: decimal_1, based_on: regional_remittance_payouts_metrics.time_to_send_first_webhook,
        _kind_hint: measure, measure: average_of_time_to_send_first_webhook, type: average,
        _type_hint: number}, {category: measure, expression: '', label: Average of
          Time to Perform Disbursement, value_format: !!null '', value_format_name: decimal_1,
        based_on: regional_remittance_payouts_metrics.time_to_perform_disbursement, _kind_hint: measure,
        measure: average_of_time_to_perform_disbursement, type: average, _type_hint: number}]
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: false
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: false
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    series_types: {}
    reference_lines: []
    trend_lines: []
    defaults_version: 1
    hidden_pivots: {}
    show_null_points: true
    interpolation: linear
    listen:
      Product Creation Timestamp Date: regional_remittance_payouts_metrics.product_creation_timestamp_date
    row: 79
    col: 12
    width: 11
    height: 7
  - name: ''
    type: text
    title_text: ''
    subtitle_text: ''
    body_text: ''
    row: 86
    col: 1
    width: 22
    height: 1
  filters:
  - name: Product Creation Timestamp Date
    title: Product Creation Timestamp Date
    type: field_filter
    default_value: 2022/12/15 to 2022/12/16
    allow_multiple_values: true
    required: false
    ui_config:
      type: day_range_picker
      display: inline
      options: []
    model: xendit-presto
    explore: regional_remittance_payouts_metrics
    listens_to_filters: []
    field: regional_remittance_payouts_metrics.product_creation_timestamp_date
