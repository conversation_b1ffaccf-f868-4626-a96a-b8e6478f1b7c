view: rmi_va_performance {

  sql_table_name: transform__remittance.remittance_collection_virtual_account_metric  ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }


  dimension: rmi_e2e_va_ut_elapsed {
    type: number
    sql: ${TABLE}.end_to_end_time ;;
  }

  dimension: rmi_internal_va_creation_ut_elapsed {
    type: number
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  dimension_group: rmi_va_created {
    type: time
    timeframes:[raw,
      time,
      hour_of_day,
      date,
      week,
      day_of_week,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  # Measure E2E
  measure:rmi_e2e_va_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_va_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_va_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_va_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_va_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_va_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.end_to_end_time ;;
  }

  # Measure internal E2E
  measure:rmi_internal_va_creation_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_va_creation_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_va_creation_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_va_creation_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_va_creation_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_va_creation_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.time_to_process_customer_request ;;
  }
}

view: rmi_vap_performance {

  sql_table_name: transform__remittance.remittance_collection_virtual_account_payment_metric  ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }


  dimension: rmi_e2e_vap_ut_elapsed {
    type: number
    sql: ${TABLE}.e2e_vap ;;
  }


  dimension_group: rmi_vap_created {
    type: time
    timeframes:[raw,
      time,
      hour_of_day,
      date,
      week,
      day_of_week,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  # Measure E2E
  measure:rmi_e2e_vap_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.e2e_vap ;;
  }

  measure:rmi_e2e_vap_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.e2e_vap ;;
  }

  measure:rmi_e2e_vap_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.e2e_vap ;;
  }

  measure:rmi_e2e_vap_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.e2e_vap ;;
  }

  measure:rmi_e2e_vap_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.e2e_vap ;;
  }

  measure:rmi_e2e_vap_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.e2e_vap ;;
  }
}

view: rmi_vapc_performance {

  sql_table_name: transform__remittance.remittance_collection_virtual_account_payment_claim_metric  ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }


  dimension: rmi_e2e_vapc_ut_elapsed {
    type: number
    sql: ${TABLE}.end_to_end_time ;;
  }

  dimension: rmi_internal_vapc_creation_ut_elapsed {
    type: number
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  dimension_group: rmi_vapc_created {
    type: time
    timeframes:[raw,
      time,
      hour_of_day,
      date,
      week,
      day_of_week,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  # Measure E2E
  measure:rmi_e2e_vapc_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapc_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapc_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapc_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapc_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapc_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.end_to_end_time ;;
  }

  # Measure internal E2E
  measure:rmi_internal_vapc_creation_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapc_creation_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapc_creation_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapc_creation_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapc_creation_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapc_creation_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.time_to_process_customer_request ;;
  }
}

view: rmi_vapr_performance {

  sql_table_name: transform__remittance.remittance_collection_virtual_account_payment_refund_metric  ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }


  dimension: rmi_e2e_vapr_ut_elapsed {
    type: number
    sql: ${TABLE}.end_to_end_time ;;
  }

  dimension: rmi_internal_vapr_creation_ut_elapsed {
    type: number
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  dimension_group: rmi_vapr_created {
    type: time
    timeframes:[raw,
      time,
      hour_of_day,
      date,
      week,
      day_of_week,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  # Measure E2E
  measure:rmi_e2e_vapr_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapr_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapr_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapr_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapr_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.end_to_end_time ;;
  }

  measure:rmi_e2e_vapr_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.end_to_end_time ;;
  }

  # Measure internal E2E
  measure:rmi_internal_vapr_creation_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapr_creation_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapr_creation_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapr_creation_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapr_creation_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.time_to_process_customer_request ;;
  }

  measure:rmi_internal_vapr_creation_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.time_to_process_customer_request ;;
  }
}
