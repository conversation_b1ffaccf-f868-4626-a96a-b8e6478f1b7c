view: regional_remittance_payouts_metrics {

  sql_table_name: transform__remittance.regional_remittance_payouts_metrics   ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }
  dimension: flow_type {
    type: string
    sql: ${TABLE}.flow_type ;;
  }
  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  dimension: destination_amount {
    type: number
    sql: ${TABLE}.destination_amount ;;
  }
  dimension: channel_type {
    type: string
    sql: ${TABLE}.channel_type ;;
  }
  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }
  dimension_group: product_creation_timestamp {
    type: time
    timeframes:[raw,
      time,
      time_of_day,
      hour_of_day,
      date,
      week,
      day_of_week,
      day_of_week_index,
      month,
      quarter,
      year]
    sql: ${TABLE}.product_creation_timestamp ;;
  }

  dimension_group: product_creation_timestamp_ph {
    type: time
    timeframes:[raw,
      time,
      time_of_day,
      hour_of_day,
      date,
      week,
      day_of_week,
      day_of_week_index,
      month,
      quarter,
      year]
    convert_tz: no
    sql: from_utc_timestamp(${TABLE}.product_creation_timestamp,'Etc/GMT-8') ;;
  }

  dimension: PH_Connector_type {
    type:  string
    case: {
      when: {
        # to update https://docs.xendit.co/multicurrency-crossborder-payouts/corridors
        sql:  ${channel_code} in ('PH_AIB','PH_ANZ','PH_BCH','PH_BKB','PH_BKI','PH_BNSP','PH_BOA','PH_BOF','PH_BOM','PH_CAB','PH_CBB','PH_CITI','PH_DEUTSCHE','PH_DCDB','PH_FBC','PH_GRB','PH_HSBC','PH_HSBI','PH_IBI','PH_IBK','PH_ICBC','PH_JPM','PH_KEB','PH_KHB','PH_MEGA','PH_MIZUHO','PH_MUFG','PH_RABI','PH_RBD','PH_RBMI','PH_SHB','PH_SMBC','PH_TYSB','PH_UOB','PH_MAYA');;
        label: "Cut-off Processing only"
      }
      else: "Instant Processing"
    }
  }

  dimension: ID_Speed_SLA { # https://docs.xendit.co/xendisburse/limits-and-processing-times#processing-time
    type:  string
    case: {
      when: {
        sql:channel_code = 'BCA' and (destination_amount < 10000 or destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '20:00' or ${product_creation_timestamp_time_of_day} <= '06:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'BCA' and (destination_amount < 10000 or destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '20:00' and ${product_creation_timestamp_time_of_day} > '06:59') ;;
        label: "instant"
      }
      when: {
        sql:(channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '03:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:(channel_code = 'MANDIRI' or channel_code = 'BRI') and (${product_creation_timestamp_time_of_day}<= '23:00' and ${product_creation_timestamp_time_of_day} > '03:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '01:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'BNI' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '01:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'SAHABAT_SAMPOERNA' and (destination_amount < *********0) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '02:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'SAHABAT_SAMPOERNA' and (destination_amount < *********0) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '02:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '00:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'PERMATA' and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '00:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'CIMB' and  (destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '21:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'CIMB' and  (destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '21:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "instant"
      }
      when: {
        sql:channel_code = 'SINARMAS' and ( destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} >= '14:00' or ${product_creation_timestamp_time_of_day} <= '08:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql:channel_code = 'SINARMAS' and ( destination_amount > ********* ) and (${product_creation_timestamp_time_of_day} < '14:00' and ${product_creation_timestamp_time_of_day} > '08:59') ;;
        label: "instant"
      }
      when: {
        sql: (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (destination_amount >= 10000 or destination_amount < ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql: (channel_code = 'BCA' or channel_code = 'CIMB' or channel_code = 'SINARMAS') and (destination_amount >= 10000 or destination_amount < ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "instant"
      }
      when: {
        sql: (destination_amount <= ********* ) and (${product_creation_timestamp_time_of_day} >= '23:00' or ${product_creation_timestamp_time_of_day} <= '04:59') ;;
        label: "bank downtimes"
      }
      when: {
        sql: (destination_amount <= ********* ) and (${product_creation_timestamp_time_of_day} < '23:00' and ${product_creation_timestamp_time_of_day} > '04:59') ;;
        label: "instant"
      }
      else: "others"
    }
  }


  dimension: PH_Speed_SLA {
    type:  string
    case: {
      when: {
        sql: ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= 50000 ;;
        label: "Channels with instant processing"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${product_creation_timestamp_day_of_week_index} < 5 and ${TABLE}.destination_amount > 50000 and  ${TABLE}.destination_amount <= ********* and ${product_creation_timestamp_ph_time_of_day} >= '06:00' and ${product_creation_timestamp_ph_time_of_day} <= '15:00'  ;;
        label: "Channels with instant and cut off processing and with > 50.000"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Instant Processing' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount > 50000 and  ${TABLE}.destination_amount <= ********* and ((${product_creation_timestamp_day_of_week_index} < 5 and (${product_creation_timestamp_ph_time_of_day}  >= '15:01' or ${product_creation_timestamp_ph_time_of_day} <= '05:59')) or ${product_creation_timestamp_day_of_week_index} >=5 ) ;;
        label: "Channels with instant cut off processing and with > 50.000 and after cut off time"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Cut-off Processing only' and ${destination_currency} = 'PHP' and ${product_creation_timestamp_day_of_week_index} < 5 and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= ********* and ${product_creation_timestamp_ph_time_of_day} >= '06:00' and ${product_creation_timestamp_ph_time_of_day} <= '15:00'  ;;
        label: "Channels with cut off processing only"
      }
      when: {
        sql:  ${PH_Connector_type} = 'Cut-off Processing only' and ${destination_currency} = 'PHP' and ${TABLE}.destination_amount >= 1 and  ${TABLE}.destination_amount <= ********* and ((${product_creation_timestamp_day_of_week_index} < 5 and (${product_creation_timestamp_ph_time_of_day}  >= '15:01' or ${product_creation_timestamp_ph_time_of_day} <= '05:59')) or ${product_creation_timestamp_day_of_week_index} >=5 ) ;;
        label: "Channels with cut off processing only and after cut off time"
      }
      # Possibly more when statements
      else: "other"
    }
  }

  dimension: end_to_end_time {
    type: number
    sql: ${TABLE}.end_to_end_time / 1000;;
  }

  dimension: end_to_end_time_without_compliance {
    type: number
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000 ;;
  }

  dimension: time_to_perform_initial_processing {
    type: number
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
  }

  dimension: time_to_perform_risk_assessment {
    type: number
    sql: ${TABLE}.time_to_perform_risk_assessment / 1000;;
  }

  dimension: time_to_perform_compliance_verification {
    type: number
    sql: ${TABLE}.time_to_perform_compliance_verification/ 1000 ;;
  }

  dimension: time_to_perform_disbursement_creation {
    type: number
    sql: ${TABLE}.time_to_perform_disbursement_creation / 1000;;
  }

  dimension: time_to_perform_disbursement {
    type: number
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
  }

  dimension: time_to_process_customer_request {
    type: number
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
  }

  dimension: time_to_process_customer_request_without_compliance {
    type: number
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
  }

  dimension: time_to_do_internal_processing {
    type: number
    sql: ${TABLE}.time_to_do_internal_processing / 1000 ;;
  }

  dimension: time_to_send_first_webhook {
    type: number
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
  }

  dimension: slow_sla_reason {
    type:  string
    case:  {
      when: {
        sql:  ${time_to_perform_disbursement} >= 13 ;;
        label: "stuck on disbursing"
      }
      when: {
        sql:  ${time_to_perform_disbursement_creation} > 3 ;;
        label: "stuck on creating disbursement"
      }
      when: {
        sql:  ${time_to_perform_risk_assessment} > 3 ;;
        label: "stuck on checking name blacklist"
      }
      when: {
        sql:  ${time_to_send_first_webhook} >= 600 ;;
        label: "failed on sending webhook (auto retry)"
      }
      when: {
        sql:  ${time_to_send_first_webhook} > 2 ;;
        label: "slow on sending webhook"
      }
      else: "others"
    }
  }

  # Measure end_to_end_time
  measure:end_to_end_time_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time / 1000;;
    value_format: "0.##"
  }

  measure:end_to_end_time_elapsed_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time / 1000;;
    value_format: "0.##"
  }

  measure:end_to_end_time_elapsed_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time / 1000;;
    value_format: "0.##"
  }

  # Measure end_to_end_time_without_compliance
  measure:end_to_end_time_without_compliance_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000;;
    value_format: "0.##"
  }

  measure:end_to_end_time_without_compliance_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.end_to_end_time_without_compliance/ 1000 ;;
    value_format: "0.##"
  }

  measure:end_to_end_time_without_compliance_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.end_to_end_time_without_compliance / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_initial_processing
  measure:time_to_perform_initial_processing_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_initial_processing_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_initial_processing_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_initial_processing / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_risk_assessment
  measure:time_to_perform_risk_assessment_99pct   {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_risk_assessment / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_risk_assessment_95pct   {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_risk_assessment / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_risk_assessment_90pct   {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_risk_assessment / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_compliance_verification
  measure:time_to_perform_compliance_verification_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_compliance_verification_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_compliance_verification_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_compliance_verification / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_disbursement_creation
  measure:time_to_perform_disbursement_creation_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_disbursement_creation / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_creation_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_disbursement_creation / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_creation_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_disbursement_creation / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_disbursement
  measure:time_to_perform_disbursement_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
    value_format: "0.##"
  }

  measure:time_to_perform_disbursement_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_perform_disbursement / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_process_customer_request
  measure:time_to_process_customer_request_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
    value_format: "0.##"
  }

  measure:time_to_process_customer_request_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
    value_format: "0.##"
  }

  measure:time_to_process_customer_request_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_process_customer_request_without_compliance
  measure:time_to_process_customer_request_without_compliance_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
    value_format: "0.##"
  }

  measure:time_to_process_customer_request_without_compliance_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
    value_format: "0.##"
  }

  measure:time_to_process_customer_request_without_compliance_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_perform_internal_processing
  measure:time_to_do_internal_processing_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_do_internal_processing / 1000;;
    value_format: "0.##"
  }

  measure:time_to_do_internal_processing_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_do_internal_processing / 1000;;
    value_format: "0.##"
  }

  measure:time_to_do_internal_processing_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_do_internal_processing / 1000;;
    value_format: "0.##"
  }

  # Measure time_to_send_first_webhook
  measure:time_to_send_first_webhook_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
    value_format: "0.##"
  }

  measure:time_to_send_first_webhook_95pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
    value_format: "0.##"
  }

  measure:time_to_send_first_webhook_90pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.time_to_send_first_webhook / 1000;;
    value_format: "0.##"
  }

  # Measures average time_to_process_customer_request_without_compliance
  measure: average_time_to_process_customer_request_without_compliance {
    type: average
    sql:  ${TABLE}.time_to_process_customer_request_without_compliance / 1000;;
    value_format: "0"
  }

  # Measures average time_to_process_customer_request_including_compliance
  measure: average_time_to_process_customer_request_including_compliance {
    type: average
    sql:  ${TABLE}.time_to_process_customer_request / 1000;;
    value_format: "0"
  }
}
