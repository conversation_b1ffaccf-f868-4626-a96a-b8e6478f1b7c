# Define the database connection to be used for this model.
connection: "presto"

# include all the views
include: "/views/**/*.view.lkml"

# Datagroups define a caching policy for an Explore. To learn more,
# use the Quick Help panel on the right to see documentation.

datagroup: new_regional_transaction_view_default_datagroup {
  # sql_trigger: SELECT MAX(id) FROM etl_log;;
  max_cache_age: "1 hour"
}

persist_with: new_regional_transaction_view_default_datagroup

# Explores allow you to join together different views (database tables) based on the
# relationships between fields. By joining a view into an Explore, you make those
# fields available to users for data analysis.
# Explores should be purpose-built for specific use cases.

# To see the Explore you’re building, navigate to the Explore menu and select an Explore under "New Regional Transaction View"

explore: business {
  join: business_entity {
    type: left_outer 
    sql_on: ${business.business_entity_id} = ${business_entity.id} ;;
    relationship: many_to_one
  }
}

# To create more sophisticated Explores that involve multiple views, you can use the join parameter.
# Typically, join parameters require that you define the join type, join relationship, and a sql_on clause.
# Each joined view also needs to define a primary key.

explore: business_entity {}

explore: transaction {
  join: business {
    type: left_outer 
    sql_on: ${transaction.business_id} = ${business.id} ;;
    relationship: many_to_one
  }

  join: business_entity {
    type: left_outer 
    sql_on: ${business.business_entity_id} = ${business_entity.id} ;;
    relationship: many_to_one
  }
}

