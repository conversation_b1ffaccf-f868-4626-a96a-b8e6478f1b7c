view: t3_balance_audit {
  derived_table: {
    sql: select id, type, user_id, result from hive.test__dev_platform.t3_balance_audit
      ;;
  }

  suggestions: yes

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: result {
    type: string
    sql: ${TABLE}.result ;;
  }

  set: detail {
    fields: [id, type, user_id, result]
  }
}
