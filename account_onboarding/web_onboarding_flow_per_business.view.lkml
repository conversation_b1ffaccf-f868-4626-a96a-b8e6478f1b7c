view: web_onboarding_flow_per_business {
  derived_table: {
    sql:
    select *
    from transform__nux_onboarding.transform_web_onboarding_flow_per_business  ;;
  }

  suggestions: no

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
    primary_key: yes
  }

  dimension_group: first_session_start_tstamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.first_session_start_tstamp ;;
  }

  dimension_group: account_activation_started {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_account_activation_start_tstamp ;;
    group_label: "First Account Activation Click Timestamp"
  }

  dimension_group: finish_lead_qual_time {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_finish_leaqual_tstamp ;;
    group_label: "First Finish Lead Qual Timestamp"
  }

  dimension_group: finish_lead_qual_to_account_activation {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_finish_leaqual_tstamp;;
    sql_end: ${TABLE}.min_account_activation_start_tstamp;;
    group_label: "Finish Lead Qual to Account Activation"
  }

  dimension_group: page1_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_page1_complete_tstamp ;;
    group_label: "First Page 1 Completion Timestamp"
  }

  dimension_group: page2_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_page2_complete_tstamp ;;
    group_label: "First Page 2 Completion Timestamp"
  }

  dimension_group: page3_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_page3_complete_tstamp ;;
    group_label: "First Page 3 Completion Timestamp"
  }

  dimension_group: page4_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_page4_complete_tstamp ;;
    group_label: "First Page 4 Completion Timestamp"
  }

  dimension_group: page5_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_page5_complete_tstamp ;;
    group_label: "First Page 5 Completion Timestamp"
  }

  dimension_group: goliverequest_complete {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_goliverequest_tstamp ;;
    group_label: "First Go Live Request Timestamp"
  }

  dimension: page1_completes {
    type: number
    sql: ${TABLE}.page1_completes ;;
    group_label: "Completion count by business"
  }

  dimension: page2_completes {
    type: number
    sql: ${TABLE}.page2_completes ;;
    group_label: "Completion count by business"
  }

  dimension: page3_completes {
    type: number
    sql: ${TABLE}.page3_completes ;;
    group_label: "Completion count by business"
  }

  dimension: page4_completes {
    type: number
    sql: ${TABLE}.page4_completes ;;
    group_label: "Completion count by business"
  }

  dimension: page5_completes {
    type: number
    sql: ${TABLE}.page5_completes ;;
    group_label: "Completion count by business"
  }

  dimension: glr_submitted {
    type: number
    sql: ${TABLE}.glr_submitted ;;
    group_label: "Completion count by business"
  }

  dimension: take_ktp_count {
    type: number
    sql: ${TABLE}.take_ktp_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: retake_ktp_count {
    type: number
    sql: ${TABLE}.retake_ktp_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: take_npwp_count {
    type: number
    sql: ${TABLE}.take_npwp_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: retake_npwp_count {
    type: number
    sql: ${TABLE}.retake_npwp_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: verify_id_count {
    type: number
    sql: ${TABLE}.verify_id_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: started_ia_count {
    type: number
    sql: ${TABLE}.started_ia_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: use_manual_verification_count {
    type: number
    sql: ${TABLE}.use_manual_verification_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: cancel_ia_count {
    type: number
    sql: ${TABLE}.cancel_ia_count ;;
    group_label: "Instant Activation Counts"
  }

  dimension: onboarding_sessions {
    type: number
    sql: ${TABLE}.onboarding_sessions ;;
    hidden: yes
  }


  dimension_group: start_account_activation_to_goliverequest {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_account_activation_start_tstamp;;
    sql_end: ${TABLE}.min_goliverequest_tstamp;;
    group_label: "Begin Account Activation to Go Live Request"
  }

  dimension_group: start_account_activation_to_page1complete {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_account_activation_start_tstamp;;
    sql_end: ${TABLE}.min_page1_complete_tstamp;;
    group_label: "Begin Account Activation to Page 1 Complete"
  }

  dimension_group: page1complete_to_page2complete {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page1_complete_tstamp;;
    sql_end: ${TABLE}.min_page2_complete_tstamp;;
    group_label: "Page 1 Complete to Page 2 Complete"
  }

  dimension_group: page2complete_to_page3complete {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page2_complete_tstamp;;
    sql_end: ${TABLE}.min_page3_complete_tstamp;;
    group_label: "Page 2 Complete to Page 3 Complete"
  }

  dimension_group: page3complete_to_page4complete {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page3_complete_tstamp;;
    sql_end: ${TABLE}.min_page4_complete_tstamp;;
    group_label: "Page 3 Complete to Page 4 Complete"
  }

  dimension_group: page4complete_to_page5complete {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page4_complete_tstamp;;
    sql_end: ${TABLE}.min_page5_complete_tstamp;;
    group_label: "Page 4 Complete to Page 5 Complete"
  }

  dimension_group: page3complete_to_goliverequest {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page3_complete_tstamp;;
    sql_end: ${TABLE}.min_goliverequest_tstamp;;
    group_label: "Page 3 Complete to Go Live Request"
  }

  dimension_group: page4complete_to_goliverequest {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page4_complete_tstamp;;
    sql_end: ${TABLE}.min_goliverequest_tstamp;;
    group_label: "Page 4 Complete to Go Live Request"
  }

  dimension_group: page5complete_to_goliverequest {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_page5_complete_tstamp;;
    sql_end: ${TABLE}.min_goliverequest_tstamp;;
    group_label: "Page 5 Complete to Go Live Request"
  }

  measure: successful_attempts {
    type: sum
    sql: ${TABLE}.successful_attempts ;;
    description: "Number of successful website validations"
    group_label: "Website Validation Stats"
  }

  measure: failed_attempts {
    type: sum
    sql: ${TABLE}.failed_attempts ;;
    description: "Number of failed website validations"
    group_label: "Website Validation Stats"
  }

  measure: failed_attempts_before_first_success {
    type: sum
    sql: ${TABLE}.failed_attempts_before_first_success ;;
    description: "Number of failed website validations before first success (or without success)"
    group_label: "Website Validation Stats"
  }

  measure: lead_qual_completes_count {
    type: sum
    sql: case when ${TABLE}.finish_lead_qual_count > 0 then 1 else 0 end;;
    group_label: "Stage Count"
  }

  measure: lead_qual_activate_latet_count {
    type: sum
    sql: case when ${TABLE}.submitted_lead_qual_activate_later_count > 0 then 1 else 0 end;;
    group_label: "Stage Count"
  }

  measure: lead_qual_activate_now_count {
    type: sum
    sql: case when ${TABLE}.submitted_lead_qual_activate_now_count > 0 then 1 else 0 end;;
    group_label: "Stage Count"
  }

  measure: begin_account_activation_count {
    type: sum
    sql: case when ${TABLE}.begin_account_activation_count > 0 then 1 else 0 end;;
    group_label: "Stage Count"
  }

  measure: begin_account_activation {
    type: sum
    sql: case when ${TABLE}.begin_account_activation_count > 0 then 1 else 0 end;;
    description: "Whether users have clicked on 'Begin Account Activation' atleast once"
    group_label: "Stage Count"
  }

  measure: page1_completes_count {
    type: sum
    sql: case when ${TABLE}.page1_completes > 0 then 1 else 0 end;;
    description: "Whether users have completed Page 1 atleast once"
    group_label: "Stage Count"
  }

  measure: page2_completes_count {
    type: sum
    sql: case when ${TABLE}.page2_completes > 0 then 1 else 0 end;;
    description: "Whether users have completed Page 2 atleast once"
    group_label: "Stage Count"
  }

  measure: page3_completes_count {
    type: sum
    sql: case when ${TABLE}.page3_completes > 0 then 1 else 0 end;;
    description: "Whether users have completed Page 3 atleast once"
    group_label: "Stage Count"
  }

  measure: page4_completes_count {
    type: sum
    sql: case when ${TABLE}.page4_completes > 0 then 1 else 0 end;;
    description: "Whether users have completed Page 4 atleast once"
    group_label: "Stage Count"
  }

  measure: page5_completes_count {
    type: sum
    sql: case when ${TABLE}.page5_completes > 0 then 1 else 0 end;;
    description: "Whether users have completed Page 5 atleast once"
    group_label: "Stage Count"
  }

  measure: glr_submitted_count {
    type: sum
    sql: case when ${TABLE}.glr_submitted > 0 then 1 else 0 end;;
    description: "Whether users clicked on 'Submit' in the Account Activation flow atleast once"
    group_label: "Stage Count"
  }

  measure: onboarding_session_count {
    type: sum
    sql: ${TABLE}.onboarding_sessions ;;
    description: "Number of unique sessions with onboarding session events"
  }

  measure: onboarding_session_count_per_business {
    type: sum_distinct
    sql_distinct_key: ${business_id} ;;
    sql: ${TABLE}.onboarding_sessions ;;
    description: "Number of unique sessions with onboarding session events per business"
  }

  measure: row_count {
    type: count
    description: "Number of businesses"
  }

  set: detail {
    fields: [
      business_id,
      first_session_start_tstamp_time,
      account_activation_started_raw,
      finish_lead_qual_time_raw,
      page1_complete_raw,
      page2_complete_raw,
      page3_complete_raw,
      page4_complete_raw,
      page5_complete_raw,
      goliverequest_complete_raw,
      begin_account_activation_count,
      lead_qual_completes_count,
      lead_qual_activate_latet_count,
      lead_qual_activate_now_count,
      page1_completes,
      page2_completes,
      page3_completes,
      page4_completes,
      page5_completes,
      glr_submitted,
      take_ktp_count,
      retake_ktp_count,
      take_npwp_count,
      retake_npwp_count,
      verify_id_count,
      started_ia_count,
      use_manual_verification_count,
      cancel_ia_count
    ]
  }
}
