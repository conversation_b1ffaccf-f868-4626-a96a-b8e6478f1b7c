# The name of this view in Looker is "Kyc Request"
view: kyc_request {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__xendit_kyc_service.kyc_request ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Business ID" in Explore.

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: kyc_fail_reason {
    type: string
    sql: ${TABLE}.kyc_fail_reason ;;
  }

  dimension: kyc_fail_reason_label {
    type: string
    sql: ${TABLE}.kyc_fail_reason_label ;;
  }

  dimension_group: kyc_failed_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_failed_timestamp ;;
  }

  dimension_group: kyc_first_submission_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_first_submission_timestamp ;;
  }

  dimension_group: kyc_last_submission_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_last_submission_timestamp ;;
  }

  dimension_group: kyc_passed_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_passed_timestamp ;;
  }

  dimension: kyc_product_code {
    type: string
    sql: ${TABLE}.kyc_product_code ;;
  }

  dimension: kyc_resubmission_count {
    type: number
    sql: ${TABLE}.kyc_resubmission_count ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_kyc_resubmission_count {
    type: sum
    sql: ${kyc_resubmission_count} ;;  }
  measure: average_kyc_resubmission_count {
    type: average
    sql: ${kyc_resubmission_count} ;;  }

  dimension_group: kyc_resubmission_requested_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_resubmission_requested_timestamp ;;
  }

  dimension: kyc_suspend_reason {
    type: string
    sql: ${TABLE}.kyc_suspend_reason ;;
  }

  dimension_group: kyc_suspended_timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.kyc_suspended_timestamp ;;
  }

  dimension: kyc_verification_notes {
    type: string
    sql: ${TABLE}.kyc_verification_notes ;;
  }

  dimension: kyc_verification_status {
    type: string
    sql: ${TABLE}.kyc_verification_status ;;
  }

  dimension: resubmission_request {
    type: string
    sql: ${TABLE}.resubmission_request ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  measure: count {
    type: count
    drill_fields: [id, business.name, business.id]
  }
}
