view: web_onboarding_flow_per_session {
  derived_table: {
    sql:
    select *
    from transform__nux_onboarding.transform_web_onboarding_flow_per_session  ;;
  }

  suggestions: no

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: domain_sessionid {
    type: string
    sql: ${TABLE}.domain_sessionid ;;
  }

  dimension_group: session_start_tstamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.session_start_tstamp ;;
  }

  dimension_group: session_end_tstamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.session_end_tstamp ;;
  }

  dimension_group: finish_lead_qual_time {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.min_finish_leaqual_tstamp ;;
    group_label: "First Finish Lead Qual Timestamp"
  }

  dimension_group: finish_lead_qual_to_account_activation {
    type: duration
    intervals: [second, minute, hour, day]
    sql_start: ${TABLE}.min_finish_leaqual_tstamp;;
    sql_end: ${TABLE}.min_page2_complete_tstamp;;
    group_label: "Finish Lead Qual to Account Activation"
  }

  measure: median_seconds_from_finish_lead_qual_to_account_activation {
    group_label: "Median seconds from finsish lead qual to account activation"
    type: number
    sql: approx_percentile(${seconds_finish_lead_qual_to_account_activation}, 0.5) ;;
    value_format: "0\" seconds\""
  }

  measure: average_seconds_from_finish_lead_qual_to_account_activation {
    group_label: "seconds from finsish lead qual to account activation"
    label: "Average seconds from finsish lead qual to account activation"
    type: average
    sql: ${seconds_finish_lead_qual_to_account_activation} ;;
    value_format: "0\" seconds\""
  }

  dimension_group: account_activation_start_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_account_activation_start_tstamp ;;
  }

  dimension_group: page1_complete_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_page1_complete_tstamp ;;
  }

  dimension_group: page2_complete_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_page2_complete_tstamp ;;
  }

  dimension_group: page3_complete_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_page3_complete_tstamp ;;
  }

  dimension_group: page4_complete_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_page4_complete_tstamp ;;
  }

  dimension_group: page5_complete_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_page5_complete_tstamp ;;
  }

  dimension_group: goliverequest_tstamp {
    type: time
    timeframes: [raw, time, date, week, month]
    sql: ${TABLE}.min_goliverequest_tstamp ;;
  }

  dimension: lead_qual_completes_count {
    type: number
    sql: ${TABLE}.finish_lead_qual_count ;;
    group_label: "Counts per session per business"
  }

  dimension: lead_qual_activate_latet_count {
    type: number
    sql: ${TABLE}.submitted_lead_qual_activate_later_count ;;
    group_label: "Counts per session per business"
  }

  dimension: lead_qual_activate_now_count {
    type: number
    sql: ${TABLE}.submitted_lead_qual_activate_now_count ;;
    group_label: "Counts per session per business"
  }

  dimension: begin_account_activation_count {
    type: number
    sql: ${TABLE}.begin_account_activation_count ;;
    group_label: "Counts per session per business"
  }

  dimension: page1_completes {
    type: number
    sql: ${TABLE}.page1_completes ;;
    group_label: "Counts per session per business"
  }

  dimension: page2_completes {
    type: number
    sql: ${TABLE}.page2_completes ;;
    group_label: "Counts per session per business"
  }

  dimension: page3_completes {
    type: number
    sql: ${TABLE}.page3_completes ;;
    group_label: "Counts per session per business"
  }

  dimension: page4_completes {
    type: number
    sql: ${TABLE}.page4_completes ;;
    group_label: "Counts per session per business"
  }

  dimension: page5_completes {
    type: number
    sql: ${TABLE}.page5_completes ;;
    group_label: "Counts per session per business"
  }

  dimension: glr_submitted {
    type: number
    sql: ${TABLE}.glr_submitted ;;
    group_label: "Counts per session per business"
  }

  dimension: take_ktp_count {
    type: number
    sql: ${TABLE}.take_ktp_count ;;
    group_label: "Counts per session per business"
  }

  dimension: retake_ktp_count {
    type: number
    sql: ${TABLE}.retake_ktp_count ;;
    group_label: "Counts per session per business"
  }

  dimension: take_npwp_count {
    type: number
    sql: ${TABLE}.take_npwp_count ;;
    group_label: "Counts per session per business"
  }

  dimension: retake_npwp_count {
    type: number
    sql: ${TABLE}.retake_npwp_count ;;
    group_label: "Counts per session per business"
  }

  dimension: verify_id_count {
    type: number
    sql: ${TABLE}.verify_id_count ;;
    group_label: "Counts per session per business"
  }

  dimension: started_ia_count {
    type: number
    sql: ${TABLE}.started_ia_count ;;
    group_label: "Counts per session per business"
  }

  dimension: use_manual_verification_count {
    type: number
    sql: ${TABLE}.use_manual_verification_count ;;
    group_label: "Counts per session per business"
  }

  dimension: cancel_ia_count {
    type: number
    sql: ${TABLE}.cancel_ia_count ;;
    group_label: "Counts per session per business"
  }

  set: detail {
    fields: [
      business_id,
      domain_sessionid,
      session_start_tstamp_time,
      session_end_tstamp_time,
      finish_lead_qual_time_raw,
      seconds_finish_lead_qual_to_account_activation,
      median_seconds_from_finish_lead_qual_to_account_activation,
      average_seconds_from_finish_lead_qual_to_account_activation,
      account_activation_start_tstamp_time,
      page1_complete_tstamp_time,
      page2_complete_tstamp_time,
      page3_complete_tstamp_time,
      page4_complete_tstamp_time,
      page5_complete_tstamp_time,
      goliverequest_tstamp_time,
      lead_qual_completes_count,
      lead_qual_activate_latet_count,
      lead_qual_activate_now_count,
      begin_account_activation_count,
      page1_completes,
      page2_completes,
      page3_completes,
      page4_completes,
      page5_completes,
      glr_submitted,
      take_ktp_count,
      retake_ktp_count,
      take_npwp_count,
      retake_npwp_count,
      verify_id_count,
      started_ia_count,
      use_manual_verification_count,
      cancel_ia_count
    ]
  }
}
