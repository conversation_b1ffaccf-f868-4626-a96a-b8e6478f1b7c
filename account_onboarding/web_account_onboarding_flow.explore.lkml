include: "//central-models-dbr/businesses/businesses.view"
include: "//central-models-dbr/businesses/business_facts.view"

include: "/account_onboarding/*.view.lkml"

explore: web_onboarding_and_account_activation {
  from: web_onboarding_flow_per_session
  description: "User activity for Account Activation on Dashboard, tracked via Snowplow Events"
  group_label: "Onboarding Flow"
  view_label: "Onboarding Details By Session"

  join: web_onboarding_flow_per_business {
    type: left_outer
    sql_on: ${web_onboarding_and_account_activation.business_id} =  ${web_onboarding_flow_per_business.business_id};;
    relationship: many_to_one
    view_label: "Onboarding Details By Business"
  }

  join: businesses {
    type: left_outer
    sql_on:${web_onboarding_and_account_activation.business_id} =  ${businesses.business_id};;
    relationship: many_to_one
  }

  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }

  always_filter: {
    filters: [businesses.is_internal: "no", businesses.is_soft_deleted_account: "no"]
  }
}
