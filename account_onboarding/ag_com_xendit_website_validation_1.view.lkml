view: ag_com_xendit_website_validation_1 {
  sql_table_name: clean__kafka_snowplow.com_xendit_website_validation_1 ;;
  suggestions: no

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: is_livewebsite {
    type: yesno
    sql: ${TABLE}.is_livewebsite ;;
  }

  dimension: root_id {
    type: string
    sql: ${TABLE}.root_id ;;
  }

  dimension_group: root_tstamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.root_tstamp ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: timestamp_ {
    type: string
    sql: ${TABLE}.timestamp_ ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: website {
    type: string
    sql: ${TABLE}.website ;;
  }

  dimension: website_type {
    type: string
    sql: ${TABLE}.website_type ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
