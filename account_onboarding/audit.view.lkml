# The name of this view in Looker is "Audit"
view: audit {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__xendit_kyc_service.audit ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Action" in Explore.

  dimension: action {
    type: string
    sql: ${TABLE}.action ;;
  }

  dimension: actor {
    type: string
    sql: ${TABLE}.actor ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: kyc_product_code {
    type: string
    sql: ${TABLE}.kyc_product_code ;;
  }

  dimension: new_snapshot {
    type: string
    sql: ${TABLE}.new_snapshot ;;
  }

  dimension: old_snapshot {
    type: string
    sql: ${TABLE}.old_snapshot ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: reference_table {
    type: string
    sql: ${TABLE}.reference_table ;;
  }

  dimension_group: timestamp {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}."timestamp" ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  measure: count {
    type: count
    drill_fields: [id]

  }
  dimension: new_snapshot_details {
    type: string
    sql: json_extract_scalar(${TABLE}.response, '$.new_snapshot') ;;
  }
  dimension: old_snapshot_details {
    type: string
    sql: json_extract_scalar(${TABLE}.response, '$.old_snapshot') ;;
  }
}
