include: "kyc_request.view.lkml"
include: "business.view.lkml"
include: "business_entity.view.lkml"


explore: kyc_request {
  from: kyc_request
  description: "View of the KYC request"
  group_label: "KYC Request"
  view_label: "KYC Request by Business"

  join: business {
    from: business
    type: inner
    sql_on: ${kyc_request.business_id} = ${business.id};;
    relationship: many_to_one
    view_label: "Merging KYC Req to Business"
  }

  join: business_entity {
    type: inner
    sql_on: ${business.business_entity_id} = ${business_entity.id};;
    relationship: many_to_one
    view_label: "Merging Business Entity to Business"
  }
}
