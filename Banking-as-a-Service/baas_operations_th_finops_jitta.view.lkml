
view: baas_operations_th_finops_jitta {
  derived_table: {
    sql: select id, account_id, reference_id, amount / 100 as amount, currency, json_extract(deposit, '$.top_up_details.channel_code') AS channel_code, created, updated from clean__bank_live.jitta__statement_entries where type='DEPOSIT' and direction='CREDIT' order by created desc ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: account_id {
    type: string
    sql: ${TABLE}.account_id ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension_group: created {
    type: time
    sql: ${TABLE}.created ;;
  }

  dimension_group: updated {
    type: time
    sql: ${TABLE}.updated ;;
  }

  set: detail {
    fields: [
      id,
      account_id,
      reference_id,
      amount,
      currency,
      channel_code,
      created_time,
      updated_time
    ]
  }
}
