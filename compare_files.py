#!/usr/bin/env python3
"""
Script to compare LookML files between two repositories and identify missing dimensions/measures
"""
import os
import re
import sys
from pathlib import Path

def extract_dimensions_and_measures(file_path):
    """Extract all dimension and measure names from a LookML file"""
    dimensions = []
    measures = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Find all dimensions
        dimension_pattern = r'dimension(?:_group)?\s*:\s*(\w+)\s*{'
        dimensions = re.findall(dimension_pattern, content)
        
        # Find all measures
        measure_pattern = r'measure\s*:\s*(\w+)\s*{'
        measures = re.findall(measure_pattern, content)
        
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        
    return set(dimensions), set(measures)

def compare_files(source_repo, target_repo, relative_path):
    """Compare two LookML files and return missing dimensions/measures"""
    source_file = os.path.join(source_repo, relative_path)
    target_file = os.path.join(target_repo, relative_path)
    
    if not os.path.exists(source_file) or not os.path.exists(target_file):
        return None, None, None
    
    source_dims, source_measures = extract_dimensions_and_measures(source_file)
    target_dims, target_measures = extract_dimensions_and_measures(target_file)
    
    missing_dims = source_dims - target_dims
    missing_measures = source_measures - target_measures
    
    return missing_dims, missing_measures, len(source_dims) + len(source_measures)

def main():
    source_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
    target_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"
    
    # Get all .view.lkml files from source repo
    source_files = []
    for root, dirs, files in os.walk(source_repo):
        for file in files:
            if file.endswith('.view.lkml'):
                relative_path = os.path.relpath(os.path.join(root, file), source_repo)
                source_files.append(relative_path)
    
    print(f"Found {len(source_files)} view files in source repository")
    
    files_with_differences = []
    
    for relative_path in source_files:
        target_path = os.path.join(target_repo, relative_path)
        if os.path.exists(target_path):
            missing_dims, missing_measures, total_elements = compare_files(source_repo, target_repo, relative_path)
            
            if missing_dims or missing_measures:
                files_with_differences.append({
                    'path': relative_path,
                    'missing_dimensions': missing_dims,
                    'missing_measures': missing_measures,
                    'total_missing': len(missing_dims) + len(missing_measures),
                    'total_elements': total_elements
                })
    
    # Sort by number of missing elements (descending)
    files_with_differences.sort(key=lambda x: x['total_missing'], reverse=True)
    
    print(f"\nFound {len(files_with_differences)} files with missing dimensions/measures:")
    print("=" * 80)
    
    for i, file_info in enumerate(files_with_differences[:20]):  # Show top 20
        print(f"{i+1}. {file_info['path']}")
        print(f"   Missing dimensions: {len(file_info['missing_dimensions'])}")
        print(f"   Missing measures: {len(file_info['missing_measures'])}")
        print(f"   Total missing: {file_info['total_missing']}")
        if file_info['missing_dimensions']:
            print(f"   Dimensions: {', '.join(sorted(file_info['missing_dimensions']))}")
        if file_info['missing_measures']:
            print(f"   Measures: {', '.join(sorted(file_info['missing_measures']))}")
        print()

if __name__ == "__main__":
    main()
