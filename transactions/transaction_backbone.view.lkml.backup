include: "./ag_views/transaction_backbone.view.lkml"

view: +transaction_backbone {
################################################
# Config (Sets, parameters, access grants etc) {
################################################
  filter: date_partition {
    type: date
    convert_tz: no
    sql: {% condition date_partition %} ${TABLE}.dt {% endcondition %} ;;
  }

  set: products {
    fields: [money_flow,product_type,product_subtype, channel_name]
  }
################################################
# Config (Sets, parameters, access grants etc) }
################################################

################################################
# Primary keys {
################################################
  dimension: pk1_reference {
    primary_key: yes
    hidden: yes
    sql: ${id} ;;
  }
################################################
# Primary keys }
################################################

################################################
# Dimensions {
################################################
  dimension: id {
    description: "Unique identifier of each transaction"
    type: string
    sql: ${TABLE}.reference ;;
  }

  dimension: user_id {
    hidden: yes
    description: "Identifier of the user that made the transaction. This is the same as business_id in businesses"
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
    description: "Xendit entity that processed a transaction"
  }

  dimension: money_flow {
    group_label: "Product"
    description: "Money In VS Money Out"
    allow_fill: yes
    case_sensitive: no
    case: {
      when:{sql:${TABLE}.money_flow = 'MONEY_IN' ;; label: "Money In"}
      when:{sql:${TABLE}.money_flow = 'MONEY_OUT' ;; label: "Money Out"}
      when:{sql:${TABLE}.money_flow = 'IN-HOUSE TRANSACTION';; label: "In-house Transaction"}
      else: "Uncategorised"
    }
    suggestions: ["Money In", "Money Out", "In-house Transaction", "Uncategorised"]
  }

  dimension: product_subtype {
    group_label: "Product"
    type: string
    sql:${TABLE}.product_subtype;;
    case_sensitive: no
    suggest_persist_for: "72 hours"
  }

  dimension: product_type {
    group_label: "Product"
    description: "The name of channel product of a transaction"
    allow_fill: yes
    case: {
      when:{sql:${TABLE}.product_type = 'CREDIT_CARD' ;; label: "Credit Card"}
      when:{sql:${TABLE}.product_type = 'DIRECT_DEBIT' ;; label: "Direct Debit"}
      when:{sql:${TABLE}.product_type = 'DISBURSEMENT' ;; label: "Disbursement"}
      when:{sql:${TABLE}.product_type = 'EWALLET' ;; label: "eWallet"}
      when:{sql:${TABLE}.product_type = 'RETAIL_OUTLET' ;; label: "Retail Outlet"}
      when:{sql:${TABLE}.product_type = 'IN-HOUSE TRANSACTION' ;; label: "In-house Transaction"}
      when:{sql:${TABLE}.product_type = 'VIRTUAL_ACCOUNT' ;; label: "Virtual Account"}
      when:{sql:${TABLE}.product_type = 'QR_CODE' ;; label: "QR Code"}
      when:{sql:${TABLE}.product_type = 'PAYLATER' ;; label: "PayLater"}
      else: "Uncategorised"
    }
    case_sensitive: no
  }

  dimension: product_type_db {
    hidden: yes
    sql: ${TABLE}.product_type ;;
  }

  dimension: channel_name {
    description: "Bank name, retail outlet name, or ewallet name. In case of disbursement, it is the recipient bank."
    group_label: "Product"
    type: string
    sql: ${TABLE}.channel_name ;;
    case_sensitive: no
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      day_of_month,
      day_of_year,
      week,
      month,
      quarter,
      year,
      quarter_of_year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      day_of_month,
      day_of_year,
      week,
      month,
      quarter,
      year,
      quarter_of_year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension_group: dt {
    label: "Created (UTC)"
    description: "Created Date of transaction in UTC time. Partitioned column, highly recommended to use as filter"
    type: time
    timeframes: [
      raw,
      date,
      day_of_month,
      day_of_year,
      week,
      month,
      quarter,
      year,
      quarter_of_year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension_group: settled {
    description:
    "Settled time of transactions in chosen timezone.
    Should match with ledger / T4 settled time and allow for billing reconciliation.
    Note this is not supported for Escrow VA, Escrow Disbursements and Remittance VAs."
    type: time
    timeframes: [
      raw,
      date,
      day_of_month,
      day_of_year,
      week,
      month,
      quarter,
      year,
      quarter_of_year
    ]
    sql: ${TABLE}.settled ;;
  }

  dimension: papi_instrument_id {
    hidden: yes
    type: string
    sql: ${TABLE}.papi_instrument_id ;;
  }

  dimension: is_papi_transaction {
    label: "Is PAPI Transaction"
    description: "Whether this transaction passed through PAPI interface"
    type: yesno
    sql: ${papi_instrument_id} is not null ;;
  }

  dimension_group: transaction_duration {
    group_label: "Transaction Duration"
    type: duration
    intervals: [second, minute, hour]
    sql_start: ${created_raw} ;;
    sql_end: ${updated_raw} ;;
  }
  dimension: transaction_duration_ms {
    group_label: "Transaction Duration"
    group_item_label: "Milliseconds Transaction Duration"
    sql: timestampdiff(MILLISECOND,${updated_raw},${created_raw}) ;;
  }
  dimension: status {# if you update status, don't forget to update
    # 1. 28d/monthly growth metrics views under growth_metrics table
    # 2. transacting_live under businesses to make sure that it's using COMPLETED transactions only
    # 3. have_created_tpi_request, api, and invoice transaction
    label: "Status Summary"
    description: "Summarised status of the payment into Pending, Completed or Failed"
    type: string
    sql: CASE
      WHEN UPPER(${TABLE}.status) in ('COMPLETED', 'SETTLED', 'CAPTURED', 'SETTLEMENT', 'CLAIMED', 'SUCCEEDED', 'SUCCESS', 'SUCCESS_COMPLETED', 'FORCE_PAYMENT', 'SUCCESSFUL', 'SETTLING') then 'COMPLETED'
      WHEN UPPER(${TABLE}.status) in ('FAILED', 'DENY') then 'FAILED'
      WHEN UPPER(${TABLE}.status) in ('REVERSED', 'CANCEL', 'DELETED') then 'REVERSED'
      WHEN UPPER(${TABLE}.status) in ('REFUNDED', 'REFUNDING') then 'REFUNDED'
      ELSE 'PENDING' END
    ;;
    suggestions: ["COMPLETED", "FAILED", "REVERSED", "REFUNDED", "PENDING"]
  }

  dimension: integration_type {
    description: "DEPRECATED. Replaced by Interface and Transaction Source"
    hidden: yes
    type: string
    sql: ${TABLE}.integration_type ;;
  }

  dimension: interface {
    description: "The medium of transaction initiation such as API, Invoice, or Dashboard"
    type: string
    sql: ${TABLE}.integration_type ;;
    suggest_persist_for: "72 hours"
  }

  dimension: is_api_transaction {
    description: "If the transaction is via API, then yes. Otherwise(transaction is via Invoice, or Dashboard), no"
    type: yesno
    sql: ${interface} = 'API';;
  }

  dimension: is_web_or_app {
    label: "Transaction Source"
    description: "Whether the transaction is coming from mobile APP or Web(PC + Mobile Web)"
    type: string
    sql: ${TABLE}.is_web_or_app ;;
    suggestions: ["MOBILE APP", "WEB"]
  }

  dimension: is_tpi_transaction {
    label: "Is TPI Transaction"
    description: "If the transaction is via TPI, then yes. Otherwise, no"
    type: yesno
    sql: ${TABLE}.is_tpi_transaction = 'TPI';;
  }

  dimension: tpi_platform_name {
    label: "TPI Platform Name"
    description: "Which third party platform was used to generate the transaction. Null if it's not via TPI"
    type: string
    sql: ${TABLE}.tpi_platform_name ;;
  }

  dimension: currency {
    description: "The presentment currency of the transaction"
    type: string
    sql: ${TABLE}.currency ;;
    suggestions: ["IDR", "MYR", "PHP", "SGD", "USD", "THB", "VND"]
  }

  dimension: amount {
    description: "The value of the individual transaction (local currency)"
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: amount_usd {
    description: "The value of the individual transaction (USD)"
    value_format_name: "usd"
    type: number
    sql: ${amount} / ${fx_rates.rate_usd} ;;
  }
################################################
# Dimensions }
################################################

################################################
# Measures {
################################################
  measure: transaction_duration_p50 {
    group_label: "Transaction Durations"
    description: "The median transaction duration, in minutes"
    type: percentile
    percentile: 50
    value_format_name: decimal_2
    sql: ${transaction_duration_ms};;
  }

  measure: transaction_duration_p90 {
    group_label: "Transaction Durations"
    description: "The 90th percentile transaction duration, in minutes"
    type: percentile
    percentile: 90
    value_format_name: decimal_2
    sql: ${transaction_duration_ms} ;;
  }

  measure: last_transaction_created_time_in_UTC {
    description: "Last transaction created time in UTC - this doesn't do automatic conversion of timezone"
    type: date_time
    sql: max(${dt_raw}) ;;
    convert_tz: no
  }
  measure: count {
    type: count
    drill_fields: [products*, count]
  }

  measure: completed_count {
    hidden: yes
    type: count
    filters: [status: "COMPLETED, REFUNDED"]
  }

  measure: failed_count {
    hidden: yes
    type: count
    filters: [status: "FAILED"]
  }

  measure: completion_ratio {
    description: "The ratio of completed transaction count to total transaction count"
    type: number
    value_format_name: percent_2
    sql: cast(${completed_count} as double) / ${count} ;;
  }

  measure: tpv {
    label: "TPV (Local)"
    description: "Total value of payments processed (Local currency)"
    value_format_name: decimal_2
    type: sum
    sql: ${amount} ;;
    drill_fields:[products*, tpv, count]
  }

  measure: tpv_usd {
    label: "TPV (USD)"
    description: "Total value of payments processed (USD)"
    value_format_name: "usd"
    type: sum
    sql: ${amount_usd} ;;
    drill_fields: [products*, tpv_usd, count]
  }

  measure: average_amount_usd {
    label: "Average Transaction Amount (USD)"
    description: "Average of transaction amount (USD)"
    value_format_name: "usd"
    type: average
    sql: ${amount_usd} ;;
  }

  measure: p50_amount_usd {
    group_label: "Percentiles of transaction amount (USD)"
    label: "P50 (USD)"
    description: "Median of transaction amount (USD)"
    value_format_name: "usd"
    type: percentile
    percentile: 50
    sql: ${amount_usd} ;;
  }

  measure: p90_amount_usd {
    group_label: "Percentiles of transaction amount (USD)"
    label: "P90 (USD)"
    description: "90th percentile of transaction amount (USD)"
    value_format_name: "usd"
    type: percentile
    percentile: 90
    sql: ${amount_usd} ;;
  }

  measure: p95_amount_usd {
    group_label: "Percentiles of transaction amount (USD)"
    label: "P95 (USD)"
    description: "95th percentile of transaction amount (USD)"
    value_format_name: "usd"
    type: percentile
    percentile: 95
    sql: ${amount_usd} ;;
  }

  measure: p99_amount_usd {
    group_label: "Percentiles of transaction amount (USD)"
    label: "P99 (USD)"
    description: "99th percentile of transaction amount (USD)"
    value_format_name: "usd"
    type: percentile
    percentile: 99
    sql: ${amount_usd} ;;
  }

  measure: total_used_product_types {
    type: string
    sql: array_join(array_distinct(array_agg(${TABLE}.product_type)), ', ', '') ;;
  }

  measure: total_currencies_used {
    type: number
    sql: count(distinct ${TABLE}.currency) ;;
    hidden: yes
  }

  measure: total_uncategorised_product_type_count {
    type: number
    sql: count(distinct case when ${product_type} = 'Uncategorised' then reference end) ;;
    hidden: yes
  }

  measure: total_uncategorised_money_flow_count {
    type: number
    sql: count(distinct case when ${money_flow} = 'Uncategorised' then reference end) ;;
    hidden: yes
  }

  dimension_group: since_business_creation {
    hidden: yes
    type: duration
    sql_start: ${businesses.created_raw} ;;
    sql_end: ${created_raw}  ;;
  }

# -- Measures for dynamic fact table creation
  measure: first_transaction {
    hidden: yes
    sql: min(${created_raw}) ;;
  }

  measure: latest_transaction {
    hidden: yes
    sql: max(${created_raw}) ;;
  }

  measure: this_period_transactions {
    hidden: yes
    type: count
    filters: [
      created_date: "28 days ago for 28 days"
    ]
  }
  measure: prior_period_transactions {
    hidden: yes
    type: count
    filters: [
      created_date: "56 days ago for 28 days"
    ]
  }
  measure: this_and_prior_period_transactions {
    hidden: yes
    type: count
    filters: [
      created_date: "56 days ago for 56 days"
    ]
  }
  measure: first_month_count {
    hidden: yes
    type: count
    filters: [
      months_since_business_creation: "0"
    ]
  }
  measure: second_month_count {
    hidden: yes
    type: count
    filters: [
      months_since_business_creation: "1"
    ]
  }
  measure: third_month_count {
    hidden: yes
    type: count
    filters: [
      months_since_business_creation: "2"
    ]
  }
  measure: first_18_months_count {
    hidden: yes
    type: count
    filters: [
      months_since_business_creation: "<=17"
    ]
  }
  measure: first_month_tpv {
    hidden: yes
    type: sum
    sql: ${amount_usd} ;;
    filters: [
      months_since_business_creation: "0"
    ]
  }
  measure: second_month_tpv {
    hidden: yes
    type: sum
    sql: ${amount_usd} ;;
    filters: [
      months_since_business_creation: "1"
    ]
  }
  measure: third_month_tpv {
    hidden: yes
    type: sum
    sql: ${amount_usd} ;;
    filters: [
      months_since_business_creation: "2"
    ]
  }
  measure: first_18_months_tpv {
    hidden: yes
    type: sum
    sql: ${amount_usd} ;;
    filters: [
      months_since_business_creation: "<=17"
    ]
  }

  measure: used_product_type {
    hidden: yes
    type: string
    sql: array_sort(array_distinct(array_agg(${TABLE}.product_type))) ;;
    # sql: array_join(array_sort(array_distinct(array_agg(${TABLE}.product_type))), ', ', '');;
  }

  measure: used_product_subtype {
    hidden: yes
    type: string
    sql: array_sort(array_distinct(array_agg(${TABLE}.product_subtype))) ;;
    # sql: array_join(array_sort(array_distinct(array_agg(${TABLE}.product_type))), ', ', '');;
  }

  measure: used_channel_name {
    hidden: yes
    type: string
    sql: array_sort(array_distinct(array_agg(CASE WHEN ${TABLE}.channel_name = 'ID_SHOPEEPAY' THEN 'SHOPEEPAY'
                                                    WHEN ${TABLE}.product_type = 'EWALLET'
                                                    OR ${TABLE}.product_type = 'RETAIL_OUTLET'
                                                    OR ${TABLE}.product_type = 'CARDLESS_CREDIT'
                                                    OR ${TABLE}.product_type = 'QR_CODE' THEN ${TABLE}.channel_name
                                            ELSE ${TABLE}.product_type END ))) ;;
  }
################################################
# Measures }
################################################
}
