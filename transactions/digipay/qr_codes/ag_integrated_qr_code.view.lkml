view: ag_integrated_qr_code {
  sql_table_name: transform__payments.integrated_qr ;;
  suggestions: no

  dimension: api_client {
    type: string
    sql: ${TABLE}.api_client ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: interface {
    type: string
    sql: ${TABLE}.interface ;;
  }

  dimension: is_tpi_transaction {
    type: string
    sql: ${TABLE}.is_tpi_transaction ;;
  }

  dimension: is_web_or_app {
    type: string
    sql: ${TABLE}.is_web_or_app ;;
  }

  dimension: issuer_name {
    type: string
    sql: ${TABLE}.issuer_name ;;
  }

  dimension: partner_reference_id {
    type: string
    sql: ${TABLE}.partner_reference_id ;;
  }

  dimension: payment_amount {
    type: number
    sql: ${TABLE}.payment_amount ;;
  }

  dimension_group: payment_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_created ;;
  }

  dimension: payment_external_id {
    type: string
    sql: ${TABLE}.payment_external_id ;;
  }

  dimension: payment_id {
    type: string
    sql: ${TABLE}.payment_id ;;
  }

  dimension_group: payment_updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_updated ;;
  }

  dimension: primary_key {
    type: string
    sql: ${TABLE}.primary_key ;;
  }

  dimension: qr_code_type {
    type: string
    sql: ${TABLE}.qr_code_type ;;
  }

  dimension: version {
    type: string
    sql: ${TABLE}.version ;;
  }

  dimension: request_amount {
    type: number
    sql: ${TABLE}.request_amount ;;
  }

  dimension_group: request_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.request_created ;;
  }

  dimension: request_external_id {
    type: string
    sql: ${TABLE}.request_external_id ;;
  }

  dimension: request_id {
    type: string
    sql: ${TABLE}.request_id ;;
  }

  dimension_group: request_updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.request_updated ;;
  }

  dimension_group: request_valid_until {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.request_valid_until ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: tpi_platform_name {
    type: string
    sql: ${TABLE}.tpi_platform_name ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension: user_receipt_id {
    type: string
    sql: ${TABLE}.user_receipt_id ;;
  }

  dimension: single_use_QR {
    type: yesno
    sql: ${TABLE}.single_use_QR ;;
  }

  measure: count {
    type: count
    drill_fields: [channel_name, issuer_name, tpi_platform_name]
  }
}
