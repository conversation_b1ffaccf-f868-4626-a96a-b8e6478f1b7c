view: ag_fds_card_risk_assessment_light_ver {
  derived_table: {
    sql: select
        f.*,
        bin.bin_number,
        bin.bank as card_bank,
        bin.card as card_network,
        bin.country as card_bank_issuing_country,
        bin.country_code as card_bank_issuing_country_code,
        bin.level as card_level,
        bin.type as card_type,
        bin.bank_code as card_bank_code,
        rs.description as xenshield_premium_ruleset_name
      from clean__xendit_fraud_detection_service.card_risk_assessment f
      left join clean__xendit_fraud_rule_manager.fraud_profile_assessments a on f.profile_decision_id = a.id
      left join clean__xendit_fraud_rule_manager.rule_sets rs on a.triggered_ruleset = rs.id
      left join clean__xendit_fraud_static_data_provider.bin bin on (
        bin.bin_number = substr(f.masked_card_number, 1, 6)
        and bin.is_deleted = false
      )
      where f.created_at >= timestamp '2022-01-01'
      order by f.created_at desc
  ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: business_is_whitelisted {
    type: yesno
    sql: ${TABLE}.business_is_whitelisted ;;
  }

  dimension: token_ip_address {
    type: string
    sql: ${TABLE}.token_ip_address ;;
  }

  dimension: card_data_id {
    type: string
    sql: ${TABLE}.card_data_id ;;
  }

  dimension: masked_card_number {
    type: string
    sql: ${TABLE}.masked_card_number ;;
  }

  dimension: authorized_amount {
    type: number
    sql: ${TABLE}.authorized_amount ;;
  }

  dimension: custom_rule_allowed_reason {
    type: string
    sql: ${TABLE}.custom_rule_allowed_reason ;;
  }

  dimension: custom_rule_blocked_reason {
    type: string
    sql: ${TABLE}.custom_rule_blocked_reason ;;
  }

  dimension: custom_rule_is_allowed {
    type: yesno
    sql: ${TABLE}.custom_rule_is_allowed ;;
  }

  dimension: custom_rule_is_blocked {
    type: yesno
    sql: ${TABLE}.custom_rule_is_blocked ;;
  }

  dimension: custom_rule_context {
    type: string
    sql: ${TABLE}.custom_rule_context ;;
  }

  dimension: fds_decision_context {
    type: string
    sql: ${TABLE}.fds_decision_context ;;
  }

  dimension: ml_result_context {
    type: string
    sql: ${TABLE}.ml_result_context ;;
  }

  dimension: ml_risk_factor {
    type: string
    sql: ${TABLE}.ml_risk_factor ;;
  }

  dimension: risk_level {
    type: string
    sql: ${TABLE}.risk_level ;;
  }

  dimension: risk_score {
    type: number
    sql: ${TABLE}.risk_score ;;
  }

  dimension: transaction_is_allowed {
    type: yesno
    sql: ${TABLE}.transaction_is_allowed ;;
  }

  dimension: transaction_is_blocked {
    type: yesno
    sql: ${TABLE}.transaction_is_blocked ;;
  }

  dimension_group: created_at {
    type: time
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: updated_at {
    type: time
    sql: ${TABLE}.updated_at ;;
  }

  dimension: is_deleted {
    type: yesno
    sql: ${TABLE}.is_deleted ;;
  }

  dimension: authentication_id {
    type: string
    sql: ${TABLE}.authentication_id ;;
  }

  dimension_group: authentication_created {
    type: time
    sql: ${TABLE}.authentication_created ;;
  }

  dimension: authentication_eci {
    type: string
    sql: ${TABLE}.authentication_eci ;;
  }

  dimension: authentication_veres {
    type: string
    sql: ${TABLE}.authentication_veres ;;
  }

  dimension: authentication_pares {
    type: string
    sql: ${TABLE}.authentication_pares ;;
  }

  dimension: device_ip_address {
    type: string
    sql: ${TABLE}.device_ip_address ;;
  }

  dimension: device_user_agent {
    type: string
    sql: ${TABLE}.device_user_agent ;;
  }

  dimension: device_language {
    type: string
    sql: ${TABLE}.device_language ;;
  }

  dimension: device_referrer {
    type: string
    sql: ${TABLE}.device_referrer ;;
  }

  dimension: business_country_of_incorporation {
    type: string
    sql: ${TABLE}.business_country_of_incorporation ;;
  }

  dimension: business_country_of_operation {
    type: string
    sql: ${TABLE}.business_country_of_operation ;;
  }

  dimension: business_industry_sector {
    type: string
    sql: ${TABLE}.business_industry_sector ;;
  }

  dimension: business_mcc {
    type: string
    sql: ${TABLE}.business_mcc ;;
  }

  dimension: business_installment_enabled {
    type: yesno
    sql: ${TABLE}.business_installment_enabled ;;
  }

  dimension: business_merchant_id {
    type: string
    sql: ${TABLE}.business_merchant_id ;;
  }

  dimension: payment_billing_given_names {
    type: string
    sql: ${TABLE}.payment_billing_given_names ;;
  }

  dimension: payment_billing_middle_name {
    type: string
    sql: ${TABLE}.payment_billing_middle_name ;;
  }

  dimension: payment_billing_surname {
    type: string
    sql: ${TABLE}.payment_billing_surname ;;
  }

  dimension: payment_billing_email {
    type: string
    sql: ${TABLE}.payment_billing_email ;;
  }

  dimension: payment_billing_phone_number {
    type: string
    sql: ${TABLE}.payment_billing_phone_number ;;
  }

  dimension: payment_billing_address_country {
    type: string
    sql: ${TABLE}.payment_billing_address_country ;;
  }

  dimension: payment_billing_address_street_line1 {
    type: string
    sql: ${TABLE}.payment_billing_address_street_line1 ;;
  }

  dimension: payment_billing_address_street_line2 {
    type: string
    sql: ${TABLE}.payment_billing_address_street_line2 ;;
  }

  dimension: payment_billing_address_city {
    type: string
    sql: ${TABLE}.payment_billing_address_city ;;
  }

  dimension: payment_billing_address_province {
    type: string
    sql: ${TABLE}.payment_billing_address_province ;;
  }

  dimension: payment_billing_address_state {
    type: string
    sql: ${TABLE}.payment_billing_address_state ;;
  }

  dimension: payment_billing_address_postal_code {
    type: string
    sql: ${TABLE}.payment_billing_address_postal_code ;;
  }

  dimension: payment_billing_address_description {
    type: string
    sql: ${TABLE}.payment_billing_address_description ;;
  }

  dimension: payment_credit_card_token_id {
    type: string
    sql: ${TABLE}.payment_credit_card_token_id ;;
  }

  dimension: payment_card_data_id {
    type: string
    sql: ${TABLE}.payment_card_data_id ;;
  }

  dimension: payment_charge_type {
    type: string
    sql: ${TABLE}.payment_charge_type ;;
  }

  dimension: payment_masked_card_number {
    type: string
    sql: ${TABLE}.payment_masked_card_number ;;
  }

  dimension: payment_expiry_month {
    type: string
    sql: ${TABLE}.payment_expiry_month ;;
  }

  dimension: payment_expiry_year {
    type: string
    sql: ${TABLE}.payment_expiry_year ;;
  }

  dimension: payment_client_type {
    type: string
    sql: ${TABLE}.payment_client_type ;;
  }

  dimension: payment_merchant_reference_code {
    type: string
    sql: ${TABLE}.payment_merchant_reference_code ;;
  }

  dimension: order_reference_id {
    type: string
    sql: ${TABLE}.order_reference_id ;;
  }

  dimension: order_promotion_reference_id {
    type: string
    sql: ${TABLE}.order_promotion_reference_id ;;
  }

  dimension: order_promotion_original_amount {
    type: number
    sql: ${TABLE}.order_promotion_original_amount ;;
  }

  dimension: order_promotion_transaction_limit {
    type: number
    sql: ${TABLE}.order_promotion_transaction_limit ;;
  }

  dimension: order_promotion_promo_code {
    type: string
    sql: ${TABLE}.order_promotion_promo_code ;;
  }

  dimension: order_promotion_discount_amount {
    type: number
    sql: ${TABLE}.order_promotion_discount_amount ;;
  }

  dimension: order_promotion_discount_percent {
    type: number
    sql: ${TABLE}.order_promotion_discount_percent ;;
  }

  dimension: order_total_amount {
    type: number
    sql: ${TABLE}.order_total_amount ;;
  }

  dimension: order_currency {
    type: string
    sql: ${TABLE}.order_currency ;;
  }

  dimension: customer_id {
    type: string
    sql: ${TABLE}.customer_id ;;
  }

  dimension: business_is_facilitator {
    type: yesno
    sql: ${TABLE}.business_is_facilitator ;;
  }

  dimension: payment_billing_mobile_number {
    type: string
    sql: ${TABLE}.payment_billing_mobile_number ;;
  }

  dimension: order_items {
    type: string
    sql: ${TABLE}.order_items ;;
  }

  dimension: payment_acquiring_bank_name {
    type: string
    sql: ${TABLE}.payment_acquiring_bank_name ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: profile_decision_id {
    type: string
    sql: ${TABLE}.profile_decision_id ;;
  }

  dimension: bank_status {
    type: string
    sql: ${TABLE}.bank_status ;;
  }

  dimension: bank_failure_reason {
    type: string
    sql: ${TABLE}.bank_failure_reason ;;
  }

  dimension: device_ip_country {
    type: string
    sql: ${TABLE}.device_ip_country ;;
  }

  dimension: device_platform {
    type: string
    sql: ${TABLE}.device_platform ;;
  }

  dimension: card_processor {
    type: string
    sql: ${TABLE}.card_processor ;;
  }

  dimension: charge_id {
    type: string
    sql: ${TABLE}.charge_id ;;
  }

  dimension_group: dt {
    type: time
    sql: ${TABLE}.dt ;;
  }

  dimension: bin_number {
    type: string
    sql: ${TABLE}.bin_number ;;
  }

  dimension: card_bank {
    type: string
    sql: ${TABLE}.card_bank ;;
  }

  dimension: card_network {
    type: string
    sql: ${TABLE}.card_network ;;
  }

  dimension: card_bank_issuing_country {
    type: string
    sql: ${TABLE}.card_bank_issuing_country ;;
  }

  dimension: card_bank_issuing_country_code {
    type: string
    sql: ${TABLE}.card_bank_issuing_country_code ;;
  }

  dimension: card_level {
    type: string
    sql: ${TABLE}.card_level ;;
  }

  dimension: card_type {
    type: string
    sql: ${TABLE}.card_type ;;
  }

  dimension: card_bank_code {
    type: string
    sql: ${TABLE}.card_bank_code ;;
  }

  dimension: xenshield_premium_ruleset_name {
    type: string
    sql: ${TABLE}.xenshield_premium_ruleset_name ;;
  }

  set: detail {
    fields: [
      id,
      environment,
      business_id,
      business_is_whitelisted,
      token_ip_address,
      card_data_id,
      masked_card_number,
      authorized_amount,
      custom_rule_allowed_reason,
      custom_rule_blocked_reason,
      custom_rule_is_allowed,
      custom_rule_is_blocked,
      custom_rule_context,
      fds_decision_context,
      ml_result_context,
      ml_risk_factor,
      risk_level,
      risk_score,
      transaction_is_allowed,
      transaction_is_blocked,
      created_at_time,
      updated_at_time,
      is_deleted,
      authentication_id,
      authentication_created_time,
      authentication_eci,
      authentication_veres,
      authentication_pares,
      device_ip_address,
      device_user_agent,
      device_language,
      device_referrer,
      business_country_of_incorporation,
      business_country_of_operation,
      business_industry_sector,
      business_mcc,
      business_installment_enabled,
      business_merchant_id,
      payment_billing_given_names,
      payment_billing_middle_name,
      payment_billing_surname,
      payment_billing_email,
      payment_billing_phone_number,
      payment_billing_address_country,
      payment_billing_address_street_line1,
      payment_billing_address_street_line2,
      payment_billing_address_city,
      payment_billing_address_province,
      payment_billing_address_state,
      payment_billing_address_postal_code,
      payment_billing_address_description,
      payment_credit_card_token_id,
      payment_card_data_id,
      payment_charge_type,
      payment_masked_card_number,
      payment_expiry_month,
      payment_expiry_year,
      payment_client_type,
      payment_merchant_reference_code,
      order_reference_id,
      order_promotion_reference_id,
      order_promotion_original_amount,
      order_promotion_transaction_limit,
      order_promotion_promo_code,
      order_promotion_discount_amount,
      order_promotion_discount_percent,
      order_total_amount,
      order_currency,
      customer_id,
      business_is_facilitator,
      payment_billing_mobile_number,
      order_items,
      payment_acquiring_bank_name,
      metadata,
      profile_decision_id,
      bank_status,
      bank_failure_reason,
      device_ip_country,
      device_platform,
      card_processor,
      charge_id,
      dt_time,
      bin_number,
      card_bank,
      card_network,
      card_bank_issuing_country,
      card_bank_issuing_country_code,
      card_level,
      card_type,
      card_bank_code,
      xenshield_premium_ruleset_name
    ]
  }
}
