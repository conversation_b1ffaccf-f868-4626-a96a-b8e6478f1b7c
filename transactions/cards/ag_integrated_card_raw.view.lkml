view: ag_integrated_card_raw {
  derived_table: {
    sql: select
        charges.business_id AS business_id,
        tokens.id  AS token_id,
        tokens.status  AS token_status,
        tokens.created  AS token_created,
        tokens.updated  AS token_updated,
        tokens.card_expiration_month,
        tokens.card_expiration_year,
        tokens.is_single_use AS token_is_single_use,
        bin.country  AS card_country,
        bin.country_code  AS card_country_code,
        bin.type  AS card_type,
        bin.bank  AS issuing_bank,
        bin.card  AS card_brand,
        bin.bank_code  AS issuing_bank_code, -- NEW: 2023-03-28
        authentications.id  AS authentication_id,
        authentications.status  AS authentication_status,
        authorizations.id  AS authorization_id,
        authorizations.status  AS authorization_status,
        authorizations.is_high_risk_transaction  AS is_high_risk_transaction,
        authorizations.created AS authorization_created,
        authorizations.amount,
        authorizations.transaction_failure_reason,
        authorizations.mid_label  AS mid_label,
        authorizations.avs_code  AS avs_code,
        authorizations.cof_type AS cof_type, -- NEW: 2023-03-28
        case
            when authorizations.processor_response is not null then authorizations.processor_response
            when charges.processor_response is not null then charges.processor_response
            else charges.processor_response
        end as acquirer_response_code, -- NEW: 2023-03-28
        case
            when authorizations.payment_processor_response.authorization_response.reason_code is not null then authorizations.payment_processor_response.authorization_response.reason_code
            when charges.payment_processor_response.authorization_response.reason_code is not null then charges.payment_processor_response.authorization_response.reason_code
            else charges.payment_processor_response.authorization_response.reason_code
        end as cybersource_response_code, -- NEW: 2023-03-28
        authentications.created AS authentication_created,
        authentications.veres_enrolled,
        authentications.pares_status,
        charges.id  AS charge_id,
        charges.status  AS charge_status,
        charges.created  AS charge_created,
        charges.updated  AS charge_updated,
        charges.capture_amount  AS charge_amount,
        case
          when charges.created < timestamp '2019-08-01' then coalesce(charges.currency, 'IDR')
          else charges.currency
        end as charge_currency, -- NEW: 2023-02-16
        charges.charge_type AS charge_type,
        charges.approval_code  AS approval_code,
        charges.acquiring_bank_name  AS acquiring_bank_name,
        charges.credit_card_processor AS credit_card_processor,
        charges.cvn_code  AS cvn_code,
        charges.failure_reason  AS failure_reason,
        charges.eci  AS eci,
        charges.is_cvn_submitted  AS is_cvn_submitted,
        charges.is_blocked_by_fraud AS is_blocked_by_fraud,
        charges.card_data_id,
        charges.masked_card_number  AS masked_card_number,
        charges.merchant_reference_code  AS merchant_reference_code,
        charges.merchant_id  AS merchant_id,
        charges.settlement_updated  AS settlement_completed_date,
        charges.settlement_status  AS settlement_status,
        charges.authorization_request_id,
        charges.external_id,
        charges.client_type,
        charges.bank_reconciliation_id,
        charges.retrieval_reference_number,
        case
          when charges.installment.count is null then 'CC_REGULAR'
          when charges.installment.count = 3 then 'CC_INSTALLMENT_03_MO'
          when charges.installment.count = 6 then 'CC_INSTALLMENT_06_MO'
          when charges.installment.count = 9 then 'CC_INSTALLMENT_09_MO'
          when charges.installment.count = 12 then 'CC_INSTALLMENT_12_MO'
          when charges.installment.count = 15 then 'CC_INSTALLMENT_15_MO'
          when charges.installment.count = 18 then 'CC_INSTALLMENT_18_MO'
          when charges.installment.count = 24 then 'CC_INSTALLMENT_24_MO'
          when charges.installment.count = 36 then 'CC_INSTALLMENT_36_MO'
          when charges.installment.count > 0 then 'CC_INSTALLMENT_DEFAULT' -- NEW: 2023-02-16
          else 'CC_REGULAR'
        end as product_subtype, --intervals are always in months
        charges.credit_card_payment_channel,
        charges.fee_amount,
        -- refunds.id  AS refund_id, -- 1 charge may have multiple refunds
        -- refunds.created  AS refund_created,
        -- refunds.amount  AS refund_amount,
        -- sum(refunds.amount) filter(where refunds.status in ('SUCCEEDED', 'PENDING')) AS refund_amount,
        -- refunds.status  AS refund_status,
        fds.device_ip_address AS token_ip_address,
        fds.device_ip_country AS token_ip_address_country_code,
        fds.device_user_agent AS token_user_agent,
        fds.risk_level AS risk_level,
        fds.fds_decision_context,
        'CREDIT_CARD' AS product_type,
        'MONEY_IN' AS money_flow,
        settings.is_facilitator AS is_switcher,
        businesses.entity AS entity,
        case
          when invoices.client_type = 'STOREFRONT' then 'ONLINE_STORE'
          when invoices.id is not null then 'INVOICE'
          else 'API'
        end as integration_type,
        case
          when invoices.client_type = 'MOBILE' then 'MOBILE APP'
          else 'WEB'
        end as source_client_type,
        case
          when tpi.id is null then 'NO_TPI'
          else 'TPI'
        end tpi_client_type,
        tpi.platform_name,
        '' as processing_platform,
        case
          when charges.is_switcher = true then 'SWITCHER'
          else 'AGGREGATOR'
        end as commercial_model,
        '' as is_xendit_mid,
        coalesce(id_t4.settled_at, ph_t4.settled_at) as settled

      from clean__credit_card_production.creditcardcharges charges
      left join clean__credit_card_production.authorizations authorizations on charges.authorization_id = authorizations.id
      left join clean__credit_card_production.creditcardtokens tokens on tokens.id = charges.credit_card_token_id
      left join clean__credit_card_production.authenticatedcreditcardtokens as authentications on authentications.id =  charges.authentication_id
      left join clean__xendit_credit_card_settings_service.creditcardsettings as settings on settings.business_id = charges.business_id
      left join clean__xendit_fraud_detection_service.card_risk_assessment as fds on fds.id =  charges.assessment_id
      -- left join clean__credit_card_production.creditcardrefunds as refunds on refunds.credit_card_charge_id = charges.id
      left join clean__xendit_business_service.businesses as businesses on charges.business_id = businesses.id
      left join clean__xendit_fraud_static_data_provider.bin  AS bin ON (
        SUBSTRING(charges.masked_card_number, 1, 6) = bin.bin_number
        -- or SUBSTRING(charges.masked_card_number, 1, 8) = bin.bin_number
      )
      left join clean__xendit_tpi_service.invoices as tpi on charges.id = tpi.charge_id
      left join clean__xendit_invoice_service.invoices as invoices on charges.id = invoices.credit_card_charge_id
      left join clean__tcdb.transaction as id_t4 on id_t4.payment_id = charges.id
      left join clean__transaction_service_v4.transaction as ph_t4 on ph_t4.payment_id = charges.id
      WHERE authorizations.created >= current_timestamp - interval '6' month
       ;;
  }

  suggestions: no

  dimension: issuing_bank_code {
    type: string
    sql: ${TABLE}.issuing_bank_code ;;
  }

  dimension: acquirer_response_code {
    type: string
    sql: ${TABLE}.acquirer_response_code ;;
  }

  dimension: cybersource_response_code {
    type: string
    sql: ${TABLE}.cybersource_response_code ;;
  }


  dimension: acquiring_bank_name {
    type: string
    sql: ${TABLE}.acquiring_bank_name ;;
  }

  dimension: approval_code {
    type: string
    sql: ${TABLE}.approval_code ;;
  }

  dimension_group: authentication_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.authentication_created ;;
  }

  dimension: authentication_id {
    type: string
    sql: ${TABLE}.authentication_id ;;
  }

  dimension: authentication_status {
    type: string
    sql: ${TABLE}.authentication_status ;;
  }

  dimension: authorization_amount {
    type: number
    sql: ${TABLE}.authorization_amount ;;
  }

  dimension_group: authorization_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.authorization_created ;;
  }

  dimension: authorization_id {
    type: string
    sql: ${TABLE}.authorization_id ;;
  }

  dimension: authorization_request_id {
    type: string
    sql: ${TABLE}.authorization_request_id ;;
  }

  dimension: authorization_status {
    type: string
    sql: ${TABLE}.authorization_status ;;
  }

  dimension: avs_code {
    type: string
    sql: ${TABLE}.avs_code ;;
  }

  dimension: cof_type {
    type: string
    sql: ${TABLE}.cof_type ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: card_brand {
    type: string
    sql: ${TABLE}.card_brand ;;
  }

  dimension: card_country {
    type: string
    sql: ${TABLE}.card_country ;;
  }

  dimension: card_country_code {
    type: string
    sql: ${TABLE}.card_country_code ;;
  }

  dimension: card_type {
    type: string
    sql: ${TABLE}.card_type ;;
  }

  dimension: charge_amount {
    type: number
    sql: ${TABLE}.charge_amount ;;
  }

  dimension_group: charge_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.charge_created ;;
  }

  dimension: charge_currency {
    type: string
    sql: ${TABLE}.charge_currency ;;
  }

  dimension: charge_id {
    type: string
    sql: ${TABLE}.charge_id ;;
  }

  dimension: charge_status {
    type: string
    sql: ${TABLE}.charge_status ;;
  }

  dimension: charge_type {
    type: string
    sql: ${TABLE}.charge_type ;;
  }

  dimension: client_type {
    type: string
    sql: ${TABLE}.client_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  # dimension: installment_code {
  #   type: string
  #   sql: get_json_object(${TABLE}.installment, '$.code') ;;
  # }

  # dimension: installment {
  #   type: string
  #   sql: ${TABLE}.installment ;;
  # }

  dimension: cvn_code {
    type: string
    sql: ${TABLE}.cvn_code ;;
  }

  dimension: eci {
    type: string
    sql: ${TABLE}.eci ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: failure_reason {
    type: string
    sql: ${TABLE}.failure_reason ;;
  }

  dimension: is_blocked_by_fraud {
    type: yesno
    sql: ${TABLE}.is_blocked_by_fraud ;;
  }

  dimension: is_cvn_submitted {
    type: yesno
    sql: ${TABLE}.is_cvn_submitted ;;
  }

  dimension: is_high_risk_transaction {
    type: yesno
    sql: ${TABLE}.is_high_risk_transaction ;;
  }

  dimension: is_switcher {
    type: yesno
    sql: ${TABLE}.is_switcher ;;
  }

  dimension: issuing_bank {
    type: string
    sql: ${TABLE}.issuing_bank ;;
  }

  dimension: card_data_id {
    type: string
    sql: ${TABLE}.card_data_id ;;
  }

  dimension: masked_card_number {
    type: string
    sql: ${TABLE}.masked_card_number ;;
  }

  dimension: merchant_id {
    type: string
    sql: ${TABLE}.merchant_id ;;
  }

  dimension: merchant_reference_code {
    type: string
    sql: ${TABLE}.merchant_reference_code ;;
  }

  dimension: mid_label {
    type: string
    sql: ${TABLE}.mid_label ;;
  }

  dimension: pares_status {
    type: string
    sql: ${TABLE}.pares_status ;;
  }

  dimension: refund_amount {
    type: number
    sql: ${TABLE}.refund_amount ;;
  }

  dimension_group: refund_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.refund_created ;;
  }

  dimension: refund_id {
    type: string
    sql: ${TABLE}.refund_id ;;
  }

  dimension: refund_status {
    type: string
    sql: ${TABLE}.refund_status ;;
  }

  dimension_group: settlement_completed {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.settlement_completed_date ;;
  }

  dimension: settlement_status {
    type: string
    sql: ${TABLE}.settlement_status ;;
  }

  dimension_group: token_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.token_created ;;
  }

  dimension_group: token_updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.token_updated ;;
  }


  dimension: token_id {
    type: string
    sql: ${TABLE}.token_id ;;
  }

  dimension: token_is_single_use {
    type: yesno
    sql: ${TABLE}.token_is_single_use ;;
  }

  dimension: token_status {
    type: string
    sql: ${TABLE}.token_status ;;
  }

  dimension: transaction_failure_reason {
    type: string
    sql: ${TABLE}.transaction_failure_reason ;;
  }

  dimension: veres_enrolled {
    type: string
    sql: ${TABLE}.veres_enrolled ;;
  }

  dimension: is_api_transaction {
    type: yesno
    sql: ${TABLE}.external_id NOT LIKE '%invoice%';;
  }

  dimension: credit_card_payment_channel {
    type: string
    sql: ${TABLE}.credit_card_payment_channel ;;
  }

  dimension: fee_amount {
    type: number
    sql: ${TABLE}.fee_amount ;;
  }

  # dimension: fee_amount_in_usd {
  #   type: number
  #   sql:
  #   (
  #     CASE
  #     WHEN ${TABLE}.charge_currency = 'IDR' THEN ${TABLE}.fee_amount * 0.000064
  #     WHEN ${TABLE}.charge_currency = 'PHP' THEN ${TABLE}.fee_amount * 0.018
  #     END
  #   ) ;;
  # }

  dimension: token_ip_address {
    type: string
    sql: ${TABLE}.token_ip_address ;;
  }

  dimension: token_ip_address_country_code {
    type: string
    sql: ${TABLE}.token_ip_address_country_code ;;
  }

  dimension: token_user_agent {
    type: string
    sql: ${TABLE}.token_user_agent ;;
  }

  dimension: fds_decision_context {
    type: string
    sql: ${TABLE}.fds_decision_context ;;
  }

  measure: count {
    type: count
    drill_fields: [acquiring_bank_name]
  }

  measure: valid_transaction_count {
    description: "Transactions which are not REVERSED"
    type: sum
    sql: CASE WHEN  ${TABLE}.charge_status != 'REVERSED' THEN 1 ELSE 0 END;;
    drill_fields: [charge_id, acquiring_bank_name, is_api_transaction, charge_status]
    filters: [charge_id: "-NULL"]
  }

  measure: successful_transaction_count {
    type: sum
    sql: CASE WHEN ${TABLE}.charge_status = 'CAPTURED' THEN 1 ELSE 0 END;;
    drill_fields: [charge_id, acquiring_bank_name, is_api_transaction, charge_status]
    filters: [charge_id: "-NULL"]
  }

  measure: payment_success_rate {
    description: "Ratio of COMPLETED transactions to approved transactions"
    type: number
    value_format_name: percent_2
    sql: cast(${successful_transaction_count} as double)/${valid_transaction_count} ;;
  }

  measure: revenue_transaction {
    type: sum
    sql:(
      CASE
        WHEN ${TABLE}.charge_currency = 'IDR' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0)
        WHEN ${TABLE}.charge_currency = 'PHP' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.0036
        WHEN ${TABLE}.charge_currency = 'SGD' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.000086
        WHEN ${TABLE}.charge_currency = 'MYR' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.00028
        WHEN ${TABLE}.charge_currency = 'THB' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.0022
      END
    ) ;;
  }

  measure: revenue_transaction_usd {
    type: sum
    sql:(
      CASE
        WHEN ${TABLE}.charge_currency = 'IDR' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) * 0.000064 + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.000064
        WHEN ${TABLE}.charge_currency = 'PHP' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) * 0.018 + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.018
        WHEN ${TABLE}.charge_currency = 'SGD' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) * 0.75 + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.75
        WHEN ${TABLE}.charge_currency = 'MYR' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) * 0.23 + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.23
        WHEN ${TABLE}.charge_currency = 'THB' THEN (charge_amount * coalesce(sql_cards_billing_rate.cc_mdr_fee, 0)) * 0.029 + coalesce(sql_cards_billing_rate.cc_fixed_fee, 0) * 0.029
      END
    ) ;;
  }
}
