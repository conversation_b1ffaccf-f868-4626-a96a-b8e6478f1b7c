view: issue_trackers {
  sql_table_name: clean__google_sheets_card_issue_tracker.sheet1 ;;
  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # measure: avg_resolution_time {
  #   type: average
  #   sql: ${resolved_duration_time} ;;
  #   drill_fields: [detail*]
  # }

  # measure: p50_resolution_time {
  #   type: median
  #   sql: ${resolved_duration_time} ;;
  #   drill_fields: [detail*]
  # }

  dimension_group: created_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: to_timestamp(${TABLE}.created_at,  'yyyy-MM-dd HH:mm:ss.SSSSSS') ;;
  }

  dimension: created_by {
    type: string
    sql: ${TABLE}.created_by ;;
  }

  dimension: thread_message {
    type: string
    sql: ${TABLE}.thread_message ;;
  }

  dimension: thread_link {
    type: string
    sql: ${TABLE}.thread_link ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }

  dimension: business_name {
    type: string
    sql: ${TABLE}.business_name ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: current_state_note {
    type: string
    sql: ${TABLE}.current_state_note ;;
  }

  dimension_group: resolved_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: to_timestamp(${TABLE}.resolved_at, 'yyyy-MM-dd HH:mm:ss.SSSSSS') ;;
  }

  dimension_group: resolved_duration {
    type: time
    timeframes: [
      raw,
      time
    ]
    sql: to_timestamp(${TABLE}.resolved_duration, 'HH:mm:ss.SSSSSS') ;;
  }

  dimension: resolution {
    type: string
    sql: ${TABLE}.resolution ;;
  }

  dimension: root_cause_origin {
    type: string
    sql: ${TABLE}.root_cause_origin ;;
  }

  dimension: root_cause {
    type: string
    sql: ${TABLE}.root_cause ;;
  }

  dimension: how_to_avoid_note {
    type: string
    sql: ${TABLE}.how_to_avoid_note ;;
  }

  dimension: jira_link {
    type: string
    sql: ${TABLE}.jira_link ;;
  }

  # dimension: resolution_duration_hour {
  #   type: number
  #   sql: datediff(hour, cast(${created_at_date}, as date), cast(${resolved_at_date} as date)) ;;
  # }

  set: detail {
    fields: [
      created_at_raw,
      created_by,
      thread_message,
      thread_link,
      status,
      channel,
      business_name,
      business_id,
      current_state_note,
      resolved_at_raw,
      resolved_duration_time,
      resolution,
      root_cause_origin,
      root_cause,
      how_to_avoid_note,
      jira_link
    ]
  }
}
