view: ag_disbursements {
  derived_table: {
    sql_trigger_value:
      select max(tstamp)
      from
        (
          select max(createdat) as tstamp
          from clean__xendit_disbursement_service.disbursements

          union all

          select max(created) as tstamp
          from clean__disb_routing_service_idr.disbursement_route

          union all

          select max(created) as tstamp
          from clean__disbursement_service.disbursement
        );;
    sql:
        -- Queries all money out from all currencies EXCEPT IDR
        -- and all types (BATCH_DISBURSEMENTS, REMITTANCE_PAYOUT, DIRECT_DISBURSEMENT) EXCEPT of type WITHDRAWAL
        SELECT
          disbursement.id,
          disbursement.connector_reference as bank_reference,
          disbursement.type as disbursement_type,
          disbursement.entity,
          disbursement.reference_id as external_id,
          disbursement.failure_code,
          disbursement.status,
          disbursement.user_id,
          disbursement.created as createdat,
          disbursement.updated as updatedat,
          disbursement.updated as status_updated,
          disbursement.amount,
          disbursement.account_number AS bank_account_number,
          disbursement.channel_code AS bank_code,
          disbursement.description,
          null AS events,
          '' AS fulfillment_type,
          true AS is_instant,
          disbursement.account_name AS name,
          '' AS partner_id,
          '' AS processor_holder_name,
          disbursement.connector_code AS sender_bank_code,
          null as sequence,
          null as should_inhouse,
          null as should_prioritize_disbursement,
          '' AS source_account_number,
          disbursement.transaction_id as txn_id,
          currency
        FROM clean__disbursement_service.disbursement
        WHERE disbursement.type <> 'WITHDRAWAL' and currency <> 'IDR'

        UNION ALL

        -- Queries all IDR money out transaction using the legacy routing
        -- EXCEPT of type WITHDRAWAL and SETTLEMENT_DISBURSEMENT.
        SELECT
          COALESCE(rpdisb.id, COALESCE(idpdisb.id, disbursements.id)) AS id,
          disbursements.bank_reference,
          disbursement_type,
          disbursements.entity,
          COALESCE(rpdisb.reference_id, COALESCE(idpdisb.external_id, disbursements.external_id))as external_id,
          disbursements.failure_code,
          COALESCE(rpdisb.status,
            COALESCE(
              CASE WHEN idpdisb.status = 'FAILED' and idpdisb.completed_transaction_id is not null THEN 'REVERSED' ELSE idpdisb.status END,
              disbursements.status
            ))
          AS status,
          disbursements.user_id,
          --disbursements.createdat,
          COALESCE(idpdisb.created, rpdisb.created) AS createdat,
          --disbursements.updatedat,
          COALESCE(idpdisb.updated, rpdisb.updated) AS updatedat,
          disbursements.status_updated,
          disbursements.amount,
          disbursements.bank_account_number,
          disbursements.bank_code,
          disbursements.description,
          disbursements.events,
          disbursements.fulfillment_type,
          disbursements.is_instant,
          disbursements.name,
          disbursements.partner_id,
          disbursements.processor_holder_name,
          disbursements.sender_bank_code,
          disbursements.sequence,
          disbursements.should_inhouse,
          disbursements.should_prioritize_disbursement,
          disbursements.source_account_number,
          COALESCE(COALESCE(idpdisb.transaction_id, rpdisb.transaction_id), CAST(get_json_object(bd.metadata, '$["transaction_id"]') AS string)) as txn_id,
          'IDR' as currency
        FROM clean__xendit_disbursement_service.disbursements
        LEFT JOIN clean__xendit_withdrawal_service.directdisbursements as idpdisb on disbursements.external_id = idpdisb.id
        LEFT JOIN clean__disbursement_service.disbursement as rpdisb on disbursements.external_id = rpdisb.id
        LEFT JOIN clean__batch_disbursement_service.batch_disbursement_item as bditem on disbursements.external_id = bditem.disbursement_id
        LEFT JOIN clean__batch_disbursement_service.batch_disbursement as bd on bditem.batch_disbursement_id = bd.id
        WHERE (disbursements.disbursement_type IN (
          'ESCROW_VIRTUAL_ACCOUNT_PAYMENT_INTERNAL_DISBURSEMENT',
          'REMITTANCE_PAYOUT',
          'INTERNAL_LOAN_DISBURSEMENT_REFUND',
          'REMITTANCE_COLLECTION_REFUND',
          'INTERNAL_LOAN_DISBURSEMENT',
          'ESCROW_WITHDRAWAL_INTERNAL_DISBURSEMENT',
          'ESCROW_WITHDRAWAL_EXTERNAL_DISBURSEMENT',
          'REMITTANCE',
          'EXTERNAL_LOAN_DISBURSEMENT',
          'ESCROW_WITHDRAWAL_INTERNAL_DISBURSEMENT_REFUND',
          'BATCH_DISBURSEMENT',
          'ESCROW_RDL_REPAYMENT',
          'ESCROW_RDL_REFUND',
          'DIRECT_DISBURSEMENT'
        ))

        UNION ALL

        -- Queries all IDR money out transaction using the new routing
        -- EXCEPT of type WITHDRAWAL.
        SELECT
          COALESCE(rpdisb.id, COALESCE(idpdisb.id, disbursement_route.id)) AS id,
          disbursement_route.connector_reference as bank_reference,
          disbursement_type,
          disbursement_route.entity,
          COALESCE(rpdisb.reference_id, COALESCE(idpdisb.external_id, disbursement_route.reference_id))as external_id,
          COALESCE(disbursement_route.failure_code, COALESCE(idpdisb.failure_code, rpdisb.failure_code)) AS failure_code,
          COALESCE(rpdisb.status, COALESCE(idpdisb.status, disbursement_route.status)) AS status,
          disbursement_route.merchant_id as user_id,
          --disbursement_route.created as createdat,
          COALESCE(idpdisb.created, rpdisb.created) AS createdat,
          --disbursement_route.updated as updatedat,
          COALESCE(idpdisb.updated, rpdisb.updated) AS updatedat,
          disbursement_route.updated as status_updated,
          CAST(get_json_object(disbursement_snapshot, '$["amount"]') AS double) AS amount,
          CAST(get_json_object(disbursement_snapshot, '$["account_number"]') AS string) AS bank_account_number,
          CAST(get_json_object(disbursement_snapshot, '$["channel_code"]') AS string) AS bank_code,
          CAST(get_json_object(disbursement_snapshot, '$["description"]') AS string) AS description,
          null AS events,
          '' AS fulfillment_type,
          true AS is_instant,
          CAST(get_json_object(disbursement_snapshot, '$["name"]') AS string) AS name,
          '' AS partner_id,
          '' AS processor_holder_name,
          CAST(
          COALESCE(
          COALESCE(
          get_json_object(connector_snapshot, '$["sender_bank_code"]'),
          get_json_object(connector_snapshot, '$["disbursement"]["sender_bank_code"]')
          ),
          get_json_object(connector_snapshot, '$["disbursement"]["connector_code"]')
          ) AS string) AS sender_bank_code,
          null as sequence,
          null as should_inhouse,
          disbursement_route.is_priority as should_prioritize_disbursement,
          CAST(get_json_object(disbursement_snapshot, '$["source_account_number"]') AS string) AS source_account_number,
          COALESCE(idpdisb.transaction_id, rpdisb.transaction_id) as txn_id,
          'IDR' as currency
        FROM clean__disb_routing_service_idr.disbursement_route
        LEFT JOIN clean__xendit_withdrawal_service.directdisbursements as idpdisb on disbursement_route.reference_id = idpdisb.id
        LEFT JOIN clean__disbursement_service.disbursement as rpdisb on disbursement_route.reference_id = rpdisb.id
        WHERE disbursement_type <> 'WITHDRAWAL'
      ;;

  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  measure: m_amount {
    type: sum
    sql: ${amount} ;;
  }

  dimension: bank_account_number {
    type: string
    sql: ${TABLE}.bank_account_number ;;
  }

  dimension: bank_code {
    type: string
    sql: ${TABLE}.bank_code ;;
  }

  dimension: bank_reference {
    type: string
    sql: ${TABLE}.bank_reference ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: disbursement_type {
    description: "Type of Disbursement (e.g. WITHDRAWAL, BATCH_DISBURSEMENT, REMITTANCE_PAYOUT)"
    type: string
    sql: ${TABLE}.disbursement_type ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: events {
    type: string
    sql: ${TABLE}.events ;;
  }

  dimension: external_id_from_disbursement_service {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: fulfillment_type {
    type: string
    sql: ${TABLE}.fulfillment_type ;;
  }

  dimension: is_instant {
    type: yesno
    sql: ${TABLE}.is_instant ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: partner_id {
    type: string
    sql: ${TABLE}.partner_id ;;
  }

  dimension: processor_holder_name {
    type: string
    sql: ${TABLE}.processor_holder_name ;;
  }

  dimension: sender_bank_code {
    type: string
    sql: ${TABLE}.sender_bank_code ;;
  }

  dimension: sequence {
    type: number
    sql: ${TABLE}.sequence ;;
  }

  dimension: should_inhouse {
    type: yesno
    sql: ${TABLE}.should_inhouse ;;
  }

  dimension: should_prioritize_disbursement {
    type: yesno
    sql: ${TABLE}.should_prioritize_disbursement ;;
  }

  dimension: source_account_number {
    type: string
    sql: ${TABLE}.source_account_number ;;
  }

  dimension: source_bank_code {
    type: string
    sql: ${TABLE}.source_bank_code ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      day_of_month
    ]
    sql: ${TABLE}.createdat ;;
    convert_tz: no
  }

  dimension_group: status_updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      day_of_month
    ]
    sql: ${TABLE}.status_updated ;;
    convert_tz: no
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      day_of_month
    ]
    sql: ${TABLE}.updatedat ;;
    convert_tz: no
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: v {
    type: number
    sql: ${TABLE}.v ;;
  }

  dimension: txn_id {
    type: string
    sql: ${TABLE}.txn_id ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }


  measure: count {
    type: count
    drill_fields: [id, name, processor_holder_name]
  }

  measure: successful_transaction_count {
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' THEN 1 ELSE 0 END;;
    drill_fields: [id, name, processor_holder_name]
#     hidden: yes
  }

  measure: tpv {
    label: "TPV (Local)"
    description: "Total value of payments processed (Local currency)"
    type: sum
    sql: ${amount} ;;
    drill_fields: [id, external_id_from_disbursement_service]
    filters: [id: "-NULL"]
  }

  dimension: amount_usd {
    description: "The value of the individual transaction (USD)"
    value_format_name: "usd"
    type: number
    sql: ${amount} /  ${fx_rates.rate_usd} ;;
  }

  measure: tpv_usd {
    label: "TPV (USD)"
    description: "Total value of payments processed (USD)"
    value_format_name: "usd"
    type: sum
    sql: ${amount_usd} ;;
    drill_fields: [businesses.business_id, businesses.internal_name, businesses.business_name]
  }

  measure: payment_success_rate {
    description: "Ratio of COMPLETED transactions to total transactions"
    type: number
    value_format_name: percent_2
    sql: cast(${successful_transaction_count} as double)/${count} ;;
  }

  dimension_group: response_duration {
    description: "From payment creation to status update"
    type: duration
    intervals: [second, minute, hour]
    sql_start: ${created_raw} ;;
    sql_end: ${status_updated_raw};;
  }
}
