view: ag_batchdisbursementitems {
  fields_hidden_by_default: yes

  derived_table: {
    sql: select
            bdi.*,
            coalesce(bdi.connector_code, d.sender_bank_code) as sender_bank_code
        from clean__batch_disbursement_service.batch_disbursement_item as bdi
          LEFT JOIN clean__xendit_disbursement_service.disbursements d on bdi.disbursement_id = d.id ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: account_number {
    type: string
    sql: ${TABLE}.account_number ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: beneficiary {
    type: string
    sql: ${TABLE}.beneficiary ;;
  }

  dimension: receipt_notification {
    type: string
    sql: ${TABLE}.receipt_notification ;;
  }

  dimension: batch_disbursement_id {
    type: string
    sql: ${TABLE}.batch_disbursement_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: ledger_account_id {
    type: string
    sql: ${TABLE}.ledger_account_id ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension: need_attention_fields {
    type: string
    sql: ${TABLE}.need_attention_fields ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension_group: deleted {
    type: time
    sql: ${TABLE}.deleted ;;
  }

  dimension: fee_amount {
    type: number
    sql: ${TABLE}.fee_amount ;;
  }

  dimension: vat_amount {
    type: number
    sql: ${TABLE}.vat_amount ;;
  }

  dimension: connector_code {
    type: string
    sql: ${TABLE}.connector_code ;;
  }

  dimension: connector_reference {
    type: string
    sql: ${TABLE}.connector_reference ;;
  }

  dimension: user_inputs {
    type: string
    sql: ${TABLE}.user_inputs ;;
  }

  dimension: iluma_nv_request_id {
    type: string
    sql: ${TABLE}.iluma_nv_request_id ;;
  }

  dimension: nv_retry_count {
    type: number
    sql: ${TABLE}.nv_retry_count ;;
  }

  dimension: valid_name {
    type: string
    sql: ${TABLE}.valid_name ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension_group: dt {
    type: time
    sql: ${TABLE}.dt ;;
  }

  dimension: sender_bank_code {
    type: string
    sql: ${TABLE}.sender_bank_code ;;
  }

  dimension: account_holder_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: bank_code {
    type: string
    sql: json_extract_scalar(metadata, '$.bank_code') ;;
  }

  dimension: bank_reference {
    type: string
    sql: json_extract_scalar(metadata, '$.bank_reference') ;;
  }

  dimension: failure_message {
    type: string
    sql: json_extract_scalar(metadata, '$.failure_message') ;;
  }

  dimension: fees_paid_amount {
    type: string
    sql: json_extract_scalar(metadata, '$.fees_paid_amount') ;;
  }

  dimension: vat_paid_amount {
    type: string
    sql: json_extract_scalar(metadata, '$.vat_paid_amount') ;;
  }

  measure: count {
    description: "Total transaction count"
    type: count
    drill_fields: [detail*]
  }

  measure: approved_transaction_count {
    description: "Transactions whose status is not PENDING, DELETED, OR NEEDS_NAME_CONFIRMATION"
    type: sum
    sql: CASE WHEN  ${TABLE}.status NOT IN ('PENDING', 'DELETED', 'NEEDS_NAME_CONFIRMATION') THEN 1 ELSE 0 END;;
    drill_fields: [detail*]
  }

  measure: successful_transaction_count {
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' THEN 1 ELSE 0 END;;
    drill_fields: [detail*]
#     hidden: yes
  }

  measure: payment_success_rate {
    description: "Ratio of COMPLETED transactions to approved transactions"
    type: number
    value_format_name: percent_2
    sql: cast(${successful_transaction_count} as double)/${count} ;;
  }

  dimension_group: payment_duration {
    description: "Time taken from batch disbursement approval to payment completion"
    type: duration
    intervals: [second, minute, hour]
    sql_start: ${ag_batchdisbursements.approved_raw}  ;;
    sql_end: ${updated_raw};;
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
      id,
      account_number,
      account_holder_name,
      amount,
      channel_code,
      currency,
      entity,
      description,
      beneficiary,
      receipt_notification,
      batch_disbursement_id,
      status,
      business_id,
      ledger_account_id,
      reference_id,
      transaction_id,
      need_attention_fields,
      disbursement_id,
      failure_code,
      created_time,
      updated_time,
      deleted_time,
      fee_amount,
      vat_amount,
      connector_code,
      connector_reference,
      user_inputs,
      iluma_nv_request_id,
      nv_retry_count,
      valid_name,
      metadata,
      dt_time,
      sender_bank_code
    ]
  }
}
