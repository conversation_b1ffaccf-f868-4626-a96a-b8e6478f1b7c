view: ag_payouts_partners {
  derived_table: {
    sql:
      SELECT
        currency,
        connector_code
      FROM clean__disb_routing_service_idr.route_config
      GROUP BY 1, 2
      UNION ALL
      SELECT
        currency,
        connector_code
      FROM clean__disb_routing_service_php.route_config
      GROUP BY 1, 2
      UNION ALL
      SELECT
         currency,
         connector_code
       FROM clean__disb_routing_service_myr.route_config
       GROUP BY 1, 2
      UNION ALL
      SELECT
        currency,
        connector_code
      FROM clean__disb_routing_service_vnd.route_config
      GROUP BY 1, 2
      UNION ALL
      SELECT
        currency,
        connector_code
      FROM clean__disb_routing_service_thb.route_config
      GROUP BY 1, 2
      UNION ALL
      SELECT
        currency,
        connector_code
      FROM clean__disb_routing_service_sgd.route_config
      GROUP BY 1, 2
      ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: connector_code{
    type: string
    sql: ${TABLE}.connector_code ;;
  }
}
