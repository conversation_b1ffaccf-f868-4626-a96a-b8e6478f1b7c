view: ag_loan_disbursements {
  sql_table_name: clean__xendit_escrow_service_live.loan_disbursements ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: admin_amount {
    type: number
    sql: ${TABLE}.admin_amount ;;
  }

  dimension: admin_id {
    type: string
    sql: ${TABLE}.admin_id ;;
  }

  dimension: admin_transaction_id {
    type: string
    sql: ${TABLE}.admin_transaction_id ;;
  }

  dimension: annual_percentage_rate {
    type: number
    sql: ${TABLE}.annual_percentage_rate ;;
  }

  dimension: borrower_id {
    type: string
    sql: ${TABLE}.borrower_id ;;
  }

  dimension: business_entity {
    type: string
    sql: ${TABLE}.business_entity ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: completed_transaction_id {
    type: string
    sql: ${TABLE}.completed_transaction_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: destination_account_code {
    type: string
    sql: ${TABLE}.destination_account_code ;;
  }

  dimension: destination_account_holder_name {
    type: string
    sql: ${TABLE}.destination_account_holder_name ;;
  }

  dimension: destination_account_number {
    type: string
    sql: ${TABLE}.destination_account_number ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: external_disbursement_bank_reference {
    type: string
    sql: ${TABLE}.external_disbursement_bank_reference ;;
  }

  dimension: external_disbursement_failure_code {
    type: string
    sql: ${TABLE}.external_disbursement_failure_code ;;
  }

  dimension: external_disbursement_id {
    type: string
    sql: ${TABLE}.external_disbursement_id ;;
  }

  dimension: external_disbursement_processor_holder_name {
    type: string
    sql: ${TABLE}.external_disbursement_processor_holder_name ;;
  }

  dimension: external_disbursement_sender_bank_code {
    type: string
    sql: ${TABLE}.external_disbursement_sender_bank_code ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: internal_disbursement_bank_reference {
    type: string
    sql: ${TABLE}.internal_disbursement_bank_reference ;;
  }

  dimension: internal_disbursement_failure_code {
    type: string
    sql: ${TABLE}.internal_disbursement_failure_code ;;
  }

  dimension: internal_disbursement_id {
    type: string
    sql: ${TABLE}.internal_disbursement_id ;;
  }

  dimension: internal_disbursement_processor_holder_name {
    type: string
    sql: ${TABLE}.internal_disbursement_processor_holder_name ;;
  }

  dimension: internal_disbursement_sender_bank_code {
    type: string
    sql: ${TABLE}.internal_disbursement_sender_bank_code ;;
  }

  dimension: internal_loan_id {
    type: string
    sql: ${TABLE}.internal_loan_id ;;
  }

  dimension: is_direct {
    type: yesno
    sql: ${TABLE}.is_direct ;;
  }

  dimension: loan_id {
    type: string
    sql: ${TABLE}.loan_id ;;
  }

  dimension_group: loan_maturity {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.loan_maturity_date ;;
  }

  dimension: principal_amount {
    type: number
    sql: ${TABLE}.principal_amount ;;
  }

  dimension: refund_disbursement_bank_reference {
    type: string
    sql: ${TABLE}.refund_disbursement_bank_reference ;;
  }

  dimension: refund_disbursement_failure_code {
    type: string
    sql: ${TABLE}.refund_disbursement_failure_code ;;
  }

  dimension: refund_disbursement_id {
    type: string
    sql: ${TABLE}.refund_disbursement_id ;;
  }

  dimension: refund_disbursement_processor_holder_name {
    type: string
    sql: ${TABLE}.refund_disbursement_processor_holder_name ;;
  }

  dimension: refund_disbursement_sender_bank_code {
    type: string
    sql: ${TABLE}.refund_disbursement_sender_bank_code ;;
  }

  dimension: refunded_transaction_id {
    type: string
    sql: ${TABLE}.refunded_transaction_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
      id,
      destination_account_holder_name,
      internal_disbursement_processor_holder_name,
      external_disbursement_processor_holder_name,
      refund_disbursement_processor_holder_name,
      lender_loan_disbursements.count
    ]
  }
}
