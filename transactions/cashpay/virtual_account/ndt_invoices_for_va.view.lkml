include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
include: "/transactions/invoices/ag_invoices.view.lkml"

view: ext_invoices_for_va {
  extends: [ag_invoices]
  dimension: transaction_id {sql:'no transaction_id';;}
  dimension: api_or_invoice {sql:'Invoice';;}
  dimension: product_subtype {sql: CASE wHEN ext_invoices_for_va.is_switching = true then 'VA_FIXED'
                                        ELSE 'VA_NONFIXED'
                                   END;;}
  filter: date_partition {
    type: date
    convert_tz: no
    sql: {% condition date_partition %} ${TABLE}.dt {% endcondition %} ;;
  }
}

explore: ext_invoices_for_va {
  hidden: yes

  join: fx_rates {
    relationship: many_to_one
    sql_on: case when {{fx_rates.use_current_year_fx._parameter_value}} then year(CURRENT_DATE) else ${ext_invoices_for_va.created_year} end = ${fx_rates.year}
    and ${ext_invoices_for_va.currency} = ${fx_rates.currency} ;;
  }
}

view: ndt_invoices_for_va {
  derived_table: {
    explore_source: ext_invoices_for_va{
      column: id {}
      column: amount {field:ext_invoices_for_va.paid_amount}
      column: bank_code {}
      column: account_number {field:ext_invoices_for_va.payment_destination}
      column: currency {}
      column: description {}
      column: external_id {}
      column: fees_paid_amount {}
      column: status {}
#       derived_column: transaction_id_yesno {sql:'no transaction_id';;} cannot use here, because puts columns out of order for union,have to define in view
      column: transaction_id {}
      column: paid_at {field:ext_invoices_for_va.paid_raw}
      column: updated {field:ext_invoices_for_va.updated_raw}
      column: user_id {}
      column: vat_paid_amount {}
#       derived_column: api_or_invoice {sql:'Invoice';;}cannot use here, because puts columns out of order for union,have to define in view
#       derived_column: product_subtype {sql:'VA_FIXED';;}cannot use here, because puts columns out of order for union,have to define in view
      column: is_switching {}
      column: api_or_invoice  {}
      column: product_subtype  {}
      filters: [ext_invoices_for_va.bank_code: "-NULL"]
    }
  }

  dimension: id {}

  }
