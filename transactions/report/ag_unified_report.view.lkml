# The name of this view in Looker is "Report"
view: ag_unified_report {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__dev_merchant_financial_experience.report ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Business ID" in Explore.

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: completed {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.completed_time ;;
  }

  dimension: config {
    type: string
    sql: ${TABLE}.config ;;
  }

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  dimension_group: end {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.end_date ;;
  }

  dimension: error_message {
    type: string
    sql: ${TABLE}.error_message ;;
  }

  dimension: failure_reason {
    type: string
    sql: ${TABLE}.failure_reason ;;
  }

  dimension: file_url {
    type: string
    sql: ${TABLE}.file_url ;;
  }

  dimension: format {
    type: string
    sql: ${TABLE}.format ;;
  }

  dimension: ledger_account_ids {
    type: string
    sql: ${TABLE}.ledger_account_ids ;;
  }

  dimension: number_of_rows {
    type: number
    sql: ${TABLE}.number_of_rows ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_number_of_rows {
    type: sum
    sql: ${number_of_rows} ;;  }
  measure: average_number_of_rows {
    type: average
    sql: ${number_of_rows} ;;  }

  dimension: time_taken_minute {
    type: number
    sql: ${TABLE}.time_taken_ms/60000 ;;
  }

  dimension_group: request {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.request_time ;;
  }

  dimension_group: start {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.start_date ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: time_taken_ms {
    type: number
    sql: ${TABLE}.time_taken_ms ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }

  dimension: utc_time_offset {
    type: number
    sql: ${TABLE}.utc_time_offset ;;
  }
  measure: count {
    type: count
    drill_fields: [id]
  }
}
