
view: overdraft_ledger_daily_count {
  derived_table: {
    sql: select date_trunc('DAY',ll.created) as date, t.currency, t.type, ll.subtype, count(*) as ledger_line_count
      from clean__tcdb.ledger_line ll
      inner join clean__tcdb.ledger_account la
      on ll.ledger_account_id = la.id
      inner join clean__tcdb.transaction t
      on ll.transaction_id = t.id
      where la.type = 'BUSINESS_OVERDRAFT_LOAN'
      and ll.created > timestamp '2023-10-01'
      and ll.type = 'DEBIT'
      group by date_trunc('DAY',ll.created),t.currency, t.type, ll.subtype
      union
      select date_trunc('DAY',ll.created) as date, t.currency, t.type, ll.subtype, count(*) as ledger_line_count
      from clean__transaction_service_v4.ledger_line ll
      inner join clean__transaction_service_v4.ledger_account la
      on ll.ledger_account_id = la.id
      inner join clean__transaction_service_v4.transaction t
      on ll.transaction_id = t.id
      where la.type = 'BUSINESS_OVERDRAFT_LOAN'
      and ll.created > timestamp '2023-10-01'
      and ll.type = 'DEBIT'
      group by date_trunc('DAY',ll.created),t.currency, t.type, ll.subtype ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: date {
    type: time
    sql: ${TABLE}."date" ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: subtype {
    type: string
    sql: ${TABLE}.subtype ;;
  }

  dimension: ledger_line_count {
    type: number
    sql: ${TABLE}.ledger_line_count ;;
  }

  set: detail {
    fields: [
        date_time,
  currency,
  type,
  subtype,
  ledger_line_count
    ]
  }
}
