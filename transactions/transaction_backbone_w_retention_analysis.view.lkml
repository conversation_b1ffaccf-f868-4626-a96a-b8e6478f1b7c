include: "./transaction_backbone.view.lkml"

view: +transaction_backbone {
  # Refinement with retention analysis fields
  dimension: how_many_periods_ago_28d_window {
    hidden: yes
    type: number
    sql: (date_diff('day', date(${TABLE}.created + interval '7' hour), CURRENT_DATE) - 1)/28 ;;
  }

  dimension: week_truncation_in_jkt_time {
    hidden: yes
    type: date
    sql: date_trunc('week', ${TABLE}.created + interval '7' hour);;
  }

  dimension: month_truncation_in_jkt_time {
    hidden: yes
    type: date
    sql: date_trunc('month', ${TABLE}.created + interval '7' hour);;
  }

  dimension: quarter_truncation_in_jkt_time {
    hidden: yes
    type: date
    sql: date_trunc('quarter', ${TABLE}.created + interval '7' hour);;
  }
}
