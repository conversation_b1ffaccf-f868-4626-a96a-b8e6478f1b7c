- dashboard: metrics_fx
  title: "[Metrics] FX Consolidated Dashboard - Databricks"
  layout: newspaper
  preferred_viewer: dashboards-next
  tile_size: 100

  filters:

  elements:
    - name: e08dfb15-4233-474b-97f5-04be1beebbc6
      title: FX - Booking - TTP - Databricks
      model: xendit-databricks
      explore: metrics_booking
      type: looker_line
      fields: [metrics_booking.product_creation_timestamp_month,
        metrics_booking.average_time_to_process_customer_request]
      filters:
        metrics_booking.product_creation_timestamp_month: 6 months
      sorts: [metrics_booking.product_creation_timestamp_month desc]
      column_limit: 50
      x_axis_gridlines: true
      y_axis_gridlines: true
      show_view_names: false
      show_y_axis_labels: true
      show_y_axis_ticks: true
      y_axis_tick_density: default
      y_axis_tick_density_custom: 5
      show_x_axis_label: true
      show_x_axis_ticks: true
      y_axis_scale_mode: linear
      x_axis_reversed: false
      y_axis_reversed: false
      plot_size_by_field: false
      trellis: ''
      stacking: ''
      limit_displayed_rows: false
      legend_position: center
      point_style: none
      show_value_labels: false
      label_density: 25
      x_axis_scale: auto
      y_axis_combined: true
      show_null_points: true
      interpolation: linear
      defaults_version: 1
      width: 20
    - name: 2b0033d2-49f8-455a-9962-696f8ca5b4e8
      title: FX - Settlement - TTP - Databricks
      model: xendit-databricks
      explore: metrics_settlement
      type: looker_line
      fields: [metrics_settlement.product_creation_timestamp_month,
        metrics_settlement.average_time_to_process_customer_request]
      filters:
        metrics_settlement.product_creation_timestamp_month: 6 months
        metrics_settlement.status: SUCCEEDED
      sorts: [metrics_settlement.product_creation_timestamp_month desc]
      column_limit: 50
      x_axis_gridlines: true
      y_axis_gridlines: true
      show_view_names: false
      show_y_axis_labels: true
      show_y_axis_ticks: true
      y_axis_tick_density: default
      y_axis_tick_density_custom: 5
      show_x_axis_label: true
      show_x_axis_ticks: true
      y_axis_scale_mode: linear
      x_axis_reversed: false
      y_axis_reversed: false
      plot_size_by_field: false
      trellis: ''
      stacking: ''
      limit_displayed_rows: false
      legend_position: center
      point_style: none
      show_value_labels: false
      label_density: 25
      x_axis_scale: auto
      y_axis_combined: true
      show_null_points: true
      interpolation: linear
      defaults_version: 1
      width: 20
    - name: 4498916e-62c2-4dc3-86c1-0046d8fe29f6
      title: FX - Trade - TTP - Databricks
      model: xendit-databricks
      explore: metrics_trade
      type: looker_line
      fields: [metrics_trade.product_creation_timestamp_month,
        metrics_trade.average_time_to_process_customer_request]
      filters:
        metrics_trade.product_creation_timestamp_month: 6 months
        metrics_trade.status: SUCCEEDED
      sorts: [metrics_settlement.product_creation_timestamp_month desc]
      column_limit: 50
      x_axis_gridlines: true
      y_axis_gridlines: true
      show_view_names: false
      show_y_axis_labels: true
      show_y_axis_ticks: true
      y_axis_tick_density: default
      y_axis_tick_density_custom: 5
      show_x_axis_label: true
      show_x_axis_ticks: true
      y_axis_scale_mode: linear
      x_axis_reversed: false
      y_axis_reversed: false
      plot_size_by_field: false
      trellis: ''
      stacking: ''
      limit_displayed_rows: false
      legend_position: center
      point_style: none
      show_value_labels: false
      label_density: 25
      x_axis_scale: auto
      y_axis_combined: true
      show_null_points: true
      interpolation: linear
      defaults_version: 1
      width: 20
