view: scheduled_interviews {
  label: "Scheduled Interview"
  sql_table_name: clean__greenhouse.scheduled_interviews ;;

  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
    primary_key: yes
    hidden: yes
  }

  dimension: application_id {
    type: number
    sql: ${TABLE}.application_id ;;
    hidden: yes
  }

  dimension: interview_id {
    type: number
    sql: ${TABLE}.interview_id ;;
    hidden: yes
  }

  dimension: status {
    sql: ${TABLE}.status ;;
  }

  dimension_group: scheduled_at {
    type: time
    timeframes: [date, week, month, year]
    sql: ${TABLE}.scheduled_at ;;
  }

  dimension_group: starts_at {
    type: time
    timeframes: [time, date, month, year]
    sql: ${TABLE}.starts_at ;;
  }

  dimension_group: ends_at {
    type: time
    timeframes: [time, date, month, year]
    sql: ${TABLE}.ends_at ;;
  }

  dimension: minutes {
    type: number
    sql: datediff(MINUTE, ${TABLE}.starts_at, ${TABLE}.ends_at) ;;
  }

  dimension: stage_name {
    sql: ${TABLE}.stage_name ;;
  }

  measure: total_hour {
    type: sum
    sql: ${minutes}/60.0;;
  }
}
