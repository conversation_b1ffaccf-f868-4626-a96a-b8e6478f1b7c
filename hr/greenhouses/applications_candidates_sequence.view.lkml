view: applications_candidates_sequence {
  derived_table: {
    sql: SELECT
      id as application_id,
      ROW_NUMBER() OVER (
        PARTITION BY candidate_id ORDER BY created_at DESC
      ) AS candidate_sequences
      FROM ${applications.SQL_TABLE_NAME} ;;
  }

  dimension: application_id {
    type: number
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.application_id ;;
  }

  dimension: candidate_sequences {
    type: number
    hidden: yes
    sql: ${TABLE}.candidate_sequences ;;
  }
}
