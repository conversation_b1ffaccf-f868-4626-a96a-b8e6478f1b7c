include: "/events/ag_views/unstruct_login.view"

view: user_login_activity {
#   # Or, you could make this view a derived table, like this:
derived_table: {
  sql: SELECT
        t2.user_id,
        t2.business_id,
        max(cast(t1.root_tstamp as timestamp)) as last_login_timestamp,
        count(case when datediff(day, cast(t1.root_tstamp as timestamp), current_timestamp) <= 7 then t1.user_id else null end) as past_7day_logins,
        count(case when datediff(day, cast(t1.root_tstamp as timestamp), current_timestamp) <= 14 then t1.user_id else null end) as past_14day_logins,
        count(case when datediff(day, cast(t1.root_tstamp as timestamp), current_timestamp) <= 30 then t1.user_id else null end) as past_30day_logins,
        count(t1.user_id) as lifetime_logins
      FROM
      clean__xendit_user_service.userbusinesses t2
      left join ${ag_login_events.SQL_TABLE_NAME} t1 on t1.user_id = t2.user_id
      group by 1, 2
      ;;
}

# Define your dimensions and measures here, like this:
dimension: user_id {
  description: "Logged in user's ID from user service"
  type: string
  sql: ${TABLE}.user_id ;;
}

dimension: business_id {
  description: "Logged in user's business ID"
  type: string
  sql: ${TABLE}.business_id ;;
}


dimension_group: last_login_timestamp {
  description: "The date when the user last logged in"
  type: time
  timeframes: [
    raw,
    date,
    week,
    month,
    quarter,
    year
  ]
  sql: ${TABLE}.last_login_timestamp ;;
}

dimension: past_7day_logins {
  group_label: "Login Activity"
  label: "Past 7 day logins"
  description: "Number of times the user logged in, in the past 7 days"
  type: number
  sql: ${TABLE}.past_7day_logins ;;
}

dimension: past_14day_logins {
  group_label: "Login Activity"
  label: "Past 14 day logins"
  description: "Number of times the user logged in, in the past 14 days"
  type: number
  sql: ${TABLE}.past_14day_logins ;;
}

dimension: past_30day_logins {
  group_label: "Login Activity"
  label: "Past 30 day logins"
  description: "Number of times the user logged in, in the past 30 days"
  type: number
  sql: ${TABLE}.past_30day_logins ;;
}

dimension: lifetime_logins {
  group_label: "Login Activity"
  description: "Number of times the user logged in since tracking began (Dec, 2021)"
  type: number
  sql: ${TABLE}.lifetime_logins ;;
}

}
