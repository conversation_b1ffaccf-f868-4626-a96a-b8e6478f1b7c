view: aft_first_txn_30day_ui_activity {
  derived_table: {
    sql: with user_mapping as (
        select distinct
          domain_sessionid,
          business_id
        from
          transform__events.web_user_mapping
      )

      ,transactions as (
      select
      business_id,
      first_transaction_tstamp as first_transaction
      from
      ${transacting_live.SQL_TABLE_NAME}
      where
      first_transaction_tstamp IS NOT NULL
      )

      ,activity as (
      select
      page_url_path,
      session_id,
      page_view_start
      from
      transform__events.page_views
      where
      app_id = 'xendit-websites'
      and page_url_host like '%dashboard.xendit%'
      )

      select
      t2.business_id,
      count(distinct case when t1.page_url_path like '%invoices%' then t1.session_id end) as first_30day_invoice_sessions,
      count(distinct t1.session_id) as first_30day_total_sessions
      from
      activity t1 inner join user_mapping t2 on t1.session_id = t2.domain_sessionid
      inner join transactions t3 on t2.business_id = t3.business_id
      where
      t1.page_view_start >= t3.first_transaction
      and t1.page_view_start <= dateadd(day, 30, t3.first_transaction)
      group by 1
      ;;
    datagroup_trigger: global__start_of_day_jkt
  }

  suggestions: no

  # measure: count {
  #   type: count
  #   drill_fields: [detail*]
  # }

  dimension: business_id {
    type: string
    hidden: yes
    sql: ${TABLE}.business_id ;;
  }

  # dimension: first_30day_invoice_sessions {
  #   type: number
  #   description: "Sessions with Invoice page visits within the first 30 days after the first transaction"
  #   sql: ${TABLE}.first_30day_invoice_sessions ;;
  # }

  # dimension: first_30day_total_sessions {
  #   type: number
  #   description: "Total sessions within the first 30 days after the first transaction"
  #   sql: ${TABLE}.first_30day_total_sessions ;;
  # }

  measure: first_30day_invoice_sessions {
    view_label: "Post First Transaction Web Behavior"
    description: "Shows the total number of sessions with visits to the invoice page, i.e. /invoices on Dashboard"
    label: "F30D - Total Invoice sessions"
    type: sum
    sql: ${TABLE}.first_30day_invoice_sessions ;;
  }

  measure: first_30day_avg_invoice_sessions {
    view_label: "Post First Transaction Web Behavior"
    description: "Shows the average number of sessions with visits to the invoice page, i.e. /invoices on Dashboard"
    label: "F30D - Average Invoice sessions"
    type: average
    sql: ${TABLE}.first_30day_invoice_sessions ;;
  }

  measure: first_30day_total_sessions {
    view_label: "Post First Transaction Web Behavior"
    description: "Shows the total number of sessions on Dashboard in the 30 days after first transaction"
    label: "F30D - Total sessions"
    type: sum
    sql: ${TABLE}.first_30day_total_sessions ;;
  }

  measure: first_30day_avg_sessions {
    view_label: "Post First Transaction Web Behavior"
    description: "Shows the average number of sessions on Dashboard in the 30 days after first transaction"
    label: "F30D - Average # of sessions"
    type: sum
    sql: ${TABLE}.first_30day_total_sessions ;;
  }

  measure: first_30day_ui_return_activity {
    type: count_distinct
    view_label: "Post First Transaction Web Behavior"
    label: "F30D - # of returning merchants"
    description: "Number of merchants with Dashboard activity in first 30 days after the first transaction"
    sql: case when ${TABLE}.first_30day_total_sessions > 0 then ${business_id} else null end ;;
  }

  measure: invoice_affinity {
    type: number
    value_format: "0.00\%"
    view_label: "Post First Transaction Web Behavior"
    description: "Proportion of sessions in first 30 days after first transction with Invoice Activity"
    sql: 100.00 * ${first_30day_invoice_sessions} / NULLIF(${first_30day_total_sessions}, 0) ;;
  }

  set: detail {
    fields: [business_id, first_30day_invoice_sessions, first_30day_total_sessions]
  }
}
