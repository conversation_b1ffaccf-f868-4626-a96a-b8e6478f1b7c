view: ag_businesssettings {
  sql_table_name: clean__xendit_business_service.businesssettings ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

#   dimension: account_holder_name {
#     type: string
#     sql: ${TABLE}.account_holder_name ;;
#   }

#   dimension: allow_forex {
#     type: yesno
#     sql: ${TABLE}.allow_forex ;;
#   }

#   dimension: api_version {
#     type: string
#     sql: ${TABLE}.api_version ;;
#   }

#   dimension: auto_withdrawal_bank_account_id {
#     type: string
#     sql: ${TABLE}.auto_withdrawal_bank_account_id ;;
#   }
#
#   dimension: auto_withdrawal_frequency {
#     type: string
#     sql: ${TABLE}.auto_withdrawal_frequency ;;
#   }
#
#   dimension: auto_withdrawal_minimum_balance {
#     type: number
#     sql: ${TABLE}.auto_withdrawal_minimum_balance ;;
#   }

  dimension: ban_disbursements {
    type: yesno
    sql: ${TABLE}.ban_disbursements ;;
    group_label: "Business Restrictions"
  }

#   dimension: bank_account_number {
#     type: string
#     sql: ${TABLE}.bank_account_number ;;
#   }
#
#   dimension: bank_code {
#     type: string
#     sql: ${TABLE}.bank_code ;;
#   }

#   dimension: batch_disbursement_email_footer {
#     type: string
#     sql: ${TABLE}.batch_disbursement_email_footer ;;
#   }
#
#   dimension: batch_disbursement_support_email {
#     type: string
#     sql: ${TABLE}.batch_disbursement_support_email ;;
#   }
#
#   dimension: billing_payment_term {
#     type: number
#     sql: ${TABLE}.billing_payment_term ;;
#   }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
    group_label: "Business Details"
  }

  dimension: business_legal_address {
    type: string
    sql: ${TABLE}.business_legal_address ;;
    group_label: "Business Details"
  }

  dimension: business_legal_name {
    type: string
    sql: ${TABLE}.business_legal_name ;;
    group_label: "Business Details"
  }

#   dimension: callback_notification_email_recipients {
#     type: string
#     sql: ${TABLE}.callback_notification_email_recipients ;;
#   }
#
#   dimension: callback_virtual_account_banks {
#     type: string
#     sql: ${TABLE}.callback_virtual_account_banks ;;
#   }
#
#   dimension: callback_virtual_account_prefix {
#     type: string
#     sql: ${TABLE}.callback_virtual_account_prefix ;;
#   }
#
#   dimension: callback_virtual_account_settlement_types {
#     type: string
#     sql: ${TABLE}.callback_virtual_account_settlement_types ;;
#   }
#
#   dimension: can_use_international_va {
#     type: yesno
#     sql: ${TABLE}.can_use_international_va ;;
#   }

  dimension: can_view_disbursement {
    type: yesno
    sql: ${TABLE}.can_view_disbursement ;;
    group_label: "Business Restrictions"
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

#   dimension: credit_card_development_api_key {
#     type: string
#     sql: ${TABLE}.credit_card_development_api_key ;;
#   }
#
#   dimension: credit_card_development_merchant_id {
#     type: string
#     sql: ${TABLE}.credit_card_development_merchant_id ;;
#   }
#
#   dimension: credit_card_development_processor {
#     type: string
#     sql: ${TABLE}.credit_card_development_processor ;;
#   }
#
#   dimension: credit_card_development_shared_secret {
#     type: string
#     sql: ${TABLE}.credit_card_development_shared_secret ;;
#   }
#
#   dimension: credit_card_fds_is_whitelisted {
#     type: yesno
#     sql: ${TABLE}.credit_card_fds_is_whitelisted ;;
#   }
#
#   dimension: credit_card_live_api_key {
#     type: string
#     sql: ${TABLE}.credit_card_live_api_key ;;
#   }
#
#   dimension: credit_card_live_merchant_id {
#     type: string
#     sql: ${TABLE}.credit_card_live_merchant_id ;;
#   }
#
#   dimension: credit_card_live_processor {
#     type: string
#     sql: ${TABLE}.credit_card_live_processor ;;
#   }
#
#   dimension: credit_card_live_shared_secret {
#     type: string
#     sql: ${TABLE}.credit_card_live_shared_secret ;;
#   }
#
#   dimension: credit_card_payment_channel {
#     type: string
#     sql: ${TABLE}.credit_card_payment_channel ;;
#   }
#
#   dimension: deleted_invoice_banks_as_requested {
#     type: string
#     sql: ${TABLE}.deleted_invoice_banks_as_requested ;;
#   }
#
#   dimension: development_callback_virtual_account_banks {
#     type: string
#     sql: ${TABLE}.development_callback_virtual_account_banks ;;
#   }
#
#   dimension: development_fixed_payment_codes {
#     type: string
#     sql: ${TABLE}.development_fixed_payment_codes ;;
#   }
#
#   dimension: development_invoice_banks {
#     type: string
#     sql: ${TABLE}.development_invoice_banks ;;
#   }
#
#   dimension: development_invoice_ewallets {
#     type: string
#     sql: ${TABLE}.development_invoice_ewallets ;;
#   }
#
#   dimension: development_invoice_retail_outlets {
#     type: string
#     sql: ${TABLE}.development_invoice_retail_outlets ;;
#   }
#
#   dimension: disabled_callback_virtual_account_banks {
#     type: string
#     sql: ${TABLE}.disabled_callback_virtual_account_banks ;;
#   }
#
#   dimension: disabled_virtual_accounts {
#     type: string
#     sql: ${TABLE}.disabled_virtual_accounts ;;
#   }
#
#   dimension: disbursement_description {
#     type: string
#     sql: ${TABLE}.disbursement_description ;;
#   }
#
#   dimension: disbursement_fulfillment_type {
#     type: string
#     sql: ${TABLE}.disbursement_fulfillment_type ;;
#   }

#   dimension: disbursement_xendit_fee_amount {
#     type: number
#     sql: ${TABLE}.disbursement_xendit_fee_amount ;;
#   }
#
#   dimension: email_recipients {
#     type: string
#     sql: ${TABLE}.email_recipients ;;
#   }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
    group_label: "Business Details"
  }

#   dimension: finance_emails {
#     type: string
#     sql: ${TABLE}.finance_emails ;;
#   }
#
#   dimension: fixed_credit_card_fees {
#     type: number
#     sql: ${TABLE}.fixed_credit_card_fees ;;
#   }
#
#   dimension: fixed_payment_codes {
#     type: string
#     sql: ${TABLE}.fixed_payment_codes ;;
#   }

#   dimension: invoice_banks {
#     type: string
#     sql: ${TABLE}.invoice_banks ;;
#   }
#
#   dimension: invoice_banks_exclude {
#     type: string
#     sql: ${TABLE}.invoice_banks_exclude ;;
#   }
#
#   dimension: invoice_banks_excluded {
#     type: string
#     sql: ${TABLE}.invoice_banks_excluded ;;
#   }
#
#   dimension: invoice_ewallets {
#     type: string
#     sql: ${TABLE}.invoice_ewallets ;;
#   }
#
#   dimension: invoice_retail_outlets {
#     type: string
#     sql: ${TABLE}.invoice_retail_outlets ;;
#   }
#
#   dimension: invoice_ttl {
#     type: number
#     sql: ${TABLE}.invoice_ttl ;;
#   }

#   dimension: is_english_language_in_invoice {
#     type: yesno
#     sql: ${TABLE}.is_english_language_in_invoice ;;
#   }
#
#   dimension: is_high {
#     type: yesno
#     sql: ${TABLE}.is_high ;;
#   }

  dimension: is_xen_platform {
    type: yesno
    sql: ${TABLE}.is_xen_platform ;;
    group_label: "Business Details"
  }

#   dimension: last_click_gclid {
#     type: string
#     sql: ${TABLE}.last_click_gclid ;;
#   }

  dimension: merchant_name {
    type: string
    sql: ${TABLE}.merchant_name ;;
    group_label: "Business Details"
  }

#   dimension: merchant_profile_picture_url {
#     type: string
#     sql: ${TABLE}.merchant_profile_picture_url ;;
#   }
#
#   dimension: multiple_use_token_in_invoice {
#     type: yesno
#     sql: ${TABLE}.multiple_use_token_in_invoice ;;
#   }
#
#   dimension: old_callback_virtual_account_banks {
#     type: string
#     sql: ${TABLE}.old_callback_virtual_account_banks ;;
#   }
#
#   dimension: old_invoice_banks {
#     type: string
#     sql: ${TABLE}.old_invoice_banks ;;
#   }
#
#   dimension: old_invoice_ttl {
#     type: number
#     sql: ${TABLE}.old_invoice_ttl ;;
#   }
#
#   dimension: old_production_bank_account_data_callback_url {
#     type: string
#     sql: ${TABLE}.old_production_bank_account_data_callback_url ;;
#   }
#
#   dimension: old_production_batch_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.old_production_batch_disbursement_callback_url ;;
#   }
#
#   dimension: old_should_authenticate_credit_card {
#     type: yesno
#     sql: ${TABLE}.old_should_authenticate_credit_card ;;
#   }
#
#   dimension: old_staging_invoice_status_callback_url {
#     type: string
#     sql: ${TABLE}.old_staging_invoice_status_callback_url ;;
#   }

#   dimension: percentage_credit_card_fees {
#     type: number
#     sql: ${TABLE}.percentage_credit_card_fees ;;
#   }

#   dimension: production_bank_account_data_callback_url {
#     type: string
#     sql: ${TABLE}.production_bank_account_data_callback_url ;;
#   }
#
#   dimension: production_batch_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.production_batch_disbursement_callback_url ;;
#   }
#
#   dimension: production_callback_virtual_account_paid_callback_url {
#     type: string
#     sql: ${TABLE}.production_callback_virtual_account_paid_callback_url ;;
#   }
#
#   dimension: production_callback_virtual_account_status_callback_url {
#     type: string
#     sql: ${TABLE}.production_callback_virtual_account_status_callback_url ;;
#   }
#
#   dimension: production_card_payment_callback_url {
#     type: string
#     sql: ${TABLE}.production_card_payment_callback_url ;;
#   }
#
#   dimension: production_card_payment_validation_callback_url {
#     type: string
#     sql: ${TABLE}.production_card_payment_validation_callback_url ;;
#   }
#
#   dimension: production_credit_card_refund_callback_url {
#     type: string
#     sql: ${TABLE}.production_credit_card_refund_callback_url ;;
#   }
#
#   dimension: production_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.production_disbursement_callback_url ;;
#   }
#
#   dimension: production_escrow_callback_virtual_account_paid_callback_url {
#     type: string
#     sql: ${TABLE}.production_escrow_callback_virtual_account_paid_callback_url ;;
#   }
#
#   dimension: production_escrow_callback_virtual_account_status_callback_url {
#     type: string
#     sql: ${TABLE}.production_escrow_callback_virtual_account_status_callback_url ;;
#   }
#
#   dimension: production_escrow_virtual_account_activated_callback_url {
#     type: string
#     sql: ${TABLE}.production_escrow_virtual_account_activated_callback_url ;;
#   }
#
#   dimension: production_escrow_virtual_account_payment_status_updated_callback_url {
#     type: string
#     sql: ${TABLE}.production_escrow_virtual_account_payment_status_updated_callback_url ;;
#   }
#
#   dimension: production_escrow_withdrawal_callback_url {
#     type: string
#     sql: ${TABLE}.production_escrow_withdrawal_callback_url ;;
#   }
#
#   dimension: production_fixed_payment_code_callback_url {
#     type: string
#     sql: ${TABLE}.production_fixed_payment_code_callback_url ;;
#   }
#
#   dimension: production_forex_conversion_callback_url {
#     type: string
#     sql: ${TABLE}.production_forex_conversion_callback_url ;;
#   }
#
#   dimension: production_invoice_status_callback_url {
#     type: string
#     sql: ${TABLE}.production_invoice_status_callback_url ;;
#   }
#
#   dimension: production_loan_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.production_loan_disbursement_callback_url ;;
#   }
#
#   dimension: production_npwp_data_callback_url {
#     type: string
#     sql: ${TABLE}.production_npwp_data_callback_url ;;
#   }
#
#   dimension: production_rdl_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.production_rdl_disbursement_callback_url ;;
#   }
#
#   dimension: production_rdl_investor_activated_callback_url {
#     type: string
#     sql: ${TABLE}.production_rdl_investor_activated_callback_url ;;
#   }
#
#   dimension: production_rdl_payment_callback_url {
#     type: string
#     sql: ${TABLE}.production_rdl_payment_callback_url ;;
#   }
#
#   dimension: production_recurring_payment_callback_url {
#     type: string
#     sql: ${TABLE}.production_recurring_payment_callback_url ;;
#   }
#
#   dimension: production_remittance_callback_url {
#     type: string
#     sql: ${TABLE}.production_remittance_callback_url ;;
#   }
#
#   dimension: production_repayment_callback_url {
#     type: string
#     sql: ${TABLE}.production_repayment_callback_url ;;
#   }
#
#   dimension: production_switcher_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.production_switcher_disbursement_callback_url ;;
#   }
#
#   dimension: production_xen_platform_callback_url {
#     type: string
#     sql: ${TABLE}.production_xen_platform_callback_url ;;
#   }

#   dimension: send_invoice_expired_email_to_merchant {
#     type: yesno
#     sql: ${TABLE}.send_invoice_expired_email_to_merchant ;;
#   }
#
#   dimension: send_invoice_paid_email_to_merchant {
#     type: yesno
#     sql: ${TABLE}.send_invoice_paid_email_to_merchant ;;
#   }

#   dimension: settlement_days {
#     type: number
#     sql: ${TABLE}.settlement_days ;;
#   }

#   dimension: should_allow_auto_withdrawal {
#     type: yesno
#     sql: ${TABLE}.should_allow_auto_withdrawal ;;
#   }
#
#   dimension: should_allow_forex {
#     type: yesno
#     sql: ${TABLE}.should_allow_forex ;;
#   }
#
#   dimension: should_authenticate_credit_card {
#     type: yesno
#     sql: ${TABLE}.should_authenticate_credit_card ;;
#   }
#
#   dimension: should_automatically_disburse_daily {
#     type: yesno
#     sql: ${TABLE}.should_automatically_disburse_daily ;;
#   }
#
#   dimension: should_exclude_credit_card_fee {
#     type: yesno
#     sql: ${TABLE}.should_exclude_credit_card_fee ;;
#   }
#
#   dimension: should_exclude_credit_card_in_invoice {
#     type: yesno
#     sql: ${TABLE}.should_exclude_credit_card_in_invoice ;;
#   }
#
#   dimension: should_exclude_xendit_credit_card_fees {
#     type: yesno
#     sql: ${TABLE}.should_exclude_xendit_credit_card_fees ;;
#   }
#
#   dimension: should_prioritize_disbursement {
#     type: yesno
#     sql: ${TABLE}.should_prioritize_disbursement ;;
#   }
#
#   dimension: should_send_callback_notification_email {
#     type: yesno
#     sql: ${TABLE}.should_send_callback_notification_email ;;
#   }
#
#   dimension: should_settle_directly {
#     type: string
#     sql: ${TABLE}.should_settle_directly ;;
#   }
#
#   dimension: should_use_new_create_va_flow {
#     type: yesno
#     sql: ${TABLE}.should_use_new_create_va_flow ;;
#   }
#
#   dimension: should_use_new_top_up_flow {
#     type: yesno
#     sql: ${TABLE}.should_use_new_top_up_flow ;;
#   }
#
#   dimension: staging_bank_account_data_callback_url {
#     type: string
#     sql: ${TABLE}.staging_bank_account_data_callback_url ;;
#   }
#
#   dimension: staging_batch_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.staging_batch_disbursement_callback_url ;;
#   }
#
#   dimension: staging_callback_virtual_account_paid_callback_url {
#     type: string
#     sql: ${TABLE}.staging_callback_virtual_account_paid_callback_url ;;
#   }
#
#   dimension: staging_callback_virtual_account_status_callback_url {
#     type: string
#     sql: ${TABLE}.staging_callback_virtual_account_status_callback_url ;;
#   }
#
#   dimension: staging_card_payment_callback_url {
#     type: string
#     sql: ${TABLE}.staging_card_payment_callback_url ;;
#   }
#
#   dimension: staging_credit_card_refund_callback_url {
#     type: string
#     sql: ${TABLE}.staging_credit_card_refund_callback_url ;;
#   }
#
#   dimension: staging_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.staging_disbursement_callback_url ;;
#   }
#
#   dimension: staging_email_recipients {
#     type: string
#     sql: ${TABLE}.staging_email_recipients ;;
#   }
#
#   dimension: staging_escrow_callback_virtual_account_paid_callback_url {
#     type: string
#     sql: ${TABLE}.staging_escrow_callback_virtual_account_paid_callback_url ;;
#   }
#
#   dimension: staging_escrow_callback_virtual_account_status_callback_url {
#     type: string
#     sql: ${TABLE}.staging_escrow_callback_virtual_account_status_callback_url ;;
#   }
#
#   dimension: staging_escrow_virtual_account_activated_callback_url {
#     type: string
#     sql: ${TABLE}.staging_escrow_virtual_account_activated_callback_url ;;
#   }
#
#   dimension: staging_escrow_virtual_account_payment_status_updated_callback_url {
#     type: string
#     sql: ${TABLE}.staging_escrow_virtual_account_payment_status_updated_callback_url ;;
#   }
#
#   dimension: staging_escrow_withdrawal_callback_url {
#     type: string
#     sql: ${TABLE}.staging_escrow_withdrawal_callback_url ;;
#   }
#
#   dimension: staging_fixed_payment_code_callback_url {
#     type: string
#     sql: ${TABLE}.staging_fixed_payment_code_callback_url ;;
#   }
#
#   dimension: staging_forex_conversion_callback_url {
#     type: string
#     sql: ${TABLE}.staging_forex_conversion_callback_url ;;
#   }
#
#   dimension: staging_invoice_status_callback_url {
#     type: string
#     sql: ${TABLE}.staging_invoice_status_callback_url ;;
#   }
#
#   dimension: staging_loan_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.staging_loan_disbursement_callback_url ;;
#   }
#
#   dimension: staging_npwp_data_callback_url {
#     type: string
#     sql: ${TABLE}.staging_npwp_data_callback_url ;;
#   }
#
#   dimension: staging_rdl_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.staging_rdl_disbursement_callback_url ;;
#   }
#
#   dimension: staging_rdl_investor_activated_callback_url {
#     type: string
#     sql: ${TABLE}.staging_rdl_investor_activated_callback_url ;;
#   }
#
#   dimension: staging_rdl_payment_callback_url {
#     type: string
#     sql: ${TABLE}.staging_rdl_payment_callback_url ;;
#   }
#
#   dimension: staging_recurring_payment_callback_url {
#     type: string
#     sql: ${TABLE}.staging_recurring_payment_callback_url ;;
#   }
#
#   dimension: staging_remittance_callback_url {
#     type: string
#     sql: ${TABLE}.staging_remittance_callback_url ;;
#   }
#
#   dimension: staging_repayment_callback_url {
#     type: string
#     sql: ${TABLE}.staging_repayment_callback_url ;;
#   }
#
#   dimension: staging_switcher_disbursement_callback_url {
#     type: string
#     sql: ${TABLE}.staging_switcher_disbursement_callback_url ;;
#   }
#
#   dimension: supported_card_brands {
#     type: string
#     sql: ${TABLE}.supported_card_brands ;;
#   }
#
#   dimension: threshold_amount {
#     type: number
#     sql: ${TABLE}.threshold_amount ;;
#   }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

#   dimension: v {
#     type: number
#     sql: ${TABLE}.v ;;
#   }
#
  dimension: whitelisted_ips {
    type: string
    sql: ${TABLE}.whitelisted_ips ;;
    group_label: "Business Restrictions"
  }

#   dimension: withdrawal_type {
#     type: string
#     sql: ${TABLE}.withdrawal_type ;;
#   }

#   dimension: xendit_business_id {
#     type: string
#     sql: ${TABLE}.xendit_business_id ;;
#   }
#
#   dimension: xendit_fee_percentage {
#     type: number
#     sql: ${TABLE}.xendit_fee_percentage ;;
#   }

  measure: count {
    type: count
    drill_fields: [id, business_legal_name, merchant_name]
  }
}
