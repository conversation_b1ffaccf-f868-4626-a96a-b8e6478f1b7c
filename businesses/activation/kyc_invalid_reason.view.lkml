view: kyc_invalid_reason {
  derived_table: {
    sql:   WITH invalid_reason AS (
    SELECT DISTINCT
      l.id,
      get_json_object(l.response, '$.id') AS common_company_detail_id,
      CASE
          WHEN get_json_object(l.body, '$.kyc_verification_status') = 'INVALID'
          THEN doc.master_document.multi_language_label.en.value
          ELSE NULL
      END AS document_name,
      CASE
          WHEN get_json_object(l.body, '$.kyc_verification_status') = 'INVALID'
          THEN doc.reason
          ELSE NULL
      END AS invalid_reason
  FROM clean__xendit_admin_dashboard.logs l
  LATERAL VIEW explode(
      from_json(
        get_json_object(l.body, '$.common_company_detail_documents'),
        'array<struct<
            common_company_detail_id:bigint,
            created_at:string,
            file_address:string,
            file_name:string,
            file_status:string,
            file_url:string,
            id:bigint,
            master_document:struct<
              code:string,
              country_id:bigint,
              created_at:string,
              document_name:string,
              document_type:string,
              id:bigint,
              multi_language_label:struct<
                en:struct<
                  additional_value:string,
                  created_at:string,
                  id:bigint,
                  language_code:string,
                  notes:string,
                  reference_id:bigint,
                  table_owner:string,
                  updated_at:string,
                  value:string
                >,
                id:struct<
                  additional_value:string,
                  created_at:string,
                  id:bigint,
                  language_code:string,
                  notes:string,
                  reference_id:bigint,
                  table_owner:string,
                  updated_at:string,
                  value:string
                >
              >,
              timestamp:string,
              updated_at:string
            >,
            master_document_id:bigint,
            reason:string,
            reasonLabel:struct<
              en:string,
              id:string
            >,
            rejected_reason_label:struct<
              en:string,
              id:string
            >,
            updated_at:string,
            verification_type:string
          >>'
      )
  ) exploded_docs AS doc
  WHERE l.action = 'UPDATE_SME_ONBOARDING_INFORMATION'
    AND get_json_object(l.response, '$.error_code') IS NULL
    AND get_json_object(l.body, '$.kyc_verification_status') = 'INVALID'
    )

    SELECT
    common_company_detail_id,
    document_name,
    invalid_reason
    FROM invalid_reason

       ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: compound_primary_key {
    primary_key: yes
    hidden: yes
    type: string
    sql: concat(cast(${TABLE}.common_company_detail_id as string), ' ', ${TABLE}.document_name) ;;
  }

  dimension: common_company_detail_id {
    hidden: yes
    type: number
    sql: ${TABLE}.common_company_detail_id ;;
  }

  dimension: document_name {
    type: string
    sql: ${TABLE}.document_name ;;
  }

  dimension: invalid_reason {
    type: string
    sql: ${TABLE}.invalid_reason ;;
  }

  set: detail {
    fields: [
      common_company_detail_id,
      document_name,
      invalid_reason
    ]
  }
}
