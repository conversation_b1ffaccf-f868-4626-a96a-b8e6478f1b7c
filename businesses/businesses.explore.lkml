#@TODO:Migration check uniqueness of sales_attribution. PK is not business ID for 1 row
#@TODO:Migration check billing rates are correct given introduction of revenue clarity
#@TODO:Migration include tests into this file
#@TODO:Migration rename explores to use base table names and re-reference content

include: "//central-models-dbr/businesses/businesses.explore"
include: "/dashboard/im_brand_consolidation.view"
include: "/salesforce/ndt_salesforce_account.view"
include: "/finance/billing_rates.view"
include: "/growth/sales_attribution.view"
include: "/marketing/business_id_marketing_source.view.lkml"

explore: +businesses {
  description: "Businesses list from Admin Dashboard and its details (xendit databricks)"
  join: billing_rates {
    type: left_outer
    sql_on: ${businesses.business_id} = ${billing_rates.business_id} ;;
    relationship: one_to_one
  }

  join: sales_attribution {
    view_label: "Sales Attribution"
    sql_on: ${businesses.business_id} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: one_to_one
  }

  join : ndt_salesforce_account {
    view_label: "Salesforce"
    type: left_outer
    sql_on: ${businesses.business_id} = ${ndt_salesforce_account.business_id} ;;
    relationship: many_to_one
  }

  join: business_id_marketing_source {
    from: business_id_marketing_source
    view_label: "Marketing Source by Business ID"
    sql_on: ${businesses.business_id} = ${business_id_marketing_source.business_id__c};;
    relationship: one_to_one
    type :  left_outer
  }
}

explore: +users {
  join: brand_consolidation_migrations {
    view_label: "IM Brand Consolidation Migrations"
    type: left_outer
    sql_on: ${businesses.business_id} = ${brand_consolidation_migrations.business_id} ;;
    relationship: one_to_one
  }
}
