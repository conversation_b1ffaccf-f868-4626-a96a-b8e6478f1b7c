view: integrated_topup {
  derived_table: {
    sql:
      select
      id,
      country,
      currency,
      created,
      updated,
      triggered,
      merchant_id,
      amount,
      product_type,
      product_sub_type,
      money_flow,
      topup_type,
      status,
      transaction_id,
      topup_identifier,
      transacting_entity,
      topup_channel,
      account_number
      from transform__topups_withdrawals.integrated_topup
      UNION ALL
      select
      id,
      SUBSTRING(currency, 1, 2) AS country,
      currency,
      created,
      updated,
      created AS triggered,
      business_id AS merchant_id,
      amount,
      'TOPUP' AS product_type,
      'TOPUP' AS product_sub_type,
      'MONEY_IN' AS money_flow,
      'DIRECT_TOPUP' AS topup_type,
      status,
      transaction_id,
      '' AS topup_identifier,
      '' AS transacting_entity,
      'N/A' AS topup_channel,
      'N/A' AS account_number
      from clean__finops_ledger_service.finops_ledger where action = 'TOP_UP'
      ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: account_number {
    type: string
    sql: ${TABLE}.account_number ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      day_of_month,
      month_name
    ]
    convert_tz: yes
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: merchant_id {
    type: string
    sql: ${TABLE}.merchant_id ;;
  }

  dimension: money_flow {
    type: string
    sql: ${TABLE}.money_flow ;;
  }

  dimension: product_sub_type {
    type: string
    sql: ${TABLE}.product_sub_type ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: topup_channel {
    type: string
    sql: ${TABLE}.topup_channel ;;
  }

  dimension: topup_identifier {
    type: string
    sql: ${TABLE}.topup_identifier ;;
  }

  dimension: topup_type {
    type: string
    sql: ${TABLE}.topup_type ;;
  }

  dimension: transacting_entity {
    type: string
    sql: ${TABLE}.transacting_entity ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: triggered {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: yes
    sql: ${TABLE}.triggered ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: yes
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }

  # Custom

  dimension_group: response_duration {
    description: "From triggered callback from bank until topup"
    type: duration
    intervals: [second, minute, hour]
    sql_start: ${triggered_raw} ;;
    sql_end: ${updated_raw};;
  }

  measure: successful_topup_count {
    description: "Success topup within 5 minutes and exclude manual topup"
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' and ${TABLE}.topup_type != 'DIRECT_TOPUP' and ${minutes_response_duration} <= 5 THEN 1 ELSE 0 END;;
    drill_fields: [id, topup_type , topup_channel, amount, currency]
  }

  dimension: amount_usd {
    description: "The value of the individual transaction (USD)"
    value_format_name: "usd"
    type: number
    sql: ${amount} /  ${fx_rates.rate_usd} ;;
  }

  measure: topup_success_rate {
    description: "Ratio of COMPLETED transactions to total transactions"
    type: number
    value_format_name: percent_2
    drill_fields: [successful_topup_count, count]
    sql: cast(${successful_topup_count} as double)/${count} ;;
  }

}
