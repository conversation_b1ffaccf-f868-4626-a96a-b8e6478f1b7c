view: integrated_withdrawal {
  sql_table_name: transform__topups_withdrawals.integrated_withdrawal ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: account_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension: account_number {
    type: string
    sql: ${TABLE}.account_number ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      day_of_month,
      month_name
    ]
    convert_tz: yes
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: integration_type {
    type: string
    sql: ${TABLE}.integration_type ;;
  }

  dimension: merchant_id {
    type: string
    sql: ${TABLE}.merchant_id ;;
  }

  dimension: money_flow {
    type: string
    sql: ${TABLE}.money_flow ;;
  }

  dimension: product_sub_type {
    type: string
    sql: ${TABLE}.product_sub_type ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension_group: requested {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: yes
    sql: ${TABLE}.requested ;;
  }

  dimension: routing_id {
    type: string
    sql: ${TABLE}.routing_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: transacting_entity {
    type: string
    sql: ${TABLE}.transacting_entity ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: yes
    sql: ${TABLE}.updated ;;
  }

  dimension: withdrawal_type {
    type: string
    sql: ${TABLE}.withdrawal_type ;;
  }

  measure: count {
    type: count
    drill_fields: [id, account_name]
  }

  ## Custom

  measure: on_demand_withdrawal_count {
    type: count_distinct
    sql: ${id} ;;
    filters: [withdrawal_type: "CUSTOM_WITHDRAWAL"]
  }

  measure: auto_withdrawal_count {
    type: count_distinct
    sql: ${id} ;;
    filters: [withdrawal_type: "AUTO_WITHDRAWAL"]
  }

  dimension_group: response_duration {
    description: "From triggered callback from bank until topup"
    type: duration
    intervals: [second, minute, hour]
    sql_start: ${requested_raw} ;;
    sql_end: ${updated_raw};;
  }

  measure: successful_withdrawal_count {
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' and ${minutes_response_duration} <= 15 THEN 1 ELSE 0 END;;
    drill_fields: [id, merchant_id, withdrawal_type, channel_code, amount]
  }

  measure: successful_on_demand_withdrawal_count {
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' and ${TABLE}.withdrawal_type = 'CUSTOM_WITHDRAWAL' and ${minutes_response_duration} <= 15 THEN 1 ELSE 0 END;;
    drill_fields: [id, merchant_id, withdrawal_type, channel_code, amount]
  }

  measure: successful_auto_withdrawal_count {
    type: sum
    sql: CASE WHEN ${TABLE}.status = 'COMPLETED' and ${TABLE}.withdrawal_type = 'AUTO_WITHDRAWAL' and ${minutes_response_duration} <= 15 THEN 1 ELSE 0 END;;
    drill_fields: [id, merchant_id, withdrawal_type, channel_code, amount]
  }

  dimension: amount_usd {
    description: "The value of the individual transaction (USD)"
    value_format_name: "usd"
    type: number
    sql: ${amount} / ${fx_rates.rate_usd} ;;
  }

  measure: withdrawal_success_rate {
    description: "Ratio of COMPLETED transactions to total transactions"
    type: number
    value_format_name: percent_2
    drill_fields: [successful_withdrawal_count, count]
    sql: cast(${successful_withdrawal_count} as double)/${count} ;;
  }

  measure: on_demand_withdrawal_success_rate {
    description: "Ratio of on demand withdrawal COMPLETED transactions to total transactions"
    type: number
    value_format_name: percent_2
    drill_fields: [successful_on_demand_withdrawal_count, on_demand_withdrawal_count]
    sql: cast(${successful_on_demand_withdrawal_count} as double)/${on_demand_withdrawal_count} ;;
  }

  measure: auto_withdrawal_success_rate {
    description: "Ratio of auto withdrawal COMPLETED transactions to total transactions"
    type: number
    value_format_name: percent_2
    drill_fields: [successful_auto_withdrawal_count, auto_withdrawal_count]
    sql: cast(${successful_auto_withdrawal_count} as double)/${auto_withdrawal_count} ;;
  }

}
