include: "//central-models-dbr/0_assets/fx_rates.view"
include: "//central-models-dbr/businesses/*.view"
include: "/topups_withdrawals/*.view.lkml"

explore: integrated_withdrawals {
  group_label: "PG Transactions"
  description: "Withdrawals transactions"
  view_label: "Topups & Withdrawals"
  from: integrated_withdrawal
  case_sensitive: no

  always_filter: {
    filters: [businesses.is_internal: "no", businesses.is_soft_deleted_account: "no"]
  }

  join: fx_rates {
    relationship: many_to_one
    sql_on: case when {{fx_rates.use_current_year_fx._parameter_value}} then year(CURRENT_DATE) else ${integrated_withdrawals.created_year} end = ${fx_rates.year}
    and ${integrated_withdrawals.currency} = ${fx_rates.currency} ;;
  }

  join: businesses {
    sql_on: ${businesses.business_id} = ${integrated_withdrawals.merchant_id} ;;
    relationship: many_to_one
    type :  inner
  }
  join: business_marketing_attribution {
    view_label: "Businesses - Marketing Attribution"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_marketing_attribution.business_id};;
    relationship: one_to_one
  }
  join: xp_businesses {
    type: full_outer
    view_label: "xenPlatform Master Account"
    sql_on: ${businesses.master_account_id} = ${xp_businesses.business_id} ;;
    relationship: many_to_one
  }
  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }
}
