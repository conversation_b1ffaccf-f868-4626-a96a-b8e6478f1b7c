view: useractiontimerecords {
  derived_table: {
    sql:
    SELECT *
    FROM clean__xendit_admin_dashboard.useractiontimerecords
    WHERE action = 'KYC_ONBOARDING_MANAGEMENT'
    AND end_action IN ('OVERRIDDEN','CLOSE_MODAL')
    AND email NOT IN ('ken<PERSON>@xendit.co','<EMAIL>','ar<PERSON><EMAIL>','and<PERSON><PERSON>@xendit.co','<EMAIL>','lan-b<PERSON><EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>')
    AND duration < 600000
    ;;
  }
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    hidden: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: action {
    hidden: yes
    type: string
    sql: ${TABLE}.action ;;
  }

  dimension: business_id {
    hidden:  yes
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  # dimension_group: dt {
  #   type: time
  #   timeframes: [
  #     raw,
  #     date,
  #     week,
  #     month,
  #     quarter,
  #     year
  #   ]
  #   convert_tz: no
  #   datatype: date
  #   sql: ${TABLE}.dt ;;
  # }

  dimension: duration {
    type: number
    sql: ${TABLE}."duration"/1000 ;;
    value_format: "0 \"seconds\""
  }

  dimension: agent_email {
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: end_action {
    hidden: yes
    type: string
    sql: ${TABLE}.end_action ;;
  }

  # dimension_group: end_timestamp {
  #   type: time
  #   timeframes: [
  #     raw,
  #     time,
  #     date,
  #     week,
  #     month,
  #     quarter,
  #     year
  #   ]
  #   sql: ${TABLE}.end_timestamp ;;
  # }

  # dimension_group: start_timestamp {
  #   type: time
  #   timeframes: [
  #     raw,
  #     time,
  #     date,
  #     week,
  #     month,
  #     quarter,
  #     year
  #   ]
  #   sql: ${TABLE}.start_timestamp ;;
  # }

  # dimension_group: updated {
  #   type: time
  #   timeframes: [
  #     raw,
  #     time,
  #     date,
  #     week,
  #     month,
  #     quarter,
  #     year
  #   ]
  #   sql: ${TABLE}.updated ;;
  # }

  # dimension: user_id {
  #   type: string
  #   sql: ${TABLE}.user_id ;;
  # }

  # dimension: v {
  #   type: number
  #   sql: ${TABLE}.v ;;
  # }

  # measure: count {
  #   type: count
  #   drill_fields: [id]
  # }
}
