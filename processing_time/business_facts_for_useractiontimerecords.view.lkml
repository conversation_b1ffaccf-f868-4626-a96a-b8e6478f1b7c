view: business_facts_for_useractiontimerecords {
  derived_table: {
    sql:
    SELECT business_id,
           'KYC_ONBOARDING_MANAGEMENT' as action,
           'OVERRIDDEN_OR_CLOSE_MODAL' as end_action,
           sum(duration) as sum_of_duration
    FROM clean__xendit_admin_dashboard.useractiontimerecords
    WHERE action = 'KYC_ONBOARDING_MANAGEMENT'
    AND end_action IN ('OVERRIDDEN','CLOSE_MODAL')
    AND email NOT IN ('<EMAIL>','<EMAIL>','<EMAIL>','and<PERSON><EMAIL>','<EMAIL>','lan-b<PERSON><PERSON>@xendit.co','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','w<PERSON><PERSON>@xendit.co')
    AND duration < 600000
    GROUP BY 1
    ;;
  }
  suggestions: no

  # dimension: compound_primary_key {
  #   primary_key: yes
  #   hidden: yes
  #   type: string
  #   sql:  concat(${TABLE}.business_id, ' ',${TABLE}.action, ' ',${TABLE}.end_action, ' ');;
  # }

  dimension: business_id {
    hidden: yes
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: action {
    hidden: yes
    type: string
    sql: ${TABLE}.action ;;
  }

  dimension: end_action {
    hidden: yes
    type: string
    sql: ${TABLE}.end_action ;;
  }

  dimension: sum_of_duration {
    type: number
    sql: ${TABLE}.sum_of_duration/1000 ;;
    value_format: "0 \"seconds\""
  }

# sql_table_name: {% if group_by._parameter_value == 'business_id' %} ${group_by_business_id_useractiontimerecords.SQL_TABLE_NAME}
#   {% else %} ${group_by_email_useractiontimerecords.SQL_TABLE_NAME}
#   {% endif %} ;;

#   suggestions: no

#   parameter: group_by {
#     type: unquoted
#     default_value: "business_id"
#     allowed_value: {
#       label: "Business ID"
#       value: "business_id"
#     }
#     allowed_value: {
#       label: "Email"
#       value: "email"
#     }
#   }

#   dimension: business_id {
#     hidden: yes
#     type: string
#     sql: ${TABLE}.business_id ;;
#   }

#   dimension: email {
#     hidden:  yes
#     type: string
#     sql: ${TABLE}.email ;;
#   }

#   dimension: sum_of_duration {
#     type: number
#     sql: ${TABLE}.sum_of_duration ;;
#     value_format: "0 \"seconds\""
#   }

#   dimension: action {
#     type: string
#     sql: ${TABLE}.action ;;
#   }

#   dimension: end_action {
#     type: string
#     sql: ${TABLE}.end_action ;;
#   }

}
