include: "/processing_time/*.view.lkml"

explore: useractiontimerecords {
  group_label: "Processing Time"
  label: "Merchant Ops Time Tracker"
  view_label: "User Time Record"

  join: businesses {
    from: common_company_detail
    relationship: many_to_one
    sql_on: ${useractiontimerecords.business_id} = ${businesses.business_id} ;;
  }
  join: aggregated_user_time_record_metrics {
    from: business_facts_for_useractiontimerecords
    relationship: one_to_one
    sql_on: ${businesses.business_id} = ${aggregated_user_time_record_metrics.business_id} ;;
  }
}
