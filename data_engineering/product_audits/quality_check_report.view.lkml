view: quality_check_report {
  sql_table_name: transform__billing.quality_check_report ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: t4_amount {
    type: number
    sql: ${TABLE}.t_4_amount ;;
  }

  dimension: t4_status {
    type: string
    sql: ${TABLE}.t_4_status ;;
  }

  dimension_group: t4_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.t_4_created ;;
  }

  dimension: product_db_amount {
    type: number
    sql: ${TABLE}.product_db_amount ;;
  }

  dimension: product_db_status {
    type: string
    sql: ${TABLE}.product_db_status ;;
  }

  dimension_group: product_db_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.product_db_created ;;
  }

  dimension: customer_migrated {
    type: yesno
    sql: ${TABLE}.customer_migrated;;
  }

  dimension: quality_check_result {
    type: string
    sql: ${TABLE}.quality_check_result ;;
  }

  dimension: quality_check_details {
    type: string
    sql: ${TABLE}.quality_check_details ;;
  }

  dimension: other_details {
    type: string
    sql: ${TABLE}.other_details ;;
  }

  dimension_group: audited_on {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.inspection_date ;;
  }

  dimension: product {
    type: string
    sql: ${TABLE}.transaction_type ;;
  }

  measure: txn_count {
    type: count
    drill_fields: [product, id]
  }

  set: all_fields {
    fields: [
      audited_on_raw,
      product,
      business_id,
      id,
      product_db_created_raw,
      product_db_status,
      product_db_amount,
      t4_created_raw,
      t4_status,
      t4_amount,
      customer_migrated,
      quality_check_result,
      quality_check_details,
      other_details,
    ]
  }

  measure: audited_transactions {
    type: count
    drill_fields: [all_fields*]
    filters: [quality_check_result: "FAIL, PASS", customer_migrated: "Yes"]
  }

  measure: transactions_with_not_matching_product_data {
    type: count
    drill_fields: [all_fields*]
    filters: [quality_check_result: "FAIL", customer_migrated: "Yes"]
  }

  measure: transactions_with_matching_product_data {
    type: count
    drill_fields: [all_fields*]
    filters: [quality_check_result: "PASS", customer_migrated: "Yes"]
  }

  measure: transactions_with_matching_product_data_rate {
    type: number
    drill_fields: [transactions_with_matching_product_data_rate]
    value_format: "0.00\%"
    sql:
      ${transactions_with_matching_product_data}/cast(${audited_transactions} as double) * 100 ;;
  }

  measure: transactions_with_not_matching_product_data_rate {
    type: number
    drill_fields: [transactions_with_not_matching_product_data_rate]
    value_format: "0.00\%"
    sql:
      ${transactions_with_not_matching_product_data}/cast(${audited_transactions} as double) * 100 ;;
  }

  measure: test_coverage {
    type: number
    drill_fields: [test_coverage]
    value_format: "0.00\%"
    sql:
      SUM(CASE WHEN ${customer_migrated} = true and ${quality_check_result}!='UNKNOWN' THEN 1 ELSE 0 END) /
      cast(${txn_count} as double) * 100 ;;
  }

  measure: not_t4_customer {
    type: number
    drill_fields: [not_t4_customer]
    value_format: "0.00\%"
    sql:
      SUM(CASE WHEN ${customer_migrated} = false THEN 1 ELSE 0 END) /
      cast(${txn_count} as double) * 100 ;;
  }

  measure: t4_customer_and_audit_result_unknown {
    type: number
    drill_fields: [t4_customer_and_audit_result_unknown]
    value_format: "0.00\%"
    sql:
      SUM(CASE WHEN ${quality_check_result}='UNKNOWN' and ${customer_migrated} = true THEN 1 ELSE 0 END) /
      cast(${txn_count} as double) * 100 ;;
  }

}
