view: pipeline_executions {
  sql_table_name: clean__buddy.pipeline_executions ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: branch {
    type: string
    sql: ${TABLE}.branch ;;
  }

  dimension_group: start {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.start_date ;;
    drill_fields: [project, status, seconds_build_duration, minutes_build_duration]
  }

  dimension_group: finish {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.finish_date ;;
    hidden: yes
  }

  dimension_group: build_duration {
    type: duration
    sql_start: ${start_time::datetime} ;;  # often this is a single database column
    sql_end: ${finish_time::datetime} ;;  # often this is a single database column
    intervals: [minute, hour] # valid intervals described below
    label: "build_duration"
  }


  dimension: from_revision {
    type: string
    sql: ${TABLE}.from_revision;;
  }

  dimension: html_url {
    type: string
    sql: ${TABLE}.html_url ;;
  }

  dimension_group: loaded {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.loaded_at ;;
  }

  dimension: mode {
    type: string
    sql: ${TABLE}.mode ;;
  }

  dimension: pipeline_id {
    type: number
    sql: ${TABLE}.pipeline_id ;;
  }

  dimension: project {
    type: string
    sql: ${TABLE}.project ;;
    drill_fields: [id, project, status, start_date]

  }

  dimension: pull_request {
    type: string
    sql: ${TABLE}.pull_request ;;
  }

  dimension: refresh {
    type: yesno
    sql: ${TABLE}.refresh ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
    drill_fields: [id, project, start_date]
  }

  dimension: tag {
    type: string
    sql: ${TABLE}.tag ;;
  }

  dimension: to_revision {
    type: string
    sql: ${TABLE}.to_revision ;;
    hidden: yes
  }

  dimension: unique_id {
    type: string
    sql: ${TABLE}.unique_id ;;
    hidden: yes
  }

  dimension: url {
    type: string
    sql: ${TABLE}.url ;;
  }

  dimension: workspace {
    type: string
    sql: ${TABLE}.workspace ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
