include: "../data_platform.explore"

view: data_quality_summary {
  derived_table: {
    explore_source: pipeline_runs {
      column: fq_table_name {}
      column: test_source { field: data_quality_check_results.test_source }
      column: test_type { field: data_quality_check_results.test_type }
      column: status { field: data_quality_check_results.status }
      column: count { field: data_quality_check_results.count }
      derived_column: uuid {
        sql: concat(fq_table_name, test_source, test_type, status) ;;
      }
      derived_column: is_dqc_failed {
        sql: sum(case when status = 'FAILED' then count else 0 end) over (partition by fq_table_name) >= 1 ;;
      }
      filters: {
        field: pipeline_runs.dt_date
        value: "1 day ago for 1 day"
      }
    }
  }
  dimension: uuid {
    primary_key: yes
    hidden: yes
  }

  dimension: is_dqc_failed {
    type: yesno
    hidden: yes
  }
  dimension: fq_table_name {
    hidden: yes
  }
  dimension: test_source {
    description: ""
  }
  dimension: test_type {
    description: ""
  }
  dimension: status { hidden: yes }
  measure: count {
    type: sum
    sql: ${TABLE}.count ;;
  }
  measure: count_tests_failed {
    type: sum
    sql: ${TABLE}.count ;;
    filters: [status: "FAILED"]
  }
  measure: count_tests_passed {
    type: sum
    sql: ${TABLE}.count ;;
    filters: [status: "SUCCESS"]
  }
}
