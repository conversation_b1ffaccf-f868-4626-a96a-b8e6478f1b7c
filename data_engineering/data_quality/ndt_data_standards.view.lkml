# Tried to define stds for prod dbs
# This tries to calculate the source quality. eg is there an index on updated column?
# Possible to pull from cortex data instead? this moved to Eng Ops
view: ndt_data_standards {
  derived_table: {
    sql: select
        e.id,
        e.name,
        ds.description,
        ds.type,
        ds.score_type,
        e.is_compliant,
        e.compliance_percentage,
        e.compliance_percentage/100 * ds.max_score as score,
        ds.max_score,
        e.cluster_name,
        e.database_name,
        e.db_type,
        e.schema_name,
        e.table_name,
        e.collection_name,
        e.team,
        e.created,
        ds.created as datastandard_created,
        ds.updated as datastandard_updated
      from clean__de_events.events e
      left join clean__de_events.data_standards ds
          on e.data_standards_id = ds.id
       ;;
  }

  suggestions: yes

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: score_type {
    type: string
    sql: ${TABLE}.score_type ;;
  }

  dimension: is_compliant {
    type: string
    sql: ${TABLE}.is_compliant ;;
  }

  dimension: compliance_percentage {
    type: number
    sql: ${TABLE}.compliance_percentage ;;
  }

  dimension: score {
    type: number
    sql: ${TABLE}.score ;;
  }

  dimension: max_score {
    type: number
    sql: ${TABLE}.max_score ;;
  }

  dimension: cluster_name {
    type: string
    sql: ${TABLE}.cluster_name ;;
    drill_fields: [database_name]
  }

  dimension: database_name {
    type: string
    sql: ${TABLE}.database_name ;;
    # drill_fields: [table_name, collection_name]
    drill_fields: [table_or_collection_name]
  }

  dimension: db_type {
    type: string
    sql: ${TABLE}.db_type ;;
  }

  dimension: schema_name {
    type: string
    sql: ${TABLE}.schema_name ;;
  }

  dimension: table_name {
    type: string
    sql: ${TABLE}.table_name ;;
    # drill_fields: [scores_by_id*]
  }

  dimension: collection_name {
    type: string
    sql: ${TABLE}.collection_name ;;
    # drill_fields: [scores_by_id*]
    }

  dimension: table_or_collection_name {
    type: string
    sql: CASE
      WHEN ${TABLE}.collection_name != '' THEN ${TABLE}.collection_name
      WHEN ${TABLE}.table_name != '' THEN ${TABLE}.table_name
      ELSE ''
      END;;
    drill_fields: [scores_by_id*]
  }

  set: scores_by_id {
    fields: [name]
  }

  dimension: team {
    type: string
    sql: ${TABLE}.team ;;
  }

  dimension_group: created {
    type: time
    sql: ${TABLE}.created ;;
  }

  dimension_group: datastandard_created {
    type: time
    sql: ${TABLE}.datastandard_created ;;
  }

  dimension_group: datastandard_updated {
    type: time
    sql: ${TABLE}.datastandard_updated ;;
  }

  measure: sum_of_scores {
    type: sum
    value_format: "0.00"
    sql: CAST(score AS DOUBLE);;
  }

  measure: sum_of_max_scores {
    type: sum
    value_format: "0.00"
    sql: CAST(max_score AS DOUBLE);;
    }

  measure: overall_score {
    type: number
    value_format: "0%"
    sql: ${sum_of_scores}/${sum_of_max_scores} ;;
  }

  set: detail {
    fields: [
      id,
      name,
      description,
      type,
      score_type,
      is_compliant,
      compliance_percentage,
      score,
      max_score,
      cluster_name,
      database_name,
      db_type,
      schema_name,
      table_name,
      collection_name,
      team,
      created_time,
      datastandard_created_time,
      datastandard_updated_time
    ]
  }
}
