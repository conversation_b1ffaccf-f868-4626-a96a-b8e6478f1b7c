view: data_quality_check_results {
  sql_table_name: transform__data_engineering.data_quality_check_results ;;
  drill_fields: [id]
  suggestions: no

  ################################################
  # Primary keys {
  ################################################
  dimension: pk1_id {
    hidden: yes
    sql: ${id} ;;
  }
  ################################################
  # Primary keys }
  ################################################

  ################################################
  # Sets {
  ################################################
  set: test_details {
    fields: [
      exception
      , exception_message
      , id
      , runtime_seconds
      , started_raw
      , status
      , test_name
      , test_source
      , test_type
    ]
  }
  set: metrics {
    fields: [
      count
      , total_runtime_seconds
    ]
  }
  ################################################
  # Sets }
  ################################################

  ################################################
  # Dimensions {
  ################################################
  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: data_layer {
    type: string
    sql: ${TABLE}.data_layer ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: exception {
    type: string
    sql: ${TABLE}.exception ;;
  }

  dimension: exception_message {
    type: string
    sql: ${TABLE}.exception_message ;;
  }

  dimension: full_table_name {
    type: string
    sql: ${TABLE}.full_table_name ;;
  }

  dimension: is_last_run {
    type: yesno
    sql: ${TABLE}.is_last_run ;;
  }

  dimension: pipeline_type {
    type: string
    sql: ${TABLE}.pipeline_type ;;
  }

  dimension: runtime_seconds {
    type: number
    sql: ${TABLE}.runtime_seconds ;;
  }

  dimension: schema_name {
    type: string
    sql: ${TABLE}.schema_name ;;
  }

  dimension_group: started {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.started_at ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: table_name {
    type: string
    sql: ${TABLE}.table_name ;;
  }

  dimension: test_name {
    type: string
    sql: ${TABLE}.test_name ;;
  }

  dimension: test_source {
    type: string
    sql: ${TABLE}.test_source ;;
  }

  dimension: test_type {
    type: string
    sql: ${TABLE}.test_type ;;
  }
  ################################################
  # Dimensions }
  ################################################

  ################################################
  # Measures {
  ################################################
  measure: count {
    type: count
    drill_fields: [id, test_name, full_table_name, schema_name, table_name]
  }
  measure: total_runtime_seconds {
    type: sum
    sql: ${runtime_seconds} ;;
  }
  ################################################
  # Measures }
  ################################################
}
