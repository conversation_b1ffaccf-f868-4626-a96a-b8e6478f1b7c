view: data_platform_events {
  derived_table: {
    sql: select
        ds.dt, t.presto_table_name
      from ${date_scaffold.SQL_TABLE_NAME} ds
      cross join ${tables.SQL_TABLE_NAME} t
      where ds.dt >= current_date - interval '90' day
        and ds.dt <= current_date ;;
  }
  suggestions: no

  dimension: dt {
    label: "Event Date (UTC)"
    convert_tz: no
    type: date
    sql: ${TABLE}.dt ;;
  }

  dimension: presto_table_name {
    hidden: yes
    type: string
    sql: ${TABLE}.presto_table_name ;;
  }
}
