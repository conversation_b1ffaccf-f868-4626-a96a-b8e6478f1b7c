view: query_created_event {
  sql_table_name: clean__trino_eks.query_created_event ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: createtime {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.createtime ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.dt ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Environment" in Explore.

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: history_slug {
    type: string
    sql: ${TABLE}.history_slug ;;
  }

  dimension: instance_slug {
    type: string
    sql: ${TABLE}.instance_slug ;;
  }

  dimension: jsonplan {
    type: string
    sql: ${TABLE}.jsonplan ;;
  }

  dimension: payload {
    type: string
    sql: ${TABLE}.payload ;;
  }

  dimension: plan {
    type: string
    sql: ${TABLE}.plan ;;
  }

  dimension: preparedquery {
    type: string
    sql: ${TABLE}.preparedquery ;;
  }

  dimension: query {
    type: string
    sql: ${TABLE}.query ;;
  }

  dimension: queryid {
    type: string
    sql: ${TABLE}.queryid ;;
  }

  dimension: querystate {
    type: string
    sql: ${TABLE}.querystate ;;
  }

  dimension: retrypolicy {
    type: string
    sql: ${TABLE}.retrypolicy ;;
  }

  dimension: routines {
    type: string
    sql: ${TABLE}.routines ;;
  }

  dimension: serveraddress {
    type: string
    sql: ${TABLE}.serveraddress ;;
  }

  dimension: serverversion {
    type: string
    sql: ${TABLE}.serverversion ;;
  }

  dimension: tables {
    type: string
    sql: ${TABLE}.tables ;;
  }

  dimension: transactionid {
    type: string
    sql: ${TABLE}.transactionid ;;
  }

  dimension: updatetype {
    type: string
    sql: ${TABLE}.updatetype ;;
  }

  dimension: uri {
    type: string
    sql: ${TABLE}.uri ;;
  }

  dimension: user {
    type: string
    sql: ${TABLE}.user ;;
  }

  dimension: user_id {
    type: number
    sql: ${TABLE}.user_id ;;
  }
  measure: count {
    type: count
  }
}
