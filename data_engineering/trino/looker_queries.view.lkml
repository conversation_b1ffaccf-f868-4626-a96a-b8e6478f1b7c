# This should be new version of the Presto folders, which can be deleted
# Refactor this to add costs and drive for solution for errors

view: looker_queries {
  sql_table_name: transform__trino_eks.looker_queries ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # ----- GENERAL -----
  dimension: queryid {
    type: string
    label: "Query ID"
    group_label: "Query"
    sql: ${TABLE}.queryid ;;
  }
  dimension: query {
    type: string
    label: "Query SQL Statement"
    group_label: "Query"
    sql: ${TABLE}.query ;;
  }

  # ----- QUERY CONTENT -----

  dimension: tables_raw {
    type: string
    group_label: "Query Content"
    sql: ${TABLE}.tables ;;
  }
  dimension: tables_list {
    type: string
    group_label: "Query Content"
    sql: reduce(
          ${TABLE}.tables,
          '',
          (state, x) -> IF(x is NULL, state, state || IF(state = '', '', ', ') || x.catalog || '.' || x.schema || '.' || x.`table`),
          state -> state);;
  }
  dimension: tables_list_distinct {
    type: string
    group_label: "Query Content"
    sql: reduce(
          array_distinct(${TABLE}.tables),
          '',
          (state, x) -> IF(x is NULL, state, state || IF(state = '', '', ', ') || x.catalog || '.' || x.schema || '.' || x.`table`),
          state -> state);;
  }
  dimension: tables_count {
    type: number
    group_label: "Query Content"
    sql:  cardinality(${TABLE}.tables) ;;
  }
  dimension: tables_count_distinct {
    type: number
    group_label: "Query Content"
    sql:  cardinality(array_distinct(${TABLE}.tables)) ;;
  }

  # cardinality(split(your_string_column, ','))
  dimension: contain_pdt {
    type: yesno
    group_label: "Query Content"
    sql: ${TABLE}.tables_testing LIKE '%looker_pdt%' ;;
  }


  # ----- QUERY TIME ----

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    description: "Represents the date the query was created on"
    datatype: date
    group_label: "Query Time"
    sql: ${TABLE}.dt ;;
  }

  dimension_group: created {
    label: "Query Created"
    type: time
    timeframes: [raw, time, hour, date, week, month, quarter, year]
    datatype: timestamp
    description: "Represents when the query was created on"
    group_label: "Query Time"
    sql: ${TABLE}.createTime ;;
  }

  dimension_group: executionstarttime {
    type: time
    datatype: timestamp
    timeframes: [raw, time, hour, date, week, month, quarter, year]
    group_label: "Query Time"
    sql: ${TABLE}.executionstarttime ;;
  }

  dimension_group: endtime {
    type: time
    datatype: timestamp
    timeframes: [raw, time, hour, date, week, month, quarter, year]
    group_label: "Query Time"
    sql: ${TABLE}.endtime ;;
  }



  # ----- USER INFO -----
  dimension: user_id {
    type: string
    description: "The Looker User Id that ran this query, if applicable"
    group_label: "Looker User Information"
    sql: ${TABLE}.user_id ;;
  }
  dimension: user_email {
    type: string
    group_label: "Looker User Information"
    description: "The Looker User Email that ran this query, if applicable"
    sql: ${TABLE}.user_email ;;
    drill_fields: [query]
  }

  dimension: history_slug {
    type: string
    group_label: "Looker User Information"
    sql: ${TABLE}.history_slug ;;
  }


  # ----- FAILURE INFO ------

  dimension: failure_message {
    type: string
    description: "Human readable failure message"
    group_label: "Failure Info"
    sql: ${TABLE}.failure_message ;;
  }

  dimension: failure_task {
    type: string
    group_label: "Failure Info"
    sql: ${TABLE}.failure_task ;;
  }

  dimension: failure_type {
    type: string
    group_label: "Failure Info"
    sql: ${TABLE}.failure_type ;;
  }

  dimension: failure_type_function {
    type: string
    group_label: "Failure Info"
    sql: ${TABLE}.failure_type_function ;;
  }

  dimension: error_code {
    type: string
    label: "Code Type"
    group_label: "Failure Info"
    sql: ${TABLE}.error_code ;;
  }

  # ----------QUERY STATE ===-------

  dimension: created_query_state {
    type: string
    group_label: "Query State"
    sql: ${TABLE}.created_query_state ;;
  }

  dimension: completed_query_state {
    type: string
    group_label: "Query State"
    sql: ${TABLE}.completed_query_state ;;
  }

  dimension: is_query_completed {
    type: yesno
    group_label: "Query State"
    sql: ${TABLE}.is_query_completed ;;
  }

  # ----------QUERY RUNTIME-----------
  dimension: queuedtime_second {
    type: number
    sql: ${TABLE}.queuedtime ;;
    group_label: "Query Runtime - Queued Time"
  }
  dimension: queuedtime_second_tier {
    type: tier
    tiers: [0,60,300,600,900,1200,1800]
    style: relational
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime ;;
  }
  dimension: queuedtime_minute {
    type: number
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime / 60;;
    value_format: "#,##0.000"
  }
  dimension: queuedtime_minute_tier {
    type: tier
    tiers: [0,1,5,10,15,20,30]
    style: relational
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime / 60;;
  }
  measure: queuedtime_average {
    type: average
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime ;;
  }
  measure: queuedtime_median {
    group_label: "Query Runtime - Queued Time"
    type: average
    sql: ${TABLE}.queuedtime ;;
  }
  measure: queuedtime_75th_percentile {
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime ;;
    type: percentile
    percentile: 75
  }
  measure: queuedtime_95th_percentile {
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime ;;
    type: percentile
    percentile: 95
  }
  measure: queuedtime_99th_percentile {
    group_label: "Query Runtime - Queued Time"
    sql: ${TABLE}.queuedtime ;;
    type: percentile
    percentile: 99
  }
  measure: queuedtime_second_tier_count {
    type: number
    sql:  COUNT(${analysistime_second_tier});;
  }
  measure: queuedtime_minute_tier_count {
    type: number
    sql:  COUNT(${analysistime_minute_tier});;
  }

  dimension: cputime {
    type: number
    group_label: "Query Runtime - CPU Time"
    sql: ${TABLE}.cputime ;;
  }
  measure: cputime_99th_percentile {
    group_label: "Query Runtime - CPU Time"
    sql: ${TABLE}.cputime ;;
    type: percentile
    percentile: 99
  }

  dimension: analysistime_second {
    type: number
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
  }
  dimension: analysistime_second_tier {
    type: tier
    tiers: [0,60,300,600,900,1200,1800]
    style: relational
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
  }
  dimension: analysistime_minute {
    type: number
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime / 60;;
    value_format: "#,##0.000"
  }
  dimension: analysistime_minute_tier {
    type: tier
    tiers: [0,1,5,10,15,20,30]
    style: relational
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime / 60;;
  }
  measure: analysistime_average {
    type: average
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
  }
  measure: analysistime_median {
    group_label: "Query Runtime - Analysis Time"
    type: average
    sql: ${TABLE}.analysistime ;;
  }
  measure: analysistime_75th_percentile {
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
    type: percentile
    percentile: 75
  }
  measure: analysistime_95th_percentile {
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
    type: percentile
    percentile: 95
  }
  measure: analysistime_99th_percentile {
    group_label: "Query Runtime - Analysis Time"
    sql: ${TABLE}.analysistime ;;
    type: percentile
    percentile: 99
  }
  measure: analysistime_second_tier_count {
    type: number
    sql:  COUNT(${analysistime_second_tier});;
  }
  measure: analysistime_minute_tier_count {
    type: number
    sql:  COUNT(${analysistime_minute_tier});;
  }

  dimension: walltime_second {
    type: number
    group_label: "Query Runtime - Wall Time"
    sql: ${TABLE}.walltime ;;
  }
  dimension: walltime_second_tier {
    type: tier
    tiers: [0,60,300,600,900,1200,1800]
    style: relational
    group_label: "Query Runtime - Wall Time"
    sql: ${TABLE}.walltime ;;
  }
  dimension: walltime_minute {
    type: number
    group_label: "Query Runtime - Wall Time"
    sql: ${TABLE}.walltime / 60;;
    value_format: "#,##0.000"
  }
  dimension: walltime_minute_tier {
    type: tier
    tiers: [0,1,5,10,15,20,30]
    style: relational
    group_label: "Query Runtime - Wall Time"
    sql: ${TABLE}.walltime / 60;;
  }
  measure: wall_time_average {
    type: average
    group_label: "Query Runtime - Wall Time"
    description: "Wall time represents human perceived time for the query"
    sql: ${TABLE}.walltime ;;
  }
  measure: wall_time_median {
    type: median
    group_label: "Query Runtime - Wall Time"
    description: "Wall time represents human perceived time for the query"
    sql: ${TABLE}.walltime ;;
  }
  measure: wall_time_75th_percentile {
    type: percentile
    group_label: "Query Runtime - Wall Time"
    label: "Wall Time Seconds 75th Percentile"
    percentile: 75
    sql: ${TABLE}.walltime ;;
  }
  measure: wall_time_95th_percentile {
    type: percentile
    group_label: "Query Runtime - Wall Time"
    label: "Wall Time Seconds 95th Percentile"
    description: "Wall time represents human perceived time for the query"
    percentile: 95
    sql: ${TABLE}.walltime ;;
  }
  measure: wall_time_99th_percentile {
    type: percentile
    group_label: "Query Runtime - Wall Time"
    label: "Wall Time Seconds 99th Percentile"
    description: "Wall time represents human perceived time for the query"
    percentile: 99
    sql: ${TABLE}.walltime ;;
  }
  measure: walltime_second_tier_count {
    type: number
    sql:  COUNT(${walltime_second_tier});;
  }
  measure: walltime_minute_tier_count {
    type: number
    sql:  COUNT(${walltime_minute_tier});;
  }


  # ------- QUERY MEMORY --------
  # 1 GB = 1073741824 bytes
  # 1 TB = 1099511627776 bytes
  dimension: peaktasktotalmemory_bytes {
    type: number
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory ;;
  }
  dimension: peaktasktotalmemory_GB {
    type: number
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
    value_format: "#,##0.000"
  }
  measure: peaktasktotalmemory_GB_average {
    type: average
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
  }
  measure: peaktasktotalmemory_GB_median {
    group_label: "Query Memory - Peak Task Total Memory"
    type: median
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
  }
  measure: peaktasktotalmemory_GB_75th_percentile {
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
    type: percentile
    percentile: 75
  }
  measure: peaktasktotalmemory_GB_95th_percentile {
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
    type: percentile
    percentile: 95
  }
  measure: peaktasktotalmemory_GB_99th_percentile {
    group_label: "Query Memory - Peak Task Total Memory"
    sql: ${TABLE}.peaktasktotalmemory / 1073741824 ;;
    type: percentile
    percentile: 99
  }

  dimension: peaktaskusermemory_bytes {
    type: number
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory ;;
  }
  dimension: peaktaskusermemory_GB {
    type: number
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
  }
  measure: peaktaskusermemory_GB_average {
    type: average
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
  }
  measure: peaktaskusermemory_GB_median {
    group_label: "Query Memory - Peak Task User Memory"
    type: median
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
  }
  measure: peaktaskusermemory_GB_75th_percentile {
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
    type: percentile
    percentile: 75
  }
  measure: peaktaskusermemory_GB_95th_percentile {
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
    type: percentile
    percentile: 95
  }
  measure: peaktaskusermemory_GB_99th_percentile {
    group_label: "Query Memory - Peak Task User Memory"
    sql: ${TABLE}.peaktaskusermemory / 1073741824;;
    type: percentile
    percentile: 99
  }

  dimension: peakusermemorybytes {
    type: number
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes ;;
  }
  dimension: peakusermemory_GB {
    type: number
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
  }
  measure: peakusermemory_GB_average {
    type: average
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
  }
  measure: peakusermemory_GB_median {
    group_label: "Query Memory - Peak User Memory Bytes"
    type: median
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
  }
  measure: peakusermemory_GB_75th_percentile {
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
    type: percentile
    percentile: 75
  }
  measure: peakusermemory_GB_95th_percentile {
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
    type: percentile
    percentile: 95
  }
  measure: peakusermemory_GB_99th_percentile {
    group_label: "Query Memory - Peak User Memory Bytes"
    sql: ${TABLE}.peakusermemorybytes / 1073741824;;
    type: percentile
    percentile: 99
  }

  dimension: cumulativememory {
    type: number
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory ;;
  }
  dimension: cumulativememory_tb {
    type: number
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
  }
  measure: cumulativememory_tb_average {
    type: average
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
  }
  measure: cumulativememory_tb_median {
    group_label: "Query Memory - Cumulative Memory"
    type: median
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
  }
  measure: cumulativememory_tb_75th_percentile {
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
    type: percentile
    percentile: 75
  }
  measure: cumulativememory_tb_95th_percentile {
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
    type: percentile
    percentile: 95
  }
  measure: cumulativememory_tb_99th_percentile {
    group_label: "Query Memory - Cumulative Memory"
    sql: ${TABLE}.cumulativememory / 1099511627776 ;;
    type: percentile
    percentile: 99
  }

  # ------- QUERY OUTPUT SIZE -----

  dimension: outputbytes {
    type: number
    group_label: "Query Output Size"
    sql: ${TABLE}.outputbytes ;;
  }

  dimension: outputrows {
    type: number
    group_label: "Query Output Size"
    sql: ${TABLE}.outputrows ;;
  }

  dimension: totalbytes {
    type: number
    group_label: "Query Output Size"
    sql: ${TABLE}.totalbytes ;;
  }

  dimension: totalrows {
    type: number
    group_label: "Query Output Size"
    sql: ${TABLE}.totalrows ;;
  }

# ------ SERVER INFO -----

  dimension: retrypolicy {
    type: string
    group_label: "Server Info"
    sql: ${TABLE}.retrypolicy ;;
  }

  dimension: serveraddress {
    type: string
    group_label: "Server Info"
    sql: ${TABLE}.serveraddress ;;
  }

  dimension: serverversion {
    type: string
    group_label: "Server Info"
    sql: ${TABLE}.serverversion ;;
  }

  dimension: environment {
    type: string
    group_label: "Server Info"
    sql: ${TABLE}.environment ;;
  }

  dimension: uri {
    type: string
    group_label: "Server Info"
    sql: ${TABLE}.uri ;;
  }

  # ------- UNCATEGORIZED -------- -
  set: user_info {
    fields: [user_id, user_email]
  }
  set: failure_info {
    fields: [error_code, failure_task, failure_type, failure_message, failure_type_function]
  }
  set: query_content {
    fields: [tables_list, tables_count, query]
  }

  measure: count {
    type: count
    group_label: "Query Count"
    drill_fields: [user_info*, failure_info*, query_content*]
  }

  # ------- QUERY SUCCESSFULLNESS -----
  measure: count__finished_queries {
    type: count
    group_label: "Query Count"
    filters: [
      completed_query_state: "FINISHED"
    ]
  }
  measure: ratio__finished_queries {
    type: number
    group_label: "Query Count"
    value_format: "0.00\%"
    sql: ${count__finished_queries} / cast(${count} as double) * 100 ;;
  }
  measure: count__failed_queries {
    type: count
    group_label: "Query Count"
    filters: [
      completed_query_state: "FAILED"
    ]
  }
  measure: ratio__failed_queries {
    type: number
    group_label: "Query Count"
    value_format: "0.00\%"
    sql: ${count__failed_queries} / cast(${count} as double) * 100 ;;
  }
}
