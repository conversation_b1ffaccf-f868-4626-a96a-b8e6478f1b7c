# The name of this view in Looker is "Query Completed Event"
view: query_completed_event {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__trino_eks.query_completed_event ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Analysistime" in Explore.

  dimension: analysistime {
    type: number
    sql: ${TABLE}.analysistime ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_analysistime {
    type: sum
    sql: ${analysistime} ;;  }
  measure: average_analysistime {
    type: average
    sql: ${analysistime} ;;  }

  dimension: cputime {
    type: number
    sql: ${TABLE}.cputime ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: createtime {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.createtime ;;
  }

  dimension: cumulativememory {
    type: number
    sql: ${TABLE}.cumulativememory ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.dt ;;
  }

  dimension_group: endtime {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.endtime ;;
  }

  dimension_group: executionstarttime {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.executionstarttime ;;
  }

  dimension: failureinfo {
    type: string
    sql: ${TABLE}.failureinfo ;;
  }

  dimension: jsonplan {
    type: string
    sql: ${TABLE}.jsonplan ;;
  }

  dimension: outputbytes {
    type: number
    sql: ${TABLE}.outputbytes ;;
  }

  dimension: outputrows {
    type: number
    sql: ${TABLE}.outputrows ;;
  }

  dimension: payload {
    type: string
    sql: ${TABLE}.payload ;;
  }

  dimension: peaktasktotalmemory {
    type: number
    sql: ${TABLE}.peaktasktotalmemory ;;
  }

  dimension: peaktaskusermemory {
    type: number
    sql: ${TABLE}.peaktaskusermemory ;;
  }

  dimension: peakusermemorybytes {
    type: number
    sql: ${TABLE}.peakusermemorybytes ;;
  }

  dimension: plan {
    type: string
    sql: ${TABLE}.plan ;;
  }

  dimension: preparedquery {
    type: string
    sql: ${TABLE}.preparedquery ;;
  }

  dimension: queryid {
    type: string
    sql: ${TABLE}.queryid ;;
  }

  dimension: querystate {
    type: string
    sql: ${TABLE}.querystate ;;
  }

  dimension: queuedtime {
    type: number
    sql: ${TABLE}.queuedtime ;;
  }

  dimension: routines {
    type: string
    sql: ${TABLE}.routines ;;
  }

  dimension: tables {
    type: string
    sql: ${TABLE}.tables ;;
  }

  dimension: totalbytes {
    type: number
    sql: ${TABLE}.totalbytes ;;
  }

  dimension: totalrows {
    type: number
    sql: ${TABLE}.totalrows ;;
  }

  dimension: transactionid {
    type: string
    sql: ${TABLE}.transactionid ;;
  }

  dimension: updatetype {
    type: string
    sql: ${TABLE}.updatetype ;;
  }

  dimension: uri {
    type: string
    sql: ${TABLE}.uri ;;
  }

  dimension: walltime {
    type: number
    sql: ${TABLE}.walltime ;;
  }

  dimension: warnings {
    type: string
    sql: ${TABLE}.warnings ;;
  }
  measure: count {
    type: count
  }
}
