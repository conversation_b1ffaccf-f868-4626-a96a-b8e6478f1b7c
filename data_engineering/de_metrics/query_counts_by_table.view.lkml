view: query_counts_by_table {
  derived_table: {
    sql: -- <PERSON><PERSON><PERSON><PERSON>CKS QUERY COUNT
      with filtered_databricks_sql as (
          select
          -- split the sql string by whitespace to a list, and filter out empty string values from the list
            filter(
              split(regexp_replace(sql_query, '[\r\n\t]+', ' '), ' '),
              word -> word != ''
            ) as word_array
            , service_name
            , dt
          from
              transform__data_engineering_databricks.queries
          where (service_name = 'notebook' and lower(substr(sql_query, 0, 4)) in ('sele', 'with')
              or service_name = 'databrickssql')
              and dt > CURRENT_TIMESTAMP - interval '90' day
              and sql_query is not null and sql_query != ''
      ),
      transformed_databricks_sql as (
        select
          *
          , transform(
            sequence(1, cardinality(word_array)),
            i -> case
              when lower(word_array[i]) IN ('from', 'join') and i + 1 <= cardinality(word_array) then replace(word_array[i + 1], '`', '')
              else ''
              end
            ) as table_list
        from filtered_databricks_sql
      ),
      databricks_tables_referenced_by_query as (
        select
          *
          , filter(table_list, word -> word not in ('', '(')) as databricks_tables
        from transformed_databricks_sql
      ),
      databricks_exploded as (
          select
              databricks_table
              , service_name
              , dt
          from
              databricks_tables_referenced_by_query lateral view explode(databricks_tables)t as databricks_table
      ),
      databricks_table_query_count as (
          select
              databricks_table as schema_table
              , service_name as source
              , dt
              , count(*) as query_count
          from
              databricks_exploded
          group by
              1, 2, 3
      ),
      -- LOOKER QUERY COUNT
      filtered_looker_queries as (
          select
              tables
              , dt
          from
              transform__trino_eks.looker_queries
          where
              cardinality(tables) > 0
              and dt > CURRENT_TIMESTAMP - interval '90' day
      ),
      looker_exploded as (
          select
              concat(schema, '.', "table") as schema_table
              , dt
          from
              filtered_looker_queries lateral view explode(tables) as table_struct
      ),
      looker_table_query_count as (
          select
              schema_table
              , 'looker' as source
              , dt
              , COUNT(*) as query_count
          from
              looker_exploded
          GROUP BY
              1, 2, 3
      )
      select
          schema_table
          , source
          , dt
          , query_count
      from
          databricks_table_query_count
      union all
      select
          schema_table
          , source
          , dt
          , query_count
      from
          looker_table_query_count ;;
  }

  suggestions: no

  ################################################
  # Primary keys {
  ################################################
  dimension: pk1_uuid {
    primary_key: yes
    hidden: yes
    sql: concat(${pk3_schema_table_name}, ${pk3_dt}, ${pk3_source}) ;;
  }

  dimension: pk3_schema_table_name {
    hidden: yes
    sql: ${TABLE}.schema_table  ;;
  }
  dimension: pk3_dt {
    hidden: yes
    sql: ${dt}  ;;
  }
  dimension: pk3_source {
    hidden: yes
    sql: ${query_source}  ;;
  }

  ################################################
  # Primary keys }
  ################################################

  ################################################
  # Sets {
  ################################################
  set: detail {
    fields: [
      schema_name,
      table_name,
      query_source,
      dt,
      count
    ]
  }
  ################################################
  # Sets }
  ################################################

  ################################################
  # Dimensions {
  ################################################
  dimension: full_name {
    type: string
    sql: case
      when coalesce(split(${pk3_schema_table_name}, '.')[1],null) in ('hive_metastore', 'hive', 'main') then regexp_replace(${pk3_schema_table_name}, '([^\.]*)(.*)', '$2')
      when substr(${pk3_schema_table_name}, 1, 10) = 'looker_pdt' then regexp_replace(${pk3_schema_table_name}, '^([^.]*\.)(lr_[^_]*_)(.*)$', '$1xxxx_$3')
      else ${pk3_schema_table_name} end ;;
  }
  dimension: database_name {
    type: string
    sql: coalesce(element_at(split(${pk3_schema_table_name}, '.'),-3),null) ;;
  }
  dimension: schema_name {
    type: string
    sql: coalesce(element_at(split(${full_name}, '.'),-2),null) ;;
  }
  dimension: table_name {
    type: string
    sql: coalesce(element_at(split(${full_name}, '.'),-1),null) ;;
  }

  dimension: query_source {
    type: string
    sql: ${TABLE}.source ;;
  }

  dimension: dt {
    label: "Query Date (UTC)"
    convert_tz: no
    type: date
    sql: ${TABLE}.dt ;;
  }
  ################################################
  # Dimensions }
  ################################################

  measure: count {
    type: sum
    sql: ${TABLE}.query_count ;;
  }
}
