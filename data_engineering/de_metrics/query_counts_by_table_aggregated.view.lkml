include: "../data_platform.explore"

view: query_counts_by_table_aggregated {
  derived_table: {
    explore_source: query_counts_by_table {
      column: full_name {}
      column: count {}
      column: dt {}
      bind_filters: {
        from_field: query_counts_by_table_aggregated.dt
        to_field: query_counts_by_table.dt
      }
      bind_filters: {
        from_field: query_counts_by_table_aggregated.query_source
        to_field: query_counts_by_table.query_source
      }
    }
  }
  filter: dt {
    label: "Query Date (UTC)"
    description: "Filter for queries run within a timeframe. Note: Oldest data is 90 days"
    type: date
  }
  filter: query_source {
    description: "Filter for queries run in Looker or Databricks"
    suggestions: ["looker", "databrickssql"]
    type: string
  }

  dimension: pk1_name_date {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.full_name || date_format(${TABLE}.dt,'yyyy-MM-dd') ;;
  }
  dimension: full_name {
    hidden: yes
  }

  measure: query_count_by_table {
    description: "Query count for each table. Note that this will not aggregate above the table level. Use Overall Query Count for if you need that functionality"
    type: sum
    sql: ${TABLE}.count ;;
    link: {
      label: "View all {{ rendered_value }} by day"
      url: "/explore/{{ _model._name }}/query_counts_by_table?fields=query_counts_by_table.count,query_counts_by_table.full_name,query_counts_by_table.query_source,query_counts_by_table.dt&f[query_counts_by_table.full_name]={{ full_name._value }}&f[query_counts_by_table.dt]={{ _filters['query_counts_by_table_aggregated.dt'] | url_encode }}&sorts=query_counts_by_table.dt+desc+0&pivots=query_counts_by_table.query_source"
    }
  }
  measure: overall_query_count {
    type: sum
    sql: ${TABLE}.count ;;
  }
}
