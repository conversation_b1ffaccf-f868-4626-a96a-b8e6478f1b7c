include: "ndt_batch_pipeline_runs.view"
explore: ndt_batch_pipeline_runs {
  group_label: "Data Engineering"
  label: "Data Pipeline Health"
  from: ndt_batch_pipeline_runs
}
include: "query_time_employee_errors.view"
explore: query_time_employee_errors {
  group_label: "Data Engineering"
  label: "Trino Errors"
  from:  query_time_employee_errors
  description: "Trino Errors"
}

# include: "/data_engineering/presto/looker/*.view.lkml"
# explore: production_trino_detailed {
#   group_label: "Data Engineering"
#   label: "Trino Detailed Queries"
#   from: executed_queries_detailed
# }
# explore: production_trino_detailed_schema_table_column {
#   group_label: "Data Engineering"
#   label: "Trino Schema Table Column Use"
#   from: executed_queries_detailed_schema_table_column
# }


include: "/data_engineering/trino/*.view.lkml"
explore: trino_eks_queries {
  group_label: "Data Engineering"
  label: "Trino EKS Queries"
  from: looker_queries
}


include: "/data_engineering/buddy/*.view.lkml"
explore: buddy_pipeline_executions{
  group_label: "Engineering / Internal"
  label: "Buddy Pipeline Executions"
  from:  pipeline_executions
  description: "Pipeline execution information from Buddy, e.g. build duration, status, and project information"
}


# include: "/data_engineering/datastandard/*.view.lkml"
# explore: datastandard{
#   group_label: "Data Engineering"
#   label: "Data Standards"
#   from:  ndt_data_standards
#   description: "Data Standards Compliance Checks"
# }


include: "/data_engineering/product_audits/*.view.lkml"
explore: product_audits{
  group_label: "Data Engineering"
  label: "Product audits"
  from:  quality_check_report
  description: "Data audits between product databases and T4."
}

# Add Airflow task & dag list to explore
# include: "/data_engineering/airflow/task_list.view.lkml"
# explore: airflow_task_list{
#   group_label: "Data Engineering"
#   label: "Airflow Task List"
#   from:  task_list
#   description: "Airflow Task List details."
# }

# Add Presto Executed Queries to explore
# include: "/data_engineering/presto/executed_queries.view.lkml"
# explore: presto_executed_queries{
#   group_label: "Data Engineering"
#   label: "Presto Executed Queries"
#   from:  executed_queries
#   description: "Combined data from Looker and Redash presto queries."
# }

# Add de_metrics
include: "/data_engineering/airflow/pipeline_runs.view.lkml"
explore: dlth {
  group_label: "Data Engineering"
  from: pipeline_runs
  label: "DLTH"
  description: "DE metrics - Data lakehouse table health (DLTH)"
}

# include: "/data_engineering/airflow/pipeline_runs_summary.view"
# explore:  dlth_pipeline_runs_summary{
#   group_label: "Data Engineering"
#   label: "DLTH Pipeline summary"
#   from:  pipeline_runs_summary
#   description: "DE metrics - DLTH Pipeline summary"
# }
