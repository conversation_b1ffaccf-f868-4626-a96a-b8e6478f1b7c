view: pipeline_run_events {
  sql_table_name: clean__de_events.pipeline_runs ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: allow_auto_recovery {
    type: yesno
    sql: ${TABLE}.allow_auto_recovery ;;
  }

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension: db_name {
    type: string
    sql: ${TABLE}.db_name ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.dt ;;
  }

  dimension: exception {
    type: string
    sql: ${TABLE}.exception ;;
  }

  dimension: exception_message {
    type: string
    sql: ${TABLE}.exception_message ;;
  }

  dimension: final_db_name {
    type: string
    sql: ${TABLE}.final_db_name ;;
  }

  dimension: final_table_name {
    type: string
    sql: ${TABLE}.final_table_name ;;
  }

  dimension: fq_table_name {
    type: string
    sql: ${TABLE}.fq_table_name ;;
  }

  dimension: in_auto_recovery {
    type: yesno
    sql: ${TABLE}.in_auto_recovery ;;
  }

  dimension: pipeline_status {
    type: yesno
    sql: ${TABLE}.pipeline_status ;;
  }

  dimension: pipeline_type {
    type: string
    sql: ${TABLE}.pipeline_type ;;
  }

  dimension: rows_inserted {
    type: number
    sql: ${TABLE}.rows_inserted ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_rows_inserted {
    type: sum
    sql: ${rows_inserted} ;;  }
  measure: average_rows_inserted {
    type: average
    sql: ${rows_inserted} ;;  }

  dimension: rows_read {
    type: number
    sql: ${TABLE}.rows_read ;;
  }

  dimension: rows_updated {
    type: number
    sql: ${TABLE}.rows_updated ;;
  }

  dimension_group: run_end {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.run_end ;;
  }

  dimension_group: run_start {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.run_start ;;
  }

  dimension: source_name {
    type: string
    sql: ${TABLE}.source_name ;;
  }

  dimension: source_type {
    type: string
    sql: ${TABLE}.source_type ;;
  }

  dimension: table_name {
    type: string
    sql: ${TABLE}.table_name ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
      id,
      source_name,
      db_name,
      final_db_name,
      table_name,
      final_table_name,
      fq_table_name
    ]
  }

}
