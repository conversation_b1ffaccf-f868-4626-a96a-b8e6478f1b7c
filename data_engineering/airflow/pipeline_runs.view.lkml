view: pipeline_runs {
  sql_table_name: transform__data_engineering_airflow.pipeline_runs_v3 ;;
  suggestions: no

  dimension: dlth_de {
    type: number
    sql: ${TABLE}.dlth_de ;;
  }

  dimension: dlth_xendit {
    type: number
    sql: ${TABLE}.dlth_xendit ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: exception {
    type: string
    sql: ${TABLE}.exception ;;
  }

  dimension: exception_message {
    type: string
    sql: ${TABLE}.exception_message ;;
  }

  dimension: failed_tests_per_day {
    type: number
    sql: ${TABLE}.failed_tests_per_day ;;
  }

  dimension_group: first_successful_job {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.first_successful_job_at ;;
  }

  dimension: fq_table_name {
    type: string
    sql: ${TABLE}.fq_table_name ;;
  }

  dimension_group: last_successful_job {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.last_successful_job_at ;;
  }

  dimension: meet_dqc_slo {
    type: number
    sql: ${TABLE}.meet_dqc_slo ;;
  }

  dimension: meet_freshness_slo {
    type: number
    sql: ${TABLE}.meet_freshness_slo ;;
  }

  dimension: meet_slo {
    type: number
    sql: ${TABLE}.meet_slo ;;
  }

  dimension: omd_link {
    type: string
    sql: ${TABLE}.omd_link ;;
  }

  dimension: p50_duration_30_days {
    type: number
    sql: ${TABLE}.p50_duration_30_days ;;
  }

  dimension: p90_duration_30_days {
    type: number
    sql: ${TABLE}.p90_duration_30_days ;;
  }

  dimension: p99_duration_30_days {
    type: number
    sql: ${TABLE}.p99_duration_30_days ;;
  }

  dimension: pipeline_duration {
    type: number
    sql: ${TABLE}.pipeline_duration ;;
  }

  dimension: pipeline_runs_per_day {
    type: number
    sql: ${TABLE}.pipeline_runs_per_day ;;
  }

  dimension: pipeline_status {
    type: yesno
    sql: ${TABLE}.pipeline_status ;;
  }

  dimension: pipeline_type {
    type: string
    sql: ${TABLE}.pipeline_type ;;
  }

  dimension: row {
    type: number
    sql: ${TABLE}."row" ;;
  }

  dimension: rows_inserted_per_day {
    type: number
    sql: ${TABLE}.rows_inserted_per_day ;;
  }

  dimension: rows_updated_per_day {
    type: number
    sql: ${TABLE}.rows_updated_per_day ;;
  }

  dimension: rows_read_per_day {
    type: number
    sql: ${TABLE}.rows_read_per_day ;;
  }

  dimension_group: run_end {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.run_end ;;
  }

  dimension_group: run_start {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.run_start ;;
  }

  dimension: successful_tests_per_day {
    type: number
    sql: ${TABLE}.successful_tests_per_day ;;
  }

  dimension: uid {
    type: string
    sql: ${TABLE}.uid ;;
  }
  measure: count {
    type: count
    drill_fields: [fq_table_name]
  }
}
