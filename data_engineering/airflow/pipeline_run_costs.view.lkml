
view: pipeline_run_costs {
  derived_table: {
    datagroup_trigger: global__start_of_day_jkt
    sql: with aws_agg as (
        select
          cluster_name,
          sum(case when product_servicename = 'Amazon Elastic Compute Cloud' then cost else 0 end) as aws_ec2_cost_usd,
          sum(case when product_servicename != 'Amazon Elastic Compute Cloud' then cost else 0 end) as aws_other_direct_cost_usd
        from (
          select
            regexp_replace(resource_tags_user_cluster_name, '-', '_') as cluster_name,
            product_servicename,
            sum(line_item_net_unblended_cost) as cost
          from
            clean__xendit_aws.usage_logs_v2
          where
            line_item_usage_account_id = '************'
          group by
            1, 2
        ) subquery
        group by
          1
      ), staging as (select
        databricks.cluster_name,
        databricks.run_id,
        databricks.cost_usd as databricks_cost_usd,
        databricks.team_name,
        databricks.task,
        databricks.db_name,
        databricks.source_name,
        databricks.source,
        databricks.dt,
        aws.aws_ec2_cost_usd,
        aws.aws_other_direct_cost_usd,
        cast(null as int) as overhead_cost
      from transform__cost_management.databricks_usage_logs as databricks
      left join aws_agg as aws
        on databricks.cluster_name = aws.cluster_name
      where
        databricks.run_id is not NULL
    )
    select
      *
      , row_number() over (ORDER BY dt, run_id) as rn          --need to include order by column in databricks
      , case
        when task like 'generic_pipeline%' then
          'clean__' || regexp_replace(db_name, '-', '_')
        when task = 'generic_transformation_pipeline' then
          regexp_replace('transform__' || db_name || '.' || source_name, '_main\.', '\.')
        else null end as fq_table_name
    from staging;;
  }

  suggestions: no

  dimension: pk1_rn {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.rn ;;
  }

  dimension: cluster_name {
    description: "The databricks cluster used to run the job"
    type: string
    sql: ${TABLE}.cluster_name ;;
  }

  dimension: fq_table_name {
    description: "Best efforts at parsing a full table name"
    hidden: yes
    type: string
    sql: ${TABLE}.fq_table_name ;;
  }

  dimension: schema_name {
    type: string
    sql: element_at(split(${fq_table_name}, '.'), 1) ;;
  }

  dimension: table_name {
    type: string
    sql: coleasce(element_at(split(${fq_table_name}, '.'), 2),null) ;;
  }

  dimension: run_id {
    type: string
    sql: ${TABLE}.run_id ;;
  }

  dimension: team_name {
    type: string
    sql: ${TABLE}.team_name ;;
  }

  dimension: task {
    type: string
    sql: ${TABLE}.task ;;
  }

  dimension: db_name {
    label: "Source Database Name"
    description: "The source for the pipeline, if relevant"
    type: string
    sql: ${TABLE}.db_name ;;
  }

  dimension: source_name {
    label: "Source Service Name"
    description: "The source for the pipeline, if relevant"
    type: string
    sql: ${TABLE}.source_name ;;
  }

  dimension: source {
    type: string
    sql: ${TABLE}.source ;;
  }

  dimension: dt {
    label: "UTC Date"
    type: date
    convert_tz: no
    sql: ${TABLE}.dt ;;
  }

  measure: aws_ec2_cost_usd {
    value_format_name: usd
    type: sum
    sql: ${TABLE}.aws_ec2_cost_usd ;;
  }

  measure: aws_other_direct_cost_usd {
    value_format_name: usd
    type: sum
    sql: ${TABLE}.aws_other_direct_cost_usd ;;
  }

  measure: databricks_cost_usd {
    value_format_name: usd
    type: sum
    sql: ${TABLE}.databricks_cost_usd ;;
  }

  measure: overhead_cost_usd {
    value_format_name: usd
    type: sum
    sql: ${TABLE}.overhead_cost ;;
  }

  measure: total_cost_usd {
    value_format_name: usd
    type: number
    sql: ${aws_ec2_cost_usd} + ${aws_other_direct_cost_usd} + ${databricks_cost_usd} + ${overhead_cost_usd}  ;;
  }

  set: detail {
    fields: [
        cluster_name,
        run_id,
        databricks_cost_usd,
        team_name,
        task,
        db_name,
        source_name,
        source,
        dt,
        aws_ec2_cost_usd,
        aws_other_direct_cost_usd,
        overhead_cost_usd
    ]
  }
}
