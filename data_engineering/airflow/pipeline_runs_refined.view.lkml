include: "pipeline_runs.view"

view: +pipeline_runs {
  suggestions: yes

  ################################################
  # Primary keys {
  ################################################
  dimension: pk1_uuid {
    hidden: yes
    primary_key: yes
    sql: ${uid} ;;
  }
  dimension: pk2_full_name {
    hidden: yes
    sql: ${fq_table_name} ;;
  }
  dimension: pk2_dt {
    hidden: yes
    sql: ${dt_raw} ;;
  }
  ################################################
  # Primary keys }
  ################################################

  ################################################
  # Sets {
  ################################################
  set: daily_metrics {
    fields: [
      pipeline_runs_per_day
      , rows_inserted_per_day
      , rows_read_per_day
      , rows_updated_per_day
    ]
  }
  ################################################
  # Sets }
  ################################################

  ################################################
  # Dimensions {
  ################################################
  parameter: timeframe_picker {
    label: "Date Granularity"
    type: string
    allowed_value: { value: "Date" }
    allowed_value: { value: "Week" }
    allowed_value: { value: "Month" }
    default_value: "Week"
  }

  dimension: dynamic_timeframe {
    type: string
    sql:
    CASE
      WHEN {% parameter timeframe_picker %} = 'Date' THEN ${dt_date}
      WHEN {% parameter timeframe_picker %} = 'Week' THEN ${dt_week}
      WHEN {% parameter timeframe_picker %} = 'Month' THEN ${dt_month}
    END ;;
  }

  filter: in_de_dlth {
    label: "In DE DLTH"
    description: "Yes if the table is under Data Engineering's scope and control"
    type: yesno
    sql: ${dlth_de} = 1 ;;
  }
  dimension: dlth_de { hidden: yes }

  filter: in_xendit_dlth {
    label: "In Xendit DLTH"
    description: "Yes if the table is included in overall data platform health"
    type: yesno
    sql: ${dlth_xendit} = 1 ;;
  }
  dimension: dlth_xendit { hidden: yes }

  dimension: databricks_url_path {
    hidden: yes
    type: string
    sql: replace(${fq_table_name}, '.', '/') ;;
  }

  dimension_group: dt { label: "UTC" }

  dimension: failed_tests_per_day { hidden: yes }

  dimension: fq_table_name {
    label: "Full Table Name"
    sql: replace(${TABLE}.fq_table_name, ' ', '') ;;
    link: {
      label: "View table in Databricks"
      url: "https://xendit-prod.cloud.databricks.com/sql/explore/data/main/{{ databricks_url_path._value }}"
    }
    link: {
      label: "View table in OpenMetadata"
      url: "https://openmetadata-prod-live.de.tidnex.com/table/databricks-prod.main.{{ value }}"
    }
  }

  dimension_group: first_successful_job {
    label: "First Successful Run End"
    description: "The finish time of the first successful run for this day. Can be used to see when the table was available for query in Lakehouse"
    timeframes: [raw, time]
  }

  dimension: issue_type {
    type: string
    sql: CASE
      WHEN not ${meet_dqc_slo} THEN 'DQC Failure'
      WHEN not ${pipeline_status} THEN 'Pipeline Failure'
      WHEN not ${meet_freshness_slo} THEN 'Freshness Failure'
      WHEN ${meet_slo} THEN null
      ELSE 'Other'
      END;;
  }

  dimension_group: last_successful_job {
    label: "Last Successful Run End"
    description: "The finish time of the last successful run for this day. Can be used to see when the table was available for query in Lakehouse"
    timeframes: [raw, time]
  }

  dimension: meet_dqc_slo {
    label: "Meets DQC SLO"
    type: yesno
    sql: ${TABLE}.meet_dqc_slo = 1 ;;
  }

  dimension: meet_freshness_slo {
    label: "Meets Freshness SLO"
    type: yesno
    sql: ${TABLE}.meet_freshness_slo = 1 ;;
  }

  dimension: meet_slo {
    label: "Meets Overall SLO"
    type: yesno
    sql: ${TABLE}.meet_slo = 1 ;;
  }

  dimension: omd_link { hidden: yes }

  dimension: pipeline_status {
    label: "Pipeline Completed"
    description: "Whether the pipeline load completed, irrespective of the status of data quality checks"
  }

  dimension: pipeline_duration {
    group_label: "Pipeline Run Time (Minutes)"
    group_item_label: "Actual Pipeline Run Time"
    value_format_name: decimal_2
    sql: ${TABLE}.pipeline_duration / 60 ;;
  }
  dimension: p50_duration_30_days {
    group_label: "Pipeline Run Time (Minutes)"
    value_format_name: decimal_0
    sql: ${TABLE}.p50_duration_30_days / 60 ;;
  }
  dimension: p90_duration_30_days {
    group_label: "Pipeline Run Time (Minutes)"
    value_format_name: decimal_0
    sql: ${TABLE}.p90_duration_30_days / 60 ;;
  }
  dimension: p99_duration_30_days {
    group_label: "Pipeline Run Time (Minutes)"
    value_format_name: decimal_0
    sql: ${TABLE}.p99_duration_30_days / 60 ;;
  }

  dimension: pipeline_type { label: "Table Type" }

  dimension: row { hidden: yes }

  dimension_group: run_end { hidden: yes }

  dimension_group: run_start {
    label: "First Attempted Run Start"
    timeframes: [raw, time, date]
  }

  dimension_group: since_successful_run {
    type: duration
    intervals: [minute, hour, day]
    sql_start: ${last_successful_job_raw} ;;
    sql_end: current_timestamp ;;
  }

  dimension: successful_tests_per_day { hidden: yes }

  dimension: table_version {
    type:  string
    sql:
    CASE
      WHEN date_trunc('day', ${run_start_raw} ) = date_trunc('day', current_timestamp ) THEN 'Current'
      WHEN date_trunc('day', ${run_start_raw} ) = date_trunc('day', current_timestamp ) - interval '1' day THEN 'Yesterday'
      ELSE NULL
    END;;
  }

  dimension: uid { hidden: yes }
  ################################################
  # Dimensions }
  ################################################

  ################################################
  # Measures {
  ################################################
  measure: count {
    label: "Count Table Runs"
  }
  measure: count_successfully_updated {
    type: count
    filters: [ meet_slo: "Yes" ]
  }
  measure: success_rate {
    type: number
    value_format_name: percent_2
    sql: ${count_successfully_updated} / cast(${count} as double) ;;
  }
  measure: count_failed_tests {
    type: sum
    sql: ${failed_tests_per_day} ;;
  }
  measure: count_successful_tests {
    type: sum
    sql: ${successful_tests_per_day} ;;
  }
  measure: average_duration {
    label: "Average Pipeline Run Time (Minutes)"
    type: average
    sql: ${pipeline_duration} ;;
  }
  ################################################
  # Measures }
  ################################################
}
