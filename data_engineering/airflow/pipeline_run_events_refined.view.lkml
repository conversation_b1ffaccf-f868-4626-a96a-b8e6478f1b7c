# @TODO add in attempt number by day as a window function

include: "pipeline_run_events.view"

view: +pipeline_run_events {
  ################################################
  # Primary keys {
  ################################################
  dimension: pk1_uuid {
    hidden: yes
    sql: ${id} ;;
  }
  dimension: pk2_full_name {
    hidden: yes
    sql: ${fq_table_name} ;;
  }
  dimension: pk2_run_start {
    hidden: yes
    sql: ${run_start_raw} ;;
  }
  ################################################
  # Primary keys }
  ################################################

  ################################################
  # Sets {
  ################################################
  set: primary_keys {
    fields: [pk1_uuid, pk2_full_name, pk2_run_start]
  }
  set: run_data {
    fields: [
      id
      , run_start_raw
      , run_start_time
      , run_start_date
      , run_end_raw
      , run_end_time
      , run_end_date
      , allow_auto_recovery
      , in_auto_recovery
      , pipeline_status
      , pipeline_durations*
      , exception
      , exception_message
      , rows_read
      , rows_inserted
      , rows_updated
    ]
  }
  set: run_metrics {
    fields: [
      , total_rows_read
      , average_rows_read
      , total_rows_inserted
      , average_rows_inserted
      , total_rows_updated
      , average_rows_updated
      , p50_minutes_pipeline
      , p90_minutes_pipeline
      , p95_minutes_pipeline
      , count
    ]
  }
  set: pipeline_durations {
    fields: [
      minutes_pipeline
      , minute10s_pipeline
      , minute30s_pipeline
      , hours_pipeline
    ]
  }
  ################################################
  # Sets }
  ################################################

  ################################################
  # Dimensions {
  ################################################
  dimension_group: pipeline {
    label: "Of Pipeline Run"
    type: duration
    timeframes: [minute, minute10, minute30, hour]
    sql_start: ${run_start_raw} ;;
    sql_end: ${run_end_raw} ;;
  }
  dimension: pipeline_status {
    type: string
    sql: case
      when ${TABLE}.pipeline_status then 'COMPLETED'
      when ${exception} is not null then 'FAILED'
    else null end ;;
  }
  ################################################
  # Dimensions }
  ################################################

  ################################################
  # Measures {
  ################################################
  measure: count_failed {
    type: count
    filters: [exception: "-NULL"]
  }
  measure: count_completed {
    type: count
    filters: [pipeline_status: "COMPLETED"]
  }
  measure: total_rows_read {
    type: sum
    sql: ${rows_read} ;;
  }
  measure: average_rows_read {
    type: average
    sql: ${rows_read} ;;
  }
  measure: total_rows_updated {
    type: sum
    sql: ${rows_updated} ;;
  }
  measure: average_rows_updated {
    type: average
    sql: ${rows_updated} ;;
  }
  measure: average_minutes_pipeline {
    label: "Average Duration Of Pipeline Run (Minutes)"
    type: average
    sql: ${minutes_pipeline} ;;
  }
  measure: p50_minutes_pipeline {
    label: "P50 Duration Of Pipeline Run (Minutes)"
    type: percentile
    percentile: 50
    sql: ${minutes_pipeline} ;;
  }
  measure: p90_minutes_pipeline {
    label: "P90 Duration Of Pipeline Run (Minutes)"
    type: percentile
    percentile: 90
    sql: ${minutes_pipeline} ;;
  }
  measure: p95_minutes_pipeline {
    label: "P95 Duration Of Pipeline Run (Minutes)"
    type: percentile
    percentile: 95
    sql: ${minutes_pipeline} ;;
  }
  ################################################
  # Measures}
  ################################################
}
