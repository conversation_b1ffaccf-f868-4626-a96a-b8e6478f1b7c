view: query_time_employee_errors {
  derived_table: {
    sql: SELECT
  trino.dt AS query_created,
  trino.createtime AS queryCreatedTime,
  employees.id as `employees.id`,
  employees.name as `employees.name`,
  LOW<PERSON>(employees.email) AS `employees.email`,
  teams.name as `teams.name`,
  roles.name as `roles.name`,
  person_teams.created_at AS role_created_at,
  unix_timestamp(endTime) - unix_timestamp(executionStartTime) AS query_execution_time,
  trino.metadata.queryId AS query_id,
  trino.context.queryType AS query_type,
  trino.context.source AS source,
  trino.statistics.peakTaskTotalMemory AS peakTaskTotalMemory,
  trino.statistics.cpuTime AS cpuTime_seconds,
  trino.statistics.wallTime AS wallTime,
  trino.statistics.queuedTime AS queuedTime,
  trino.completed_metadata.queryState AS queryState,
  -- trino.failureinfo.errorcode.name AS error_name,
  -- trino.failureinfo.errorcode.type AS error_type,
  -- trino.failureinfo.failureType AS failure_type,
  SUBSTRING(
    trino.metadata.query,
    position('}' IN trino.metadata.query) + 1,
    length(trino.metadata.query)
  ) AS query
FROM
  clean__engr_ops_prod.persons AS employees
  INNER JOIN(
    SELECT
      *
    FROM
      clean__engr_ops_prod.person_teams n
    WHERE
      created_at =(
        SELECT
          MAX(created_at)
        FROM
          clean__engr_ops_prod.person_teams
        WHERE
          person_id = n.person_id
      )
  ) AS person_teams ON employees.id = person_teams.person_id
  LEFT JOIN clean__engr_ops_prod.teams AS teams ON person_teams.team_id = teams.id
  LEFT JOIN clean__engr_ops_prod.ptrs AS ptrs ON person_teams.id = ptrs.person_team_id
  LEFT JOIN clean__engr_ops_prod.roles AS roles ON ptrs.role_id = roles.id
  LEFT JOIN transform__presto.executed_queries_detailed AS trino ON LOWER(trino.user_email) = LOWER(employees.email)
GROUP BY
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  10,
  11,
  12,
  13,
  14,
  15,
  16,
  17,
  18 --19,
  -- 20,
  -- 21
ORDER BY
  2 DESC
      ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }



  dimension_group: query_created {
    label: "Query Created"
    type: time
    timeframes: [
      raw,
      minute15,
      hour,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    description: "Represents the date the query was created on"
    datatype: date
    sql: ${TABLE}.query_created ;;
  }

  dimension_group: queryCreatedTime {
    label: "Query Created Time"
    type: time
    timeframes: [
      raw,
      minute15,
      hour,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    description: "Represents the datetime the query was created on"
    datatype: timestamp
    sql: ${TABLE}.queryCreatedTime ;;
  }


  parameter: timeframe_picker {
    label: "Date Granularity"
    type: string
    allowed_value: { value: "15 Mins" }
    allowed_value: { value: "Hour" }
    allowed_value: { value: "Date" }
    allowed_value: { value: "Week" }
    allowed_value: { value: "Month" }
    allowed_value: { value: "Quarter" }
    default_value: "Week"
  }

  dimension: dynamic_timeframe {
    type: string
    sql:
    CASE
      WHEN {% parameter timeframe_picker %} = '15 Mins' THEN ${query_time_employee_errors.queryCreatedTime_minute15}
      WHEN {% parameter timeframe_picker %} = 'Hour' THEN ${query_time_employee_errors.queryCreatedTime_hour}
      WHEN {% parameter timeframe_picker %} = 'Date' THEN ${query_time_employee_errors.queryCreatedTime_date}
      WHEN {% parameter timeframe_picker %} = 'Week' THEN ${query_time_employee_errors.queryCreatedTime_week}
      WHEN {% parameter timeframe_picker %} = 'Month' THEN ${query_time_employee_errors.queryCreatedTime_month}
      WHEN {% parameter timeframe_picker %} = 'Quarter' THEN ${query_time_employee_errors.queryCreatedTime_quarter}
    END ;;
    drill_fields: [teams_name, employees_email]
  }

  # dimension: error_name_groups {
  #   type: string
  #   sql: CASE ${TABLE}.error_name
  #           WHEN 'ADMINISTRATIVELY_KILLED' THEN 'ADMINISTRATIVELY_KILLED'
  #           WHEN 'HIVE_METASTORE_ERROR' THEN 'HIVE_ERROR'
  #           WHEN 'HIVE_UNKNOWN_ERROR' THEN 'HIVE_ERROR'
  #           WHEN 'HIVE_FILESYSTEM_ERROR' THEN 'HIVE_ERROR'
  #           WHEN 'HIVE_BAD_DATA' THEN 'HIVE_ERROR'
  #           WHEN 'HIVE_CANNOT_OPEN_SPLIT' THEN 'HIVE_ERROR'
  #           WHEN 'HIVE_PARTITION_DROPPED_DURING_QUERY' THEN 'HIVE_ERROR'

  #           WHEN 'TABLE_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'COLUMN_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'FUNCTION_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'OPERATOR_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'SCHEMA_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'TYPE_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'
  #           WHEN 'CATALOG_NOT_FOUND' THEN 'OBJECT_NOT_FOUND'

  #           WHEN 'REMOTE_TASK_MISMATCH' THEN 'TRINO_OVERLOAD'
  #           WHEN 'TOO_MANY_REQUESTS_FAILED' THEN 'TRINO_OVERLOAD'
  #           WHEN 'REMOTE_TASK_ERROR' THEN 'TRINO_OVERLOAD'
  #           WHEN 'REMOTE_HOST_GONE' THEN 'TRINO_OVERLOAD'
  #           WHEN 'NO_NODES_AVAILABLE' THEN 'TRINO_OVERLOAD'
  #           ELSE 'OTHER'
  #       END;;
  #   drill_fields: [error_name]

  # }

  dimension: employees_id {
    type: string
    sql: ${TABLE}.employees.id ;;
  }

  dimension: employees_name {
    type: string
    sql: ${TABLE}.employees.name ;;

  }

  dimension: employees_email {
    type: string
    sql: ${TABLE}.employees.email ;;
    drill_fields: [statistics_wallTime_bucketed]
  }

  dimension: teams_name {
    type: string
    sql: ${TABLE}.teams.name ;;
    drill_fields: [employees_email]
  }

  dimension: roles_name {
    type: string
    sql: ${TABLE}.roles.name ;;
  }

  dimension_group: role_created_at {
    type: time
    sql: ${TABLE}.role_created_at ;;
  }

  dimension: query_execution_time {
    type: number
    sql: ${TABLE}.query_execution_time ;;
  }

  dimension: query_id {
    type: string
    sql: ${TABLE}.query_id ;;
  }

  dimension: source {
    type: string
    sql: ${TABLE}.source ;;
  }

  dimension: query_type {
    type: string
    sql: ${TABLE}.query_type ;;
  }

  dimension: peakTaskTotalMemory {
    type: number
    sql: ${TABLE}.peakTaskTotalMemory ;;
  }

  dimension: cpuTime_seconds {
    type: number
    sql: ${TABLE}.cpuTime.seconds ;;
  }

  dimension: wall_time {
    type: number
    sql: ${TABLE}.wallTime ;;
  }

  dimension: queued_time {
    type: number
    sql: ${TABLE}.queuedTime ;;
  }

  dimension: query_state {
    type: string
    sql: ${TABLE}.queryState ;;
  }


  dimension: error_name {
    type: string
    sql: ${TABLE}.error_name ;;
    drill_fields: [teams_name]
  }

  dimension: error_type {
    type: string
    sql: ${TABLE}.error_type ;;
    drill_fields: [error_name]
  }

  dimension: failure_type {
    type: string
    sql: ${TABLE}.failure_type ;;
  }

  dimension: query {
    type: string
    sql: ${TABLE}.query ;;
  }


  dimension: statistics_wallTime_bucketed {
    type: tier
    label: "Wall Time Seconds - Bucketed"
    tiers: [1,15, 30,60,120,180,240,300]
    style: integer
    group_label: "Classes"
    sql: ${TABLE}.wallTime ;;
    drill_fields: [teams_name, employees_email]
  }


  measure: average_peakTaskTotalMemory {
    type: average
    description: "Average peakTaskTotalMemory"
    sql: ${TABLE}.peakTaskTotalMemory ;;
    drill_fields: [detail*]
  }

  measure: average_cpuTime {
    type: average
    description: "Average cpuTime"
    sql: ${TABLE}.cpuTime.seconds ;;
    drill_fields: [detail*]
  }

  measure: average_walltime {
    type: average
    description: "Average Wall Time"
    sql: ${TABLE}.wallTime ;;
    drill_fields: [detail*]
  }

  measure: wall_time_75th_percentile {
    type: percentile
    label: "Wall Time Seconds 75th Percentile"
    percentile: 75
    sql: ${TABLE}.wallTime ;;
    drill_fields: [detail*]
  }
  measure: wall_time_90th_percentile {
    type: percentile
    label: "Wall Time Seconds 90th Percentile"
    description: "Wall time represents human perceived time for the query"
    percentile: 90
    sql: ${TABLE}.wallTime ;;
    drill_fields: [detail*]
  }
  measure: median_wall_time {
    type: median
    description: "Wall time represents human perceived time for the query"
    sql: ${TABLE}.wallTime ;;
    drill_fields: [detail*]

  }

  measure: queued_time_75th_percentile {
    type: percentile
    label: "Queued Time Seconds 75th Percentile"
    percentile: 75
    sql: ${TABLE}.queuedTime ;;
    drill_fields: [detail*]
  }
  measure: queued_time_90th_percentile {
    type: percentile
    label: "Queued Time Seconds 90th Percentile"
    percentile: 90
    sql: ${TABLE}.queuedTime ;;
    drill_fields: [detail*]
  }
  measure: median_queued_time {
    type: median
    sql: ${TABLE}.queuedTime ;;
    drill_fields: [detail*]
  }

  measure: average_query_execution {
    type: average
    description: "Average Query Execution Time"
    sql: ${TABLE}.query_execution_time ;;
    drill_fields: [detail*]

  }
  measure: query_execution_90th_percentile {
    type: percentile
    label: "Query Execution Time Seconds 90th Percentile"
    percentile: 90
    sql: ${TABLE}.query_execution_time ;;
    drill_fields: [detail*]
  }

  measure: count__failed_queries {
    type: count
    drill_fields: [detail*]
    filters: [
      query_state: "FAILED"
      ]
  }

  measure: count__successful_queries {
    type: count
    drill_fields: [detail*]
    filters: [
      query_state: "FINISHED"
    ]
  }

  measure: ratio__successful_queries {
    type: number
    drill_fields: [detail*]
    value_format: "0.00\%"
    sql: ${count__successful_queries} / cast(${count} as double) * 100 ;;
  }

  measure: ratio__failed_queries {
    type: number
    drill_fields: [detail*]
    value_format: "0.00\%"
    sql: ${count__failed_queries} / cast(${count} as double) * 100 ;;
  }


  set: detail {
    fields: [
      queryCreatedTime_date,
      queryCreatedTime_hour,
      employees_id,
      employees_name,
      employees_email,
      teams_name,
      roles_name,
      role_created_at_time,
      query_execution_time,
      queued_time,
      query_id,
      query_type,
      wall_time,
      peakTaskTotalMemory,
      cpuTime_seconds,
      queued_time,
      error_name,
      error_type,
      query
    ]
  }
}
