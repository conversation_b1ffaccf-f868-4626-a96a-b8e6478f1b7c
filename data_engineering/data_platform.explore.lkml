# Notes
# Total pipelines here are only ~75 / ~200. Need to investigate why the others are missing
# Linkage of costs to pipelines is poor (~1/3) as we don't have a UUID to link on. Databricks job runs != Airflow task instances
# Queries are more complex than necessary as table structures are many-many. Refactor required long term
# Naming convention for tables should be standardised into catalog.schema.table as the mix of nomenclature with/without catalog causes potential for many-many joins
# @TODO expand to show error rates and costs in query explore
# @TODO refine pipeline run explore to allow links to specific task ids and databricks job ids
# @TODO create NDT from pipeline run explore to pull in latest pipeline status into data assets explore
# @TODO check for missing data in DQC results, esp vs BI's dbt results table

include: "./openmetadata/data_platform_assets.view"
include: "./openmetadata/table_entity.view"
include: "./openmetadata/database_schema_entity.view"
include: "./openmetadata/database_entity.view"
include: "./openmetadata/pipeline_entity.view"
include: "./openmetadata/dashboard_entity.view"
include: "./de_metrics/query_counts_by_table_aggregated.view"
include: "./airflow/pipeline_run_costs.view"
include: "./data_quality/data_quality_summary.view"

include: "./airflow/pipeline_runs_refined.view"
include: "./airflow/pipeline_run_events_refined.view"
include: "./de_metrics/query_counts_by_table.view"
include: "./data_quality/data_quality_check_results.view"

explore: data_platform_assets {
  group_label: "Data Engineering"
  fields: [ALL_FIELDS*, -data_platform_assets*]
  label: "Data Platform content"
  description: "The current state of tables, dashboards, pipelines and other data assets that make up the data platform"
  join: table_entity {
    view_label: "Tables"
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_table_id} = ${table_entity.pk1_id};;
  }
  join: database_schema_entity {
    view_label: "Tables"
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_schema_id} = ${database_schema_entity.pk1_id} ;;
  }
  join: database_entity {
    view_label: "Tables"
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_database_id} = ${database_entity.pk1_id} ;;
  }
  join: pipeline_entity {
    view_label: "Airflow Pipelines"
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_pipeline_id} = ${pipeline_entity.pk1_id} ;;
  }
  join: dashboard_entity {
    view_label: "Dashboards"
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_dashboard_id} = ${dashboard_entity.pk1_id} ;;
  }
  # join: dbt_models { TBC }
  # join: lookml_models { TBC }
  # join: summary pipeline status { }
  join: query_counts_by_table_aggregated {
    view_label: "Table Query Counts"
    type: left_outer
    relationship: many_to_one
    sql_on: ${table_entity.full_name} = ${query_counts_by_table_aggregated.full_name} ;;
  }
  join: pipeline_run_costs {
    view_label: "Pipeline Run Costs (By Schema)"
    type: full_outer
    relationship: one_to_many
    sql_on: ${database_schema_entity.name} = ${pipeline_run_costs.schema_name} ;;
  }
  join: data_quality_summary {
    type: left_outer
    relationship: many_to_many
    sql_on: ${table_entity.full_name} = ${data_quality_summary.fq_table_name} ;;
  }
}

explore: query_counts_by_table {
  group_label: "Data Engineering"
  label: "Data Platform Queries"
  description: "The activities that happen on data platform content. Starts from a grain of by day by table"
  # join: errors { TBC }
  # join: costs { TBC }
}

explore: pipeline_runs {
  group_label: "Data Engineering"
  label: "Data Pipeline Runs"
  description: "The activities on Airflow data pipelines. Starts from a grain of by run by table by attempt"
  view_label: "Pipeline Runs By Table By Day"
  fields: [
    pipeline_run_events.primary_keys*, pipeline_run_events.run_data*, pipeline_run_events.run_metrics*, pipeline_run_events.dt_raw
    , pipeline_runs*, -pipeline_runs.daily_metrics*, -pipeline_runs.exception, -pipeline_runs.exception_message
    , data_quality_check_results.test_details*, data_quality_check_results.metrics*
    , pipeline_entity*
  ]
  join: pipeline_run_events {
    type: left_outer
    relationship: one_to_many
    sql_on: ${pipeline_runs.pk2_full_name} = ${pipeline_run_events.pk2_full_name} and ${pipeline_runs.pk2_dt} = ${pipeline_run_events.dt_raw} ;;
  }
  join: data_quality_check_results {
    type: left_outer
    relationship: one_to_many
    sql_on: ${pipeline_runs.pk2_full_name} = ${data_quality_check_results.full_table_name} and ${pipeline_runs.pk2_dt} = ${data_quality_check_results.dt_raw} ;;
  }
  join: data_platform_assets {
    fields: []
    type: left_outer
    sql_on: ${pipeline_runs.pk2_full_name} = ${data_platform_assets.fk_fqtablename} ;;
    relationship: many_to_many
  }
  join: pipeline_entity {
    view_label: "Airflow Pipelines"
    fields: [pipeline_entity.name, pipeline_entity.type]
    type: left_outer
    relationship: many_to_one
    sql_on: ${data_platform_assets.omd_pipeline_id} = ${pipeline_entity.pk1_id};;
  }
}
