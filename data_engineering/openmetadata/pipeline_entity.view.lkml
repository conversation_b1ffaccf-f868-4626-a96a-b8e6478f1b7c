include: "generic_omd_entity.view"

view: pipeline_entity {
  extends: [generic_omd_entity]
  sql_table_name: clean__openmetadata_db.pipeline_entity ;;

  dimension: url_path {
    hidden: yes
    type: string
    sql: coalesce(get_json_object(${json}, '$.sourceUrl'),null) ;;
  }
  dimension: name {
    link: {
      label: "View pipeline in Airflow"
      url: "{{ url_path._value }}"
    }
    link: {
      label: "View in Pipeline Run Explore"
      url: "{% if table_entity.full_name._in_query %}
        /explore/{{ _model._name }}/pipeline_runs?fields=pipeline_runs.fq_table_name,pipeline_runs.dt_date,pipeline_runs.last_successful_job_time,pipeline_runs.meet_dqc_slo,pipeline_runs.meet_freshness_slo,pipeline_run_events.exception,pipeline_run_events.count,pipeline_runs.average_duration,pipeline_runs.count_failed_tests,pipeline_runs.count_successful_tests,data_quality_check_results.count&f[pipeline_runs.fq_table_name]={{ _filters['table_entity.full_name'] | url_encode }}&f[pipeline_runs.dt_date]=14+days&f[pipeline_entity.name]={{ value }}&sorts=pipeline_runs.dt_date+desc
      {% else %}
        /explore/{{ _model._name }}/pipeline_runs?fields=pipeline_runs.fq_table_name,pipeline_runs.dt_date,pipeline_runs.last_successful_job_time,pipeline_runs.meet_dqc_slo,pipeline_runs.meet_freshness_slo,pipeline_run_events.exception,pipeline_run_events.count,pipeline_runs.average_duration,pipeline_runs.count_failed_tests,pipeline_runs.count_successful_tests,data_quality_check_results.count&f[pipeline_runs.dt_date]=14+days&f[pipeline_entity.name]={{ value }}&sorts=pipeline_runs.dt_date+desc
      {% endif %}"
    }
  }
  dimension: type {
    sql: case
      when ${name} like '%_replication_%' then 'batch_replication'
      when ${name} like '%_source_dqc_%' then 'source_data_quality_check'
      when ${name} like '%_transformation_%' then 'batch_transformation'
      else 'unclassified' end ;;
  }
}
