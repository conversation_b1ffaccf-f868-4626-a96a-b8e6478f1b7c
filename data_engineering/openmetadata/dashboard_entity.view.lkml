include: "generic_omd_entity.view"

view: dashboard_entity {
  extends: [generic_omd_entity]
  sql_table_name: clean__openmetadata_db.dashboard_entity ;;

  dimension: url_path {
    hidden: yes
    type: string
    sql: coalesce(get_json_object(${json}, '$.sourceUrl'),null) ;;
  }
  dimension: name {
    sql: coalesce(coalesce(get_json_object(${json}, '$.displayName'),null), ${TABLE}.name) ;;
    link: {
      label: "View dashboard in Looker/Databricks"
      url: "{{ url_path._value }}"
    }
  }
}
