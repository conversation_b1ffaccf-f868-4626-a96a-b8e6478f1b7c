view: generic_omd_entity {
  extension: required

################################################
# Primary keys {
################################################
  dimension: pk1_id {
    hidden: yes
    sql: ${id};;
  }
################################################
# Primary keys }
################################################

################################################
# Sets {
################################################
  set: detail {
    fields: [id, name, deleted, json]
  }
################################################
# Sets }
################################################

################################################
# Dimensions {
################################################
  dimension: fqnhash {
    hidden: yes
    description: "Fully qualified name hash. An internal marker for OpenMetadata to determine if there are changes to this entity"
    type: string
    sql: ${TABLE}.fqnhash ;;
  }
  dimension: id {
    description: "The unique OpenMetadata entity id for this data asset"
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: is_deleted {
    description: "Whether the entity has been soft deleted from OpenMetadata"
    type: yesno
    sql: ${TABLE}.deleted ;;
  }
  dimension: json {
    label: "Detailed Metadata"
    description: "Detailed content pertaining to the entity. Schemas will vary between entity types OpenMetadata"
    type: string
    sql: ${TABLE}.json ;;
  }
  dimension: name {
    description: "The name of this data asset. Typically has a link back to the asset in OMD"
    type: string
    sql: ${TABLE}.name ;;
  }
  dimension_group: metadata_updated {
    description: "When the entity was last loaded into OpenMetadata"
    type: time
    datatype: epoch
    timeframes: [date, time]
    sql: ${TABLE}.updatedat / 1000 ;;
  }
  dimension: updatedby {
    label: "Metadata Updated By"
    description: "Who last updated the entity in OpenMetadata, typically a bot"
    type: string
    sql: ${TABLE}.updatedby ;;
  }
################################################
# Dimensions }
################################################

################################################
# Measures {
################################################
  measure: count {
    type: count
    drill_fields: [detail*]
  }
################################################
# Measures }
################################################
}
