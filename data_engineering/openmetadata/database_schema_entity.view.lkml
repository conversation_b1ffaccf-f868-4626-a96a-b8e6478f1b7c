include: "generic_omd_entity.view"

view: database_schema_entity {
  extends: [generic_omd_entity]
  sql_table_name: clean__openmetadata_db.database_schema_entity ;;
  fields_hidden_by_default: yes

  dimension: name {
    hidden: no
    label: "Schema Name"
    link: {
      label: "View schema in Databricks"
      url: "https://xendit-prod.cloud.databricks.com/sql/explore/data/{{ databricks_url_path._value }}"
    }
    link: {
      label: "View schema in OpenMetadata"
      url: "https://openmetadata-prod-live.de.tidnex.com/databaseSchema/databricks-prod.{{ database_entity.name._value }}.{{ value }}"
    }
  }

  dimension: databricks_url_path {
    hidden: yes
    type: string
    sql: coalesce(replace(replace(get_json_object(${json}, '$.fullyQualifiedName'), 'databricks-prod.', ''), '.', '/'),null) ;;
  }
}
