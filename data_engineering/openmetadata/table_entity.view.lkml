include: "generic_omd_entity.view"

view: table_entity {
  extends: [generic_omd_entity]
  sql_table_name: clean__openmetadata_db.table_entity ;;

  dimension: pk1_fqname {
    hidden: yes
    sql: concat_ws('.', ${database_entity.name}, ${full_name}) ;;
  }

  dimension: databricks_url_path {
    hidden: yes
    type: string
    sql: coalesce(replace(replace(get_json_object(${json}, '$.fullyQualifiedName'), 'databricks-prod.', ''), '.', '/'),null) ;;
  }
  dimension: full_name {
    type: string
    # sql:  coalesce(concat_ws('.', ${database_entity.name}, ${database_schema_entity.name}, ${name}),'Unavailable') ;;
    sql:  coalesce(concat_ws('.', ${database_schema_entity.name}, ${name}),'Unavailable') ;;
    link: {
      label: "View table in Databricks"
      url: "https://xendit-prod.cloud.databricks.com/sql/explore/data/{{ databricks_url_path._value }}"
    }
    link: {
      label: "View table in OpenMetadata"
      url: "https://openmetadata-prod-live.de.tidnex.com/table/databricks-prod.{{ pk1_fqname._value }}"
    }
  }
  dimension: name {
    link: {
      label: "View table in Databricks"
      url: "https://xendit-prod.cloud.databricks.com/sql/explore/data/{{ databricks_url_path._value }}"
    }
    link: {
      label: "View table in OpenMetadata"
      url: "https://openmetadata-prod-live.de.tidnex.com/table/databricks-prod.{{ pk1_fqname._value }}"
    }
  }
  dimension: last_updated_text {
    hidden: yes
    type: string
    sql: coalesce(get_json_object(${json}, '$.extension.pipelineRunFinishedLatest'),null) ;;
  }
  dimension_group: last_updated {
    description: "The last time this table was updated with new data"
    type: time
    timeframes: [raw, time, date, week, month]
    sql: to_utc_timestamp(concat(
      cast(regexp_extract(${last_updated_text}, '\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}') as timestamp),' '
      , regexp_extract(${last_updated_text}, '\((.*)\)', 1)
    ),"UTC");;
  }
  dimension: is_statically_archived {
    # List of tables that are under static archival
    type: yesno
    sql: ${full_name} in (
        'clean__tcdb.t3_transactions',
        'clean__tcdb.t3_lines',
        'clean__tcdb.migrations',
        'clean__tcdb.ledger_transfer_map',
        'clean__tcdb.ledger_account_map',
        'clean__tcdb.deposits',
        'clean__xendit_reconciliation_service_live.billing_rates',
        'clean__xendit_reconciliation_service_live.bank_product_reconciliations',
        'clean__xendit_reconciliation_service_live.bankstatementrows',
        'clean__xendit_reconciliation_service_live.billing_transaction_statistics',
        'clean__xendit_reconciliation_service_live.billing_results',
        'clean__xendit_reconciliation_service_live.bankstatements',
        'clean__xendit_reconciliation_service_live.reconciliation_modification_requests'
      ) ;;
  }
  dimension_group: since_last_update {
    type: duration
    intervals: [minute, hour, day]
    sql_start: ${last_updated_raw} ;;
    sql_end: current_timestamp ;;
  }

  dimension: data_quality_check_status {
    description: "Yesterday's data quality check result, if available"
    type: string
    sql: {% if _explore._name == 'data_platform_assets' %}
      case
        when ${data_quality_summary.is_dqc_failed} then 'Failed'
        when not ${data_quality_summary.is_dqc_failed} then 'Passed'
        else 'Not tested' end
    {% else %}
      'Not available in this explore'
    {% endif %} ;;
  }
}
