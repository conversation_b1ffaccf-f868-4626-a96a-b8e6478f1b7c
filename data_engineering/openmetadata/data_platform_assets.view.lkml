view: data_platform_assets {
  derived_table: {
    datagroup_trigger: global__start_of_day_jkt
    sql: select
      dbe.id as omd_database_id
      , se.id as omd_schema_id
      , te.id as omd_table_id
      , coalesce(pe.id, pe2.id) as omd_pipeline_id
      , er3.toid as omd_dashboard_datamodel_id
      , de.id as omd_dashboard_id
      , se.name as schema_name
      , te.name as table_name
    from clean__openmetadata_db.table_entity te
    left join clean__openmetadata_db.entity_relationship er1 on er1.toid = te.id and er1.fromentity = 'databaseSchema' and not er1.deleted
    left join clean__openmetadata_db.database_schema_entity se on er1.fromid = se.id
    left join clean__openmetadata_db.entity_relationship er1b on er1b.toid = se.id and er1b.fromentity = 'database' and not er1b.deleted
    left join clean__openmetadata_db.database_entity dbe on er1b.fromid = dbe.id
    left join clean__openmetadata_db.entity_relationship er2 on er2.toid = te.id and er2.fromentity = 'pipeline' and not er2.deleted
    left join clean__openmetadata_db.pipeline_entity pe on er2.toid = te.id and er2.fromid = pe.id
    left join clean__openmetadata_db.entity_relationship er3 on er3.fromid = te.id and er3.toentity = 'dashboardDataModel' and not er3.deleted
    left join clean__openmetadata_db.entity_relationship er4 on er4.fromid = er3.toid and er4.toentity = 'dashboard' and not er4.deleted
    left join clean__openmetadata_db.dashboard_entity de on er4.toid = de.id
    left join (select
        toid
        , get_json_object(json, '$.pipeline.id') as pipeline_id
      from clean__openmetadata_db.entity_relationship
      where fromentity = 'table' and toentity = 'table' and not deleted
      group by 1,2
    ) er5 on er5.toid = te.id
    left join clean__openmetadata_db.pipeline_entity pe2 on er5.pipeline_id = pe2.id ;;
  }

  suggestions: no

  dimension: fk_fqtablename {
    hidden: yes
    sql: ${TABLE}.schema_name || '.' || ${TABLE}.table_name;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }
  measure: count_dashboards {
    type: count_distinct
    sql: ${omd_dashboard_id} ;;
    drill_fields: [detail*]
  }
  measure: count_tables {
    type: count_distinct
    sql: ${omd_table_id};;
    drill_fields: [detail*]
  }
  measure: count_schemas {
    type: count_distinct
    sql: ${omd_schema_id} ;;
    drill_fields: [detail*]
  }

  dimension: omd_database_id {
    hidden: yes
    type: string
    sql: ${TABLE}.omd_database_id ;;
  }
  dimension: omd_schema_id {
    hidden: yes
    type: string
    sql: ${TABLE}.omd_schema_id ;;
  }

  dimension: omd_table_id {
    hidden: yes
    type: string
    sql: ${TABLE}.omd_table_id ;;
  }

  dimension: omd_pipeline_id {
    hidden: yes
    type: string
    sql: ${TABLE}.omd_pipeline_id ;;
  }

  dimension: omd_dashboard_id {
    hidden: yes
    type: string
    sql: ${TABLE}.omd_dashboard_id ;;
  }

  set: detail {
    fields: [
        omd_schema_id,
        omd_table_id,
        omd_pipeline_id,
        omd_dashboard_id
    ]
  }
}
