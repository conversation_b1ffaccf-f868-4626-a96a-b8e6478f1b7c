include: "generic_omd_entity.view"

view: database_entity {
  extends: [generic_omd_entity]
  sql_table_name: clean__openmetadata_db.database_entity ;;
  fields_hidden_by_default: yes

  dimension: name {
    hidden: no
    label: "Database Name"
    link: {
      label: "View database in Databricks"
      url: "https://xendit-prod.cloud.databricks.com/sql/explore/data/{{ databricks_url_path._value }}"
    }
    link: {
      label: "View database in OpenMetadata"
      url: "https://openmetadata-prod-live.de.tidnex.com/database/databricks-prod.{{ value }}"
    }
  }
  dimension: databricks_url_path {
    hidden: yes
    type: string
    sql: coalesce(replace(replace(get_json_object(${json}, '$.fullyQualifiedName'), 'databricks-prod.', ''), '.', '/'),null) ;;
  }
}
