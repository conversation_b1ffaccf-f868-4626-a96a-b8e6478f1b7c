view: ndt_batch_pipeline_runs {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time

    sql:
        WITH pipeline_runs as (
          SELECT
          r.name as presto_table_name,
          r.started_at,
          r.completed_at,
          q.checked_at,
          first_value(q.status) over (partition by date(r.started_at), r.name order by q.checked_at desc) as data_quality_status

          FROM clean__de_events.data_pipeline_runs as r
          LEFT JOIN clean__de_events.quality_checks as q on (
            date(q.checked_at)=date(r.started_at) and r.name=q.name
          )
        )

        SELECT
          date(started_at) as started_date,
          presto_table_name,
          data_quality_status,
          max(started_at) as started_at,
          max(completed_at) as completed_at,
          max(checked_at) as checked_at

        FROM pipeline_runs
        WHERE checked_at is not null

        AND presto_table_name not like 'clean__tcdb%'

        GROUP BY 1,2,3;;


  }


  parameter: timeframe_picker {
    label: "Date Granularity"
    type: string
    allowed_value: { value: "Date" }
    allowed_value: { value: "Week" }
    allowed_value: { value: "Month" }
    default_value: "Week"
  }

  dimension: dynamic_timeframe {
    type: string
    sql:
    CASE
      WHEN {% parameter timeframe_picker %} = 'Date' THEN ${ndt_batch_pipeline_runs.started_at_date}
      WHEN {% parameter timeframe_picker %} = 'Week' THEN ${ndt_batch_pipeline_runs.started_at_week}
      WHEN {% parameter timeframe_picker %} = 'Month' THEN ${ndt_batch_pipeline_runs.started_at_month}
    END ;;
  }

  dimension: presto_table_name {
    type: string
    sql: ${TABLE}.presto_table_name ;;
  }

  dimension_group: started_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.started_at ;;
  }

  dimension_group: completed_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.completed_at ;;
  }

  dimension_group: checked_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.checked_at ;;
  }

  dimension: data_quality_status {
    type: string
    sql: ${TABLE}.data_quality_status ;;
  }

  measure: count {
    type: count
    drill_fields: [presto_table_name]
  }

  measure: count__success {
    type: sum
    drill_fields: [presto_table_name]
    sql:
    CASE
      WHEN ${TABLE}.data_quality_status  in ('success', 'sucess') THEN 1
      ELSE 0
    END ;;
  }

  measure: ratio__success {
    type: number
    drill_fields: [presto_table_name]
    value_format: "0.00\%"
    sql: ${count__success} / cast(${count} as double) * 100 ;;
  }
}
