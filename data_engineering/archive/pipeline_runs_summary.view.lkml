# The name of this view in Looker is "Pipeline Runs Summary"
view: pipeline_runs_summary {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__data_engineering_airflow.pipeline_runs_summary ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Avg Arrival Last 30 Days" in Explore.

  dimension_group: bi_week {
    type: time
    timeframes: [week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.bi_week ;;
  }
  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Dag ID" in Explore.

  dimension: dag_id {
    type: string
    sql: ${TABLE}.dag_id ;;
  }

  dimension: dependency_count {
    type: number
    sql: ${TABLE}.dependency_count ;;
  }

  dimension: duration {
    type: number
    sql: ${TABLE}."duration" ;;
  }

  dimension: duration_min {
    type: number
    sql: round(${TABLE}.duration/60);;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.T

  measure: total_duration {
    type: sum
    sql: ${duration} ;;  }

  measure: average_duration_min {
    type: average
    sql: ${duration}/60 ;;  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension_group: finished {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.finished_at ;;
  }

  dimension_group: first_successful_job_ever {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.first_successful_job_ever ;;
  }

  dimension_group: first_successful_job_finished {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.first_successful_job_finished ;;
  }

  dimension_group: last_successful_job_at {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.first_successful_job_started ;;
  }

  dimension: included_in_dlth {
    type: number
    sql: ${TABLE}.included_in_dlth ;;
  }

  dimension: job_id {
    type: number
    sql: ${TABLE}.job_id ;;
  }

  dimension: meet_freshness_sla {
    type: number
    sql: ${TABLE}.meet_freshness_sla ;;
  }

  dimension: meet_freshness_sla_new {
    type: number
    sql: ${TABLE}.meet_freshness_sla_new ;;
  }

  dimension: meet_freshness_sla_new_warning {
    type: number
    sql: ${TABLE}.meet_freshness_sla_new_warning ;;
  }

  dimension: missed_sla__long_running_job {
    type: number
    sql: ${TABLE}.missed_sla__long_running_job ;;
  }

  dimension: missed_sla__pipeline_failure {
    type: number
    sql: ${TABLE}.missed_sla__pipeline_failure ;;
  }

  dimension: missed_sla__upstream_failure {
    type: number
    sql: ${TABLE}.missed_sla__upstream_failure ;;
  }

  dimension: operator {
    type: string
    sql: ${TABLE}.operator ;;
  }

  dimension: pipeline_type {
    type: string
    sql: ${TABLE}.pipeline_type ;;
  }

  dimension: priority {
    type: string
    sql: ${TABLE}.priority ;;
  }

  dimension: queue {
    type: string
    sql: ${TABLE}.queue ;;
  }

  dimension: run_id {
    type: string
    sql: ${TABLE}.run_id ;;
  }

  dimension_group: scheduled_at {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.scheduled_at ;;
  }

  dimension_group: scheduled_date {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: yes
    datatype: date
    sql: ${TABLE}.scheduled_date ;;
  }

  dimension_group: scheduled_month {
    type: time
    timeframes: [month, quarter, year]
    convert_tz: yes
    datatype: date
    sql: ${TABLE}.scheduled_month ;;
  }

  dimension_group: scheduled_week {
    type: time
    timeframes: [week, month, quarter, year]
    convert_tz: yes
    datatype: date
    sql: ${TABLE}.scheduled_week ;;
  }

  dimension_group: started {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.started_at ;;
  }

  dimension: state {
    type: string
    sql: ${TABLE}.state ;;
  }

  dimension: task_id {
    type: string
    sql: ${TABLE}.task_id ;;
  }

  dimension: airflow_link {
    type: string
    sql: 'http://airflow.de.tidnex.com/dags/' || dag_id || '/grid?dag_run_id=' || url_encode(run_id) || '&task_id=' || url_encode(task_id);;
  }

  dimension: uc_link {
    type: string
    sql: CASE WHEN table_name LIKE '%.%' THEN 'https://xendit-prod.cloud.databricks.com/explore/data/main/' || split(table_name, '.')[1] || '/' || split(table_name, '.')[2] ELSE '' END;;
  }

  dimension: task {
    type: string
    label: "Table name / Airflow task"
    sql: CASE
      WHEN ${TABLE}.table_name is not NULL THEN ${TABLE}.table_name
      WHEN ${TABLE}.task_id LIKE 'batch_replicating_%' THEN replace(${TABLE}.task_id, 'batch_replicating_', '')
      WHEN ${TABLE}.task_id LIKE 'batch_transform_%' THEN replace(${TABLE}.task_id, 'batch_transform_', '')
      ELSE ${TABLE}.task_id
    END;;
    link: {
      label: "Airflow task"
      url: "{{ airflow_link }}"
    }
    link: {
      label: "Unity Catalog table"
      url: "{{ uc_link }}"
    }
  }

  dimension: failure_reason {
    type: string
    sql: CASE
      WHEN ${TABLE}.missed_sla__pipeline_failure = 1 THEN 'PIPELINE FAILURE'
      WHEN ${TABLE}.missed_sla__upstream_failure = 1 THEN 'UPSTREAM FAILURE'
      WHEN ${TABLE}.missed_sla__long_running_job = 1 THEN 'LONG RUNNING JOB'
      WHEN ${TABLE}.meet_freshness_sla_new = 0 THEN 'UNKNOWN'
    END;;
  }


  dimension: tier {
    type: string
    sql: ${TABLE}.tier ;;
  }

  dimension: try_number {
    type: number
    sql: ${TABLE}.try_number ;;
  }

  dimension_group: today {
    type: time
    timeframes: [time, date]
    convert_tz: no
    datatype: date
    sql: from_utc_timestamp(current_timestamp,'Asia/Jakarta');;
  }

  dimension_group: yesterday {
    type: time
    timeframes: [time, date]
    convert_tz: no
    datatype: date
    sql: (from_utc_timestamp(current_timestamp,'Asia/Jakarta')) - interval '1' day ;;
  }

  dimension: table_version {
    type:  string
    sql:
    CASE
      WHEN ${scheduled_at_date} = ${today_date} THEN 'Current'
      WHEN ${scheduled_at_date} = ${yesterday_date} THEN 'Yesterday'
      ELSE NULL
    END;;
  }

  parameter: timeframe_picker {
    label: "Date Granularity"
    type: string
    allowed_value: { value: "Date" }
    allowed_value: { value: "Week" }
    allowed_value: { value: "Month" }
    default_value: "Week"
  }

  dimension: dynamic_timeframe {
    type: string
    sql:
    CASE
      WHEN {% parameter timeframe_picker %} = 'Date' THEN ${scheduled_at_date}
      WHEN {% parameter timeframe_picker %} = 'Week' THEN ${scheduled_at_week}
      WHEN {% parameter timeframe_picker %} = 'Month' THEN ${scheduled_at_month}
    END ;;
  }

  dimension: hours_sice_last_update {
    type: number
    sql: round((unix_timestamp(current_timestamp) - unix_timestamp(last_successful_job_ever))/3600);;
  }

  dimension_group: p50_arrival_last_30_days_temp {
    type: time
    timeframes: [time]
    sql:from_unixtime(unix_timestamp(${TABLE}.scheduled_at) + ${TABLE}.p50_arrival_last_30_days);;
  }

  dimension: p50_arrival_last_30_days {
    type: string
    sql: DATE_FORMAT( cast(${p50_arrival_last_30_days_temp_time} as timestamp),'HH:mm:ss');;
  }

  dimension_group: p90_arrival_last_30_days_temp {
    type: time
    timeframes: [time]
    sql:from_unixtime(unix_timestamp(${TABLE}.scheduled_at) + ${TABLE}.p90_arrival_last_30_days);;
  }

  dimension: p90_arrival_last_30_days {
    type: string
    sql: DATE_FORMAT( cast(${p90_arrival_last_30_days_temp_time} as timestamp),'HH:mm:ss');;
  }

  dimension_group: p99_arrival_last_30_days_temp {
    type: time
    timeframes: [time]
    sql:from_unixtime(unix_timestamp(${TABLE}.scheduled_at) + ${TABLE}.p99_arrival_last_30_days);;
  }

  dimension: p99_arrival_last_30_days {
    type: string
    sql: DATE_FORMAT( cast(${p99_arrival_last_30_days_temp_time} as timestamp),'HH:mm:ss');;
  }

  measure: count {
    type: count
  }

  measure: count__successfully_updated {
    type: count
    drill_fields: [tier, pipeline_type, run_id, dag_id, task_id]
    filters: [
      meet_freshness_sla_new: "1"
    ]
  }

  measure: success_rate {
    type: number
    drill_fields: [run_id, dag_id, task_id, meet_freshness_sla_new, tier, pipeline_type]
    value_format: "0.00\%"
    sql: ${count__successfully_updated} / cast(${count} as double) * 100 ;;
  }

}
