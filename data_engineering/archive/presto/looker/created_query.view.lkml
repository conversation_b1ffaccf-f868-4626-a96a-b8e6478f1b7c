view: looker_created_query {
  sql_table_name: looker__hive.created_query ;;
  suggestions: no

  dimension: context {
    type: string
    sql: ${TABLE}.context ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: createtime {
    type: string
    sql: ${TABLE}.createtime ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
