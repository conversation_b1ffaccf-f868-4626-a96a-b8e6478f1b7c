view: executed_queries_detailed_schema_table_column {
  sql_table_name: transform__presto.executed_queries_detailed_schema_table_column ;;
  suggestions: no

  dimension: catalogname {
    type: string
    label: "Catalog"
    sql: ${TABLE}.catalogname ;;
  }

  dimension: columnname {
    type: string
    label: "Column"
    sql: ${TABLE}.columnname ;;
  }

  dimension: count_star {
    type: yesno
    label: "Count(*)"
    sql: ${TABLE}.count_star ;;
  }

  dimension: failureinfo {
    type: string
    label: "Full Failure Info"
    sql: ${TABLE}.failureinfo ;;
  }

  dimension: pdt {
    type: yesno
    label: "PDT"
    sql: ${TABLE}.pdt ;;
  }

  dimension: query {
    type: string
    label: "Query"
    sql: ${TABLE}.query ;;
  }

  dimension: querystate {
    type: string
    label: "Query State"
    sql: ${TABLE}.querystate ;;
  }

  dimension: schemaname {
    type: string
    label: "Schema"
    sql: ${TABLE}.schemaname ;;
  }

  dimension: tablename {
    type: string
    label: "Table"
    sql: ${TABLE}.tablename ;;
  }

  dimension: user_email {
    type: string
    label: "User Email"
    sql: ${TABLE}.user_email ;;
  }

  dimension: user_id {
    type: string
    label: "User Id"
    sql: ${TABLE}.user_id ;;
  }

  dimension_group: created {
    label: "Query Created"
    type: time
    timeframes: [
      raw,
      hour,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: timestamp
    sql: ${TABLE}.createTime ;;
  }

  measure: count {
    type: count
    drill_fields: [catalogname, columnname, schemaname, tablename]
  }
}
