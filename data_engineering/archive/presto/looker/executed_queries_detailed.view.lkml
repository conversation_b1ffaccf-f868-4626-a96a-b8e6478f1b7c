view: executed_queries_detailed {
  sql_table_name: transform__presto.executed_queries_detailed ;;
  suggestions: no

  dimension_group: dt {
    label: "Query Created"
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    description: "Represents the date the query was created on"
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension_group: created {
    label: "Query Created"
    type: time
    timeframes: [
      raw,
      hour,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: timestamp
    description: "Represents when the query was created on"
    sql: ${TABLE}.createTime ;;
  }

  dimension: user_id {
    type: string
    description: "The Looker User Id that ran this query, if applicable"
    group_label: "Looker User Information"
    sql: ${TABLE}.user_id ;;
  }
  dimension: user_email {
    type: string
    group_label: "Looker User Information"
    description: "The Looker User Email that ran this query, if applicable"
    sql: LOWER(${TABLE}.user_email) ;;
    drill_fields: [metadata_query]
  }

  dimension: full_failure_info {
    type: string
    description: "The full, un-parsed failure string"
    group_label: "Failure Info"
    sql: ${TABLE}.failureinfo ;;
  }
  dimension: failure_code_name {
    type: string
    label: "Code Name"
    group_label: "Failure Info"
    sql: ${TABLE}.failureinfo.errorcode.name;;
  }
  dimension: failure_code_type {
    type: string
    label: "Code Type"
    group_label: "Failure Info"
    sql: ${TABLE}.failureinfo.errorcode.type;;
  }
  dimension: failure_message {
    type: string
    label: "Message"
    description: "Human readable failure message"
    group_label: "Failure Info"
    sql: ${TABLE}.failureinfo.failureMessage;;
  }
  dimension: failure_type {
    type: string
    label: "Exception Type"
    group_label: "Failure Info"
    sql: ${TABLE}.failureinfo.failureType;;
  }

  dimension: context_client_capabilities {
    type: string
    label: "Client Capabilities"
    description: "Advanced - Special capabilities of the query client"
    group_label: "Context"
    sql: ${TABLE}.context.clientCapabilities;;
  }
  dimension: context_principal {
    type: string
    label: "Principal"
    description: "Advanced - Principal used to authenticate with coordinator"
    group_label: "Context"
    sql: ${TABLE}.context.principal ;;
  }
  dimension: context_query_type {
    type: string
    label: "Query Type"
    group_label: "Context"
    sql: ${TABLE}.context.queryType;;
  }
  dimension: context_resource_group_id {
    type: string
    label: "Resource Groups"
    description: "Advanced - Trino side resource group breakdown assigned to query. This is assigned and filtered by DE."
    group_label: "Context"
    sql: ${TABLE}.context.resourceGroupId;;
  }
  dimension: context_source {
    type: string
    label: "Source"
    description: "Source of the query - e.g. Looker or Trino cli"
    group_label: "Context"
    sql: ${TABLE}.context.source;;
  }
  dimension: context_trino_user_id {
    type: string
    label: "Trino User Id"
    description: "User Id the principal is using to run the query"
    group_label: "Context"
    sql: ${TABLE}.context.user;;
  }
  dimension: context_user_agent {
    type: string
    label: "User Agent"
    description: "Agent with which the user ran the query, e.g. jdbc, python requests"
    group_label: "Context"
    sql: ${TABLE}.context.useragent;;
  }

  dimension: metadata_query {
    type: string
    label: "Query"
    description: "The actual query itself"
    group_label: "Metadata"
    sql: ${TABLE}.metadata.query;;
  }
  dimension: metadata_query_id {
    type: string
    label: "Query Id"
    description: "Id assigned the query by Trino"
    group_label: "Metadata"
    sql: ${TABLE}.metadata.queryId;;
  }
  dimension: metadata_query_state {
    type: string
    label: "Query State"
    group_label: "Metadata"
    sql: ${TABLE}.metadata.queryState;;
  }
  dimension: metadata_routines {
    type: string
    label: "Routines"
    description: "Specific functions run in the query, such as TO_UNIX_TIME."
    group_label: "Metadata"
    sql: reduce(
    ${TABLE}.metadata.routines,
    '',
    (state, x) -> IF(x is NULL, state, state || IF(state = '', '', ', ') || x.routine),
    state -> state);;
  }

  dimension: metadata_routine_count {
    type: string
    label: "Routine Count"
    description: "Count of specific functions run in the query, such as TO_UNIX_TIME."
    group_label: "Metadata"
    sql: cardinality(${TABLE}.metadata.routines);;
  }
  dimension: metadata_tables {
    type: string
    label: "Tables"
    group_label: "Metadata"
    description: "See the explore 'Trino Schema Table Column Use'"
    sql: reduce(
    ${TABLE}.metadata.tables,
    '',
    (state, x) -> IF(x is NULL, state, state || IF(state = '', '', ', ') || x.catalog || '.' || x.schema || '.' || x.table_name),
    state -> state);;
  }

  dimension: metadata_update_type {
    type: string
    label: "Update Type"
    description: "CALL, CREATE TABLE, DROP TABLE, INSERT"
    group_label: "Metadata"
    sql: ${TABLE}.metadata.updateType;;
  }
  dimension: metadata_pdt {
    type: yesno
    label: "PDT"
    description: "If the general create PDT syntax is discovered in the query"
    group_label: "Metadata"
    sql: ${TABLE}.metadata.pdt;;
  }
  dimension: metadata_full_info {
    type: string
    label: "Metadata Full Info"
    description: "The raw metadata json string"
    group_label: "Metadata"
    sql: ${TABLE}.metadata;;
  }

  # ==== ioMetadata ====
  dimension: full_ioMetadata {
    type: string
    group_label: "IO Metadata"
    description: "The raw io metadata json string"
    sql: ${TABLE}.iometadata ;;
  }


  dimension: ioMetadata_inputs_schema {
    type: string
    label: "Input Schema"
    group_label: "IO Metadata"
    sql: transform(${TABLE}.iometadata.inputs, x -> x.schema) ;;
  }

  dimension: ioMetadata_inputs_table_name {
    type: string
    label: "Input Table Name"
    group_label: "IO Metadata"
    sql: transform(${TABLE}.iometadata.inputs, x -> x.table_name) ;;
  }

  dimension: ioMetadata_input_columns {
    type: string
    label: "Input Columns"
    group_label: "IO Metadata"
    sql:transform(${TABLE}.iometadata.inputs, x -> x.columns) ;;
  }

  dimension: ioMetadata_inputs_physicalInputBytes {
    type: string
    label: "Input Total Physical Bytes"
    group_label: "IO Metadata"
    sql: reduce(
          transform(${TABLE}.iometadata.inputs, x -> x.physicalInputBytes),
          0,
          (s, x) -> IF(x IS NULL, s, s + x),
          s -> s);;
  }

  dimension: ioMetadata_inputs_physicalInputRows {
    type: string
    label: "Input Total PhysicalInputRows"
    group_label: "IO Metadata"
    sql: reduce(
          transform(${TABLE}.iometadata.inputs, x -> x.physicalInputRows),
          0,
          (s, x) -> IF(x IS NULL, s, s + x),
          s -> s);;
  }

  dimension: ioMetadata_output_schema {
    type: string
    label: "Output Schema"
    group_label: "IO Metadata"
    sql: ${TABLE}.iometadata.output.schema ;;
  }

  dimension: ioMetadata_output_table_name {
    type: string
    label: "Output Table Name"
    group_label: "IO Metadata"
    sql: ${TABLE}.iometadata.output.table_name ;;
  }

  dimension: ioMetadata_output_columns {
    type: string
    label: "Output Columns"
    group_label: "IO Metadata"
    sql:transform(${TABLE}.iometadata.output.columns, x -> x.columnName) ;;
  }



  dimension: statistics_analysisTime {
    type: number
    label: "Analysis Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.analysisTime.seconds ;;
  }


  dimension: statistics_completedSplits {
    type: string
    label: "Completed Splits"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.completedSplits ;;
  }


  dimension: statistics_cpuTime {
    type: number
    label: "cpu Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.cpuTime ;;
  }


  dimension: statistics_cumulativeMemory {
    type: number
    label: "Cumulative Memory"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.cumulativeMemory ;;
  }


  dimension: statistics_cumulativeSystemMemory {
    type: number
    label: "Cumulative System Memory"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.cumulativeSystemMemory ;;
  }


  dimension: statistics_executionTime {
    type: number
    label: "Execution Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.executionTime.seconds ;;
  }


  dimension: statistics_internalNetworkBytes {
    type: number
    label: "Internal Network Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.internalNetworkBytes ;;
  }


  dimension: statistics_internalNetworkRows {
    type: number
    label: "Internal Network Rows"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.internalNetworkRows ;;
  }


  dimension: statistics_outputBytes {
    type: number
    label: "Output Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.outputBytes ;;
  }


  dimension: statistics_outputRows {
    type: number
    label: "Output Rows"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.outputRows ;;
  }


  dimension: statistics_peakTaskTotalMemory {
    type: number
    label: "Peak Task Total Memory"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.peakTaskTotalMemory ;;
  }


  dimension: statistics_peakTaskUserMemory {
    type: number
    label: "Peak Task User Memory"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.peakTaskUserMemory ;;
  }


  dimension: statistics_peakTotalNonRevocableMemoryBytes {
    type: number
    label: "Peak Total Non-Revocable Memory Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.peakTotalNonRevocableMemoryBytes ;;
  }


  dimension: statistics_peakUserMemoryBytes {
    type: number
    label: "Peak User Memory Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.peakUserMemoryBytes ;;
  }


  dimension: statistics_physicalInputBytes {
    type: number
    label: "Physical Input Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.physicalInputBytes ;;
  }


  dimension: statistics_physicalInputRows {
    type: number
    label: "Physical Input Rows"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.physicalInputRows ;;
  }


  dimension: statistics_planningTime {
    type: string
    label: "Planning Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.planningTime.seconds ;;
  }


  dimension: statistics_queuedTime {
    type: number
    label: "Queued Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.queuedTime ;;
  }

  dimension: statistics_queuedTime_bucketed {
    type: tier
    label: "Queued Time Seconds - Bucketed"
    tiers: [1,10,20,30,40,50,60,120]
    style: integer
    group_label: "Statistics"
    sql: ${statistics_queuedTime} ;;
  }

  dimension: statistics_resourceWaitingTime {
    type: number
    label: "Resource Waiting Time Seconds"
    description: "Waiting for resources (buffer space, memory, splits, etc.)"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.resourceWaitingTime.seconds ;;
  }


  dimension: statistics_scheduledTime {
    type: number
    label: "Scheduled Time Seconds"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.scheduledTime.seconds ;;
  }


  dimension: statistics_totalBytes {
    type: number
    label: "Total Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.totalBytes ;;
  }


  dimension: statistics_totalRows {
    type: number
    label: "Total Rows"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.totalRows ;;
  }


  dimension: statistics_wallTime {
    type: number
    label: "Wall Time Seconds"
    description: "Wall time represents human perceived time for the query"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.wallTime ;;
  }

  dimension: statistics_wallTime_bucketed {
    type: tier
    label: "Wall Time Seconds - Bucketed"
    tiers: [1,15, 30,60,120,180,240,300]
    style: integer
    group_label: "Statistics"
    sql: ${statistics_wallTime} ;;
  }


  dimension: statistics_writtenBytes {
    type: number
    label: "Written Bytes"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.writtenBytes ;;
  }


  dimension: statistics_writtenRows {
    type: number
    label: "Written Rows"
    group_label: "Statistics"
    sql: ${TABLE}.statistics.writtenRows ;;
  }

  dimension: statistics {
    type: string
    label: "Full Statistics Info"
    group_label: "Statistics"
    description: "Raw json string of available query statistics"
    sql: ${TABLE}.statistics ;;
  }

  dimension: warnings {
    type: string
    group_label: "Warnings"
    label: "Full Warning Info"
    sql: reduce(
    ${TABLE}.warnings,
    '',
    (state, x) -> IF(x is NULL, state, state || '{"message": "' || x.message || '", "warningCode": {"code": ' || cast(x.warningCode.code as varchar) || ', "name": ' || x.warningCode.name || '}} '),
    state -> state);;
  }

  dimension: warnings_message {
    type: string
    label: "Message"
    group_label: "Warnings"
    sql:transform(${TABLE}.warnings, x -> x.message) ;;
  }

  dimension: warnings_code {
    type: string
    group_label: "Warnings"
    label: "Code"
    sql: transform(${TABLE}.warnings, x -> x.warningCode.code) ;;
  }
  dimension: warnings_name {
    type: string
    group_label: "Warnings"
    label: "Name"
    sql: transform(${TABLE}.warnings, x -> x.warningCode.name) ;;
  }
  # dimension: endtime {
  #  type: string
  #  sql: ${TABLE}.endtime ;;
  # }

  # dimension: executionstarttime {
  #  type: string
  #  sql: ${TABLE}.executionstarttime ;;
  # }
  # measure: count {
  #  type: count
  #  drill_fields: []
  # }
  # dimension: context {
  #  type: string
  #  sql: ${TABLE}.context ;;
  # }

  # dimension: createtime {
  #  type: string
  #  sql: ${TABLE}.createtime ;;
  # }

  measure: wall_time_75th_percentile {
    type: percentile
    label: "Wall Time Seconds 75th Percentile"
    percentile: 75
    sql: ${TABLE}.statistics.wallTime.seconds ;;
  }
  measure: queued_time_75th_percentile {
    type: percentile
    label: "Queued Time Seconds 75th Percentile"
    percentile: 75
    sql: ${TABLE}.statistics.queuedTime.seconds ;;
  }
  measure: wall_time_95th_percentile {
    type: percentile
    label: "Wall Time Seconds 95th Percentile"
    description: "Wall time represents human perceived time for the query"
    percentile: 95
    sql: ${TABLE}.statistics.wallTime.seconds ;;
  }
  measure: queued_time_95th_percentile {
    type: percentile
    label: "Queued Time Seconds 95th Percentile"
    percentile: 95
    sql: ${TABLE}.statistics.queuedTime.seconds ;;
  }

  measure: median_wall_time {
    type: median
    description: "Wall time represents human perceived time for the query"
    sql: ${TABLE}.statistics.wallTime.seconds ;;
  }

  measure: median_queued_time {
    type: median
    sql: ${TABLE}.statistics.queuedTime.seconds ;;
  }

  measure: count {
    type: count
  }

}
