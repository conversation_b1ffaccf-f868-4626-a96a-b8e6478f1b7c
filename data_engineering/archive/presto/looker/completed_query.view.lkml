view: looker_completed_query {
  sql_table_name: looker__hive.completed_query ;;
  suggestions: no

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      minute15,
      hour,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: createtime {
    type: string
    sql: ${TABLE}.createtime ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: endtime {
    type: string
    sql: ${TABLE}.endtime ;;
  }

  dimension: executionstarttime {
    type: string
    sql: ${TABLE}.executionstarttime ;;
  }

  dimension: failedtasks {
    type: string
    sql: ${TABLE}.failedtasks ;;
  }

  dimension: failureinfo {
    type: string
    sql: ${TABLE}.failureinfo ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: query_length_minute {
    type: number
    sql: ${TABLE}.query_length_minute ;;
  }

  dimension: query_length_second {
    type: number
    sql: ${TABLE}.query_length_second ;;
  }

  dimension: statistics {
    type: string
    sql: ${TABLE}.statistics ;;
  }

  dimension: total_rows{
    type: number
    sql: ${TABLE}.statistics.totalrows;;
  }

  dimension: cumulative_memory {
    type: number
    sql: ${TABLE}.statistics.cumulativememory ;;
  }

  dimension: peak_user_memory_bytes {
    type: number
    sql: ${TABLE}.statistics.peakusermemorybytes ;;

  }

  dimension: peak_task_total_memory {
    type: number
    sql: ${TABLE}.statistics.peaktasktotalmemory ;;

  }

  dimension: peak_task_user_memory {
    type: number
    sql: ${TABLE}.statistics.peaktaskusermemory ;;

  }
  measure: count {
    type: count
    drill_fields: []
  }
  measure: sum_peak_task_total_memory {
    type:  sum
    sql: ${peak_task_total_memory} ;;
  }

  measure: sum_peak_user_memory_bytes {
    type:  sum
    sql: ${peak_user_memory_bytes} ;;
  }

  measure: sum_peak_task_user_memory_bytes {
    type:  sum
    sql: ${peak_task_user_memory} ;;
  }
}
