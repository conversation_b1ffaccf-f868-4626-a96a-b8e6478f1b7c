include: "*.view.lkml"
include: "//central-models-dbr/0_assets/fx_rates.view.lkml"

explore: dragonpay_transactions {
  hidden: yes
  view_name: dp_transaction_backbone
  join: sw_txns {
    type: left_outer
    relationship: one_to_one
    sql_on: ${sw_txns.refno} = ${dp_transaction_backbone.refno}
    and ${dp_transaction_backbone.money_flow} = 'MONEY_IN' ;;
  }
  join: po_txns {
    type: left_outer
    relationship: one_to_one
    sql_on: ${po_txns.refno} = ${dp_transaction_backbone.refno}
      and ${dp_transaction_backbone.money_flow} = 'MONEY_OUT' ;;
  }
  join: lookup_processors {
    type: left_outer
    relationship: one_to_one
    sql_on: ${dp_transaction_backbone.processor} = ${lookup_processors.procid} ;;
  }
  join: pg_offlinetxns {
    type: left_outer
    relationship: one_to_one
    sql_on: ${sw_txns.refno} = ${pg_offlinetxns.refno} ;;
  }
  join: pg_reporttxns {
    type: left_outer
    relationship: one_to_many
    sql_on: ${sw_txns.refno} = ${pg_reporttxns.refno} ;;
  }
  join: pg_scrapetxns {
    type: left_outer
    relationship: one_to_one
    sql_on: ${sw_txns.refno} = ${pg_scrapetxns.refno} ;;
  }
  join: fx_rates  {
    type: left_outer
    relationship: many_to_one
    sql_on: case when {{fx_rates.use_current_year_fx._parameter_value}} then year(CURRENT_DATE) else ${dp_transaction_backbone.dt_year} end = ${fx_rates.year}
    and ${dp_transaction_backbone.currency} = ${fx_rates.currency} ;;
  }
}
