registra_type: REPLICATION
source_name: PG__BILLING_PROD_DEV
source_type: postgres
source_group: dev_postgres
cron_schedule: 0 15 * * *
data_config:
  fee-service-dev:
    tables:
      product_rate_setting:
        source_dqc_configs:
          include_last_x_days: 7
          run_quality_check: false
        dqc_key: created
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
          schema_file_column_casts:
          - col_name: tier_1_end
            cast_to: bigint
          - col_name: tier_2_end
            cast_to: bigint
        dqc_tolerance:
        - -500000
        - 500000
      vat_rate_setting:
        backfill_filters:
        - backfill_id: updated
          backfill_days: 5
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
      migrations:
        etl_mode: FULL_RELOAD
      billable_transaction_detail:
        source_dqc_configs:
          include_last_x_days: 7
        dqc_key: created
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
