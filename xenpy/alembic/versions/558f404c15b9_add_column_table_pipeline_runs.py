"""add_column_table_pipeline_runs

Revision ID: 558f404c15b9
Revises: 
Create Date: 2023-10-17 11:22:26.283584

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "558f404c15b9"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "pipeline_runs",
        sa.Column("rows_read", sa.Integer(), nullable=False, server_default="0"),
    )
    op.add_column(
        "pipeline_runs",
        sa.Column("rows_inserted", sa.Integer(), nullable=False, server_default="0"),
    )
    op.add_column(
        "pipeline_runs",
        sa.Column("rows_updated", sa.Integer(), nullable=False, server_default="0"),
    )


def downgrade() -> None:
    op.drop_column("pipeline_runs", "rows_updated")
    op.drop_column("pipeline_runs", "rows_inserted")
    op.drop_column("pipeline_runs", "rows_read")
