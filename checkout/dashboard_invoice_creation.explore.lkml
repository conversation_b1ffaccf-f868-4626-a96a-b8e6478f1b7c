include: "//central-models/businesses/businesses.view"
include: "//central-models/businesses/business_facts.view"

include: "/checkout/*.view.lkml"

explore: dashboard_invoice_creation {
  from: l2_invoice_creation_flow
  description: "User activity for Invoice Creation on Dashboard, tracked via Snowplow Events"
  group_label: "Events: Payment interfaces"
  view_label: "Invoice Creation Sessions"

  join: businesses {
    type: left_outer
    sql_on:${dashboard_invoice_creation.business_id} =  ${businesses.business_id};;
    relationship: many_to_one
  }

  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }
}

explore: dashboard_invoice_creation_updated {
  from: l2_invoice_creation_flow_update
  description: "User activity for PaymentLink Creation on Dashboard, tracked via Snowplow Events"
  group_label: "Events: Payment interfaces"
  view_label: "Payment Link Creation Sessions"

  join: businesses {
    type: left_outer
    sql_on:${dashboard_invoice_creation_updated.business_id} =  ${businesses.business_id};;
    relationship: many_to_one
  }

  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }
}
