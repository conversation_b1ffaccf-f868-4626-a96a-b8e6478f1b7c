view: l1_dashboard_invoice_update {
  sql_table_name: transform__events.l1_dashboard_invoice_creation_backbone_duplicate ;;
  suggestions: no

  dimension: app_id {
    type: string
    sql: ${TABLE}.app_id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: collector_tstamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.collector_tstamp ;;
  }

  dimension_group: derived_tstamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.derived_tstamp ;;
  }

  dimension: domain_sessionid {
    type: string
    sql: ${TABLE}.domain_sessionid ;;
  }

  dimension: domain_userid {
    type: string
    sql: ${TABLE}.domain_userid ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension_group: dvce_created_tstamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dvce_created_tstamp ;;
  }

  dimension: element_id {
    type: string
    sql: ${TABLE}.element_id ;;
  }

  dimension: event {
    type: string
    sql: ${TABLE}.event  ;;
    description: "Combination of form fill and click events during the Invoice Creation Flow"
  }

  dimension: event_id {
    type: string
    sql: ${TABLE}.event_id ;;
  }

  dimension: name_tracker {
    type: string
    sql: ${TABLE}.name_tracker ;;
  }

  dimension: network_userid {
    type: string
    sql: ${TABLE}.network_userid ;;
  }

  dimension: se_action {
    type: string
    sql: ${TABLE}.se_action ;;
  }

  dimension: se_category {
    type: string
    sql: ${TABLE}.se_category ;;
  }

  dimension: se_label {
    type: string
    sql: ${TABLE}.se_label ;;
  }

  dimension: session_invoice_idx {
    type: number
    value_format_name: id
    sql: ${TABLE}.session_invoice_idx ;;
    description: "Events identified by Invoice Creation Start per session. NULL if events not from the same session"
  }

  dimension: test_flag {
    type: yesno
    sql: ${TABLE}.test_flag == 1 ;;
    description: "Whether the event is created using Google Tag Manager Preview Mode"
  }

  dimension: v_tracker {
    type: string
    sql: ${TABLE}.v_tracker ;;
  }

  dimension: value {
    type: string
    sql: ${TABLE}.value ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
