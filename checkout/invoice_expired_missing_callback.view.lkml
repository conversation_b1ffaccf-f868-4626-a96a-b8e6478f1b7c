view: invoice_expired_missing_callback {
  derived_table: {
    sql: with expired_invoices as (
        select
          id as invoice_id,
          external_id as external_id,
          user_id as business_id,
          expiry_date as invoice_expiry_date
        from
          hive.clean__xendit_invoice_service.invoices
        where
          status = 'EXPIRED'
          and should_send_expire_callback = true
          -- and date(expiry_date) between date('2023-03-09') and date('2023-03-22')
      ),
      invoice_expired_webhooks as (
        select
          business_id,
          type,
          payload,
          created_at,
          json_extract_scalar(payload, '$.id') as invoice_id,
          json_extract_scalar(payload, '$.status') as invoice_status
        from
          hive.clean__webhook_service.webhooks
        where
          type = 'INVOICE'
          and json_extract_scalar(payload, '$.status') = 'EXPIRED'
          -- and date(created_at) >= date('2023-03-09')
      ),
      expired_invoice_count as (
        select
          count(invoice_id) as expired_invoice_count
        from
          expired_invoices
      ),
      expired_invoice_webhook_count as (
        select
          count(distinct json_extract_scalar(payload, '$.id')) as expired_invoice_webhook_count
        from
          invoice_expired_webhooks
      )
      select
        i.invoice_id as invoice_id,
        i.external_id as external_id,
        i.business_id as business_id,
        i.invoice_expiry_date as invoice_expiry_date,
        wh.payload as webhook_payload,
        wh.created_at as webhook_created_date
      from
        expired_invoices i
        full join invoice_expired_webhooks wh on i.invoice_id = wh.invoice_id
        left join hive.clean__xendit_invoice_service.invoicesettings s on i.business_id = s.user_id
      where
        i.invoice_id is not null
        and wh.payload is null
        and s.should_send_expire_callback = true
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: []
  }

  dimension: invoice_id {
    type: string
    sql: ${TABLE}.invoice_id ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: invoice_expiry {
    type: time
    sql: ${TABLE}.invoice_expiry_date ;;
  }

  dimension: webhook_payload {
    type: string
    sql: ${TABLE}.webhook_payload ;;
  }

  dimension_group: webhook_created {
    type: time
    sql: ${TABLE}.webhook_created_date ;;
  }
}
