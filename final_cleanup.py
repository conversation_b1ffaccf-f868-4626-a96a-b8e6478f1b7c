#!/usr/bin/env python3
"""
Final cleanup script to fix remaining include and description differences
"""

import os
import re
import subprocess

DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def get_all_explore_files():
    """Get all .explore.lkml files in DBR repository"""
    result = subprocess.run(
        ["find", DBR_REPO, "-name", "*.explore.lkml"],
        capture_output=True, text=True
    )
    files = result.stdout.strip().split('\n') if result.stdout.strip() else []
    return [f for f in files if f]

def fix_includes_and_descriptions(file_path):
    """Fix include references and descriptions in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix include references
        content = content.replace('//central-models/', '//central-models-dbr/')
        
        # Fix descriptions - case insensitive replacement
        content = re.sub(
            r'\(xendit presto\)',
            '(xendit databricks)',
            content,
            flags=re.IGNORECASE
        )
        
        # Also handle variations
        content = re.sub(
            r'\(presto\)',
            '(databricks)',
            content,
            flags=re.IGNORECASE
        )
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"  ⚠️  Error processing {file_path}: {e}")
        return False

def main():
    print("🧹 FINAL CLEANUP: FIXING INCLUDES AND DESCRIPTIONS")
    print("=" * 60)
    
    explore_files = get_all_explore_files()
    print(f"📊 Processing {len(explore_files)} .explore.lkml files...")
    
    updated_count = 0
    
    for file_path in explore_files:
        relative_path = os.path.relpath(file_path, DBR_REPO)
        
        if fix_includes_and_descriptions(file_path):
            updated_count += 1
            print(f"  ✅ Updated: {relative_path}")
    
    print(f"\n📊 CLEANUP COMPLETE:")
    print(f"  ✅ Files updated: {updated_count}")
    print(f"  📁 Total files processed: {len(explore_files)}")
    
    if updated_count > 0:
        print(f"\n🔍 Running verification...")
        # Run verification
        subprocess.run(["python3", "comprehensive_explore_sync.py"], cwd=DBR_REPO)

if __name__ == "__main__":
    main()
