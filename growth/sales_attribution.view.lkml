view: sales_attribution {
  sql_table_name: transform__growth.sales_attribution ;;

  # derived_table: {
  #   datagroup_trigger: views_updated_based_on_time
  #   sql:
  #     SELECT DISTINCT
  #     business_id
  #     ,sf_id
  #     ,ad_business_name
  #     ,sf_business_name
  #     ,type
  #     ,master_business_id
  #     ,ad_master_business_name
  #     ,sf_master_id
  #     ,sf_master_business_name
  #     ,sf_master_brand
  #     ,sf_brand
  #     ,ad_internal_name
  #     ,ad_rollup_internal_name
  #     ,sf_ultimate_parent_id
  #     ,sf_ultimate_parent_name
  #     ,sf_rollup_ultimate_parent_id
  #     ,sf_rollup_ultimate_parent_name
  #     ,sf_rollup_ultimate_parent_brand
  #     ,sf_rollup_country_of_hq
  #     ,ad_rollup_country_of_operation
  #     ,sf_rollup_industry_category
  #     ,sf_rollup_child_industry
  #     ,sf_industry_category
  #     ,sf_child_industry
  #     ,sf_rollup_churn_comments
  #     ,sf_rollup_churn_reason
  #     ,sf_rollup_has_churned
  #     ,sf_rollup_revenue_split
  #     ,sf_rollup_revenue_split_type
  #     ,sf_rollup_handover_date__c
  #     ,sf_rollup_is_regional__c
  #     ,sf_churn_comments__c
  #     ,sf_churn_reason__c
  #     ,sf_has_churned__c
  #     ,sf_revenue_split__c
  #     ,sf_revenue_split_type__c
  #     ,sf_handover_date__c
  #     ,sf_is_regional__c
  #     ,ad_cp_manager_name
  #     ,ad_rollup_cp_manager_name
  #     ,sf_cp_manager_id
  #     ,sf_cp_manager_name
  #     ,sf_cp_manager_email
  #     ,sf_rollup_cp_manager_id
  #     ,sf_rollup_cp_manager_name
  #     ,sf_rollup_cp_manager_email
  #     ,ad_bd_manager_name
  #     ,ad_rollup_bd_manager_name
  #     ,ad_sales_rep_name
  #     ,ad_rollup_sales_rep_name
  #     ,ad_account_manager_name
  #     ,ad_rollup_account_manager_name
  #     ,ad_technical_rep_name
  #     ,ad_rollup_technical_rep_name
  #     ,ad_account_owner
  #     ,ad_rollup_account_owner
  #     ,sf_owner_id
  #     ,sf_owner_name
  #     ,sf_owner_email
  #     ,sf_rollup_owner_id
  #     ,sf_rollup_owner_name
  #     ,sf_rollup_owner_email
  #     ,sf_secondary_owner_id
  #     ,sf_secondary_owner_name
  #     ,sf_secondary_owner_email
  #     ,sf_rollup_secondary_owner_id
  #     ,sf_rollup_secondary_owner_name
  #     ,sf_rollup_secondary_owner_email
  #     ,sf_cps_email
  #     ,sf_rollup_cps_email
  #     ,sf_rollup_solution_email
  #     ,sf_presales_email
  #     ,sf_rollup_presales_email
  #     ,sf_cs_email
  #     ,sf_rollup_cs_email
  #     ,sf_cpm_email
  #     ,sf_rollup_cpm_email
  #     ,sf_bdm_email
  #     ,sf_rollup_bdm_email
  #     ,sf_owner_division
  #     ,sf_team_owner
  #     ,sf_acc_owner_division
  #     ,sf_rollup_owner_division
  #     ,sf_rollup_team_owner
  #     ,sf_rollup_acc_owner_division
  #     ,sf_team_assignment
  #     ,sf_rollup_team_assignment
  #     ,sf_acc_owner_country
  #     ,sf_rollup_acc_owner_country
  #     ,rollup_min_fifth_transaction_date
  #     ,ad_user_category__c
  #     ,latest_cw_parent_opp_id
  #     ,latest_cw_parent_opp_date
  #     ,latest_cw_parent_opp_owner_email
  #     ,is_attributable
  #     ,sales_team
  #     ,CASE
  #     WHEN sales_team='Regional Chinese' THEN 'Regional'
  #     WHEN sales_team='Regional Remittance' THEN 'Regional'
  #     ELSE sales_country
  #     END AS sales_country
  #     ,ad_account_owner_email
  #     ,ad_rollup_account_owner_email
  #     ,description
  #     ,weekly_update__c
  #     ,weekly_update_history__c
  #     ,sf_solution_email
  #     ,solution_rep_assigndate
  #     ,solution_attribution_start_date
  #     ,solution_attribution_end_date
  #     ,Xendit_Account_Email__c
  #     ,bid_primary_lead_id
  #     ,bid_primary_lead_mql_date
  #     ,bid_primary_lead_create_date
  #     ,bid_primary_lead_source
  #     ,bid_primary_lead_how_do_you_hear_about_xendit
  #     ,bid_primary_lead_offer_type_1st_touch
  #     ,bid_primary_lead_offer_type_last_touch
  #     ,bid_primary_contact_id
  #     ,bid_primary_contact_create_date
  #     ,bid_primary_contact_source
  #     ,bid_primary_contact_how_do_you_hear_about_xendit
  #     ,bid_primary_contact_offer_type_1st_touch
  #     ,bid_primary_contact_offer_type_last_touch
  #     ,bid_primary_contact_opportunity
  #     ,bid_primary_contact_opportunity_create_date
  #     ,bid_primary_contact_opportunity_stage
  #     ,latest_cw_parent_opp_bd_email
  #     ,have_used_meta_tpi
  #     ,is_meta_referral
  #     ,is_meta_merchant
  #     ,is_influenced_by_marketing_campaign
  #     ,sf_rollup_ultimate_parent_id_v2
  #     ,sf_rollup_ultimate_parent_name_v2
  #     ,sf_rollup_ultimate_parent_brand_v2
  #     ,sf_rollup_owner_name_v2
  #     ,sf_rollup_owner_email_v2
  #     ,sales_team_v2
  #     ,is_cp_merchant_v2
  #     ,is_regional_merchant
  #     ,sf_cp_manager_name_join_w_owned
  #     ,referral_id
  #     ,latest_promo_code_id
  #     ,ad_rollup_business_name
  #     ,rollup_business_id
  #     ,rollup_sf_id
  #     ,sf_rollup_business_name
  #     ,sf_rollup_master_brand
  #     ,sf_referral_id
  #     ,is_cp_referral
  #     ,is_cp_reseller
  #     ,cp_partner_category
  #     ,sf_referral_cp_segmentation
  #     ,sf_referral_type
  #     ,sf_cp_partner_id
  #     ,sf_cp_partner_subcategory
  #     ,sf_rollup_ultimate_parent_brand_v3
  #     ,sf_cp_partner_name
  #     ,sf_cp_partner_category
  #     ,sf_referral_cp_segmentation_v2
  #     ,is_cp_merchant_v3
  #     FROM transform__growth.sales_attribution;;
  # }

#   derived_table: {
#     datagroup_trigger: views_updated_based_on_time
#     sql:
#     WITH meta_tpi
# AS (
#   SELECT DISTINCT t1.user_id
#     , TRUE AS have_used_meta_tpi
#   FROM transform__transaction_volumes.transaction_backbone t1
#   WHERE t1.dt >= DATE ('2023-01-01') AND UPPER(tpi_platform_name) = 'META'
#   )
#   , latest_referral_code
# AS (
#   SELECT t2.referred_business_id
#     , max(t2.created) AS created_date
#   FROM clean__xendit_promo_service_live.referral_code_usage t2
#   WHERE t2.is_active = TRUE AND t2.referred_business_id IS NOT NULL
#   GROUP BY 1
#   )
#   , referral_id
# AS (
#   SELECT t1.id AS business_id
#     , t1.referral_id
#   FROM clean__xendit_business_service.businesses t1
#   WHERE t1.referral_id IS NOT NULL AND t1.id IS NOT NULL
#   )
#   , referral_code_usage
# AS (
#   SELECT DISTINCT t3.referred_business_id AS business_id
#     , t1.referral_code AS referral_id
#   FROM clean__xendit_promo_service_live.referral_code t1
#   LEFT JOIN clean__xendit_promo_service_live.referral_code_usage t2
#     ON t1.id = t2.referral_code_id
#   LEFT JOIN latest_referral_code t3
#     ON t2.referred_business_id = t3.referred_business_id AND t3.created_date = t2.created
#   WHERE t3.referred_business_id IS NOT NULL AND t1.referral_code IS NOT NULL
#   )
#   , referral_code
# AS (
#   SELECT t1.business_id
#     , coalesce(t2.referral_id, t3.referral_id) AS referral_id
#     , CASE
#       WHEN upper(t2.referral_id) LIKE ('%META%') AND upper(t2.referral_id) NOT IN ('METATESTID2')
#         THEN TRUE
#       ELSE FALSE
#       END AS is_meta_referral
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN referral_code_usage t2
#     ON t1.business_id = t2.business_id
#   LEFT JOIN referral_id t3
#     ON t1.business_id = t3.business_id
#   )
#   ,sf_cp_referral
# AS (
#   SELECT t1.business_id, t2.referral_code__c as referral_id
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN clean__salesforce.accounts t2 ON t1.business_id = t2.business_id__c
#   WHERE t2.referral_code__c IN ('cp_reseller','cp_referral')
#   )
#   ,is_cp_referral_reseller
# AS (
#   SELECT t1.business_id, t1.master_business_id
#     , TRUE as is_cp_referral
#     , null as is_cp_reseller
#     , 'cp_referral' as referral_id
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN sf_cp_referral t2 ON t1.master_business_id=t2.business_id
#   WHERE t2.referral_id='cp_referral'

#   UNION

#   SELECT t1.business_id, t1.master_business_id
#     , TRUE as is_cp_referral
#     , null as is_cp_reseller
#     , 'cp_referral' as referral_id
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN sf_cp_referral t2 ON t1.business_id=t2.business_id
#   WHERE t2.referral_id='cp_referral'

#   UNION

#   SELECT t1.business_id, t1.master_business_id
#     , null as is_cp_referral
#     , TRUE as is_cp_reseller
#     , 'cp_reseller' as referral_id
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN sf_cp_referral t2 ON t1.master_business_id=t2.business_id
#   WHERE t2.referral_id='cp_reseller'

#   UNION

#   SELECT t1.business_id, t1.master_business_id
#     , null as is_cp_referral
#     , TRUE as is_cp_reseller
#     , 'cp_reseller' as referral_id
#   FROM transform__growth.sales_attribution t1
#   LEFT JOIN sf_cp_referral t2 ON t1.business_id=t2.business_id
#   WHERE t2.referral_id='cp_reseller'
#   )
#   , meta_merchant
# AS (
#   SELECT DISTINCT user_id AS business_id
#     , TRUE AS is_meta_merchant
#   FROM meta_tpi

#   UNION

#   SELECT DISTINCT business_id
#     , TRUE AS is_meta_merchant
#   --  FROM meta_referral
#   FROM referral_code
#   WHERE is_meta_referral = true
#   )
#   , promo_code
# AS (
#   SELECT t1.business_id
#     , t1.promo_code_id AS latest_promo_code_id
#   FROM clean__xendit_promo_service_live.promo_code_activation t1
#   INNER JOIN (
#     SELECT business_id
#       , MAX(created) AS latest_date
#     FROM clean__xendit_promo_service_live.promo_code_activation
#     GROUP BY business_id
#     ) t2
#     ON t1.business_id = t2.business_id AND t1.created = t2.latest_date
#   )
#   , marketing_influence
# AS (
#   SELECT DISTINCT t2.business_id_formula__c
#     , TRUE AS is_influenced_by_marketing_campaign
#   FROM clean__salesforce.campaign_influence t1
#   LEFT JOIN clean__salesforce.opportunities t2
#     ON t1.opportunityid = t2.id
#   LEFT JOIN clean__salesforce.campaign_influence_model t5
#     ON t1.modelid = t5.id
#   WHERE lower(t2.stagename) IN ('activation', 'closed won') AND t5.id = '03V5j000000CiBgEAK'
#   )
#   , owned_cp
# AS (
#   SELECT t1.business_id
#     , t1.subaccount_type
#     , CASE
#       WHEN t2.sf_cp_manager_name IS NULL
#         THEN t2.sf_rollup_cp_manager_name
#       ELSE t2.sf_cp_manager_name
#       END AS cp_manager_name
#   FROM transform__business_intelligence.dim_businesses t1
#   LEFT JOIN transform__growth.sales_attribution t2
#     ON t1.business_id = t2.business_id
#   WHERE t1.subaccount_type = 'owned'

#   UNION

#   SELECT t1.business_id
#     , t1.subaccount_type
#     , t2.sf_cp_manager_name AS cp_manager_name
#   FROM transform__business_intelligence.dim_businesses t1
#   LEFT JOIN transform__growth.sales_attribution t2
#     ON t1.business_id = t2.business_id
#   WHERE t1.subaccount_type NOT IN ('owned') OR t1.subaccount_type IS NULL
#   )

#   ,sf_referral_owned as (
#     SELECT t1.business_id, t2.referral_code__c as referral_id
#     FROM transform__growth.sales_attribution t1
#     LEFT JOIN clean__salesforce.accounts t2 ON t1.business_id = t2.business_id__c
#     LEFT JOIN is_cp_referral_reseller t3 ON t1.business_id=t3.business_id
#     WHERE t3.referral_id IS NULL
#     AND t1.type NOT IN ('XP OWNED')

#     UNION

#     SELECT t1.business_id, t2.referral_code__c as referral_id
#     FROM transform__growth.sales_attribution t1
#     LEFT JOIN clean__salesforce.accounts t2 ON t1.master_business_id = t2.business_id__c
#     LEFT JOIN is_cp_referral_reseller t3 ON t1.business_id=t3.business_id
#     WHERE t3.referral_id IS NULL
#     AND t1.type IN ('XP OWNED')
#   )

#   ,all_sf_referral as (
#     SELECT business_id, referral_id FROM sf_referral_owned
#     UNION
#     SELECT business_id, referral_id FROM is_cp_referral_reseller
#   )

# SELECT DISTINCT t1.*
#   -- , t2.is_top_5_rev_growing_parent_in_L30D
#   -- , t2.is_top_5_rev_dropping_parent_in_L30D
#   -- , t2.MTD_TPV
#   -- , t2.MTD_Rev
#   -- , t2.MTD_TPV_delta
#   -- , t2.MTD_Rev_delta
#   -- , t2.L30D_TPV
#   -- , t2.L30D_Rev
#   -- , t2.L30D_TPV_delta
#   -- , t2.L30D_Rev_delta
#   -- , t2.L7D_TPV
#   -- , t2.L7D_Rev
#   -- , t2.L7D_TPV_delta
#   -- , t2.L7D_Rev_delta
#   , t9.email AS latest_cw_parent_opp_bd_email
#   , t3.have_used_meta_tpi
#   , t4.is_meta_referral
#   , t5.is_meta_merchant
#   , t6.is_influenced_by_marketing_campaign
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.sf_id
#     ELSE t1.sf_rollup_ultimate_parent_id
#     END AS sf_rollup_ultimate_parent_id_v2
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.sf_business_name
#     ELSE t1.sf_rollup_ultimate_parent_name
#     END AS sf_rollup_ultimate_parent_name_v2
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.ad_internal_name
#     ELSE t1.sf_rollup_ultimate_parent_brand
#     END AS sf_rollup_ultimate_parent_brand_v2
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.sf_owner_name
#     ELSE t1.sf_rollup_owner_name
#     END AS sf_rollup_owner_name_v2
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.sf_owner_email
#     ELSE t1.sf_rollup_owner_email
#     END AS sf_rollup_owner_email_v2
#   , CASE
#     WHEN t1.sf_rollup_ultimate_parent_id = '0015j000012qKhS'
#       THEN t1.sf_owner_division
#     ELSE t1.sales_team
#     END AS sales_team_v2
#   , CASE
#     WHEN (lower(t1.ad_rollup_cp_manager_name) LIKE '%zora%' OR lower(t1.ad_rollup_cp_manager_name) LIKE '%tobing%' OR lower(t1.ad_rollup_cp_manager_name) LIKE '%pratiwi%' OR lower(t1.ad_rollup_cp_manager_name) LIKE 'julie%' OR lower(t1.ad_rollup_cp_manager_name) LIKE '%lucia%')
#       THEN true
#     WHEN (lower(t1.sf_rollup_cp_manager_name) LIKE '%zora%' OR lower(t1.sf_rollup_cp_manager_name) LIKE '%tobing%' OR lower(t1.sf_rollup_cp_manager_name) LIKE '%pratiwi%' OR lower(t1.sf_rollup_cp_manager_name) LIKE 'jiayi%' OR lower(t1.sf_rollup_cp_manager_name) LIKE '%lucia%')
#       THEN true
#     WHEN (lower(t1.ad_rollup_sales_rep_name) LIKE '%zora%' OR lower(t1.ad_rollup_sales_rep_name) LIKE '%tobing%' OR lower(t1.ad_rollup_sales_rep_name) LIKE '%pratiwi%' OR lower(t1.ad_rollup_sales_rep_name) LIKE 'julie%' OR lower(t1.ad_rollup_sales_rep_name) LIKE '%lucia%')
#       THEN true
#     WHEN (lower(t1.ad_rollup_account_manager_name) LIKE '%zora%' OR lower(t1.ad_rollup_account_manager_name) LIKE '%tobing%' OR lower(t1.ad_rollup_account_manager_name) LIKE '%pratiwi%' OR lower(t1.ad_rollup_account_manager_name) LIKE 'julie%' OR lower(t1.ad_rollup_account_manager_name) LIKE '%lucia%')
#       THEN true
#     WHEN (lower(t1.sf_rollup_owner_email) LIKE '%zora%' OR lower(t1.sf_rollup_owner_email) LIKE '%tobing%' OR lower(t1.sf_rollup_owner_email) LIKE '%pratiwi%' OR lower(t1.sf_rollup_owner_email) LIKE 'julie%' OR lower(t1.sf_rollup_owner_email) LIKE '%lucia%')
#       THEN true
#         -- when lower(t2.last_tpi_platform_name) is not null then true
#         -- when lower(t2.have_paid_tpi_transaction) is true then true
#     ELSE false
#     END AS is_cp_merchant_v2
#   , CASE
#     WHEN lower(t1.sf_rollup_country_of_hq) IN ('indonesia', 'philippines', 'thailand', 'malaysia', 'viet nam', 'vietnam')
#       THEN false
#     WHEN lower(t1.sf_rollup_country_of_hq) IS NULL
#       THEN false
#     ELSE true
#     END AS is_regional_merchant
#   , t10.cp_manager_name AS sf_cp_manager_name_join_w_owned
#   , t4.referral_id
#   , CASE
#       WHEN t14.is_cp_reseller=true AND t14.is_cp_referral IS NULL THEN 'CP Reseller'
#       WHEN t14.is_cp_referral=true AND t14.is_cp_reseller IS NULL THEN 'CP Referral'
#       ELSE t11.channel_partnership_segmentation
#     END AS channel_partnership_segmentation
#   , t12.latest_promo_code_id
#   --, t13.referral_code__c as sf_referral_id
#   , t15.referral_id as sf_referral_id
#   , CASE
#       WHEN t14.is_cp_referral IS NULL THEN false
#       ELSE t14.is_cp_referral
#     END AS is_cp_referral
#   , CASE
#       WHEN t14.is_cp_reseller IS NULL THEN false
#       ELSE t14.is_cp_reseller
#     END AS is_cp_reseller
#   , CASE
#       WHEN t1.sf_rollup_ultimate_parent_id='0015j00000mjFfi' THEN 'CP Referral'
#       WHEN t14.is_cp_reseller=true AND t14.is_cp_referral IS NULL THEN 'CP Reseller'
#       WHEN t14.is_cp_referral=true AND t14.is_cp_reseller IS NULL THEN 'CP Referral'
#       WHEN t14.is_cp_referral IS NULL AND t14.is_cp_reseller IS NULL THEN
#         CASE
#           WHEN t15.referral_id IS NOT NULL THEN 'CP Referral'
#           ELSE 'Others (No Sf Referral Code)'
#           END
#       ELSE 'Others (No Sf Referral Code)'
#   END AS cp_category
# FROM transform__growth.sales_attribution t1
# -- LEFT JOIN transform__growth.account_L30D_trend AS t2
# --   ON t1.sf_id = t2.sf_id AND t2.sf_id IS NOT NULL
# LEFT JOIN meta_tpi t3
#   ON t1.business_id = t3.user_id
# LEFT JOIN referral_code t4
#   ON t1.business_id = t4.business_id
# LEFT JOIN meta_merchant t5
#   ON t1.business_id = t5.business_id
# LEFT JOIN marketing_influence t6
#   ON t1.business_id = t6.business_id_formula__c
# LEFT JOIN transform__business_intelligence.dim_businesses t7
#   ON t1.business_id = t7.business_id
# LEFT JOIN clean__salesforce.opportunities t8
#   ON t1.latest_cw_parent_opp_id = t8.id
# LEFT JOIN clean__salesforce.users t9
#   ON t8.bd_owner__c = t9.id
# LEFT JOIN owned_cp t10
#   ON t10.business_id = t1.business_id
# LEFT JOIN promo_code t12
#   ON t1.business_id = t12.business_id
# -- LEFT JOIN clean__salesforce.accounts t13 ON t1.business_id = t13.business_id__c
# LEFT JOIN is_cp_referral_reseller t14
#   ON t1.business_id = t14.business_id
# LEFT JOIN all_sf_referral t15 ON t1.business_id = t15.business_id
# LEFT JOIN clean__google_sheets_growth.segment t11
#   ON lower(t15.referral_id) = lower(t11.referral_partner_id);;
#   }

  suggestions: yes

  dimension: sf_rollup_churn_comments {
    type: string
    sql: ${TABLE}.sf_rollup_churn_comments ;;
  }

  dimension: sf_rollup_churn_reason {
    type: string
    sql: ${TABLE}.sf_rollup_churn_reason ;;
  }

  dimension: sf_rollup_has_churned {
    type: string
    sql: ${TABLE}.sf_rollup_has_churned ;;
  }

  dimension: sf_rollup_revenue_split {
    type: string
    sql: ${TABLE}.sf_rollup_revenue_split ;;
  }

  dimension: sf_rollup_revenue_split_type {
    type: string
    sql: ${TABLE}.sf_rollup_revenue_split_type ;;
  }

  dimension_group: sf_rollup_handover_date {
    # type: string
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.sf_rollup_handover_date__c ;;
  }

  # dimension_group: bid_primary_lead_mql_date {
  #   type: time
  #   timeframes: [raw,date,week,month,quarter,year]
  #   convert_tz: no
  #   datatype: date
  #   sql: ${TABLE}.bid_primary_lead_mql_date ;;
  # }

  dimension: sf_churn_comments {
    type: string
    sql: ${TABLE}.sf_churn_comments__c ;;
  }

  dimension: sf_churn_reason {
    type: string
    sql: ${TABLE}.sf_churn_reason__c ;;
  }

  dimension: sf_has_churned {
    type: string
    sql: ${TABLE}.sf_has_churned__c ;;
  }

  dimension: sf_revenue_split {
    type: string
    sql: ${TABLE}.sf_revenue_split__c ;;
  }

  dimension: sf_revenue_split_type {
    type: string
    sql: ${TABLE}.sf_revenue_split_type__c ;;
  }

  dimension_group: sf_handover_date {
    # type: string
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.sf_handover_date__c ;;
  }

  # dimension: sf_cp_partner_category {
  #   type: string
  #   sql: ${TABLE}.sf_cp_partner_category ;;
  # }

  dimension: sf_cp_manager_name_join_w_owned {
    type: string
    sql: ${TABLE}.sf_cp_manager_name_join_w_owned ;;
  }

  dimension: referral_code {
    type: string
    sql: ${TABLE}.referral_id ;;
  }

  dimension: sf_referral_id {
    type: string
    sql: ${TABLE}.sf_referral_id ;;
  }

  dimension: sf_referral_id_cp_segmentation {
    type: string
    sql: ${TABLE}.sf_referral_cp_segmentation ;;
  }

  dimension: is_cp_referral {
    type: yesno
    label: "Under CP Referral"
    sql: ${TABLE}.is_cp_referral ;;
  }

  dimension: is_cp_reseller {
    type: yesno
    label: "Under CP Reseller"
    sql: ${TABLE}.is_cp_reseller ;;
  }

  dimension: promo_code {
    type: string
    label: "Latest Promo Code"
    sql: ${TABLE}.latest_promo_code_id ;;
  }

  #last 30D metrics
  # measure: MTD_TPV {
  #   type: sum
  #   sql: ${TABLE}.MTD_TPV ;;
  # }
  # measure: MTD_Rev {
  #   type: sum
  #   sql: ${TABLE}.MTD_Rev ;;
  # }
  # measure: MTD_TPV_delta {
  #   type: sum
  #   sql: ${TABLE}.MTD_TPV_delta ;;
  # }
  # measure: MTD_Rev_delta {
  #   type: sum
  #   sql: ${TABLE}.MTD_Rev_delta ;;
  # }
  # measure: L30D_TPV {
  #   type: sum
  #   sql: ${TABLE}.L30D_TPV ;;
  # }
  # measure: L30D_Rev {
  #   type: sum
  #   sql: ${TABLE}.L30D_Rev ;;
  # }
  # measure: L30D_TPV_delta {
  #   type: sum
  #   sql: ${TABLE}.L30D_TPV_delta ;;
  # }
  # measure: L30D_Rev_delta {
  #   type: sum
  #   sql: ${TABLE}.L30D_Rev_delta ;;
  # }
  # measure: L7D_TPV {
  #   type: sum
  #   sql: ${TABLE}.L7D_TPV ;;
  # }
  # measure: L7D_Rev {
  #   type: sum
  #   sql: ${TABLE}.L7D_Rev ;;
  # }
  # measure: L7D_TPV_delta {
  #   type: sum
  #   sql: ${TABLE}.L7D_TPV_delta ;;
  # }
  # measure: L7D_Rev_delta {
  #   type: sum
  #   sql: ${TABLE}.L7D_Rev_delta ;;
  # }

  # dimension: is_top_5_rev_dropping_parent_in_L30D {
  #   type: yesno
  #   sql: ${TABLE}.is_top_5_rev_dropping_parent_in_L30D ;;
  # }


  # dimension: is_top_5_rev_growing_parent_in_L30D {
  #   type: yesno
  #   sql: ${TABLE}.is_top_5_rev_growing_parent_in_L30D ;;
  # }

# lead attributions
  dimension: Xendit_Account_Email__c {
    type: string
    sql: ${TABLE}.Xendit_Account_Email__c ;;
  }


  dimension: bid_primary_lead_id {
    type: string
    case_sensitive: yes
    sql: ${TABLE}.bid_primary_lead_id ;;
  }


  dimension_group: bid_primary_lead_mql_date {
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.bid_primary_lead_mql_date ;;
  }

  dimension_group: bid_primary_lead_create_date {
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.bid_primary_lead_create_date ;;
  }

  dimension: bid_primary_lead_source {
    type: string
    sql: ${TABLE}.bid_primary_lead_source ;;
  }
  dimension: bid_primary_lead_how_do_you_hear_about_xendit {
    type: string
    sql: ${TABLE}.bid_primary_lead_how_do_you_hear_about_xendit ;;
  }
  dimension: bid_primary_lead_offer_type_1st_touch {
    type: string
    sql: ${TABLE}.bid_primary_lead_offer_type_1st_touch ;;
  }
  dimension: bid_primary_lead_offer_type_last_touch {
    type: string
    sql: ${TABLE}.bid_primary_lead_offer_type_last_touch ;;
  }
  dimension: bid_primary_contact_id {
    type: string
    case_sensitive: yes
    sql: ${TABLE}.bid_primary_contact_id ;;
  }

  dimension_group: bid_primary_contact_create_date {
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.bid_primary_contact_create_date ;;
  }

  dimension: bid_primary_contact_source {
    type: string
    sql: ${TABLE}.bid_primary_contact_source ;;
  }
  dimension: bid_primary_contact_how_do_you_hear_about_xendit {
    type: string
    sql: ${TABLE}.bid_primary_contact_how_do_you_hear_about_xendit ;;
  }
  dimension: bid_primary_contact_offer_type_1st_touch {
    type: string
    sql: ${TABLE}.bid_primary_contact_offer_type_1st_touch ;;
  }
  dimension: bid_primary_contact_offer_type_last_touch {
    type: string
    sql: ${TABLE}.bid_primary_contact_offer_type_last_touch ;;
  }
  dimension: bid_primary_contact_opportunity {
    type: string
    sql: ${TABLE}.bid_primary_contact_opportunity ;;
  }

  dimension_group: bid_primary_contact_opportunity_create_date {
    type: time
    timeframes: [raw,date,week,month,quarter,year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.bid_primary_contact_opportunity_create_date ;;
  }

  #ad dimensions

  dimension: ad_acc_manager_name {
    type: string
    sql: ${TABLE}.ad_account_manager_name ;;
  }

  dimension: ad_owner {
    type: string
    sql: ${TABLE}.ad_account_owner ;;
  }

  dimension: ad_owner_email {
    type: string
    sql: ${TABLE}.ad_account_owner_email ;;
  }

  dimension: ad_bd_manager_name {
    type: string
    sql: ${TABLE}.ad_bd_manager_name ;;
  }

  dimension: ad_business_name {
    type: string
    sql: ${TABLE}.ad_business_name ;;
  }

  dimension: ad_cp_manager_name {
    type: string
    sql: ${TABLE}.ad_cp_manager_name ;;
  }

  dimension: ad_internal_name {
    type: string
    sql: ${TABLE}.ad_internal_name ;;
  }

  dimension: ad_master_business_name {
    type: string
    sql: ${TABLE}.ad_master_business_name ;;
  }

  dimension: ad_rollup_acc_manager_name {
    type: string
    sql: ${TABLE}.ad_rollup_account_manager_name ;;
  }

  dimension: ad_rollup_owner {
    type: string
    sql: ${TABLE}.ad_rollup_account_owner ;;
  }

  dimension: ad_rollup_owner_email {
    type: string
    sql: ${TABLE}.ad_rollup_account_owner_email ;;
  }

  dimension: ad_rollup_bd_manager_name {
    type: string
    sql: ${TABLE}.ad_rollup_bd_manager_name ;;
  }

  dimension: ad_rollup_cp_manager_name {
    type: string
    sql: ${TABLE}.ad_rollup_cp_manager_name ;;
  }

  dimension: ad_rollup_internal_name {
    type: string
    sql: ${TABLE}.ad_rollup_internal_name ;;
  }

  dimension: ad_rollup_sales_rep_name {
    type: string
    sql: ${TABLE}.ad_rollup_sales_rep_name ;;
  }

  dimension: ad_rollup_technical_rep_name {
    type: string
    sql: ${TABLE}.ad_rollup_technical_rep_name ;;
  }

  dimension: ad_sales_rep_name {
    type: string
    sql: ${TABLE}.ad_sales_rep_name ;;
  }

  dimension: ad_technical_rep_name {
    type: string
    sql: ${TABLE}.ad_technical_rep_name ;;
  }

  dimension: business_id {
    primary_key: yes
    type: string
    sql: ${TABLE}.business_id ;;
    link: {
      label: "Open in Admin Dashboard"
      url: "https://admin-dashboard.tidnex.com/customer-management-v2/{{ value }}"
    }
  }

  dimension: master_business_id {
    type: string
    sql: ${TABLE}.master_business_id ;;
    link: {
      label: "Open in Admin Dashboard"
      url: "https://admin-dashboard.tidnex.com/customer-management-v2/{{ value }}"
    }
  }

  dimension: rollup_business_id {
    type: string
    sql: (CASE
          WHEN ${TABLE}.master_business_id='65cb3b45e70925c5d0699539' then ${TABLE}.business_id
          ELSE coalesce(${TABLE}.master_business_id, ${TABLE}.business_id)
          END
          );;
          # sql: coalesce(${TABLE}.master_business_id, ${TABLE}.business_id) ;;
      link: {
        label: "Open in Admin Dashboard"
        url: "https://admin-dashboard.tidnex.com/customer-management-v2/{{ value }}"
      }
    }

    dimension: rollup_sf_id {
      type: string
      case_sensitive: yes
      sql: (CASE
            WHEN ${TABLE}.sf_master_id='0015j00001Zm9mfAAB' then ${TABLE}.sf_id
            ELSE coalesce(${TABLE}.sf_master_id, ${TABLE}.sf_id)
          END
    );;
    # sql: coalesce(${TABLE}.sf_master_id, ${TABLE}.sf_id) ;;
        link: {
          label: "Open in Salesforce"
          url: "https://xendit.lightning.force.com/{{ value }}"
        }
      }

      dimension: ad_rollup_business_name {
        type: string
        sql: coalesce(${TABLE}.ad_master_business_name, ${TABLE}.ad_business_name) ;;
      }

      dimension: sf_rollup_business_name {
        type: string
        # sql: coalesce(${TABLE}.sf_master_business_name, ${TABLE}.sf_business_name) ;;
        # sql: coalesce(REGEXP_EXTRACT(${TABLE}.sf_master_business_name, '^(.*?\|.*?)(?=\|)'),REGEXP_EXTRACT(${TABLE}.sf_business_name, '^(.*?\|.*?)(?=\|)'));;
        sql:
        (
          CASE
              WHEN ${TABLE}.master_business_id = '65cb3b45e70925c5d0699539'
                THEN ${TABLE}.sf_business_name
              ELSE REPLACE(
                COALESCE(
                  -- Case 1 & 2: grab first two segments before the next "|"
                  REGEXP_EXTRACT(${TABLE}.sf_master_business_name, '^(.*?\\|.*?)(?=\\|)'),
                  REGEXP_EXTRACT(${TABLE}.sf_business_name,       '^(.*?\\|.*?)(?=\\|)'),

                  -- Case 3: handles trailing "| XPM"
                  REGEXP_EXTRACT(${TABLE}.sf_master_business_name, '^(.*)\\|\\s*XPM\\s*$'),
                  REGEXP_EXTRACT(${TABLE}.sf_business_name,       '^(.*)\\|\\s*XPM\\s*$'),

                  -- Case 4: handles parenthesized "(… | CC | …) - blah"
                  REGEXP_EXTRACT(${TABLE}.sf_master_business_name, '^\\((.*?\\|\\s*[A-Z]{2,3})'),
                  REGEXP_EXTRACT(${TABLE}.sf_business_name,       '^\\((.*?\\|\\s*[A-Z]{2,3})')
                ),
                '('  -- strip literal "("
              )
          END
        );;
        # trino presto version
        # (CASE
        #       WHEN ${TABLE}.master_business_id='65cb3b45e70925c5d0699539' then ${TABLE}.sf_business_name
        #       ELSE REPLACE(coalesce(REGEXP_EXTRACT(${TABLE}.sf_master_business_name, '^(.*?\|.*?)(?=\|)'),REGEXP_EXTRACT(${TABLE}.sf_business_name, '^(.*?\|.*?)(?=\|)')), '(', '')
        #       END
        #       )
        }

        dimension: sf_rollup_ultimate_parent_brand {
          type: string
          sql: coalesce(lower(${TABLE}.sf_rollup_ultimate_parent_brand_v2),lower(${ad_rollup_business_name})) ;;
        }

        dimension: sf_rollup_parent_industry_sector {
          type: string
          sql: ${TABLE}.sf_rollup_industry_category ;;
        }

        dimension: sf_rollup_industry_sector {
          type: string
          sql: ${TABLE}.sf_rollup_child_industry ;;
        }

        dimension: sf_rollup_is_regional__c {
          type: yesno
          # allow_fill: no
          sql: ${TABLE}.sf_rollup_is_regional__c ;;
        }

        dimension: sf_is_regional__c {
          type: yesno
          sql: ${TABLE}.sf_is_regional__c ;;
        }

        dimension: sf_parent_industry_sector {
          type: string
          sql: ${TABLE}.sf_industry_category ;;
        }

        dimension: sf_industry_sector {
          type: string
          sql: ${TABLE}.sf_child_industry ;;
        }

        dimension: sf_owner_country {
          type: string
          sql: ${TABLE}.sf_acc_owner_country ;;
        }

        dimension: sf_acc_owner_division {
          type: string
          sql: ${TABLE}.sf_acc_owner_division ;;
        }

        dimension: sf_business_name {
          type: string
          sql: ${TABLE}.sf_business_name ;;
        }

        dimension: sf_cp_manager_email {
          type: string
          sql: ${TABLE}.sf_cp_manager_email ;;
        }

        dimension: sf_cp_manager_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_cp_manager_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_cp_manager_name {
          type: string
          sql: ${TABLE}.sf_cp_manager_name ;;
        }

        dimension: sf_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_master_brand {
          type: string
          sql: ${TABLE}.sf_master_brand ;;
        }

        dimension: sf_rollup_master_brand {
          type: string
          #sql: ${TABLE}.sf_master_brand ;;
          sql: coalesce(${TABLE}.sf_master_brand, ${TABLE}.sf_business_name) ;;
        }

        dimension: sf_master_business_name {
          type: string
          sql: ${TABLE}.sf_master_business_name ;;
        }

        dimension: sf_master_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_master_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_owner_division {
          type: string
          sql: ${TABLE}.sf_owner_division ;;
        }

        dimension: sf_owner_email {
          type: string
          sql: ${TABLE}.sf_owner_email ;;
        }

        dimension: sf_owner_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_owner_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_owner_name {
          type: string
          sql: ${TABLE}.sf_owner_name ;;
        }

        dimension: sf_rollup_owner_country {
          type: string
          sql: ${TABLE}.sf_rollup_acc_owner_country ;;
        }

        dimension: sales_country {
          type: string
          sql: ${TABLE}.sales_country ;;
        }

        dimension: sf_rollup_acc_owner_division {
          type: string
          sql: ${TABLE}.sf_rollup_acc_owner_division ;;
        }

        dimension: sf_rollup_cp_manager_email {
          type: string
          sql: ${TABLE}.sf_rollup_cp_manager_email ;;
        }

        dimension: sf_rollup_cp_manager_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_rollup_cp_manager_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_rollup_cp_manager_name {
          type: string
          sql: ${TABLE}.sf_rollup_cp_manager_name ;;
        }

        dimension: sf_rollup_owner_division {
          type: string
          sql: ${TABLE}.sf_rollup_owner_division ;;
        }

        dimension: sf_rollup_owner_email {
          type: string
          sql: ${TABLE}.sf_rollup_owner_email_v2 ;;
        }

        dimension: sf_rollup_owner_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_rollup_owner_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_rollup_owner_name {
          type: string
          sql: ${TABLE}.sf_rollup_owner_name_v2 ;;
        }

        dimension: sf_rollup_secondary_owner_email {
          type: string
          sql: ${TABLE}.sf_rollup_secondary_owner_email ;;
        }

        dimension: sf_rollup_secondary_owner_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_rollup_secondary_owner_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_rollup_secondary_owner_name {
          type: string
          sql: ${TABLE}.sf_rollup_secondary_owner_name ;;
        }

        dimension: sf_rollup_team_owner {
          type: string
          sql: ${TABLE}.sf_rollup_team_owner ;;
        }

        dimension: sales_team {
          type: string
          sql: ${TABLE}.sales_team_v2 ;;
        }

        dimension: sf_rollup_ultimate_parent_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_rollup_ultimate_parent_id_v2 ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_rollup_ultimate_parent_name {
          type: string
          sql: ${TABLE}.sf_rollup_ultimate_parent_name_v2 ;;
        }

        dimension: sf_secondary_owner_email {
          type: string
          sql: ${TABLE}.sf_secondary_owner_email ;;
        }

        dimension: sf_secondary_owner_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_secondary_owner_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_secondary_owner_name {
          type: string
          sql: ${TABLE}.sf_secondary_owner_name ;;
        }

        dimension: sf_team_owner {
          type: string
          sql: ${TABLE}.sf_team_owner ;;
        }

        dimension: sf_ultimate_parent_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.sf_ultimate_parent_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_ultimate_parent_name {
          type: string
          sql: ${TABLE}.sf_ultimate_parent_name ;;
        }

        dimension: type {
          type: string
          sql: ${TABLE}.type ;;
        }

        dimension: description {
          type: string
          sql: ${TABLE}.description ;;
        }

        dimension: sf_team_assignment{
          type: string
          sql: ${TABLE}.sf_team_assignment ;;
        }

        dimension: sf_rollup_team_assignment {
          type: string
          sql: ${TABLE}.sf_rollup_team_assignment ;;
        }

        dimension: sf_rollup_cps_email {
          type: string
          sql: ${TABLE}.sf_rollup_cps_email ;;
        }

        dimension: sf_rollup_solution_email {
          type: string
          sql: ${TABLE}.sf_rollup_solution_email ;;
        }

        dimension: sf_rollup_presales_email {
          type: string
          sql: ${TABLE}.sf_rollup_presales_email ;;
        }

        dimension: sf_rollup_cs_email {
          type: string
          sql: ${TABLE}.sf_rollup_cs_email ;;
        }

        dimension: sf_rollup_cpm_email {
          type: string
          sql: ${TABLE}.sf_rollup_cpm_email ;;
        }

        dimension: sf_rollup_bdm_email {
          type: string
          sql: ${TABLE}.sf_rollup_bdm_email ;;
        }

        dimension: weekly_update {
          type: string
          sql: ${TABLE}.weekly_update__c ;;
        }

        dimension: weekly_update_history {
          type: string
          sql: ${TABLE}.weekly_update_history__c ;;
        }

        dimension: sf_rollup_country_of_hq {
          type: string
          sql: ${TABLE}.sf_rollup_country_of_hq ;;
        }

        dimension: ad_rollup_country_of_operation {
          type: string
          sql: ${TABLE}.ad_rollup_country_of_operation ;;
        }

        dimension: is_regional_merchant {
          type: yesno
          label: "Is Regional Merchant"
          # sql: ${TABLE}.is_regional_merchant ;;
          sql: ${TABLE}.sf_rollup_is_regional__c ;;
        }

        # ----- Sets of fields for drilling ------

        # dimension: rollup_min_fifth_transaction_date {
        #   type: string
        #   sql: ${TABLE}.rollup_min_fifth_transaction_date ;;
        # }

        dimension_group: rollup_min_fifth_transaction {
          type: time
          timeframes: [
            raw,
            date,
            week,
            month,
            quarter,
            year
          ]
          convert_tz: no
          datatype: date
          sql: ${TABLE}.rollup_min_fifth_transaction_date ;;
        }

        dimension: ad_user_category {
          type: string
          sql: ${TABLE}.ad_user_category__c ;;
        }

        dimension: latest_cw_parent_opp_id {
          type: string
          case_sensitive: yes
          sql: ${TABLE}.latest_cw_parent_opp_id ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: latest_cw_parent_opp_date {
          type: string
          sql: ${TABLE}.latest_cw_parent_opp_date ;;
        }

        dimension_group: latest_closed_won_parent_opp_date {
          type: time
          timeframes: [
            date,
            week,
            month,
            quarter,
            year
          ]
          convert_tz: no
          datatype: date
          sql: CAST(${TABLE}.latest_cw_parent_opp_date AS DATE) ;;
        }

        dimension: latest_cw_parent_opp_owner_email {
          type: string
          sql: ${TABLE}.latest_cw_parent_opp_owner_email ;;
        }

        dimension: latest_cw_parent_opp_bd_email {
          type: string
          sql: ${TABLE}.latest_cw_parent_opp_bd_email ;;
        }

        dimension: is_attributable {
          type: yesno
          # sql: ${TABLE}.is_attributable ;;
          sql: ${TABLE}.is_attributable;;
        }

        dimension: have_used_meta_tpi {
          type: yesno
          label: "Have Used META TPI"
          sql: ${TABLE}.have_used_meta_tpi ;;
        }

        dimension: is_meta_referral {
          type: yesno
          label: "Is META Referral"
          sql: ${TABLE}.is_meta_referral ;;
        }

        dimension: is_meta_merchant {
          type: yesno
          label: "Is META Merchant"
          sql: ${TABLE}.is_meta_merchant ;;
        }

        dimension: is_influenced_by_marketing_campaign {
          type: yesno
          sql: ${TABLE}.is_influenced_by_marketing_campaign ;;
        }

        dimension: is_cp_merchant {
          type: yesno
          label: "Is Claimed by ID CP"
          # sql: ${TABLE}.is_cp_merchant_v2 ;;
          sql: ${TABLE}.is_cp_merchant_v3 ;;
        }

        measure: count {
          type: count
          drill_fields: [detail*]
        }

        measure: count_sf_rollup_ultimate_parent_id {
          type: count_distinct
          drill_fields: [sf_rollup_ultimate_parent_id,sf_rollup_ultimate_parent_name,sf_rollup_owner_name,sf_rollup_cp_manager_name]
          sql: ${TABLE}.sf_rollup_ultimate_parent_id_v2 ;;
        }

        dimension: sf_referral_name {
          type: string
          label: "Sf CP Partner Name"
          sql: ${TABLE}.sf_cp_partner_name ;;
          # sql: (CASE
          #         WHEN ${is_cp_referral}=true OR ${is_cp_reseller}=true
          #           THEN lower(${sf_rollup_ultimate_parent_brand})
          #         ELSE (CASE WHEN ${is_cp_merchant}=true THEN lower(${sf_referral_id}) ELSE NULL END)
          #       END
          #       ) ;;
        }

        dimension: sf_referral_parent_id {
          type: string
          case_sensitive: yes
          label: "Sf CP Partner ID"
          sql: ${TABLE}.sf_cp_partner_id ;;
          # sql: (CASE
          #         WHEN ${is_cp_referral}=true OR ${is_cp_reseller}=true
          #           THEN ${sf_rollup_ultimate_parent_id}
          #         ELSE null
          #       END
          #       ) ;;
          link: {
            label: "Open in Salesforce"
            url: "https://xendit.lightning.force.com/{{ value }}"
          }
        }

        dimension: sf_cp_partner_direct_indirect_subcategory {
          type: string
          label: "Sf CP Partner Category"
          sql: ${TABLE}.sf_cp_partner_category ;;
          # sql: ${TABLE}.sf_cp_partner_direct_indirect_subcategory ;;
          # sql: (CASE
          #         WHEN ${is_cp_merchant}=true and ${sf_cp_partner_category}='CP Reseller'
          #           THEN 'Direct'
          #         WHEN ${is_cp_merchant}=true and ${sf_cp_partner_category}='CP Referral'
          #           THEN CASE
          #             WHEN ${sf_rollup_owner_email}='<EMAIL>' THEN 'Indirect Self Serve'
          #             WHEN ${sf_rollup_owner_email} IS NOT NULL AND ${sf_rollup_owner_email} NOT IN ('<EMAIL>') THEN 'Indirect Sales'
          #             ELSE 'Uncategorized'
          #           END
          #         WHEN ${is_cp_merchant}=true and ${sf_cp_partner_category}='Others (No Sf Referral Code)'
          #           THEN CASE
          #             WHEN ${sf_rollup_owner_email}='<EMAIL>' THEN 'Indirect Self Serve'
          #             WHEN ${sf_rollup_owner_email} IS NOT NULL AND ${sf_rollup_owner_email} NOT IN ('<EMAIL>') THEN 'Indirect Sales'
          #             WHEN ${ad_rollup_owner_email} IS NOT NULL AND ${ad_rollup_owner_email} NOT IN ('<EMAIL>') THEN 'Indirect Sales'
          #             WHEN ${ad_rollup_owner_email} IS NULL AND ${sf_rollup_owner_email} IS NULL AND ${sales_team}='ID Self Serve' THEN 'Indirect Self Serve'
          #             ELSE 'Uncategorized'
          #           END
          #         ELSE 'Uncategorized'
          #       END
          #       );;
        }

        dimension: sf_cp_partner_direct_indirect_category {
          type: string
          label: "Sf CP Partner Subcategory"
          sql: ${TABLE}.sf_cp_partner_subcategory ;;
          # sql: (CASE
          #         WHEN ${sf_cp_partner_direct_indirect_subcategory}='Direct' then 'Direct'
          #         WHEN ${sf_cp_partner_direct_indirect_subcategory}='Indirect Self Serve' then 'Indirect'
          #         WHEN ${sf_cp_partner_direct_indirect_subcategory}='Indirect Sales' then 'Indirect'
          #         ELSE NULL
          #       END
          # );;
        }

        dimension: bid_sfid {
          type: string
          hidden: yes
          sql: coalesce(${sf_id},${business_id}) ;;
        }

        measure: count_rollup_sf_id {
          type: count_distinct
          drill_fields: [rollup_sf_id,rollup_business_id,sf_rollup_business_name,sf_rollup_owner_email,sf_rollup_cp_manager_email,latest_closed_won_parent_opp_date_date]
          sql: (CASE
            WHEN ${TABLE}.sf_master_id='0015j00001Zm9mfAAB' then ${TABLE}.sf_id
            ELSE coalesce(${TABLE}.sf_master_id, ${TABLE}.sf_id)
          END
    ) ;;
        }

        # ----- Sets of fields for drilling ------
        set: detail {
          fields: [
            ad_business_name,
            sf_business_name,
            ad_master_business_name,
            sf_master_business_name,
            ad_internal_name,
            ad_rollup_internal_name,
            sf_ultimate_parent_name,
            sf_rollup_ultimate_parent_name,
            ad_cp_manager_name,
            ad_rollup_cp_manager_name,
            sf_cp_manager_name,
            sf_rollup_cp_manager_name,
            ad_bd_manager_name,
            ad_rollup_bd_manager_name,
            ad_sales_rep_name,
            ad_rollup_sales_rep_name,
            ad_acc_manager_name,
            ad_rollup_acc_manager_name,
            ad_technical_rep_name,
            ad_rollup_technical_rep_name,
            sf_owner_name,
            sf_rollup_owner_name,
            sf_secondary_owner_name,
            sf_rollup_secondary_owner_name
          ]
        }
      }
