view: fact_sales_targets {
  sql_table_name: clean__google_sheets.fact_sales_targets ;;
  suggestions: yes

  dimension: amount_type {
    type: string
    sql: ${TABLE}.amount_type ;;
  }
  dimension: company {
    type: string
    sql: ${TABLE}.company ;;
  }
  dimension: company_owner {
    type: string
    sql: ${TABLE}.company_owner ;;
  }
  # dimension: daily_amount {
  #   type: number
  #   sql: ${TABLE}.daily_amount ;;
  # }
  dimension_group: date {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.`date` ;;
  }
  dimension: metric_type {
    type: string
    sql: ${TABLE}.metric_type ;;
  }
  dimension: record_type {
    type: string
    sql: ${TABLE}.record_type ;;
  }
  dimension: region {
    type: string
    sql: ${TABLE}.region ;;
  }
  # dimension: currency {
  #   type: string
  #   sql:
  #   (
  #     CASE
  #       WHEN ${TABLE}.country = 'Indonesia' THEN 'IDR'
  #       WHEN ${TABLE}.country = 'Philippines' THEN 'PHP'
  #       WHEN ${TABLE}.country = 'Thailand' THEN 'THB'
  #       WHEN ${TABLE}.country = 'Malaysia' THEN 'MYR'
  #       WHEN ${TABLE}.country = 'Vietnam' THEN 'VND'
  #       ELSE 'Unknown'
  #     END
  #   ) ;;
  # }
  dimension: region_owner {
    type: string
    sql: ${TABLE}.region_owner ;;
  }
  dimension: segment {
    type: string
    sql: ${TABLE}.segment ;;
  }
  dimension: segment_owner {
    type: string
    sql: ${TABLE}.segment_owner ;;
  }
  dimension: team {
    type: string
    sql: ${TABLE}.team ;;
  }
  dimension: team_owner {
    type: string
    sql: ${TABLE}.team_owner ;;
  }
  dimension: user {
    type: string
    sql: ${TABLE}.`user` ;;
  }
  measure: count {
    type: count
  }
  measure: daily_amount {
    type: sum
    sql: ${TABLE}.daily_amount ;;
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0.##"
  }
}
