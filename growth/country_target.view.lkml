# The name of this view in Looker is "Sales Target V1"
view: country_target {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  # sql_table_name: clean__google_sheets.sales_target_v1 ;;
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: SELECT * FROM clean__google_sheets.sales_target_v1 WHERE lower(team) LIKE ('%country%') ;;
  }
  suggestions: yes

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Country" in Explore.

  dimension: country {
    label: "Currency"
    type: string
    # map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension: email {
    label: "Description"
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: metric {
    type: string
    sql: ${TABLE}.metric ;;
  }

  dimension: is_regional {
    type: yesno
    sql: CASE WHEN ${TABLE}.email IN ('Regional PG', 'Remittance') THEN TRUE ELSE FALSE END ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: month {
    label: "Target"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}."month" as DATE) ;;
  }

  dimension: name {
    label: "Type"
    type: string
    sql: ${TABLE}.name ;;
  }

  # dimension: target {
  #   type: number
  #   sql: ${TABLE}.target ;;
  # }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: target {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Revenue Final"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: budget {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Revenue Budget"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: reforecast {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Revenue Reforecast"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: net_target {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Net Revenue Final"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: net_budget {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Net Revenue Budget"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: net_reforecast {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "P&L Net Revenue Reforecast"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: total {
    type: sum
    sql: ${TABLE}.target ;;
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  # measure: average_target {
  #   type: average
  #   sql: ${target} ;;  }

  dimension: team {
    type: string
    sql: ${TABLE}.team ;;
  }

  # measure: count {
  #   type: count
  #   drill_fields: [name]
  # }
}
