view: solution_revenue_attribution_temp {
  #source: https://xenditco.cloud.looker.com/sql/zxtrdsr9m3vfgb?toggle=sql
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: WITH
      solutionslist AS (
            SELECT
                sr.opportunity__c as opportunity_id,
                op.accountid as account_id,
                op.business_id_formula__c as business_id,
                u.email as se_email,
                sr.request_status__c AS se_req_status,
                sr.type_of_request__c AS se_req_type,
                sr.team_assignment__c AS team_name,
                CAST(sr.solution_pic_assignment_date__c as date) as assignmentdate
            FROM clean__salesforce.solution_engineering_request sr
            LEFT JOIN clean__salesforce.opportunities op ON op.id = sr.opportunity__c
            LEFT JOIN clean__salesforce.users u ON solution_pic__c = u.id
            WHERE sr.isdeleted = FALSE
            AND sr.solution_pic__c IS NOT NULL
            AND sr.team_assignment__c='Solution Engineering Team'
            AND sr.request_status__c NOT IN ('Draft','Rejected')
        )
      ,RankedSolutions AS (
            SELECT
                *,
                ROW_NUMBER() OVER (
                  PARTITION BY opportunity_id ORDER BY assignmentdate)
                AS rn
            FROM solutionslist
        )
      ,SolutionActivation AS (
        SELECT
          rs.*
          ,CAST(op.activation_date__c as date) as activationdate
        FROM RankedSolutions rs
        LEFT JOIN clean__salesforce.opportunities op ON op.id = rs.opportunity_id
        WHERE rn=1
      )
      ,RankedAttribution AS (
        SELECT *
            ,CASE
              WHEN (assignmentdate>activationdate OR activationdate IS NULL) THEN CAST(DATE_ADD('MONTH', n.n, date_trunc('MONTH', assignmentdate)) AS DATE)
              ELSE CAST(DATE_ADD('MONTH', n.n, date_trunc('MONTH', activationdate)) AS DATE)
              END AS attribution_month
          FROM SolutionActivation
          INNER JOIN (
            SELECT ROW_NUMBER() OVER (
                ORDER BY (
                    SELECT NULL
                    )
                ) - 1 AS n
            FROM (
              VALUES (0)
                ,(1)
                ,(2)
                ,(3)
                ,(4)
                ,(5)
              ) AS numbers(n)
            ) n ON n.n < 6
          ORDER BY 10
            ,11 ASC
      )
      ,RevenueAttribution AS (
        SELECT
          *
          ,CONCAT(
              business_id
              ,CAST(attribution_month AS STRING)
          ) AS ra_key
        FROM RankedAttribution
      )
      ,transactions AS (
        SELECT *, CONCAT(t.business_id, CAST(t.transaction_month AS STRING)) AS t_key
        FROM transform__growth.monthly_salesforce_transaction t
      )
        SELECT
            t.transaction_month
            ,t.product_id
            ,t.business_id
            ,t.tpv_usd
            ,t.revenue_usd
            ,t.transactions
            ,t.product_type
            ,t.product_subtype
            ,t.channel_name
            ,t.opp_id
            ,t.parent_opp_id
            ,t.account_id
            ,t.est_revenue_product_local
            ,t.est_tpv_product_local
            ,t.est_product_trx_count
            ,t.est_revenue_product_usd
            ,t.est_tpv_product_usd
            ,ra.attribution_month as se_rev_attr_month
            ,ra.se_email as se_rev_attr
            ,ra.activationdate as se_rev_attr_opp_actvtn_date
            ,concat(t.business_id,ra.se_email,t.product_id,cast(t.transaction_month as STRING)) as primary_key
        FROM transactions t
        LEFT JOIN RevenueAttribution ra ON t.t_key=ra.ra_key;;
  }

  suggestions: no

  dimension: main_primary_key {
    type: string
    hidden: yes
    primary_key: yes
    sql: ${TABLE}.primary_key ;;
  }

  dimension: opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.opp_id ;;
  }

  dimension: parent_opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.parent_opp_id ;;
  }

  dimension: account_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.account_id ;;
  }

  dimension: business_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.business_id ;;
  }

  dimension: n {
    type: number
    sql: ${TABLE}.n ;;
  }

  dimension_group: transaction_month {
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.transaction_month AS DATE) ;;
  }

  dimension: product_id {
    type: string
    sql: ${TABLE}.product_id ;;
  }

  measure: tpv_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.tpv_usd ;;
  }

  measure: revenue_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.revenue_usd ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  measure: transaction_count {
    type: sum
    drill_fields: [detail*]
    value_format: "#,##0"
    sql: ${TABLE}.transactions ;;
  }

  measure: est_revenue_product_local {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    sql: ${TABLE}.est_revenue_product_local ;;
  }

  measure: est_tpv_product_local {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    sql: ${TABLE}.est_tpv_product_local ;;
  }

  measure: est_product_trx_count {
    type: sum
    drill_fields: [detail*]
    value_format: "#,##0"
    sql: ${TABLE}.est_product_trx_count ;;
  }

  measure: est_revenue_product_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    sql: ${TABLE}.est_revenue_product_usd ;;
  }

  measure: est_tpv_product_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    sql: ${TABLE}.est_tpv_product_usd ;;
  }

  dimension: t_key {
    type: string
    sql: ${TABLE}.t_key ;;
  }

  dimension_group: attribution_month {
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.se_rev_attr_month AS DATE) ;;
  }

  dimension: se_email {
    type: string
    sql: ${TABLE}.se_rev_attr ;;
  }

  dimension_group: se_rev_attr_opp_actvtn_date {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.se_rev_attr_opp_actvtn_date AS DATE);;
  }

  set: detail {
    fields: [
      product_id,
      business_id,
      tpv_usd,
      revenue_usd,
      transaction_count,
      product_type,
      product_subtype,
      channel_name,
      opportunity_id,
      parent_opportunity_id,
      account_id,
      n,
      est_revenue_product_local,
      est_tpv_product_local,
      est_product_trx_count,
      est_revenue_product_usd,
      est_tpv_product_usd,
      t_key,
    ]
  }

  # # You can specify the table name if it's different from the view name:
  # sql_table_name: my_schema_name.tester ;;
  #
  # # Define your dimensions and measures here, like this:
  # dimension: user_id {
  #   description: "Unique ID for each user that has ordered"
  #   type: number
  #   sql: ${TABLE}.user_id ;;
  # }
  #
  # dimension: lifetime_orders {
  #   description: "The total number of orders for each user"
  #   type: number
  #   sql: ${TABLE}.lifetime_orders ;;
  # }
  #
  # dimension_group: most_recent_purchase {
  #   description: "The date when each user last ordered"
  #   type: time
  #   timeframes: [date, week, month, year]
  #   sql: ${TABLE}.most_recent_purchase_at ;;
  # }
  #
  # measure: total_lifetime_orders {
  #   description: "Use this for counting lifetime orders across many users"
  #   type: sum
  #   sql: ${lifetime_orders} ;;
  # }
}

# view: solution_revenue_attribution_temp {
#   # Or, you could make this view a derived table, like this:
#   derived_table: {
#     sql: SELECT
#         user_id as user_id
#         , COUNT(*) as lifetime_orders
#         , MAX(orders.created_at) as most_recent_purchase_at
#       FROM orders
#       GROUP BY user_id
#       ;;
#   }
#
#   # Define your dimensions and measures here, like this:
#   dimension: user_id {
#     description: "Unique ID for each user that has ordered"
#     type: number
#     sql: ${TABLE}.user_id ;;
#   }
#
#   dimension: lifetime_orders {
#     description: "The total number of orders for each user"
#     type: number
#     sql: ${TABLE}.lifetime_orders ;;
#   }
#
#   dimension_group: most_recent_purchase {
#     description: "The date when each user last ordered"
#     type: time
#     timeframes: [date, week, month, year]
#     sql: ${TABLE}.most_recent_purchase_at ;;
#   }
#
#   measure: total_lifetime_orders {
#     description: "Use this for counting lifetime orders across many users"
#     type: sum
#     sql: ${lifetime_orders} ;;
#   }
# }
