view: sf_monthly_transactions {
  # sql_table_name: transform__growth.monthly_salesforce_transaction;;
  # suggestions: no

  # dimension: opportunity_id {
  #   type: string
  #   primary_key: yes
  #   link: {
  #     label: "Open in Salesforce"
  #     url: "https://xendit.lightning.force.com/{{ value }}"
  #   }
  #   sql: ${TABLE}.opp_id ;;
  # }

  # dimension: parent_opportunity_id {
  #   type: string
  #   link: {
  #     label: "Open in Salesforce"
  #     url: "https://xendit.lightning.force.com/{{ value }}"
  #   }
  #   sql: ${TABLE}.parent_opp_id ;;
  # }

  # dimension: account_id {
  #   type: string
  #   link: {
  #     label: "Open in Salesforce"
  #     url: "https://xendit.lightning.force.com/{{ value }}"
  #   }
  #   sql: ${TABLE}.account_id ;;
  # }

  # dimension: business_id {
  #   type: string
  #   link: {
  #     label: "Open in Salesforce"
  #     url: "https://xendit.lightning.force.com/{{ value }}"
  #   }
  #   sql: ${TABLE}.business_id ;;
  # }

  # dimension: se_email {
  #   type: string
  #   sql: ${TABLE}.se_email ;;
  # }

  # dimension: n {
  #   type: number
  #   sql: ${TABLE}.n ;;
  # }

  # dimension_group: attribution_month {
  #   type: time
  #   timeframes: [
  #     month,
  #     quarter,
  #     year
  #   ]
  #   convert_tz: no
  #   datatype: date
  #   sql: CAST(${TABLE}.attribution_month AS DATE) ;;
  # }

  # dimension_group: transaction_month {
  #   type: time
  #   timeframes: [
  #     month,
  #     quarter,
  #     year
  #   ]
  #   convert_tz: no
  #   datatype: date
  #   sql: CAST(${TABLE}.transaction_month AS DATE) ;;
  # }

  # dimension: product_id {
  #   type: string
  #   sql: ${TABLE}.product_id ;;
  # }

  # measure: tpv_usd {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
  #   # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
  #   sql: ${TABLE}.tpv_usd ;;
  # }

  # measure: revenue_usd {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
  #   # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
  #   sql: ${TABLE}.revenue_usd ;;
  # }

  # dimension: product_type {
  #   type: string
  #   sql: ${TABLE}.product_type ;;
  # }

  # dimension: product_subtype {
  #   type: string
  #   sql: ${TABLE}.product_subtype ;;
  # }

  # dimension: channel_name {
  #   type: string
  #   sql: ${TABLE}.channel_name ;;
  # }

  # measure: transaction_count {
  #   type: sum
  #   drill_fields: [detail*]
  #   value_format: "#,##0"
  #   sql: ${TABLE}.transactions ;;
  # }

  # measure: est_revenue_product_local {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   sql: ${TABLE}.est_revenue_product_local ;;
  # }

  # measure: est_tpv_product_local {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   sql: ${TABLE}.est_tpv_product_local ;;
  # }

  # measure: est_product_trx_count {
  #   type: sum
  #   drill_fields: [detail*]
  #   value_format: "#,##0"
  #   sql: ${TABLE}.est_product_trx_count ;;
  # }

  # measure: est_revenue_product_usd {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   sql: ${TABLE}.est_revenue_product_usd ;;
  # }

  # measure: est_tpv_product_usd {
  #   type: sum
  #   drill_fields: [detail*]
  #   # value_format: "#,##0"
  #   value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  #   sql: ${TABLE}.est_tpv_product_usd ;;
  # }

  # set: detail {
  #   fields: [
  #     product_id,
  #     business_id,
  #     tpv_usd,
  #     revenue_usd,
  #     transaction_count,
  #     opportunity_id,
  #     parent_opportunity_id,
  #     account_id,
  #     se_email,
  #     n,
  #     est_revenue_product_local,
  #     est_tpv_product_local,
  #     est_product_trx_count,
  #     est_revenue_product_usd,
  #     est_tpv_product_usd
  #   ]
  # }

derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql:
    SELECT
      sr.name AS req_name
      ,sr.opportunity__c AS opportunity_id
      ,op.parent_opportunity__c AS parent_opportunity_id
      ,a.id AS account_id
      ,a.business_id__c AS business_id
      ,u.email AS se_email
      ,sr.request_status__c AS se_req_status
      ,sr.type_of_request__c AS se_req_type
      ,sr.team_assignment__c AS team_name
      ,CAST(sr.solution_pic_assignment_date__c AS DATE) AS assignmentdate
      ,CAST(op.activation_date__c AS DATE) AS activationdate
      ,CASE
        WHEN op.closed_won_date__c IS NULL
          THEN CAST(sr.solution_pic_assignment_date__c AS DATE) + INTERVAL '12' MONTH
        ELSE CAST(op.closed_won_date__c AS DATE)
      END AS closedwondate
      ,CONCAT(sr.name, op.parent_opportunity__c, a.id, u.email) as primary_key
    FROM clean__salesforce.solution_engineering_request sr
    LEFT JOIN clean__salesforce.opportunities op ON op.id = sr.opportunity__c
    LEFT JOIN clean__salesforce.users u ON solution_pic__c = u.id
    LEFT JOIN clean__salesforce.accounts a ON sr.account__c = a.id
    WHERE
      sr.isdeleted = FALSE
      AND sr.solution_pic__c IS NOT NULL
      AND sr.team_assignment__c = 'Solution Engineering Team'
      AND sr.request_status__c NOT IN ('Draft','Rejected')
      AND op.record_type_name__c = 'Child Opportunity'

    UNION ALL

    SELECT
      sr.name AS req_name
      ,null AS opportunity_id
      ,sr.opportunity__c AS parent_opportunity_id
      ,a.id AS account_id
      ,a.business_id__c AS business_id
      ,u.email AS se_email
      ,sr.request_status__c AS se_req_status
      ,sr.type_of_request__c AS se_req_type
      ,sr.team_assignment__c AS team_name
      ,CAST(sr.solution_pic_assignment_date__c AS DATE) AS assignmentdate
      ,CAST(op.activation_date__c AS DATE) AS activationdate
      ,CASE
        WHEN op.closed_won_date__c IS NULL
          THEN CAST(sr.solution_pic_assignment_date__c AS DATE) + INTERVAL '12' MONTH
        ELSE CAST(op.closed_won_date__c AS DATE)
      END AS closedwondate
      ,CONCAT(sr.name, sr.opportunity__c, a.id, u.email) as primary_key
    FROM clean__salesforce.solution_engineering_request sr
    LEFT JOIN clean__salesforce.opportunities op ON op.id = sr.opportunity__c
    LEFT JOIN clean__salesforce.users u ON solution_pic__c = u.id
    LEFT JOIN clean__salesforce.accounts a ON sr.account__c = a.id
    WHERE
      sr.isdeleted = FALSE
      AND sr.solution_pic__c IS NOT NULL
      AND sr.team_assignment__c = 'Solution Engineering Team'
      AND sr.request_status__c NOT IN ('Draft','Rejected')
      AND op.record_type_name__c = 'Parent Opportunity'

    UNION ALL

    SELECT
      sr.name AS req_name
      ,null AS opportunity_id
      ,op.id AS parent_opportunity_id
      ,a.id AS account_id
      ,a.business_id__c AS business_id
      ,u.email AS se_email
      ,sr.request_status__c AS se_req_status
      ,sr.type_of_request__c AS se_req_type
      ,sr.team_assignment__c AS team_name
      ,CAST(sr.solution_pic_assignment_date__c AS DATE) AS assignmentdate
      ,CAST(op.activation_date__c AS DATE) AS activationdate
      ,CASE
        WHEN op.closed_won_date__c IS NULL
          THEN CAST(sr.solution_pic_assignment_date__c AS DATE) + INTERVAL '12' MONTH
        ELSE CAST(op.closed_won_date__c AS DATE)
      END AS closedwondate
      ,CONCAT(sr.name, op.id, a.id, u.email) as primary_key
    FROM clean__salesforce.solution_engineering_request sr
    LEFT JOIN clean__salesforce.accounts a ON sr.account__c = a.id
    LEFT JOIN clean__salesforce.opportunities op ON op.accountid = sr.account__c
    LEFT JOIN clean__salesforce.users u ON solution_pic__c = u.id
    WHERE
      sr.isdeleted = FALSE
      AND sr.solution_pic__c IS NOT NULL
      AND sr.team_assignment__c = 'Solution Engineering Team'
      AND sr.request_status__c NOT IN ('Draft','Rejected')
      AND op.record_type_name__c = 'Parent Opportunity'
      AND sr.opportunity__c IS NULL
      ;;
  }

  dimension: main_primary_key {
    type: string
    hidden: yes
    primary_key: yes
    sql: ${TABLE}.primary_key ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: req_name {
    type: string
    sql: ${TABLE}.req_name ;;
  }

  dimension: opportunity_id {
    type: string
    sql: ${TABLE}.opportunity_id ;;
  }

  dimension: parent_opportunity_id {
    type: string
    sql: ${TABLE}.parent_opportunity_id ;;
  }

  dimension: account_id {
    type: string
    sql: ${TABLE}.account_id ;;
  }

  dimension: account_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: se_email {
    type: string
    sql: ${TABLE}.se_email ;;
  }

  dimension: se_req_status {
    type: string
    sql: ${TABLE}.se_req_status ;;
  }

  dimension: se_req_type {
    type: string
    sql: ${TABLE}.se_req_type ;;
  }

  dimension: team_name {
    type: string
    sql: ${TABLE}.team_name ;;
  }

  dimension_group: se_assign_date {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.assignmentdate AS DATE) ;;
  }

  # dimension_group: activationdate {
  #   type: time
  #   timeframes: [
  #     raw,
  #     date,
  #     week,
  #     month,
  #     quarter,
  #     year
  #   ]
  #   convert_tz: no
  #   datatype: date
  #   sql: CAST(${TABLE}.activationdate AS DATE) ;;
  # }

  dimension_group: closedwondate {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.closedwondate AS DATE) ;;
  }

  dimension_group: closedwondate_attr {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.closedwondate_attr AS DATE) ;;
  }

  set: detail {
    fields: [
      req_name,
      opportunity_id,
      parent_opportunity_id,
      account_id,
      account_name,
      business_id,
      se_email,
      se_req_status,
      se_req_type,
      team_name
    ]
  }
}
