view: presales_csat_sales {
  derived_table: {
    sql: SELECT
        *
        ,date_format(to_timestamp(timestamp, 'M/d/yyyy H:mm:ss'), 'yyyy-MM-dd') AS csat_date
        ,CASE
            WHEN presales='<PERSON>ra' THEN '<EMAIL>'
            WHEN presales='Erinna' THEN '<EMAIL>'
            WHEN presales='Meitha' THEN '<EMAIL>'
            WHEN presales='Renee' THEN '<EMAIL>'
            WHEN presales='Jennie' THEN '<EMAIL>'
        END AS presales_email
      FROM clean__google_sheets.presales_csat_sales
      WHERE timestamp LIKE ('%/%') ;;
  }

  suggestions: no

  dimension: timestamp {
    type: string
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.timestamp ;;
  }

  measure: csat_count {
    type: sum
    label: "CSAT Count"
    sql: ${TABLE}.count ;;
    drill_fields: [detail*]
  }

  dimension: email {
    type: string
    label: "Sales Email"
    sql: ${TABLE}.email ;;
  }

  dimension: name {
    type: string
    label: "Sales Name"
    sql: ${TABLE}.name ;;
  }

  dimension: presales {
    type: string
    label: "Presales Name"
    sql: ${TABLE}.presales ;;
  }

  dimension: merchant {
    type: string
    sql: ${TABLE}.merchant ;;
  }

  dimension: multiple_project {
    type: string
    sql: ${TABLE}.multiple_project ;;
  }

  dimension: q1_valuable_input {
    type: number
    sql: ${TABLE}.q1_valuable_input ;;
  }

  dimension: q2_identify_pain_points {
    type: number
    sql: ${TABLE}.q2_identify_pain_points ;;
  }

  dimension: q3_well_thought_out_solution {
    type: number
    sql: ${TABLE}.q3_well_thought_out_solution ;;
  }

  dimension: q4_clearly_explained_detailed_insights {
    type: number
    sql: ${TABLE}.q4_clearly_explained_detailed_insights ;;
  }

  dimension: q5_address_business_technical_aspects {
    type: number
    sql: ${TABLE}.q5_address_business_technical_aspects ;;
  }

  dimension: q6_overall_satisfied {
    type: number
    sql: ${TABLE}.q6_overall_satisfied ;;
  }

  dimension: additional_feedback {
    type: string
    sql: ${TABLE}.additional_feedback ;;
  }

  dimension: result {
    type: number
    sql: ${TABLE}.result ;;
  }

  dimension_group: csat_date {
    type: time
    label: "CSAT Date"
    timeframes: [
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.csat_date AS DATE) ;;
  }

  dimension: presales_email {
    type: string
    sql: ${TABLE}.presales_email ;;
  }

  set: detail {
    fields: [
      csat_date_date,
      merchant,
      multiple_project,
      email,
      presales_email,
      q1_valuable_input,
      q2_identify_pain_points,
      q3_well_thought_out_solution,
      q4_clearly_explained_detailed_insights,
      q5_address_business_technical_aspects,
      q6_overall_satisfied,
      additional_feedback,
      result
    ]
  }
}
