
view: churn_analysis {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: WITH base
    AS (
    SELECT business_id
    , product_type
    , product_subtype
    , channel_name
    , SFDC_ID
    , currency
    , min(first_transaction) AS first_transaction
    , max(latest_transaction) AS latest_transaction
    , sum(transaction_count) AS transaction_count
    , sum(total_payment_volume_local) AS total_payment_volume_local
    , sum(total_payment_volume_usd) AS total_payment_volume_usd
    , sum(total_revenue_local) AS revenue_local
    , sum(revenue_usd) AS revenue_usd
    FROM (
    -- Master Subaccount (XP Managed + Owned)
    SELECT t2.master_acc_business AS business_id
    , t1.product_type
    , t1.product_subtype
    , t1.channel_name
    , t1.SFDC_ID
    , t1.currency
    , min(t1.dt) AS first_transaction
    , max(t1.dt) AS latest_transaction
    , sum(total_payment_volume_local) AS total_payment_volume_local
    , sum(total_revenue_local) AS total_revenue_local
    , sum(total_payment_volume_usd) AS total_payment_volume_usd
    , sum(revenue_usd) AS revenue_usd
    , sum(transaction_count) AS transaction_count
    FROM transform__business_intelligence_third_party_pipelines.salesforce_tpv_backbone t1
    INNER JOIN clean__xendit_platform_service_live.xenplatformrelationships t2
    ON t1.business_id = t2.sub_acc_business
    GROUP BY 1,2,3,4,5,6

    UNION ALL

    -- All Others (XP Master, Regular, XP Managed)
    SELECT t1.business_id
    , t1.product_type
    , t1.product_subtype
    , t1.channel_name
    , t1.SFDC_ID
    , t1.currency
    , min(t1.dt) AS first_transaction
    , max(t1.dt) AS latest_transaction
    , sum(total_payment_volume_local) AS total_payment_volume_local
    , sum(total_revenue_local) AS total_revenue_local
    , sum(total_payment_volume_usd) AS total_payment_volume_usd
    , sum(revenue_usd) AS revenue_usd
    , sum(transaction_count) AS transaction_count
    FROM transform__business_intelligence_third_party_pipelines.salesforce_tpv_backbone t1
    LEFT JOIN clean__xendit_platform_service_live.xenplatformrelationships t3
    ON t1.business_id = t3.sub_acc_business
    WHERE t3.master_acc_business IN ('5e8fc245da59cf4152433acb', '5ca77031dfc49109ddc30d23', '5d157e86fb78b1176386f168', '5d9aaf62ac0208283cc6fde7', '5dbfe7f80982303b92a7e8bb') OR lower(t3.type) != 'owned' OR t3.type IS NULL
    GROUP BY 1,2,3,4,5,6
    )
    GROUP BY 1,2,3,4,5,6
    )
    SELECT t1.*
    , t2.sf_id
    , t2.rollup_sf_id
    , t2.ad_rollup_business_name
    , t2.sf_rollup_business_name
    , t2.sf_rollup_ultimate_parent_id
    , t2.sf_rollup_ultimate_parent_name
    , t2.sf_rollup_ultimate_parent_brand
    , t2.sf_churn_comments__c
    , t2.sf_churn_reason__c
    , t2.sf_has_churned__c
    , t2.sf_rollup_churn_comments
    , t2.sf_rollup_churn_reason
    , t2.sf_rollup_has_churned
    , t2.sf_rollup_owner_email
    , t2.sales_team
    , date_diff(day, first_transaction, latest_transaction) AS days_active
    , date_diff(day, first_transaction, latest_transaction) / 30 AS months_active
    , date_diff(day, first_transaction, latest_transaction) / 365 AS years_active
    , date_diff(day, latest_transaction, now()) AS days_since_last_transaction
    , date_diff(day, latest_transaction, now()) / 30 AS months_since_last_transaction
    , date_diff(day, latest_transaction, now()) / 365 AS years_since_last_transaction
    , case when date_diff(day, first_transaction, latest_transaction) > 0 THEN transaction_count / date_diff(day, first_transaction, latest_transaction) * 30
    else 0 end AS avg_monthly_transaction
    , case when date_diff(day, first_transaction, latest_transaction) > 0 THEN total_payment_volume_local / date_diff(day, first_transaction, latest_transaction) * 30
    else 0 end AS avg_monthly_tpv_local
    , case when date_diff(day, first_transaction, latest_transaction) > 0 THEN total_payment_volume_usd / date_diff(day, first_transaction, latest_transaction) * 30
    else 0 end AS avg_monthly_tpv_usd
    , case when date_diff(day, first_transaction, latest_transaction) > 0 THEN revenue_local / date_diff(day, first_transaction, latest_transaction) * 30
    else 0 end AS avg_monthly_revenue_local
    , case when date_diff(day, first_transaction, latest_transaction) > 0 THEN revenue_usd / date_diff(day, first_transaction, latest_transaction) * 30
    else 0 end AS avg_monthly_revenue_usd
    , case when date_diff(month, latest_transaction, now()) > 1 THEN TRUE
    else FALSE end AS is_product_churned
    FROM base t1
    LEFT JOIN transform__growth.sales_attribution t2
    ON t1.business_id = t2.business_id ;;
  }

  suggestions: yes

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: sfdc_id {
    type: string
    sql: ${TABLE}.SFDC_ID ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  # dimension: first_transaction {
  #   type: date
  #   sql: ${TABLE}.first_transaction ;;
  # }

  dimension_group: first_transaction {
    type: time
    timeframes: [date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.first_transaction as DATE) ;;
  }

  # dimension: latest_transaction {
  #   type: date
  #   sql: ${TABLE}.latest_transaction ;;
  # }

  dimension_group: latest_transaction {
    type: time
    timeframes: [date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.latest_transaction as DATE) ;;
  }

  dimension: sf_id {
    type: string
    sql: ${TABLE}.sf_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  dimension: rollup_sf_id {
    type: string
    sql: ${TABLE}.rollup_sf_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  dimension: ad_rollup_business_name {
    type: string
    sql: ${TABLE}.ad_rollup_business_name ;;
  }

  dimension: sf_rollup_business_name {
    type: string
    sql: ${TABLE}.sf_rollup_business_name ;;
  }

  dimension: sf_rollup_ultimate_parent_id {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_id ;;
  }

  dimension: sf_rollup_ultimate_parent_name {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_name ;;
  }

  dimension: sf_rollup_ultimate_parent_brand {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_brand ;;
  }

  dimension: sf_churn_comments__c {
    type: string
    sql: ${TABLE}.sf_churn_comments__c ;;
  }

  dimension: sf_churn_reason__c {
    type: string
    sql: ${TABLE}.sf_churn_reason__c ;;
  }

  # dimension: sf_has_churned__c {
  #   type: yesno
  #   sql: ${TABLE}.sf_has_churned__c ;;
  # }

  dimension: sf_rollup_churn_comments {
    type: string
    sql: ${TABLE}.sf_rollup_churn_comments ;;
  }

  dimension: sf_rollup_churn_reason {
    type: string
    sql: ${TABLE}.sf_rollup_churn_reason ;;
  }

  # dimension: sf_rollup_has_churned {
  #   type: yesno
  #   sql: ${TABLE}.sf_rollup_has_churned ;;
  # }

  dimension: sf_rollup_owner_email {
    type: string
    sql: ${TABLE}.sf_rollup_owner_email ;;
  }

  dimension: sales_team {
    type: string
    sql: ${TABLE}.sales_team ;;
  }

  dimension: days_active {
    type: number
    sql: ${TABLE}.days_active ;;
  }

  dimension: months_active {
    type: number
    sql: ${TABLE}.months_active ;;
  }

  dimension: years_active {
    type: number
    sql: ${TABLE}.years_active ;;
  }

  dimension: days_since_last_transaction {
    type: number
    sql: ${TABLE}.days_since_last_transaction ;;
  }

  dimension: months_since_last_transaction {
    type: number
    sql: ${TABLE}.months_since_last_transaction ;;
  }

  dimension: years_since_last_transaction {
    type: number
    sql: ${TABLE}.years_since_last_transaction ;;
  }

  dimension: avg_monthly_transaction {
    type: number
    value_format: "#,##0"
    sql: ${TABLE}.avg_monthly_transaction ;;
  }

  dimension: avg_monthly_tpv_local {
    type: number
    value_format: "#,##0"
    sql: ${TABLE}.avg_monthly_tpv_local ;;
  }

  dimension: avg_monthly_tpv_usd {
    type: number
    value_format: "#,##0"
    sql: ${TABLE}.avg_monthly_tpv_usd ;;
  }

  dimension: avg_monthly_revenue_local {
    type: number
    value_format: "#,##0"
    sql: ${TABLE}.avg_monthly_revenue_local ;;
  }

  dimension: avg_monthly_revenue_usd {
    type: number
    value_format: "#,##0"
    sql: ${TABLE}.avg_monthly_revenue_usd ;;
  }

  dimension: is_product_churned {
    type: yesno
    sql: ${TABLE}.is_product_churned ;;
  }

  measure: transaction_count {
    type: sum
    label: "Lifetime Transaction"
    value_format: "#,##0"
    sql: ${TABLE}.transaction_count ;;
    drill_fields: [detail*]
  }

  measure: total_payment_volume_local {
    type: sum
    label: "Lifetime TPV Local"
    value_format: "#,##0"
    sql: ${TABLE}.total_payment_volume_local ;;
    drill_fields: [detail*]
  }

  measure: total_payment_volume_usd {
    type: sum
    label: "Lifetime TPV USD"
    value_format: "#,##0"
    sql: ${TABLE}.total_payment_volume_usd ;;
    drill_fields: [detail*]
  }

  measure: revenue_local {
    type: sum
    label: "Lifetime Revenue Local"
    value_format: "#,##0"
    sql: ${TABLE}.revenue_local ;;
    drill_fields: [detail*]
  }

  measure: revenue_usd {
    type: sum
    label: "Lifetime Revenue USD"
    value_format: "#,##0"
    sql: ${TABLE}.revenue_usd ;;
    drill_fields: [detail*]
  }

  measure: count_business_id {
    type: count_distinct
    sql: ${TABLE}.business_id ;;
    drill_fields: [detail*]
  }

  measure: count_product_id {
    type: count
    # sql: ${TABLE}.sfdc_id ;;
    drill_fields: [detail*]
  }

  measure: est_monthly_transaction {
    type: sum
    value_format: "#,##0"
  sql: ${TABLE}.avg_monthly_transaction ;;
  }

  measure: est_monthly_tpv_local {
    type: sum
    value_format: "#,##0"
  sql: ${TABLE}.avg_monthly_tpv_local ;;
  }

  measure: est_monthly_tpv_usd {
    type: sum
    value_format: "#,##0"
  sql: ${TABLE}.avg_monthly_tpv_usd ;;
  }

  measure: est_monthly_revenue_local {
    type: sum
    value_format: "#,##0"
  sql: ${TABLE}.avg_monthly_revenue_local ;;
  }

  measure: est_monthly_revenue_usd {
    type: sum
    value_format: "#,##0"
  sql: ${TABLE}.avg_monthly_revenue_usd ;;
  }

  set: detail {
    fields: [
      business_id,
      sf_id,
      product_type,
      product_subtype,
      channel_name,
      sfdc_id,
      currency,
      first_transaction_date,
      latest_transaction_date,
      sf_churn_comments__c,
      sf_churn_reason__c,
      transaction_count,
      total_payment_volume_local,
      total_payment_volume_usd,
      revenue_local,
      revenue_usd,
      avg_monthly_transaction,
      avg_monthly_tpv_local,
      avg_monthly_tpv_usd,
      avg_monthly_revenue_local,
      avg_monthly_revenue_usd,
      rollup_sf_id,
      ad_rollup_business_name,
      sf_rollup_business_name,
      sf_rollup_ultimate_parent_id,
      sf_rollup_ultimate_parent_name,
      sf_rollup_ultimate_parent_brand,
      sf_rollup_churn_comments,
      sf_rollup_churn_reason,
      sf_rollup_owner_email,
      sales_team,
      days_active,
      months_active,
      years_active,
      days_since_last_transaction,
      months_since_last_transaction,
      years_since_last_transaction,
      is_product_churned
    ]
  }
}