# The name of this view in Looker is "Account Insights"
view: account_insights {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__growth.account_insights ;;
  suggestions: yes

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Account Status" in Explore.

  dimension: account_status {
    type: string
    sql: ${TABLE}.account_status ;;
  }

  dimension: active_category {
    type: number
    sql: ${TABLE}.active_count ;;
  }

  dimension: active_count {
    type: number
    sql: ${TABLE}.active_count ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_active_count {
    type: sum
    sql: ${active_count} ;;  }

  measure: average_active_count {
    type: average
    sql: ${active_count} ;;  }

  dimension: active_productcategory {
    type: string
    sql: ${TABLE}.active_productcategory ;;
    suggestions: [
      "MONEY_IN",
      "MONEY_OUT",
      "PG_VAS",
      "DATA_PRODUCT"
    ]
  }

  dimension: active_productchannel {
    type: string
    sql: ${TABLE}.active_productchannel ;;
  }

  dimension: active_producttype {
    type: string
    sql: ${TABLE}.active_producttype ;;
    suggestions: [
      "VIRTUAL_ACCOUNT",
      "EWALLET",
      "DIRECT_DEBIT",
      "RETAIL_OUTLET",
      "QR_CODE",
      "PAYLATER",
      "DISBURSEMENT",
      "CREDIT_CARD",
      "SUB-ACCOUNT ACTIVITY",
      "DATA_PRODUCT",
      "IN-HOUSE TRANSACTION",
      "SUBSCRIPTION",
      "EARLY_SETTLEMENT",
      "FOREX"
    ]
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: business_name {
    type: string
    sql: ${TABLE}.business_name ;;
  }

  dimension: cluster {
    type: string
    # sql: ${TABLE}.cluster ;;
    sql: CASE WHEN ${TABLE}.cluster = 'OTHERS' then 'ACTIVE' else ${TABLE}.cluster end;;
  }

  dimension: cluster_rank {
    type: number
    sql: ${TABLE}.cluster_rank ;;
  }

  dimension: frequency_score {
    type: number
    sql: ${TABLE}.frequency_score ;;
  }

  dimension: industry_sector {
    type: string
    sql: ${TABLE}.industry_sector ;;
  }

  dimension: length_score {
    type: number
    sql: ${TABLE}.length_score ;;
  }

  dimension: lrfm {
    type: string
    sql: ${TABLE}.lrfm ;;
  }

  dimension: lcrfm {
    type: string
    sql: ${TABLE}.lcrfm ;;
  }

  dimension: lrfm_score {
    type: number
    sql: ${TABLE}.lrfm_score ;;
  }

  dimension: lcrfm_score {
    type: number
    sql: ${TABLE}.lcrfm_score ;;
  }

  dimension: monetary_score {
    type: number
    sql: ${TABLE}.monetary_score ;;
  }

  dimension: parent_industry_sector {
    type: string
    sql: ${TABLE}.parent_industry_sector ;;
  }

  dimension: recency_score {
    type: number
    sql: ${TABLE}.recency_score ;;
  }

  dimension: rfm_category {
    type: string
    sql: ${TABLE}.rfm_category ;;
  }

  dimension: crfm_category {
    type: string
    sql: ${TABLE}.crfm_category ;;
  }

  dimension: sales_team {
    type: string
    sql: ${TABLE}.sales_team ;;
  }

  dimension: segment {
    type: string
    sql: ${TABLE}.segment ;;
  }

  dimension: sf_id {
    type: string
    sql: ${TABLE}.sf_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  dimension: sf_rollup_country_of_hq {
    type: string
    sql: ${TABLE}.sf_rollup_country_of_hq ;;
  }

  dimension: sf_rollup_owner_email {
    type: string
    sql: ${TABLE}.sf_rollup_owner_email ;;
  }

  dimension: sf_rollup_ultimate_parent_brand {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_brand ;;
  }

  dimension: usage_pattern {
    type: string
    sql: ${TABLE}.usage_pattern ;;
  }

  dimension: xsell_productcategory {
    type: string
    sql: ${TABLE}.xsell_productcategory ;;
    suggestions: [
      "MONEY_IN",
      "MONEY_OUT",
      "PG_VAS",
      "DATA_PRODUCT"
    ]
  }

  dimension: xsell_productchannel {
    type: string
    sql: ${TABLE}.xsell_productchannel ;;
  }

  dimension: xsell_producttype {
    type: string
    sql: ${TABLE}.xsell_producttype ;;
    suggestions: [
      "VIRTUAL_ACCOUNT",
      "EWALLET",
      "DIRECT_DEBIT",
      "RETAIL_OUTLET",
      "QR_CODE",
      "PAYLATER",
      "DISBURSEMENT",
      "CREDIT_CARD",
      "SUB-ACCOUNT ACTIVITY",
      "DATA_PRODUCT",
      "IN-HOUSE TRANSACTION",
      "SUBSCRIPTION",
      "EARLY_SETTLEMENT",
      "FOREX"
    ]
  }

  dimension: xsell_pair {
    type: string
    sql: ${TABLE}.xsell_pair ;;
    suggestions: [
      "VIRTUAL_ACCOUNT",
      "EWALLET",
      "DIRECT_DEBIT",
      "RETAIL_OUTLET",
      "QR_CODE",
      "PAYLATER",
      "DISBURSEMENT",
      "CREDIT_CARD",
      "SUB-ACCOUNT ACTIVITY",
      "DATA_PRODUCT",
      "IN-HOUSE TRANSACTION",
      "SUBSCRIPTION",
      "EARLY_SETTLEMENT",
      "FOREX"
    ]
  }

  dimension: L12M_max_revenue {
    type: string
    sql: ${TABLE}.L12M_max_revenue ;;
    value_format: "#,##0"
  }

  measure: count {
    type: count
    drill_fields: [business_name]
  }

  measure: l6m_avg_tpv {
    type: sum
    # drill_fields: [detail*]
    sql: ${TABLE}.l6m_avg_tpv ;;
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
  }
}
