view: solution_attribution_estrev {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: WITH
          estrevenue
          AS (
            SELECT
                solution_attribution.parent_opportunity_id AS parent_opportunity_id,
                salesforce_parent_opportunities.name AS parent_opportunity_name,
                solution_attribution.req_name  AS req_name,
                solution_attribution.se_email  AS se_email,
                (DATE_FORMAT(CAST(CAST(solution_attribution.assignmentdate AS DATE) AS TIMESTAMP),'%Y-%m-01')) AS assign_month,
                (DATE_FORMAT(CAST(CAST(DATE_ADD('MONTH', 7, solution_attribution.assignmentdate) AS DATE)  AS TIMESTAMP),'%Y-%m-01')) AS estrev_attr_start,
                COALESCE(SUM(salesforce_parent_opportunities.addressable_merchant_revenue_monthly__c_usd ), 0) AS est_rev
            FROM looker_pdt_v4.LR_SLJGQ1716502781314_ag_parent_opportunities AS salesforce_parent_opportunities
            LEFT JOIN looker_pdt_v4.LR_SL7GD1716502226812_solution_attribution AS solution_attribution ON salesforce_parent_opportunities.id = solution_attribution.parent_opportunity_id
            GROUP BY 1,2,3,4,5,6
          )

      ,estrevenueattr
      AS (
      SELECT
      parent_opportunity_id
      ,parent_opportunity_name
      ,req_name
      ,se_email
      ,DATE_FORMAT(cast(assign_month as date),'%Y-%m') as se_assign_month
      ,estrev_attr_start
      ,est_rev
      ,DATE_ADD('MONTH', n.n, CAST(estrev_attr_start AS DATE)) AS attribution_month
      ,concat(parent_opportunity_id,CAST(DATE_ADD('MONTH', n.n, CAST(estrev_attr_start AS DATE)) AS STRING) as estrev_attr_key
      FROM estrevenue
      INNER JOIN (
      SELECT ROW_NUMBER() OVER (
      ORDER BY (
      SELECT NULL
      )
      ) - 1 AS n
      FROM (
      VALUES (0)
      ,(1)
      ,(2)
      ,(3)
      ,(4)
      ,(5)
      ) AS numbers(n)
      ) n ON n.n < 6
      ORDER BY 3,8 ASC
      )

      SELECT *
      FROM estrevenueattr ;;
  }

  suggestions: no

  dimension: solution_eng_email {
    type: string
    sql: ${TABLE}.se_email ;;
  }

  dimension: parent_opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.parent_opportunity_id ;;
  }

  dimension: parent_opportunity_name {
    type: string
    sql: ${TABLE}.parent_opportunity_name ;;
  }

  dimension: req_name {
    type: string
    sql: ${TABLE}.req_name ;;
  }

  dimension: assign_month {
    type: string
    sql: ${TABLE}.se_assign_month ;;
  }

  dimension: estrev_attr_key {
    hidden: yes
    primary_key: yes
    type: string
    sql: ${TABLE}.estrev_attr_key ;;
  }

  dimension_group: attribution_month {
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.attribution_month AS DATE) ;;
  }

  dimension: est_monthly_revenue_usd {
    type: number
    value_format: "$#,##0.00"
    # value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.est_rev ;;
  }

  measure: estimated_monthly_revenue_usd {
    type: sum
    drill_fields: [solution_eng_email,req_name,assign_month,parent_opportunity_id,parent_opportunity_name,est_monthly_revenue_usd]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.est_rev ;;
  }

  set: detail {
    fields: [
      solution_eng_email,
      req_name,
      assign_month,
      parent_opportunity_id,
      parent_opportunity_name,
      estimated_monthly_revenue_usd
    ]
  }
}
