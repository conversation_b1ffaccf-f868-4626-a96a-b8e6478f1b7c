
view: presales_csat_oim {
  derived_table: {
    sql: SELECT
              *
              ,date_format(to_timestamp(timestamp, 'M/d/yyyy H:mm:ss'), 'yyyy-MM-dd') AS csat_date
              ,CASE
                  WHEN presales='Cyra' THEN '<EMAIL>'
                  WHEN presales='Erinna' THEN '<EMAIL>'
                  WHEN presales='Meitha' THEN '<EMAIL>'
                  WHEN presales='Renee' THEN '<EMAIL>'
                  WHEN presales='Jennie' THEN '<EMAIL>'
              END AS presales_email
            FROM clean__google_sheets.presales_csat_oim
            WHERE timestamp LIKE ('%/%') ;;
  }

  suggestions: no

  dimension: timestamp {
    type: string
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.timestamp ;;
  }

  measure: csat_count {
    type: sum
    label: "CSAT Count"
    sql: ${TABLE}.count ;;
    drill_fields: [detail*]
  }

  dimension: email {
    type: string
    label: "OIM Email"
    sql: ${TABLE}.email ;;
  }

  dimension: presales {
    type: string
    label: "Presales Name"
    sql: ${TABLE}.presales ;;
  }

  dimension: merchant {
    type: string
    sql: ${TABLE}.merchant ;;
  }

  dimension: multiple_project {
    type: string
    sql: ${TABLE}.multiple_project ;;
  }

  dimension: do_you_get_handover_docs {
    type: string
    sql: ${TABLE}.do_you_get_handover_docs ;;
  }

  dimension: q1_completeness_or_clarity_of_handover {
    type: number
    sql: ${TABLE}.q1_completeness_or_clarity_of_handover ;;
  }

  dimension: q2_solution_suits_both_the_client_and_xendit {
    type: number
    sql: ${TABLE}.q2_solution_suits_both_the_client_and_xendit ;;
  }

  dimension: q3_overall_satisfaction_handover_process {
    type: number
    sql: ${TABLE}.q3_overall_satisfaction_handover_process ;;
  }

  dimension: q4_explanation_if_not_satisfied {
    type: string
    sql: ${TABLE}.q4_explanation_if_not_satisfied ;;
  }

  dimension: result {
    type: number
    sql: ${TABLE}.result ;;
  }

  dimension_group: csat_date {
    type: time
    label: "CSAT Date"
    timeframes: [
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.csat_date AS DATE) ;;
  }

  dimension: presales_email {
    type: string
    sql: ${TABLE}.presales_email ;;
  }

  set: detail {
    fields: [
      csat_date_date,
      merchant,
      multiple_project,
      email,
      presales_email,
      do_you_get_handover_docs,
      q1_completeness_or_clarity_of_handover,
      q2_solution_suits_both_the_client_and_xendit,
      q3_overall_satisfaction_handover_process,
      q4_explanation_if_not_satisfied,
      result
    ]
  }
}
