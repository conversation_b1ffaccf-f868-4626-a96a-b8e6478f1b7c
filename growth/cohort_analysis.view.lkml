view: cohort_analysis {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: WITH rate
      AS (
        SELECT 'USD' AS currency
          , usd_usd AS rate
          , year
        FROM transform__fpna.fx_rates_by_year
        WHERE year = (
            SELECT MAX(year)
            FROM transform__fpna.fx_rates_by_year
            )

      UNION ALL

      SELECT 'IDR' AS currency
      , idr_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'PHP' AS currency
      , php_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'MYR' AS currency
      , myr_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'SGD' AS currency
      , sgd_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'VND' AS currency
      , vnd_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'THB' AS currency
      , thb_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'EUR' AS currency
      , eur_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'AUD' AS currency
      , aud_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'HKD' AS currency
      , hkd_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'TWD' AS currency
      , twd_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'INR' AS currency
      , inr_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'CAD' AS currency
      , cad_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )

      UNION ALL

      SELECT 'GBP' AS currency
      , gbp_usd AS rate
      , year
      FROM transform__fpna.fx_rates_by_year
      WHERE year = (
      SELECT MAX(year)
      FROM transform__fpna.fx_rates_by_year
      )
      )
      , TRANSACTION
      AS (
      SELECT business_id
      , t1.country
      , t1.product_type
      , t1.product_subtype
      , t1.channel_name
      , t1.commercial_model
      , t1.entity
      , t1.currency
      , t1.SFDC_ID
      , date_trunc('MONTH', t1.dt) AS dt_month
      , sum(t1.total_payment_volume_local) AS total_payment_volume_local
      , sum(t1.total_revenue_local) AS revenue_local
      , sum(t1.total_payment_volume_local / rate) AS total_payment_volume_usd
      , sum(t1.total_revenue_local / rate) AS revenue_usd
      , sum(t1.transaction_count) AS transaction_count
      FROM transform__business_intelligence_third_party_pipelines.salesforce_tpv_backbone t1
      LEFT JOIN rate t2
      ON t1.currency = t2.currency
      GROUP BY 1
      , 2
      , 3
      , 4
      , 5
      , 6
      , 7
      , 8
      , 9
      , 10
      )
      SELECT t1.*
        , t3.ad_business_name
        , t3.rollup_business_id
        , t3.rollup_sf_id
        , t3.sf_id
        , t3.sf_rollup_ultimate_parent_id
        , t3.sf_rollup_ultimate_parent_name
        , t3.sf_rollup_ultimate_parent_brand
        , t3.sf_rollup_country_of_hq
        , t3.sf_rollup_owner_email
        , t3.sales_team
        , t2.created
        , t2.go_live
        , t2.first_transaction
        , t2.fifth_transaction
        -- , coalesce(t3.sf_handover_date__c, t3.sf_rollup_handover_date__c) AS handover
        , coalesce(t3.sf_rollup_handover_date__c, t3.sf_handover_date__c) AS handover
        , date_diff(MONTH, t2.created, t1.dt_month) AS months_since_created
        , date_diff(MONTH, t2.go_live, t1.dt_month) AS months_since_go_live
        , date_diff(MONTH, t2.first_transaction, t1.dt_month) AS months_since_1st_txn
        , date_diff(MONTH, t2.fifth_transaction, t1.dt_month) AS months_since_5th_txn
        -- , date_diff(month, coalesce(t3.sf_handover_date__c, t3.sf_rollup_handover_date__c), t1.dt_month) AS months_since_handover
        , date_diff(MONTH, coalesce(t3.sf_rollup_handover_date__c, t3.sf_handover_date__c), t1.dt_month) AS months_since_handover
      FROM TRANSACTION t1
      LEFT JOIN transform__business_intelligence.dim_businesses t2 ON t1.business_id = t2.business_id
      LEFT JOIN transform__growth.sales_attribution t3 ON t1.business_id = t3.business_id
      ;;
  }

  suggestions: yes
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
    link: {
      label: "Open in Admin Dashboard"
      url: "https://admin-dashboard.tidnex.com/customer-management-v2/{{ value }}"
    }
  }

  dimension: rollup_business_id {
    type: string
    sql: ${TABLE}.rollup_business_id ;;
    link: {
      label: "Open in Admin Dashboard"
      url: "https://admin-dashboard.tidnex.com/customer-management-v2/{{ value }}"
    }
  }

  dimension: ad_business_name {
    type: string
    sql: ${TABLE}.ad_business_name ;;
  }

  dimension: sf_id {
    type: string
    sql: ${TABLE}.sf_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  dimension: rollup_sf_id {
    type: string
    sql: ${TABLE}.rollup_sf_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  dimension: sf_rollup_ultimate_parent_id {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_id ;;
  }

  dimension: sf_rollup_ultimate_parent_name {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_name ;;
  }

  dimension: sf_rollup_ultimate_parent_brand {
    type: string
    sql: ${TABLE}.sf_rollup_ultimate_parent_brand ;;
  }

  dimension: sf_rollup_owner_email {
    type: string
    sql: ${TABLE}.sf_rollup_owner_email ;;
  }

  dimension: sales_team {
    type: string
    sql: ${TABLE}.sales_team ;;
  }

  dimension: country {
    type: string
    sql: ${TABLE}.country ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: commercial_model {
    type: string
    sql: ${TABLE}.commercial_model ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: sfdc_id {
    type: string
    sql: ${TABLE}.SFDC_ID ;;
  }

  # dimension: dt_month {
  #   type: date
  #   sql: ${TABLE}.dt_month ;;
  # }

  dimension_group: dt {
    # label: "Transaction"
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.dt_month as DATE) ;;
  }

  # dimension: total_payment_volume_local {
  #   type: number
  #   sql: ${TABLE}.total_payment_volume_local ;;
  # }

  # dimension: revenue_local {
  #   type: number
  #   sql: ${TABLE}.revenue_local ;;
  # }

  # dimension: total_payment_volume_usd {
  #   type: number
  #   sql: ${TABLE}.total_payment_volume_usd ;;
  # }

  # dimension: revenue_usd {
  #   type: number
  #   sql: ${TABLE}.revenue_usd ;;
  # }

  # dimension: transaction_count {
  #   type: number
  #   sql: ${TABLE}.transaction_count ;;
  # }

  dimension_group: created {
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: ${TABLE}.created ;;
  }

  dimension_group: go_live {
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: ${TABLE}.go_live ;;
  }

  dimension_group: first_transaction {
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: ${TABLE}.first_transaction ;;
  }

  dimension_group: fifth_transaction {
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: ${TABLE}.fifth_transaction ;;
  }

  dimension_group: handover {
    type: time
    timeframes: [date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: ${TABLE}.handover ;;
  }

  dimension: months_since_created {
    type: number
    sql: ${TABLE}.months_since_created ;;
  }

  dimension: months_since_go_live {
    type: number
    sql: ${TABLE}.months_since_go_live ;;
  }

  dimension: months_since_1st_txn {
    type: number
    sql: ${TABLE}.months_since_1st_txn ;;
  }

  dimension: months_since_5th_txn {
    type: number
    sql: ${TABLE}.months_since_5th_txn ;;
  }

  dimension: months_since_handover {
    type: number
    sql: ${TABLE}.months_since_handover ;;
  }

  measure: total_payment_volume_local {
    type: sum
    value_format: "#,##0"
    sql: ${TABLE}.total_payment_volume_local ;;
    drill_fields: [detail*]
  }

  measure: revenue_local {
    type: sum
    value_format: "#,##0"
    sql: ${TABLE}.revenue_local ;;
    drill_fields: [detail*]
  }

  measure: total_payment_volume_usd {
    type: sum
    value_format: "#,##0"
    sql: ${TABLE}.total_payment_volume_usd ;;
    drill_fields: [detail*]
  }

  measure: revenue_usd {
    type: sum
    value_format: "#,##0"
    sql: ${TABLE}.revenue_usd ;;
    drill_fields: [detail*]
  }

  measure: transaction_count {
    type: sum
    value_format: "#,##0"
    sql: ${TABLE}.transaction_count ;;
    drill_fields: [detail*]
  }

  measure: business_count {
    type: count_distinct
    value_format: "#,##0"
    sql: ${TABLE}.business_id ;;
    drill_fields: [detail*]
  }

  set: detail {
    fields: [
      business_id,
      ad_business_name,
      sf_id,
      sf_rollup_owner_email,
      sales_team,
      country,
      product_type,
      product_subtype,
      channel_name,
      commercial_model,
      entity,
      currency,
      sfdc_id,
      dt_month,
      total_payment_volume_local,
      revenue_local,
      total_payment_volume_usd,
      revenue_usd,
      transaction_count,
      created_month,
      go_live_month,
      first_transaction_month,
      fifth_transaction_month,
      months_since_created,
      months_since_go_live,
      months_since_1st_txn,
      months_since_5th_txn
    ]
  }
}
