include: "sales_performance.view.lkml"

# This file is a demonstration of a tabbed dashboard for use by builders across Xendit. It
# defines dimensions that render the tabs using HTML for each dashboard. This means that
# you will need to create one dimension for each dashboard you want to 'tab'.
# Caveats/Warnings:
# - Tab-dimensions are purely internal for LookML developers and you should make sure to hide
# them before publishing changes so as to not confuse end users.
# - Tab-dimensions will need to be created on a suitable explore/view that contains the full
# set of dimensions you want to pass filters for. Rather than directly operating on a base
# view file as in this example, you may want to use the following design pattern:
# -- Create a LookML folder in your team area specifically for the dashboards of interest
# -- Create an NDT from an explore containing relevant filters and persist it for performance
# -- Include only the relevant dimensions that are used to filter the dashboards
# -- Define your tab dimensions in that same NDT view
# -- Create an explore using that NDT, from which you can generate a vis tile for your dash

view: growth_team_dashboard {
  # sql_table_name: transform__growth.sales_performance ;;
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: SELECT * FROM clean__google_sheets.sales_target_v1 WHERE lower(team) LIKE ('%country%') ;;
  }
  suggestions: yes
  # set: template_tabbed_dash {
  #   fields: [template_tabbed_dash_profile, template_tabbed_dash_tpv, test_liquid_insertion]
  # }

  dimension: country {
    label: "Currency"
    type: string
    # map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  # dimension: team {
  #   type: string
  #   sql: ${TABLE}.team ;;
  # }

  # dimension: email {
  #   type: string
  #   sql: ${TABLE}.email ;;
  # }

  dimension: tab_A_1 {
    hidden: no
    label: "Tab A1 Growth"
    type: string
    sql: 'foo' ;;
    html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
        <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
          <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Growth </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/4426"> CO Net (Beta) </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Marketing </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1192"> BD </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2259"> CP </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1845"> Sales AM </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2111"> Solution </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/4155?Currency={{ _filters['growth_team_dashboard.country'] | url_encode }}"> Forecast </a>
          <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/4260?Currency={{ _filters['growth_team_dashboard.country'] | url_encode }}"> Target </a>
          </nav>
      </div>{%endif%};;
  }

  # dimension: tab_B_1 {
  #   hidden: no
  #   label: "Tab B1 Marketing"
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Growth </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Marketing </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2070"> BDCP </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2096"> Sales AM </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2242"> Solution </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2071"> Read Me </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_C_1 {
  #   hidden: no
  #   label: "Tab C1 BDCP"
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Growth </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Marketing </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> BDCP </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2096"> Sales AM </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2242"> Solution </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2071"> Read Me </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_C_2 {
  #   hidden: no
  #   label: "Tab C2 Sales AM"
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Growth </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2069"> Marketing </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2070"> BDCP </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Sales AM </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2242"> Solution </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2071"> Read Me </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_C_3 {
  #   hidden: no
  #   label: "Tab C3 Solution"
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Growth </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2069"> Marketing </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2070"> BDCP </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2096"> Sales AM </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Solution </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2071"> Read Me </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_D_1 {
  #   hidden: no
  #   label: "Tab D1 Read Me"
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'growth_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2068"> Overview </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2069"> Marketing </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2070"> BDCP </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2096"> Sales AM </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/2242"> Solution </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Read Me </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_1 {
  #   hidden: no
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'xencapital_sales_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Performance </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1780?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Account </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1832?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Pipeline </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1919?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Referral </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_2 {
  #   hidden: no
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'xencapital_sales_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/421?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Performance </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Account </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1832?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Pipeline </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1919?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Referral </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_3 {
  #   hidden: no
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'xencapital_sales_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/421?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Performance </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1780?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Account </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Pipeline </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1919?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Referral </a>
  #         </nav>
  #     </div>{%endif%};;
  # }

  # dimension: tab_4 {
  #   hidden: no
  #   type: string
  #   sql: 'foo' ;;
  #   html: {%if _explore._name == 'xencapital_sales_team_dashboard'%}<div style="border-bottom: solid 1px #4285F4;">
  #       <nav style="font-size: 18px; padding: 5px 10px 0 10px; height: 60px">
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/421?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Performance </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1780?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Account </a>
  #         <a style="padding: 5px 15px; border-bottom: solid 1px #4285F4; float: left; line-height: 40px;"href="/dashboards/1832?Email={{ _filters['xencapital_sales_team_dashboard.email'] | url_encode }}"> Pipeline </a>
  #         <a style="padding: 5px 15px; border-top: solid 1px #4285F4; border-left: solid 1px #4285F4; border-right: solid 1px #4285F4; border-radius: 5px 5px 0 0; float: left; line-height: 40px; font-weight: bold; background-color: #eaf1fe;" href="#"> Referral </a>
  #         </nav>
  #     </div>{%endif%};;
  # }
}
