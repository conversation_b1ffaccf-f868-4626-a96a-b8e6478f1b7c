view: solution_product_attribution {
  # source: https://xenditco.cloud.looker.com/sql/wdxzzkxnvkwyd7?toggle=sql
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql:
    WITH
    solutionslist
      AS (
        SELECT sr.opportunity__c AS opportunity_id
          ,op.accountid AS account_id
          ,op.business_id_formula__c AS business_id
          ,u.email AS se_email
          ,sr.request_status__c AS se_req_status
          ,sr.type_of_request__c AS se_req_type
          ,sr.team_assignment__c AS team_name
          ,CAST(sr.solution_pic_assignment_date__c AS DATE) AS assignmentdate
          ,CAST(op.activation_date__c AS DATE) AS activationdate
        FROM clean__salesforce.solution_engineering_request sr
        LEFT JOIN clean__salesforce.opportunities op ON op.id = sr.opportunity__c
        LEFT JOIN clean__salesforce.users u ON solution_pic__c = u.id
        WHERE sr.isdeleted = FALSE
          AND sr.solution_pic__c IS NOT NULL
          AND sr.team_assignment__c = 'Solution Engineering Team'
          AND sr.request_status__c NOT IN (
            'Draft'
            ,'Rejected'
            )
        )

    ,RankedSolutions
      AS (
        SELECT *
          ,ROW_NUMBER() OVER (
            PARTITION BY opportunity_id ORDER BY assignmentdate
            ) AS rn
        FROM solutionslist
        )

    ,minproducttype
    AS (
      SELECT
        business_id
        ,product_type
        ,min(transaction_month) as first_trans
        ,concat(business_id,product_type,CAST(min(transaction_month) AS STRING)) as keyprodtype_attr
        ,1 as first_trans_mark
      FROM transform__growth.monthly_salesforce_transaction t
      GROUP BY 1,2
    )
    ,minproductchannel
    AS (
      SELECT
        business_id
        ,product_id
        ,min(transaction_month) as first_trans
        ,concat(business_id,product_id,CAST(min(transaction_month) AS STRING)) as keychannel_attr
        ,1 as first_trans_mark
      FROM transform__growth.monthly_salesforce_transaction t
      GROUP BY 1,2
    )
    ,transactions
      AS (
        SELECT
          t.*
          ,CONCAT (
            t.business_id
            ,t.product_type
            ,CAST(t.transaction_month AS STRING)
            ) AS keyprodtype
          ,CONCAT (
            t.business_id
            ,t.product_id
            ,CAST(t.transaction_month AS STRING)
            ) AS keychannel
          ,rs.activationdate
          ,rs.assignmentdate AS prod_attr_se_assign
          ,rs.se_email AS prod_attr_se_email
        FROM transform__growth.monthly_salesforce_transaction t
        LEFT JOIN RankedSolutions rs ON t.business_id = rs.business_id
        WHERE rn = 1
        )

    SELECT
        t.transaction_month
        ,t.product_id
        ,t.business_id
        ,t.tpv_usd
        ,t.revenue_usd
        ,t.transactions
        ,t.product_type
        ,t.product_subtype
        ,t.channel_name
        ,t.opp_id
        ,t.parent_opp_id
        ,t.account_id
        ,t.activationdate
        ,t.prod_attr_se_email as se_email
        ,t.prod_attr_se_assign as se_assign
        ,pp.first_trans AS attribution_month_prodtype
        ,pc.first_trans AS attribution_month_prodchannel
        ,CONCAT (
            t.business_id
            ,t.prod_attr_se_email
            ,t.product_id
            ,CAST(t.transaction_month AS STRING)
            ) AS primary_key
      FROM transactions t
      LEFT JOIN minproducttype pp ON t.keyprodtype = pp.keyprodtype_attr
      LEFT JOIN minproductchannel pc ON t.keychannel = pc.keychannel_attr
      ;;
  }

  suggestions: no

  dimension: main_primary_key {
    type: string
    hidden: yes
    primary_key: yes
    sql: ${TABLE}.primary_key ;;
  }

  dimension: opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.opp_id ;;
  }

  dimension: parent_opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.parent_opp_id ;;
  }

  dimension: account_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.account_id ;;
  }

  dimension: business_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.business_id ;;
  }

  dimension: se_email {
    type: string
    sql: ${TABLE}.se_email ;;
  }

  dimension: n {
    type: number
    sql: ${TABLE}.n ;;
  }

  dimension_group: attribution_month {
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.attribution_month AS DATE) ;;
  }

  dimension_group: transaction_month {
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.transaction_month AS DATE) ;;
  }

  dimension: product_id {
    type: string
    sql: ${TABLE}.product_id ;;
  }

  measure: tpv_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.tpv_usd ;;
  }

  measure: revenue_usd {
    type: sum
    drill_fields: [detail*]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=1000000000]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.revenue_usd ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  measure: transaction_count {
    type: sum
    drill_fields: [detail*]
    value_format: "#,##0"
    sql: ${TABLE}.transactions ;;
  }

    dimension: activationdate {
      type: date
      sql: ${TABLE}.activationdate ;;
    }

    dimension_group: se_assign {
      type: time
      timeframes: [
        date,
        month,
        quarter,
        year
      ]
      convert_tz: no
      datatype: date
      sql: CAST(${TABLE}.se_assign AS DATE) ;;
    }

    dimension_group: attribution_month_prodtype {
      type: time
      timeframes: [
        month,
        quarter,
        year
      ]
      convert_tz: no
      datatype: date
      sql: CAST(${TABLE}.attribution_month_prodtype AS DATE) ;;
    }

    dimension_group: attribution_month_prodchannel {
      type: time
      timeframes: [
        month,
        quarter,
        year
      ]
      convert_tz: no
      datatype: date
      sql: CAST(${TABLE}.attribution_month_prodchannel AS DATE);;
    }

    set: detail {
      fields: [
        product_id,
        business_id,
        tpv_usd,
        revenue_usd,
        transaction_count,
        product_type,
        product_subtype,
        channel_name,
        opportunity_id,
        parent_opportunity_id,
        account_id,
        activationdate,
        se_email
      ]
    }
}
