view: billing_check {
  derived_table: {
    sql:
    WITH billing_raw AS (
    -- Money In
    SELECT
        t1.*,
        'Money In' AS trx_source,
        jt.billed_amount,
        jt.billing_currency,
        jt.calculation_method,
        jt.currency,
        jt.discount_amount_indirect,
        jt.fee_amount,
        jt.formula,
        jt.is_billed_directly,
        jt.is_switcher,
        jt.label,
        jt.money_flow,
        jt.payment_channel,
        jt.rate_type,
        jt.rounding_per_transaction,
        jt.tier,
        jt.transaction_type,
        jt.txn_amount,
        jt.txn_count,
        jt.txn_count_distinct,
        jt.vat_amount,
        CASE
            WHEN jt.transaction_type='ACH_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='CREDIT_CARD_PAYMENT' THEN 'CREDIT_CARD'
            WHEN jt.transaction_type='DATA_PRODUCT_EVENT' THEN 'DATA_PRODUCT'
            WHEN jt.transaction_type='DIRECT_DEBIT_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='DISBURSEMENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EARLY_SETTLEMENT_EVENT' THEN 'EARLY_SETTLEMENT'
            WHEN jt.transaction_type='ESCROW_MONEY_IN_EVENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='ESCROW_MONEY_OUT_EVENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EWALLET_PAYMENT' THEN 'EWALLET'
            WHEN jt.transaction_type='PAYLATER_PAYMENT' THEN 'PAYLATER'
            WHEN jt.transaction_type='QR_CODE_PAYMENT' THEN 'QR_CODE'
            WHEN jt.transaction_type='RO_PAYMENT' THEN 'RETAIL_OUTLET'
            WHEN jt.transaction_type='SUB_ACCOUNT_ACTIVITY' THEN 'SUB-ACCOUNT ACTIVITY'
            WHEN jt.transaction_type='SUBSCRIPTIONS_PLAN_EVENT' THEN 'SUBSCRIPTION'
            WHEN jt.transaction_type='VA_PAYMENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='XP_TRANSFER_OUT' THEN 'IN-HOUSE TRANSACTION'
            WHEN jt.transaction_type IN ('IN_HOUSE_TRANSACTION_UNCAPPED','IN_HOUSE_TRANSACTION_CAPPED') THEN 'IN-HOUSE TRANSACTION'
            ELSE jt.transaction_type
        END AS product_type
    FROM clean__xendit_billing_service.billingstatementv2 t1
    LATERAL VIEW explode(t1.transactions.money_in) jt_tbl AS jt
    WHERE t1.billing_statement_publishing_status = 'PRODUCTION'

    UNION ALL

    -- Money Out
    SELECT
        t1.*,
        'Money Out' AS trx_source,
        jt.billed_amount,
        jt.billing_currency,
        jt.calculation_method,
        jt.currency,
        jt.discount_amount_indirect,
        jt.fee_amount,
        jt.formula,
        jt.is_billed_directly,
        jt.is_switcher,
        jt.label,
        jt.money_flow,
        jt.payment_channel,
        jt.rate_type,
        jt.rounding_per_transaction,
        jt.tier,
        jt.transaction_type,
        jt.txn_amount,
        jt.txn_count,
        jt.txn_count_distinct,
        jt.vat_amount,
        CASE
            WHEN jt.transaction_type='ACH_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='CREDIT_CARD_PAYMENT' THEN 'CREDIT_CARD'
            WHEN jt.transaction_type='DATA_PRODUCT_EVENT' THEN 'DATA_PRODUCT'
            WHEN jt.transaction_type='DIRECT_DEBIT_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='DISBURSEMENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EARLY_SETTLEMENT_EVENT' THEN 'EARLY_SETTLEMENT'
            WHEN jt.transaction_type='ESCROW_MONEY_IN_EVENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='ESCROW_MONEY_OUT_EVENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EWALLET_PAYMENT' THEN 'EWALLET'
            WHEN jt.transaction_type='PAYLATER_PAYMENT' THEN 'PAYLATER'
            WHEN jt.transaction_type='QR_CODE_PAYMENT' THEN 'QR_CODE'
            WHEN jt.transaction_type='RO_PAYMENT' THEN 'RETAIL_OUTLET'
            WHEN jt.transaction_type='SUB_ACCOUNT_ACTIVITY' THEN 'SUB-ACCOUNT ACTIVITY'
            WHEN jt.transaction_type='SUBSCRIPTIONS_PLAN_EVENT' THEN 'SUBSCRIPTION'
            WHEN jt.transaction_type='VA_PAYMENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='XP_TRANSFER_OUT' THEN 'IN-HOUSE TRANSACTION'
            WHEN jt.transaction_type IN ('IN_HOUSE_TRANSACTION_UNCAPPED','IN_HOUSE_TRANSACTION_CAPPED') THEN 'IN-HOUSE TRANSACTION'
            ELSE jt.transaction_type
        END AS product_type
    FROM clean__xendit_billing_service.billingstatementv2 t1
    LATERAL VIEW explode(t1.transactions.money_out) jt_tbl AS jt
    WHERE t1.billing_statement_publishing_status = 'PRODUCTION'

    UNION ALL

    -- Xenplatform
    SELECT
        t1.*,
        'Xenplatform' AS trx_source,
        jt.billed_amount,
        jt.billing_currency,
        jt.calculation_method,
        jt.currency,
        jt.discount_amount_indirect,
        jt.fee_amount,
        jt.formula,
        jt.is_billed_directly,
        jt.is_switcher,
        jt.label,
        'Xenplatform' AS money_flow,
        jt.payment_channel,
        jt.rate_type,
        NULL AS rounding_per_transaction,
        jt.tier,
        jt.transaction_type,
        jt.txn_amount,
        jt.txn_count,
        jt.txn_count_distinct,
        jt.vat_amount,
        CASE
            WHEN jt.transaction_type='ACH_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='CREDIT_CARD_PAYMENT' THEN 'CREDIT_CARD'
            WHEN jt.transaction_type='DATA_PRODUCT_EVENT' THEN 'DATA_PRODUCT'
            WHEN jt.transaction_type='DIRECT_DEBIT_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='DISBURSEMENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EARLY_SETTLEMENT_EVENT' THEN 'EARLY_SETTLEMENT'
            WHEN jt.transaction_type='ESCROW_MONEY_IN_EVENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='ESCROW_MONEY_OUT_EVENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EWALLET_PAYMENT' THEN 'EWALLET'
            WHEN jt.transaction_type='PAYLATER_PAYMENT' THEN 'PAYLATER'
            WHEN jt.transaction_type='QR_CODE_PAYMENT' THEN 'QR_CODE'
            WHEN jt.transaction_type='RO_PAYMENT' THEN 'RETAIL_OUTLET'
            WHEN jt.transaction_type='SUB_ACCOUNT_ACTIVITY' THEN 'SUB-ACCOUNT ACTIVITY'
            WHEN jt.transaction_type='SUBSCRIPTIONS_PLAN_EVENT' THEN 'SUBSCRIPTION'
            WHEN jt.transaction_type='VA_PAYMENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='XP_TRANSFER_OUT' THEN 'IN-HOUSE TRANSACTION'
            WHEN jt.transaction_type IN ('IN_HOUSE_TRANSACTION_UNCAPPED','IN_HOUSE_TRANSACTION_CAPPED') THEN 'IN-HOUSE TRANSACTION'
            ELSE jt.transaction_type
        END AS product_type
    FROM clean__xendit_billing_service.billingstatementv2 t1
    LATERAL VIEW explode(t1.transactions.xenplatform) jt_tbl AS jt
    WHERE t1.billing_statement_publishing_status = 'PRODUCTION'

    UNION ALL

    -- Other
    SELECT
        t1.*,
        'Other' AS trx_source,
        jt.billed_amount,
        jt.billing_currency,
        jt.calculation_method,
        jt.currency,
        jt.discount_amount_indirect,
        jt.fee_amount,
        jt.formula,
        jt.is_billed_directly,
        jt.is_switcher,
        jt.label,
        'Other' AS money_flow,
        jt.payment_channel,
        jt.rate_type,
        NULL AS rounding_per_transaction,
        jt.tier,
        jt.transaction_type,
        jt.txn_amount,
        jt.txn_count,
        jt.txn_count_distinct,
        jt.vat_amount,
        CASE
            WHEN jt.transaction_type='ACH_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='CREDIT_CARD_PAYMENT' THEN 'CREDIT_CARD'
            WHEN jt.transaction_type='DATA_PRODUCT_EVENT' THEN 'DATA_PRODUCT'
            WHEN jt.transaction_type='DIRECT_DEBIT_PAYMENT' THEN 'DIRECT_DEBIT'
            WHEN jt.transaction_type='DISBURSEMENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EARLY_SETTLEMENT_EVENT' THEN 'EARLY_SETTLEMENT'
            WHEN jt.transaction_type='ESCROW_MONEY_IN_EVENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='ESCROW_MONEY_OUT_EVENT' THEN 'DISBURSEMENT'
            WHEN jt.transaction_type='EWALLET_PAYMENT' THEN 'EWALLET'
            WHEN jt.transaction_type='PAYLATER_PAYMENT' THEN 'PAYLATER'
            WHEN jt.transaction_type='QR_CODE_PAYMENT' THEN 'QR_CODE'
            WHEN jt.transaction_type='RO_PAYMENT' THEN 'RETAIL_OUTLET'
            WHEN jt.transaction_type='SUB_ACCOUNT_ACTIVITY' THEN 'SUB-ACCOUNT ACTIVITY'
            WHEN jt.transaction_type='SUBSCRIPTIONS_PLAN_EVENT' THEN 'SUBSCRIPTION'
            WHEN jt.transaction_type='VA_PAYMENT' THEN 'VIRTUAL_ACCOUNT'
            WHEN jt.transaction_type='XP_TRANSFER_OUT' THEN 'IN-HOUSE TRANSACTION'
            WHEN jt.transaction_type IN ('IN_HOUSE_TRANSACTION_UNCAPPED','IN_HOUSE_TRANSACTION_CAPPED') THEN 'IN-HOUSE TRANSACTION'
            ELSE jt.transaction_type
        END AS product_type
    FROM clean__xendit_billing_service.billingstatementv2 t1
    LATERAL VIEW explode(t1.transactions.other) jt_tbl AS jt
    WHERE t1.billing_statement_publishing_status = 'PRODUCTION'
)

SELECT * FROM billing_raw
;;
  }

  suggestions: no
  # measure: count {
  #   type: count
  #   drill_fields: [detail*]
  # }

  # dimension: v {
  #   type: number
  #   sql: ${TABLE}.v ;;
  # }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  # dimension: account_relation_id {
  #   type: string
  #   sql: ${TABLE}.account_relation_id ;;
  # }

  # dimension: billed_amount_total {
  #   type: number
  #   sql: ${TABLE}.billed_amount_total ;;
  # }

  # dimension: billed_amount_total_after_discount {
  #   type: number
  #   sql: ${TABLE}.billed_amount_total_after_discount ;;
  # }

  # dimension: biller {
  #   type: string
  #   sql: ${TABLE}.biller ;;
  # }

  # dimension: billing_account_number {
  #   type: string
  #   sql: ${TABLE}.billing_account_number ;;
  # }

  # dimension: billing_bank_code {
  #   type: string
  #   sql: ${TABLE}.billing_bank_code ;;
  # }

  # dimension: billing_business_name {
  #   type: string
  #   sql: ${TABLE}.billing_business_name ;;
  # }

  # dimension: billing_cva {
  #   type: string
  #   sql: ${TABLE}.billing_cva ;;
  # }

  # dimension: billing_merchant_code {
  #   type: string
  #   sql: ${TABLE}.billing_merchant_code ;;
  # }

  dimension_group: billing_month {
    type: time
    timeframes: [date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.billing_month AS DATE) ;;
  }

  # dimension: billing_statement_publishing_status {
  #   type: string
  #   sql: ${TABLE}.billing_statement_publishing_status ;;
  # }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  # dimension: country_of_operation {
  #   type: string
  #   sql: ${TABLE}.country_of_operation ;;
  # }

  dimension_group: created {
    type: time
    sql: ${TABLE}.created ;;
  }

  # dimension: discount_amount_direct {
  #   type: number
  #   sql: ${TABLE}.discount_amount_direct ;;
  # }

  # dimension: discount_amount_indirect_total {
  #   type: number
  #   sql: ${TABLE}.discount_amount_indirect_total ;;
  # }

  # dimension: discount_vat_direct {
  #   type: number
  #   sql: ${TABLE}.discount_vat_direct ;;
  # }

  # dimension_group: due_date_term {
  #   type: time
  #   sql: ${TABLE}.due_date_term ;;
  # }

  # dimension: email_notification_data {
  #   type: string
  #   sql: ${TABLE}.email_notification_data ;;
  # }

  # dimension: email_sent {
  #   type: yesno
  #   sql: ${TABLE}.email_sent ;;
  # }

  # dimension: events {
  #   type: string
  #   sql: ${TABLE}.events ;;
  # }

  # dimension: faktur_pajak_file_id {
  #   type: string
  #   sql: ${TABLE}.faktur_pajak_file_id ;;
  # }

  # dimension: fee_amount_total {
  #   type: number
  #   sql: ${TABLE}.fee_amount_total ;;
  # }

  # dimension: file_id {
  #   type: string
  #   sql: ${TABLE}.file_id ;;
  # }

  # dimension: finance_emails {
  #   type: string
  #   sql: ${TABLE}.finance_emails ;;
  # }

  # dimension: granted_discount_amount {
  #   type: number
  #   sql: ${TABLE}.granted_discount_amount ;;
  # }

  # dimension: identifier_number {
  #   type: string
  #   sql: ${TABLE}.identifier_number ;;
  # }

  # dimension: initial {
  #   type: string
  #   sql: ${TABLE}.initial ;;
  # }

  # dimension: is_soft_deleted {
  #   type: yesno
  #   sql: ${TABLE}.is_soft_deleted ;;
  # }

  # dimension: legal_address {
  #   type: string
  #   sql: ${TABLE}.legal_address ;;
  # }

  # dimension: legal_name {
  #   type: string
  #   sql: ${TABLE}.legal_name ;;
  # }

  dimension: master_business_id {
    type: string
    sql: ${TABLE}.master_business_id ;;
  }

  # dimension: payer_email {
  #   type: string
  #   sql: ${TABLE}.payer_email ;;
  # }

  # dimension: payment_term {
  #   type: number
  #   sql: ${TABLE}.payment_term ;;
  # }

  # dimension: rounding {
  #   type: number
  #   sql: ${TABLE}.rounding ;;
  # }

  # dimension: should_send_email {
  #   type: yesno
  #   sql: ${TABLE}.should_send_email ;;
  # }

  # dimension: signing_entity {
  #   type: string
  #   sql: ${TABLE}.signing_entity ;;
  # }

  # dimension: statement_generation_type {
  #   type: string
  #   sql: ${TABLE}.statement_generation_type ;;
  # }

  # dimension: status {
  #   type: string
  #   sql: ${TABLE}.status ;;
  # }

  # dimension_group: status_updated {
  #   type: time
  #   sql: ${TABLE}.status_updated ;;
  # }

  # dimension: tin {
  #   type: string
  #   sql: ${TABLE}.tin ;;
  # }

  # dimension: transactions {
  #   type: string
  #   sql: ${TABLE}.transactions ;;
  # }

  # dimension: transactions_file_id {
  #   type: string
  #   sql: ${TABLE}.transactions_file_id ;;
  # }

  # dimension: txn_amount_total {
  #   type: number
  #   sql: ${TABLE}.txn_amount_total ;;
  # }

  # dimension: txn_count_distinct_total {
  #   type: number
  #   sql: ${TABLE}.txn_count_distinct_total ;;
  # }

  # dimension: txn_count_total {
  #   type: number
  #   sql: ${TABLE}.txn_count_total ;;
  # }

  # dimension_group: updated {
  #   type: time
  #   sql: ${TABLE}.updated ;;
  # }

  # dimension: user_entity {
  #   type: string
  #   sql: ${TABLE}.user_entity ;;
  # }

  # dimension: vat_amount_total {
  #   type: number
  #   sql: ${TABLE}.vat_amount_total ;;
  # }

  # dimension: vat_rate {
  #   type: number
  #   sql: ${TABLE}.vat_rate ;;
  # }

  # dimension: received_payment_amount {
  #   type: number
  #   sql: ${TABLE}.received_payment_amount ;;
  # }

  # dimension: awaiting_wht {
  #   type: yesno
  #   sql: ${TABLE}.awaiting_wht ;;
  # }

  # dimension: wht_amount {
  #   type: number
  #   sql: ${TABLE}.wht_amount ;;
  # }

  # dimension: is_bill_disputed {
  #   type: yesno
  #   sql: ${TABLE}.is_bill_disputed ;;
  # }

  # dimension_group: dt {
  #   type: time
  #   sql: ${TABLE}.dt ;;
  # }

  # dimension_group: email_notification_data_request_created {
  #   type: time
  #   sql: ${TABLE}.email_notification_data_request_created ;;
  # }

  # dimension: trx_source {
  #   type: string
  #   sql: ${TABLE}.trx_source ;;
  # }

  dimension: billed_amount {
    type: number
    sql: ${TABLE}.billed_amount ;;
  }

  dimension: billing_currency {
    type: string
    sql: ${TABLE}.billing_currency ;;
  }

  # dimension: calculation_method {
  #   type: string
  #   sql: ${TABLE}.calculation_method ;;
  # }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  # dimension: discount_amount_indirect {
  #   type: number
  #   sql: ${TABLE}.discount_amount_indirect ;;
  # }

  dimension: fee_amount {
    type: number
    label: "Revenue Local"
    sql: ${TABLE}.fee_amount ;;
  }

  # dimension: formula {
  #   type: string
  #   sql: ${TABLE}.formula ;;
  # }

  # dimension: is_billed_directly {
  #   type: yesno
  #   sql: ${TABLE}.is_billed_directly ;;
  # }

  dimension: is_switcher {
    type: yesno
    sql: ${TABLE}.is_switcher ;;
  }

  # dimension: label {
  #   type: string
  #   sql: ${TABLE}.label ;;
  # }

  dimension: money_flow {
    type: string
    sql: ${TABLE}.money_flow ;;
  }

  dimension: payment_channel {
    type: string
    label: "Channel Name"
    sql: ${TABLE}.payment_channel ;;
  }

  # dimension: rate_type {
  #   type: string
  #   sql: ${TABLE}.rate_type ;;
  # }

  # dimension: rounding_per_transaction {
  #   type: number
  #   sql: ${TABLE}.rounding_per_transaction ;;
  # }

  # dimension: tier {
  #   type: string
  #   sql: ${TABLE}.tier ;;
  # }

  dimension: transaction_type {
    type: string
    label: "Product Type (Unedited)"
    sql: ${TABLE}.transaction_type ;;
  }

  dimension: txn_amount {
    type: number
    sql: ${TABLE}.txn_amount ;;
  }

  dimension: txn_count {
    type: number
    sql: ${TABLE}.txn_count ;;
  }

  # dimension: txn_count_distinct {
  #   type: number
  #   sql: ${TABLE}.txn_count_distinct ;;
  # }

  # dimension: vat_amount {
  #   type: number
  #   sql: ${TABLE}.vat_amount ;;
  # }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  measure: revenue_local {
    type: sum
    sql: ${TABLE}.billed_amount ;;
  }

  measure: transactions_count {
    type: sum
    sql: ${TABLE}.txn_count ;;
  }

  measure: tpv_local {
    type: sum
    label: "TPV Local"
    sql: ${TABLE}.txn_amount ;;
  }

  set: detail {
    fields: [
      # v,
      id,
      # account_relation_id,
      # billed_amount_total,
      # billed_amount_total_after_discount,
      # biller,
      # billing_account_number,
      # billing_bank_code,
      # billing_business_name,
      # billing_cva,
      # billing_merchant_code,
      # billing_month,
      # billing_statement_publishing_status,
      business_id,
      # country_of_operation,
      created_time,
      # discount_amount_direct,
      # discount_amount_indirect_total,
      # discount_vat_direct,
      # due_date_term_time,
      # email_notification_data,
      # email_sent,
      # events,
      # faktur_pajak_file_id,
      # fee_amount_total,
      # file_id,
      # finance_emails,
      # granted_discount_amount,
      # identifier_number,
      # initial,
      # is_soft_deleted,
      # legal_address,
      # legal_name,
      master_business_id,
      # payer_email,
      # payment_term,
      # rounding,
      # should_send_email,
      # signing_entity,
      # statement_generation_type,
      # status,
      # status_updated_time,
      # tin,
      # transactions,
      # transactions_file_id,
      # txn_amount_total,
      # txn_count_distinct_total,
      # txn_count_total,
      # updated_time,
      # user_entity,
      # vat_amount_total,
      # vat_rate,
      # received_payment_amount,
      # awaiting_wht,
      # wht_amount,
      # is_bill_disputed,
      # dt_time,
      # email_notification_data_request_created_time,
      # trx_source,
      billed_amount,
      billing_currency,
      # calculation_method,
      currency,
      # discount_amount_indirect,
      # fee_amount,
      # formula,
      # is_billed_directly,
      is_switcher,
      # label,
      money_flow,
      payment_channel,
      # rate_type,
      # rounding_per_transaction,
      # tier,
      transaction_type,
      txn_amount,
      txn_count,
      # txn_count_distinct,
      # vat_amount,
      product_type
    ]
  }
}
