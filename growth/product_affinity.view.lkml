# The name of this view in Looker is "Product Affinity"
view: product_affinity {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__growth.product_affinity ;;
  suggestions: yes

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Active Bid Count" in Explore.

  measure: active_bid_count {
    type: sum
    sql: ${TABLE}.active_bid_count ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  # measure: total_active_bid_count {
  #   type: sum
  #   sql: ${active_bid_count} ;;  }

  # measure: average_active_bid_count {
  #   type: average
  #   sql: ${active_bid_count} ;;  }

  dimension: correlation_rank {
    type: number
    sql: ${TABLE}.correlation_rank ;;
  }

  measure: inactive_bid_count {
    type: sum
    sql: ${TABLE}.inactive_bid_count ;;
  }

  dimension: industry_sector {
    type: string
    sql: ${TABLE}.industry_sector ;;
  }

  dimension: pair_1 {
    type: string
    sql: ${TABLE}.pair_1 ;;
  }

  dimension: parent_industry_sector {
    type: string
    sql: ${TABLE}.parent_industry_sector ;;
  }

  dimension: segment {
    type: string
    sql: ${TABLE}.segment ;;
  }

  dimension: sf_rollup_country_of_hq {
    type: string
    sql: ${TABLE}.sf_rollup_country_of_hq ;;
  }

  measure: total_bid_count {
    type: sum
    sql: ${TABLE}.total_bid_count ;;
  }

  # measure: count {
  #   type: count
  # }
}
