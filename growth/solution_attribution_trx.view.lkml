view: solution_attribution_trx {
  # source: https://xenditco.cloud.looker.com/sql/mm9nbb46rp2x47?toggle=sql
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql:
    WITH
    rawtransactions
    AS (
        SELECT
          t1.SFDC_ID as product_id
          ,t1.business_id
          ,t1.entity
          ,t1.product_type
          ,t1.product_subtype
          ,t1.channel_name
          ,t1.currency
          ,'Monthly' AS period_type
          ,CAST(DATE_FORMAT(t1.dt, '%Y-%m-01') AS DATE) AS transaction_month
          ,sum(t1.total_payment_volume_local) AS tpv_local
          ,sum(t1.total_revenue_local) AS revenue_local
          ,sum(t1.total_payment_volume_usd) AS tpv_usd
          ,sum(t1.revenue_usd) AS revenue_usd
          ,sum(t1.transaction_count) AS transactions
        FROM transform__business_intelligence_third_party_pipelines.salesforce_tpv_backbone t1
        WHERE t1.dt >= DATE ('2022-03-31')
        GROUP BY 1,2,3,4,5,6,7,8,9
    )

      ,transactions_attr
      AS (
      SELECT
      t.business_id
      ,t.product_type
      ,t.product_id
      ,t.transaction_month
      ,CONCAT(t.business_id, t.product_id, CAST(t.transaction_month AS STRING)) AS key_attr
      FROM rawtransactions t
      LEFT JOIN transform__growth.solution_attribution s ON t.business_id=s.business_id
      WHERE s.se_req_status NOT IN ('Draft', 'Rejected')
      AND t.transaction_month >= assignmentmonth
      AND t.transaction_month <= CAST(DATE_FORMAT(DATE_ADD('MONTH', 6, s.closedwondate), '%Y-%m-01') AS DATE)
      )

      ,minproducttype
      AS (
      SELECT
      t.business_id
      ,t.product_type
      ,min(t.transaction_month) AS first_trans
      ,CONCAT(t.business_id, t.product_type, CAST(min(t.transaction_month) AS STRING)) AS keyprodtype_attr
      ,1 AS first_trans_mark
      FROM transactions_attr t
      GROUP BY 1,2
      )

      ,minproductchannel
      AS (
      SELECT
      t.business_id,
      t.product_id,
      min(t.transaction_month) AS first_trans
      ,CONCAT(t.business_id, t.product_id, CAST(min(t.transaction_month) AS STRING)) AS keychannel_attr
      ,1 AS first_trans_mark
      FROM transactions_attr t
      GROUP BY 1,2
      )

      ,transactions
      AS (
      SELECT
      t.*
      ,CONCAT(t.business_id, t.product_type, CAST(t.transaction_month AS STRING)) AS keyprodtype
      ,CONCAT(t.business_id, t.product_id, CAST(t.transaction_month AS STRING)) AS keychannel
      ,a.id as account_id_new
      ,a.name as account_name
      ,CAST(op.closed_won_date__c AS DATE) as closedwondate
      ,s.activationdate
      ,s.closedwondate as closedwondate_attr
      ,s.assignmentdate AS se_assign
      ,s.se_email
      FROM rawtransactions t
      LEFT JOIN clean__salesforce.accounts a ON t.business_id = a.business_id__c
      LEFT JOIN transform__growth.solution_attribution s ON a.id = s.account_id
      LEFT JOIN clean__salesforce.opportunities op ON op.id = s.parent_opportunity_id
      )

      ,attribution_combine_with_subaccounts as (
      SELECT DISTINCT
      t1.req_name
      ,t1.opportunity_id
      ,t1.parent_opportunity_id
      ,t1.opp_name
      ,t1.account_id
      ,t1.business_id
      ,t1.master_business_id
      ,t1.se_email
      ,t1.se_req_status
      ,t1.se_req_type
      ,t1.team_name
      ,t1.assignmentdate
      ,t1.assignmentmonth
      ,t1.activationdate
      ,t1.closedwondate
      ,t1.primary_key
      ,t3.name as account_name
      FROM transform__growth.solution_attribution t1
      LEFT JOIN (SELECT DISTINCT id,name,business_id__c,master_bid__c FROM clean__salesforce.accounts) t2 ON t1.business_id = t2.master_bid__c
      LEFT JOIN clean__salesforce.accounts t3 ON t1.account_id=t3.id
      WHERE t2.master_bid__c is null

      UNION ALL

      SELECT DISTINCT
      t1.req_name
      ,t1.opportunity_id
      ,t1.parent_opportunity_id
      ,t1.opp_name
      ,t2.id as account_id
      ,t2.business_id__c as business_id
      ,t2.master_bid__c as master_business_id
      ,t1.se_email
      ,t1.se_req_status
      ,t1.se_req_type
      ,t1.team_name
      ,t1.assignmentdate
      ,t1.assignmentmonth
      ,t1.activationdate
      ,t1.closedwondate
      ,t1.primary_key
      ,t2.name as account_name
      FROM transform__growth.solution_attribution t1
      LEFT JOIN (SELECT DISTINCT id,name,business_id__c,master_bid__c FROM clean__salesforce.accounts) t2 ON t1.business_id = t2.master_bid__c
      WHERE t2.master_bid__c is not null
      )

      --exception based on https://docs.google.com/spreadsheets/d/1QzdU_bHe353mzydXgE5vwvkPUqSpc0KX/edit?gid=**********#gid=**********&range=A:D
      ,solution_attributions as (
      select distinct
      *
      from attribution_combine_with_subaccounts
      where account_id not in ('0015j00000quSKAAA2','0015j000014IXIiAAO','0015j000017ARnlAAG','0015j000018NjhPAAS','0015j000019xvRRAAY','0015j00001SEVmiAAH','0015j00001SEWr1AAH','0015j00001SEjXVAA1','0015j00001SEjYiAAL','0015j00001UOu8KAAT','0015j00001URVraAAH','0015j00001VonWLAAZ','0015j00001X9ZrkAAF','0015j00001YPnw8AAD','0015j00001ZlgqwAAB','0015j00001ZlhrzAAB','0015j00001bDUR1AAO','0015j00001blkyWAAQ','0015j00001bmHD1AAM','0015j00001bmR8cAAE','0015j00001bmR8rAAE','0015j00001bmR8wAAE','0015j00001bmR91AAE','0015j00001bo69ZAAQ','0015j00001boQKcAAM','0015j00001dE0lzAAC','001J40000038xgDIAQ','001J4000005RbyIIAS','001J4000005Ro8XIAS','001J4000005RoDhIAK','0015j00000xkzn3AAA','0015j00000xyXynAAE','0015j00000yAmSoAAK','0015j00000yAn28AAC','0015j000011JbajAAC','0015j00001B6KSxAAN','0015j00000tseotAAA','0015j00000tseotAAA','0015j00000g6Sx3AAE','0015j00000mjM9kAAE','0015j000011HvZ9AAK','0015j00000nJdykAAC','0015j00000wL4eHAAS','0015j000010o05xAAA','0015j000012miT7AAI','0015j00000nca7CAAQ','0015j00000mjI2uAAE','0015j00000nccaUAAQ','0015j00000ttyBDAAY','0015j00000zumH1AAI','0015j00001B9UFsAAN','0015j000010o05xAAA','0015j00000nccaUAAQ','0015j000012miT7AAI','0015j00001SFDo5AAH','0015j00001YPkxJAAT','0015j000019yru8AAA','0015j00001VsGcgAAF','0015j00001X6Ta7AAF','0015j00000mjf9cAAA','0015j00000mjf9HAAQ','0015j00000mjf9RAAQ','0015j00000w0PHSAA2','0015j00001Z9aXOAAZ','0015j00000tsewPAAQ','0015j00001SGkLYAA1','0015j00000qvC0FAAU','0015j00000qvqSVAAY','0015j00001VoOdXAAV','001J4000005RXHOIA4','0015j00000mjM4LAAU','0015j00000y6yorAAA','0015j00000mjM4LAAU','001J4000005U6fsIAC','001J400000KBUNJIA5','001J400000KBHpnIAH','001J4000005UDWdIAO','001J400000KBUFyIAP','001J4000005SqM9IAK','001J4000005TFh8IAG','001J4000005TYniIAG','0015j000011It57AAC','0015j00000tsL55AAE','0015j000010nK9vAAE','0015j000010nKNnAAM','0015j000012mqvBAAQ','0015j000012mr85AAA','0015j000012pBFDAA2','0015j000013OQ16AAG','0015j000013RcmaAAC','0015j000014IAvDAAW','0015j00001772GqAAI','0015j0000177JWtAAM','0015j0000179hf0AAA','0015j000018LesRAAS','0015j000018xzXTAAY','0015j000019CJoxAAG','0015j00001B7Ol5AAF','0015j00001CTpfaAAD','0015j00001CVFtVAAX','0015j00001SEXhrAAH','0015j00001SFdjJAAT','0015j00001SFdyXAAT','0015j00001SH0KPAA1','0015j00001TeNLCAA3','0015j00001UQlu8AAD','0015j00001VoWAMAA3','0015j00001Zm0TGAAZ','0015j00001ZmCtdAAF','0015j00001bCvWdAAK','0015j00001blvhoAAA','0015j00001blx1YAAQ','0015j00001blzgBAAQ','0015j00001bmOqDAAU','0015j00001bmPv8AAE','0015j00001bmPw1AAE','0015j00001boXUGAA2','0015j00001bodARAAY','0015j00001dDPVoAAO','0015j00001dDc0ZAAS','0015j00001dDfUrAAK','0015j00001dwAZaAAM','0015j00001dxfI2AAI','0015j00001dxjSzAAI','001J4000003AJL5IAO','001J4000003AXOyIAO','001J4000003ApeYIAS','001J4000003BPwVIAW','001J4000005Rcv5IAC','001J4000005Rp51IAC','001J4000005RpWlIAK','001J4000005RpYNIA0','001J4000005RqUmIAK','001J4000005Sc4OIAS','001J4000005S3GxIAK','001J4000005S4WVIA0','001J4000005S8rOIAS','001J4000005Sby6IAC','001J4000005ScsdIAC','001J4000005SlQ3IAK','001J4000005SlSOIA0','0015j00001bo0qNAAQ')
      )

      SELECT
      s.opportunity_id
      ,s.parent_opportunity_id
      ,t.account_id_new as account_id
      ,t.account_name
      ,t.business_id
      ,s.se_email
      ,t.tpv_usd
      ,t.revenue_usd
      ,t.transactions
      ,t.product_type
      ,t.product_subtype
      ,t.channel_name
      ,t.product_id
      ,t.transaction_month
      ,a.transaction_month AS attribution_month
      ,pp.first_trans AS min_trans_prodtype
      ,pc.first_trans AS min_trans_prodchannel
      ,t.activationdate
      ,t.closedwondate
      ,t.closedwondate_attr
      ,t.se_assign
      ,t.keyprodtype
      ,pp.keyprodtype_attr
      ,t.keychannel
      ,pc.keychannel_attr
      ,CASE
      WHEN (t.se_email IS NULL AND t.product_id IS NULL) THEN concat(t.business_id,'null','null',cast(t.transaction_month as STRING))
      WHEN (t.se_email IS NULL AND t.product_id IS NOT NULL) THEN concat(t.business_id,'null',t.product_id,cast(t.transaction_month as STRING))
      WHEN (t.se_email IS NOT NULL AND t.product_id IS NULL) THEN concat(t.business_id,t.se_email,'null',cast(t.transaction_month as STRING))
      ELSE concat(t.business_id,t.se_email,t.product_id,cast(t.transaction_month as STRING))
      END AS primary_key
      ,concat(t.business_id,t.product_type) as count_producttype
      ,concat(t.business_id,t.product_id) as count_productchannel
      --,concat(s.parent_opportunity_id,cast(t.transaction_month)) as estrev_attr_key
      FROM transactions t
      LEFT JOIN transactions_attr a ON t.keychannel = a.key_attr
      LEFT JOIN solution_attributions s ON t.business_id=s.business_id
      LEFT JOIN minproducttype pp ON t.keyprodtype = pp.keyprodtype_attr
      LEFT JOIN minproductchannel pc ON t.keychannel = pc.keychannel_attr
      ;;
  }

  suggestions: no

  dimension: estrev_attr_key {
    hidden: yes
    type: string
    sql: ${TABLE}.estrev_attr_key ;;
  }

  dimension: main_primary_key {
    type: string
    hidden: yes
    primary_key: yes
    sql: ${TABLE}.primary_key ;;
  }

  dimension: opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.opp_id ;;
  }

  dimension: parent_opportunity_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.parent_opp_id ;;
  }

  dimension: account_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.account_id ;;
  }

  dimension: account_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: se_email {
    type: string
    sql: ${TABLE}.se_email ;;
  }

  measure: tpv_usd {
    type: sum
    drill_fields: [
      se_email,
      account_id,
      account_name,
      tpv_usd,
      revenue_usd,
      transaction_count]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.tpv_usd ;;
  }

  measure: revenue_usd {
    type: sum
    drill_fields: [
      se_email,
      account_id,
      account_name,
      tpv_usd,
      revenue_usd,
      transaction_count]
    # value_format: "#,##0"
    value_format: "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
    # value_format: "[<1000]0\"\";[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\""
    # value_format: "[>=**********]0.00,,,\"B\";[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";[<1000]0.00\"\""
    sql: ${TABLE}.revenue_usd ;;
  }

  measure: transaction_count {
    type: sum
    drill_fields: [
      se_email,
      account_id,
      account_name,
      tpv_usd,
      revenue_usd,
      transaction_count]
    value_format: "#,##0"
    sql: ${TABLE}.transactions ;;
  }

  measure: product_types {
    type: count_distinct
    drill_fields: [
      account_id,
      account_name,
      product_type,
      min_trans_prodtype_month]
    sql: ${TABLE}.count_producttype ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: product_id {
    type: string
    sql: ${TABLE}.product_id ;;
  }

  dimension_group: transaction_month {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: CAST(${TABLE}.transaction_month AS DATE) ;;
  }

  dimension_group: attribution_month {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: CAST(${TABLE}.attribution_month AS DATE) ;;
  }

  dimension_group: min_trans_prodtype {
    label: "First Transaction"
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: CAST(${TABLE}.min_trans_prodtype AS DATE) ;;
  }

  dimension_group: min_trans_prodchannel {
    label: "First Transactions"
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: CAST(${TABLE}.min_trans_prodchannel AS DATE);;
  }

  dimension_group: activationdate {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: ${TABLE}.activationdate ;;
  }

  dimension_group: closedwondate {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: ${TABLE}.closedwondate ;;
  }

  dimension_group: closedwondate_attr {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: ${TABLE}.closedwondate_attr ;;
  }

  dimension_group: se_assign {
    type: time
    timeframes: [
      date,
      month,
      quarter,
      year
    ]
    convert_tz: no
    sql: CAST(${TABLE}.se_assign AS DATE) ;;
  }

  set: detail {
    fields: [
      product_id,
      business_id,
      tpv_usd,
      revenue_usd,
      transaction_count,
      product_type,
      product_subtype,
      channel_name,
      opportunity_id,
      parent_opportunity_id,
      account_id,
      account_name,
      se_email,
      main_primary_key
    ]
  }
}
