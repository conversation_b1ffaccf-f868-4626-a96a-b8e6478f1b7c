# The name of this view in Looker is "Sales Target V1"
view: team_target {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  # sql_table_name: clean__google_sheets.sales_target_v1 ;;
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql: SELECT * FROM clean__google_sheets.sales_target_v1 WHERE lower(name) LIKE ('%team%') ;;
  }
  suggestions: yes

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Country" in Explore.

  dimension: country {
    # label: "Currency"
    type: string
    # map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension: email {
    label: "Description"
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: metric {
    type: string
    sql: ${TABLE}.metric ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: month {
    label: "Target"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    # sql: ${TABLE}."month" ;;
    convert_tz: no
    datatype: date
    sql: CAST(${TABLE}.month as DATE) ;;
  }

  dimension: name {
    label: "Type"
    type: string
    sql: ${TABLE}.name ;;
  }

  # dimension: target {
  #   type: number
  #   sql: ${TABLE}.target ;;
  # }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: gross_revenue_target {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "Gross Revenue"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  measure: handover_gross_revenue_target {
    type: sum
    sql: ${TABLE}.target ;;
    filters: [metric: "Handover Gross Revenue"]
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  # measure: reforecast {
  #   type: sum
  #   sql: ${TABLE}.target ;;
  #   filters: [metric: "P&L Revenue Reforecast"]
  #   value_format: "$#,##0.00"
  #   # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  # }

  measure: target {
    type: sum
    sql: ${TABLE}.target ;;
    value_format: "$#,##0.00"
    # "[>=1000000]0.00,,\"M\";[>=1000]0.00,\"K\";0"
  }

  # measure: average_target {
  #   type: average
  #   sql: ${target} ;;  }

  dimension: team {
    type: string
    sql: ${TABLE}.team ;;
  }

  # measure: count {
  #   type: count
  #   drill_fields: [name]
  # }

  dimension: currency {
    type: string
    sql: CASE
          WHEN ${TABLE}.email = 'Indonesia' THEN 'IDR'
          WHEN ${TABLE}.email = 'Philippines' THEN 'PHP'
          WHEN ${TABLE}.email = 'Thailand' THEN 'THB'
          WHEN ${TABLE}.email = 'Malaysia' THEN 'MYR'
          WHEN ${TABLE}.email = 'Vietnam' THEN 'VND'
          ELSE 'Unknown'
        END ;;
  }

  dimension: category {
    type: string
    # hidden: yes
    # sql: CASE WHEN ${TABLE}.country = 'Regional' THEN TRUE ELSE FALSE END ;;
    sql:
    CASE
      WHEN
        ${TABLE}.metric = 'Budget Net Revenue'
        OR ${TABLE}.metric = 'Budget Gross Revenue'
      THEN (
        CASE
          WHEN ${TABLE}.team = 'Regional Remittance' THEN 'Regional Remittance'
          WHEN ${TABLE}.team = 'Regional AM' THEN 'Regional'
          WHEN ${TABLE}.team = 'Regional FS' THEN 'Regional'
          WHEN ${TABLE}.team = 'Regional Chinese' THEN 'Regional'
          WHEN ${TABLE}.team = 'Regional SEA' THEN 'Regional'

          WHEN ${TABLE}.team = 'ID AM - Dedicated' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'ID Pool AM' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'ID Crypto' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'Mid Market Sales' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'Enterprise Sales' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'ID Financial Services' THEN 'Indonesia Local'
          WHEN ${TABLE}.team = 'Self Serve ID' THEN 'Indonesia Local'

          WHEN ${TABLE}.team = 'PH IB + Pool AM' THEN 'Philippines Local'
          WHEN ${TABLE}.team = 'PH AM' THEN 'Philippines Local'
          WHEN ${TABLE}.team = 'PH OB' THEN 'Philippines Local'
          WHEN ${TABLE}.team = 'DP' THEN 'Philippines Local'

          ELSE NULL
      END
      )
      ELSE NULL
      END ;;
  }

  dimension: is_regional {
    type: yesno
    # sql: CASE WHEN ${TABLE}.country = 'Regional' THEN TRUE ELSE FALSE END ;;
    sql:
    CASE
      WHEN
        ${TABLE}.metric = 'Budget Net Revenue'
        OR ${TABLE}.metric = 'Budget Gross Revenue'
      THEN (
        CASE
          WHEN ${TABLE}.team = 'Regional Remittance' THEN TRUE
          WHEN ${TABLE}.team = 'Regional AM' THEN TRUE
          WHEN ${TABLE}.team = 'Regional FS' THEN TRUE
          WHEN ${TABLE}.team = 'Regional Chinese' THEN TRUE
          WHEN ${TABLE}.team = 'Regional SEA' THEN TRUE
          ELSE FALSE
        END
      )
      ELSE (
        CASE WHEN ${TABLE}.country = 'Regional' THEN TRUE ELSE FALSE END
      )
    END ;;
  }
}
