view: businesses_converted {
  derived_table: {
    sql: select
          business_id,
          name_biz,
          email,
          entity,
          created,
          DATE_FORMAT((businesses.created AT TIME ZONE 'Asia/Jakarta'), '%Y-%m') as created_month
      from
          ${businesses.SQL_TABLE_NAME} as businesses
      where
        (
          NOT (businesses.is_internal = true)
          OR (businesses.is_internal = true) IS NULL
      )
        AND (
          (
              UPPER((businesses.subaccount_type)) <> UPPER('owned')
              OR (businesses.subaccount_type) IS NULL
          )
      )
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: name_biz {
    type: string
    sql: ${TABLE}.name_biz ;;
  }

  dimension: email {
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension_group: created {
    type: time
    sql: ${TABLE}.created ;;
  }

  set: detail {
    fields: [
      business_id,
      name_biz,
      email,
      entity,
      created_time
    ]
  }
}
