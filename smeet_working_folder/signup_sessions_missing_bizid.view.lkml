view: signup_sessions_missing_bizid {
  derived_table: {
    sql: with success_sessions as (
          select
              domain_sessionid,
              max(dvce_created_tstamp) as dvce_created_tstamp_latest
          from
              clean__kafka_snowplow.events
          where
              se_category = 'userAuthentication'
              and se_action = 'register'
              and se_label = 'success'
              and dt >= date '2021-01-01'
              and user_id IS NULL
          group by
              domain_sessionid
      ),
      basetable as (
          select
              t1.domain_userid,
              t1.domain_sessionid,
              t1.user_id,
              t1.app_id,
              t1.event,
              t1.se_category,
              t1.se_action,
              t1.se_label,
              t1.dvce_created_tstamp,
              t1.dt,
              t3.device_class,
              t3.agent_class,
              t3.agent_name,
              t3.device_cpu,
              t3.device_name,
              t3.operating_system_name,
              t3.operating_system_version
          from
              clean__kafka_snowplow.events t1
              inner join success_sessions t2 on t1.domain_sessionid = t2.domain_sessionid
              left join clean__s3_snowplow.nl_basjes_yauaa_context_1 t3 on t1.event_id = t3.root_id
          where
              t1.dvce_created_tstamp <= t2.dvce_created_tstamp_latest
          order by
              t1.domain_userid,
              t1.domain_sessionid,
              t1.dvce_created_tstamp
      )
      select
          dt,
          count(distinct domain_userid) as unique_users
      from
          basetable
      group by 1
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: dt {
    type: date
    sql: ${TABLE}.dt ;;
  }

  dimension: unique_users {
    type: number
    sql: ${TABLE}.unique_users ;;
  }

  set: detail {
    fields: [dt, unique_users]
  }
}