view: businesses_wo_signupevents {
  derived_table: {
    sql: with
      relevant_businesses as (
          select
              business_id,
              name_biz,
              email,
              entity,
              created,
              DATE_FORMAT(
                  (businesses.created AT TIME ZONE 'Asia/Jakarta'),
                  '%Y-%m'
              ) as created_month
          from
              ${businesses.SQL_TABLE_NAME} as businesses
          where
              (
                  NOT (businesses.is_internal = true)
                  OR (businesses.is_internal = true) IS NULL
              )
              AND (
                  (
                      UPPER((businesses.subaccount_type)) <> UPPER('owned')
                      OR (businesses.subaccount_type) IS NULL
                  )
              )
      ),
      confirmed_signups as
      (
          select
              distinct business_id
          from
              ${traffic_sessionization.SQL_TABLE_NAME}
      )
      select
          *
      from
          relevant_businesses
      where
          business_id not IN (
              select
                  *
              from
                  confirmed_signups
          )
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: name_biz {
    type: string
    sql: ${TABLE}.name_biz ;;
  }

  dimension: email {
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  set: detail {
    fields: [
      business_id,
      name_biz,
      email,
      entity,
      created_time
    ]
  }
}
