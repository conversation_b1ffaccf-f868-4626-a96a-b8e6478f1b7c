include: "/xenconnex/*.view.lkml"

explore: XenConnex {
  group_label: "XenConnex"
  from: ag_business_setting
  view_label: "Business Settings"

  always_filter: {
    filters: [XenConnex.mode : "-NULL"]
  }


  join: ag_link_token {
    from: ag_link_token
    view_label: "Link Tokens"
    sql_on: ${XenConnex.business_id} = ${ag_link_token.business_id} ;;
    relationship: one_to_many
    type: left_outer
  }

  join: ag_account {
    from: ag_account
    view_label: "Accounts"
    sql_on: ${ag_account.link_token_id} = ${ag_link_token.id} ;;
    relationship: one_to_many
    type: left_outer
  }

  join: ag_item {
    from: ag_item
    view_label: "Items"
    sql_on: ${ag_item.link_token_id} = ${ag_link_token.id} ;;
    relationship: many_to_one
    type: left_outer
  }

  join: ag_transaction {
    from: ag_transaction_xenconnex
    view_label: "Transactions"
    sql_on: ${ag_account.id} = ${ag_transaction.account_id} ;;
    relationship: one_to_many
    type: left_outer
  }

  join: ag_institution {
    from: ag_institution
    view_label: "Institutions"
    sql_on: ${ag_institution.code} = ${ag_link_token.institution_code};;
    relationship: one_to_many
    type: left_outer
  }

  join: ag_product {
    from: ag_product
    view_label: "Products"
    sql_on: ${ag_product.institution_code} = ${ag_institution.code} ;;
    relationship: one_to_many
    type: left_outer
  }
}
