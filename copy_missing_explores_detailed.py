#!/usr/bin/env python3
"""
Script to copy specific missing explores from source to target repository
"""
import subprocess
import re
import os

def extract_explore_block(file_path, explore_name):
    """Extract a specific explore block from a file"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the explore block
    pattern = rf'^explore:\s+{re.escape(explore_name)}\s*\{{(.*?)^\}}'
    match = re.search(pattern, content, re.MULTILINE | re.DOTALL)
    
    if match:
        return f"explore: {explore_name} {{\n{match.group(1)}}}\n"
    return None

def copy_missing_explores():
    """Copy specific missing explores"""
    source_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
    target_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"
    
    # Define the remaining missing explores to copy
    missing_explores = [
        {
            "file": "growth/solution_dashboard.explore.lkml",
            "explores": ["new_solution_attribution", "sf_monthly_transactions", "solution_attribution"]
        },
        {
            "file": "salesforce/salesforce.explore.lkml", 
            "explores": ["task"]
        },
        {
            "file": "transactions/report/report.explore.lkml",
            "explores": ["transaction_tab_tracking", "reporting_service_reports", "unified_t4_transactions", 
                       "legacy_billing_v1", "billing_generation_v2", "detailed_transactions", 
                       "utv_report_exports_jobs", "unified_ledger_line"]
        },
        {
            "file": "xenCapital/xenCapital.explore.lkml",
            "explores": ["accrual_revenue_v2", "compilation_v1_v2_lms_raw", "opex_kpi_update", "lms"]
        },
        {
            "file": "xenShield/xenshield.explore.lkml",
            "explores": ["authentication_assessment_reports"]
        },
        {
            "file": "xensavings/xendit_app/xendit_app.explore.lkml",
            "explores": ["ag_nexcard_bri_application_decrypted", "xendit_app_users_joint"]
        },
        {
            "file": "xensavings/xensavings.explore.lkml",
            "explores": ["xensavings_aum", "xensavings_analytics"]
        },
        {
            "file": "zendesk/ticket.explore.lkml",
            "explores": ["service_level_aggreement"]
        }
    ]
    
    total_copied = 0
    total_failed = 0
    
    for file_info in missing_explores:
        file_path = file_info["file"]
        explores = file_info["explores"]
        
        source_file = f"{source_repo}/{file_path}"
        target_file = f"{target_repo}/{file_path}"
        
        print(f"\n🔄 Processing {file_path}...")
        
        if not os.path.exists(source_file):
            print(f"❌ Source file not found: {source_file}")
            total_failed += len(explores)
            continue
            
        if not os.path.exists(target_file):
            print(f"❌ Target file not found: {target_file}")
            total_failed += len(explores)
            continue
        
        # Read target file content
        with open(target_file, 'r') as f:
            target_content = f.read()
        
        # Extract and append missing explores
        explores_to_add = []
        for explore_name in explores:
            explore_block = extract_explore_block(source_file, explore_name)
            if explore_block:
                explores_to_add.append(explore_block)
                print(f"✅ Found explore: {explore_name}")
                total_copied += 1
            else:
                print(f"❌ Could not find explore: {explore_name}")
                total_failed += 1
        
        if explores_to_add:
            # Append explores to target file
            updated_content = target_content.rstrip() + "\n\n" + "\n".join(explores_to_add)
            
            try:
                with open(target_file, 'w') as f:
                    f.write(updated_content)
                print(f"✅ Updated {file_path} with {len(explores_to_add)} explores")
            except Exception as e:
                print(f"❌ Failed to write to {file_path}: {e}")
                total_failed += len(explores_to_add)
                total_copied -= len(explores_to_add)
    
    print(f"\n🎯 Summary:")
    print(f"Explores copied: {total_copied}")
    print(f"Explores failed: {total_failed}")

if __name__ == "__main__":
    copy_missing_explores()
