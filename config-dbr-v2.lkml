connection: "databricks-staging"

week_start_day: monday
fiscal_month_offset: 0
case_sensitive: no

# ---- Global custom formats ----
# -------------------------------
named_value_format: usd_3 {
  value_format: "$#,##0.000"
  strict_value_format: no
}


# ---- Global access grants ----
# ------------------------------
access_grant: global__can_view_merchant_pii {
  allowed_values: ["yes"]
  user_attribute: can_view_pii
}
access_grant: global__can_view_end_user_pii {
  allowed_values: ["yes"]
  user_attribute: can_view_pii
}
access_grant: global__can_view_revenue {
  allowed_values: ["yes"]
  user_attribute: can_view_revenue
}
access_grant: global__can_view_cogs {
  allowed_values: ["yes"]
  user_attribute: can_view_cogs
}
access_grant: global__can_view_hr {
  allowed_values: ["yes"]
  user_attribute: can_view_hr
}



# ---- Global datagroup definitions ----
# --------------------------------------
datagroup: global__start_of_day_jkt {
  label: "8AM Jakarta time (GMT+7)"
  max_cache_age: "24 hours"
  sql_trigger: SELECT date(current_timestamp - interval '1' hour) ;;
}

datagroup: global__start_of_day_sgt {
  label: "8AM Singapore time (GMT+8)"
  max_cache_age: "24 hours"
  sql_trigger: SELECT date(current_timestamp) ;;
}

datagroup: global__txnbb_updated {
  label: "Transaction backbone updated"
  description: "Triggers whenever there is an update of the transaction_backbone table. May be multiple times in a day if there are ad-hoc table updates"
  max_cache_age: "24 hours"
  sql_trigger:  SELECT count(*) from transform__business_intelligence_transaction_volumes.fct_transaction_backbone ;;
}

datagroup: global__businesses_updated {
  label: "Businesses table updated"
  description: "Triggers whenever there is an update of the dim_businesses table. May be multiple times in a day if there are ad-hoc table updates"
  max_cache_age: "24 hours"
  sql_trigger:  SELECT count(*) from transform__business_intelligence.dim_businesses ;;
}

datagroup: global__month_end {
  label: "Last day of the month"
  description: "Triggered on the last day of each month"
  sql_trigger: SELECT (extract(month from current_date + interval '1' day)) ;;
}

access_grant: can_view_cogs_report {
  allowed_values: ["yes"]
  user_attribute: can_view_cogs
}
