view: remittances {
  sql_table_name: clean__remittance_service.remittances ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: bank_reference {
    type: string
    sql: ${TABLE}.bank_reference ;;
  }

  dimension: completed_transaction_id {
    type: string
    sql: ${TABLE}.completed_transaction_id ;;
  }

  dimension: completed_transaction_sequence {
    type: number
    sql: ${TABLE}.completed_transaction_sequence ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: events {
    type: string
    sql: ${TABLE}.events ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: processor_holder_name {
    type: string
    sql: ${TABLE}.processor_holder_name ;;
  }

  dimension: purpose_code {
    type: string
    sql: ${TABLE}.purpose_code ;;
  }

  dimension: recipient_customer_details {
    type: string
    sql: ${TABLE}.recipient_customer_details ;;
  }

  dimension: recipient_bank_code {
    type: string
    sql: ${TABLE}.recipient_customer_details.account_code ;;
  }

  dimension: recipient_customer_id {
    type: string
    sql: ${TABLE}.recipient_customer_id ;;
  }

  dimension: refund_transaction_id {
    type: string
    sql: ${TABLE}.refund_transaction_id ;;
  }

  dimension: refund_transaction_sequence {
    type: string
    sql: ${TABLE}.refund_transaction_sequence ;;
  }

  dimension: sender_bank_code {
    type: string
    sql: ${TABLE}.sender_bank_code ;;
  }

  dimension: sender_customer_details {
    type: string
    sql: ${TABLE}.sender_customer_details ;;
  }

  dimension: sender_customer_id {
    type: string
    sql: ${TABLE}.sender_customer_id ;;
  }

  dimension: source_of_funds {
    type: string
    sql: ${TABLE}.source_of_funds ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: status_updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.status_updated ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension: transaction_sequence {
    type: number
    sql: ${TABLE}.transaction_sequence ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: v {
    type: number
    sql: ${TABLE}.v ;;
  }

  measure: count {
    type: count
    drill_fields: [id, processor_holder_name]
  }
}
