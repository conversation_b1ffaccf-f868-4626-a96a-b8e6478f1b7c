view: remittances_mongo {
  derived_table: {
    sql: select
      CAST(_id AS string) as remittance_id,
      external_id,
      user_id as business_id,
      status,
      amount,
      failure_code,
      created,
      updated,
      disbursement_id,
      sender_customer_id,
      recipient_customer_id,
      disbursement_snapshot.disbursementRoutingId as disbursement_routing_id,
      recipient_customer_details.account_code as account_code,
      compliance_detail.risk_score as risk_score,
      compliance_detail.compliance_action as compliance_action,
      compliance_detail.reasons as risk_reasons
      from "mongo-remittance-service"."remittance-service".remittances;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: remittance_id {
    type: string
    sql: ${TABLE}.remittance_id ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: sender_customer_id {
    type: string
    sql: ${TABLE}.sender_customer_id ;;
  }

  dimension: recipient_customer_id {
    type: string
    sql: ${TABLE}.recipient_customer_id ;;
  }

  dimension: risk_score {
    type: string
    sql: ${TABLE}.risk_score ;;
  }

  dimension: compliance_action {
    type: string
    sql: ${TABLE}.compliance_action ;;
  }

  dimension: risk_reasons {
    type: string
    sql: ${TABLE}.risk_reasons ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension_group: created {
    type: time
    sql: ${TABLE}.created ;;
  }

  dimension_group: updated {
    type: time
    sql: ${TABLE}.updated ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension: disbursement_routing_id {
    type: string
    sql: ${TABLE}.disbursement_routing_id ;;
  }

  dimension: account_code {
    type: string
    sql: ${TABLE}.account_code ;;
  }

  set: detail {
    fields: [
      remittance_id,
      external_id,
      business_id,
      status,
      amount,
      failure_code,
      created_time,
      updated_time,
      disbursement_id,
      disbursement_routing_id
    ]
  }
}
