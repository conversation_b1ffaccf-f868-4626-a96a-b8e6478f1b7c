view: latest_vulnerability_findings {
  sql_table_name: clean__security_scorecard.latest_vulnerability_findings ;;
  suggestions: no

  dimension: assignee_accountid {
    type: string
    sql: ${TABLE}.assignee_accountid ;;
  }

  dimension: assignee_displayname {
    type: string
    sql: ${TABLE}.assignee_displayname ;;
  }

  dimension: assignee_timezone {
    type: string
    sql: ${TABLE}.assignee_timezone ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: cast(${TABLE}.created as timestamp) ;;
  }

  dimension_group: resolution_date {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: cast(${TABLE}.resolutiondate as timestamp);;
  }

  dimension: creator_accountid {
    type: string
    sql: ${TABLE}.creator_accountid ;;
  }

  dimension: creator_displayname {
    type: string
    sql: ${TABLE}.creator_displayname ;;
  }

  dimension: creator_timezone {
    type: string
    sql: ${TABLE}.creator_timezone ;;
  }

  dimension: day_to_resolve {
    type: number
    label: "By Day"
    group_label: "Difference"
    sql: date_diff('day', ${created_raw}, ${resolution_date_raw}) ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: is_accepted_risk {
    type: yesno
    sql: ${TABLE}.is_accepted_risk ;;
  }

  dimension: is_subtask {
    type: yesno
    sql: ${TABLE}.is_subtask ;;
  }

  dimension: issue_id {
    primary_key: yes
    type: number
    sql: ${TABLE}.issue_id ;;
  }

  dimension: issue_key {
    type: string
    sql: ${TABLE}.issue_key ;;
  }

  dimension: issue_key_prefix {
    type: string
    sql: SPLIT(${issue_key}, '-')[1] ;;
    hidden: yes
  }

  dimension: labels {
    type: string
    sql: ${TABLE}.labels ;;
  }

  dimension: link {
    type: string
    sql: concat('https://xendit.atlassian.net/browse/', ${issue_key}) ;;
    link: {
      label: "{{ value }}"
      url: "{{ value }}"
    }
  }

  dimension_group: loaded {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: cast(${TABLE}.loaded_at as timestamp);;
  }

  dimension: parent_id {
    type: number
    sql: ${TABLE}.parent_id ;;
  }

  dimension: parent_key {
    type: string
    sql: ${TABLE}.parent_key ;;
  }

  dimension: priority {
    type: string
    sql: ${TABLE}.priority ;;
  }

  dimension: project {
    type: string
    sql: ${TABLE}.project ;;
  }

  dimension: reporter_accountid {
    type: string
    sql: ${TABLE}.reporter_accountid ;;
  }

  dimension: reporter_displayname {
    type: string
    sql: ${TABLE}.reporter_displayname ;;
  }

  dimension: reporter_timezone {
    type: string
    sql: ${TABLE}.reporter_timezone ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: statuscategorychangedate {
    type: string
    sql: ${TABLE}.statuscategorychangedate ;;
  }

  dimension: subtasks {
    type: string
    sql: ${TABLE}.subtasks ;;
  }

  dimension: summary {
    type: string
    sql: ${TABLE}.summary ;;
  }

  dimension: team_name {
    type: string
    sql: ${TABLE}.team_name ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: severity {
    type: string
    sql: CASE
      WHEN contains(${labels}, 'sev:critical') THEN 'Critical'
      WHEN contains(${labels}, 'sev:high') THEN 'High'
      WHEN contains(${labels}, 'sev:medium') THEN 'Medium'
      WHEN contains(${labels}, 'sev:low') THEN 'Low'
    END;;
  }

  measure: mean_time_to_remediate {
    type: average
    filters: [status: "Done"]
    sql: ${day_to_resolve} ;;
    drill_fields: [details*]
  }

  set: details {
    fields: [
      team_name,
      issue_id,
      link,
      status,
      created_date,
      resolution_date_date,
      priority,
    ]
  }
}
