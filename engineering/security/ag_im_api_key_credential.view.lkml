view: ag_im_api_key_credential {
  derived_table: {

    sql:

      WITH parse_money_flow AS(
       SELECT id,
              business_id,
              key_hash,
              name,
              permissions,
              entity,
              environment,
              is_live,
              is_soft_deleted,
              created,
              updated,
              MAP_FROM_ENTRIES(COLLECT_LIST(STRUCT(col.type, col.actions))) AS money_flow_with_access
       FROM clean__api_auth_service.api_key_credential a
       LATERAL VIEW EXPLODE(from_json(permissions, 'array<struct<type:string,actions:array<string>>>')) AS col
       GROUP BY 1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
                9,
                10,
                11
)

SELECT id,
       business_id,
       key_hash,
       name,
       permissions,
       entity,
       environment,
       is_live,
       is_soft_deleted,
       created,
       updated,
       COALESCE(money_flow_with_access['MONEY_IN'],null) as money_in_access,
       COALESCE(money_flow_with_access['MONEY_OUT'],null) as money_out_access
FROM parse_money_flow;;
  }

  drill_fields: [id]

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: is_live {
    type: yesno
    sql: ${TABLE}.is_live ;;
  }

  dimension: is_soft_deleted {
    type: yesno
    sql: ${TABLE}.is_soft_deleted ;;
  }

  dimension: key_hash {
    type: string
    sql: ${TABLE}.key_hash ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: permissions {
    type: string
    sql: ${TABLE}.permissions ;;
  }

  dimension: money_in_access {
    type: string
    sql: ${TABLE}.money_in_access ;;
  }

  dimension: money_out_access {
    type: string
    sql: ${TABLE}.money_out_access ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name]
  }
}
