view: incidents_severity_timeframe {
  derived_table: {
    sql:  SELECT
             DATE_TRUNC('month', CAST(incident_month AS DATE)) AS incident_month,
            d.severity_level
          FROM
              (VALUES
                  (
                      SEQUENCE(
                          DATE_TRUNC('month',cast(from_utc_timestamp('2019-01',"UTC")As date)),
                          DATE_TRUNC('month', CURRENT_DATE()),
                          INTERVAL '1' MONTH
                      )
                  )
              ) as t1 (date_array)
          
             lateral view explode(date_array)  t2 as incident_month
             lateral view explode(ARRAY('1', '2', '3', '4')) d as severity_level
      ;;
  }

  dimension: severity_level {
    type: string
    sql: ${TABLE}.severity_level ;;
  }

  dimension_group: incident_month {
    type: time
    datatype: date
    timeframes: [
      date,
      month,
      year
    ]
  }
}
