include: "//central-models/0_assets/fx_rates.view.lkml"

view: xenshield_revenue {
  derived_table: {
    sql: with transactions as (
          select
          c.created,
          c.business_id,
          b.name as business_name,
          s.pricing as fee_rate,
          s.product_type as product,
          c.currency,
          c.external_id,
          c.id as credit_card_charge_id,
          c.merchant_reference_code,
          c.capture_amount,

      --------------------------
      --08/02/2022 - Noted that below code is undercounting eligible transactions by discounting for any whitelisting that the customer implements using Xenshield
      -- applying adjusted calculations for all new merchants from 02/2022 forward
      --2022/05/23 - Note 1: here is list of BID is using old term where we exclude any transaction with remark of "ALLOWED_BY_*" from revenue calculation
      case
        when
          array_join(fds_decision_context,',') not like '%ALLOWED%'
          and s.business_id in (
          '5f375deb35ba7c1c0eed856e' -- Puregold
          , '6098e6b6c2fd53701b8e5f1a' -- <PERSON><PERSON>
          , '60ab517695624e28764cbd02' -- C4 (HForHype)
          , '5e38d5c0cc91934747c3664c' -- Deus <PERSON> Machina
          , '611dfd618c350c407da7a9ed' -- CompAsia
          , '5b2ef0e253941dd66d70150f' -- Repdigg
          )
          and s.business_id is not null
        then 1
        when
          s.business_id not in (
          '5f375deb35ba7c1c0eed856e' -- Puregold
          , '6098e6b6c2fd53701b8e5f1a' -- ZeusX
          , '60ab517695624e28764cbd02' -- C4 (HForHype)
          , '5e38d5c0cc91934747c3664c' -- Deus Ex Machina
          , '611dfd618c350c407da7a9ed' -- CompAsia
          , '5b2ef0e253941dd66d70150f' -- Repdigg
          )
          and s.business_id is not null
        then 1
        else 0 end as eligible_transactions,
        -- case
        --     when s.business_id is not null then 1
        --     else 0
        -- end as eligible_transactions,
        case
          when
            array_join(fds_decision_context,',') not like '%ALLOWED%'
            and s.business_id in (
            '5f375deb35ba7c1c0eed856e' -- Puregold
            , '6098e6b6c2fd53701b8e5f1a' -- ZeusX
            , '60ab517695624e28764cbd02' -- C4 (HForHype)
            , '5e38d5c0cc91934747c3664c' -- Deus Ex Machina
            , '611dfd618c350c407da7a9ed' -- CompAsia
            , '5b2ef0e253941dd66d70150f' -- Repdigg
            )
            and s.business_id is not null
          then c.capture_amount * s.pricing / 100
          when
            s.business_id not in (
            '5f375deb35ba7c1c0eed856e' -- Puregold
            , '6098e6b6c2fd53701b8e5f1a' -- ZeusX
            , '60ab517695624e28764cbd02' -- C4 (HForHype)
            , '5e38d5c0cc91934747c3664c' -- Deus Ex Machina
            , '611dfd618c350c407da7a9ed' -- CompAsia
            , '5b2ef0e253941dd66d70150f' -- Repdigg
            )
            and s.business_id is not null
          then c.capture_amount * s.pricing / 100
        else 0 end as fee,
        -- case
        --     when s.business_id is not null then c.capture_amount * s.pricing / 100
        --     else 0
        -- end as fee,
        case
          when cb.status = 'LOST' and s.product_type = 'chargeback_insurance' then cb.amount
          else 0
        end as cb_insurance_expense
      from clean__credit_card_production.creditcardcharges c
      left join clean__chargeback_service.chargeback cb on c.id = cb.charge_id
      left join clean__credit_card_production.authorizations a on a.id = c.authorization_id
      left join clean__xendit_business_service.businesses b on b.id = c.business_id
      join clean__google_sheets_xenshield_subscription.subscriptions s on s.business_id = c.business_id
      where c.status = 'CAPTURED'
      and c.created + interval '7' hour >= coverage_start_date -- @TODO Surya: Is this coverage start date always going to be in WIB?
      )

      select
        'Xendit' as billing_entity,
        date_trunc('month', created + interval '7' hour) as billing_month, -- @TODO Surya: Will this always be in WIB?
        business_id,
        business_name,
        product,
        fee_rate,
        currency,
        count(*) captured_count,
        sum(capture_amount) captured_amount,
        sum(eligible_transactions) eligible_transactions_count,
        sum(case when eligible_transactions = 1 then capture_amount else 0 end) eligible_transactions_amount,
        sum(case when eligible_transactions = 0 then 1 else 0 end) non_eligible_transactions_count,
        sum(case when eligible_transactions = 0 then capture_amount else 0 end) non_eligible_transactions_amount,
        sum(fee) as total_fee_in_local_currency,
        sum(cb_insurance_expense) as cb_insurance_expense_in_local_currency
      from transactions
      where created + interval '7' hour >= timestamp '2021-10-01'
      group by 1,2,3,4,5,6,7
      ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: pk {
    primary_key: yes
    hidden: yes
    sql: concat(${business_id}, ${billing_month}, ${product}, ${fee_rate}, ${currency}) ;;
  }

  dimension: billing_entity {
    type: string
    sql: ${TABLE}.billing_entity ;;
  }

  dimension_group: billing {
    description: "The month of the billing FDS billing. Uses a WIB time cutoff (GMT+7) for determining transactions that fall into the billing month"
    type: time
    timeframes: [month, quarter, year]
    convert_tz: no
    sql: ${TABLE}.billing_month ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: business_name {
    type: string
    sql: ${TABLE}.business_name ;;
  }

  dimension: product {
    type: string
    sql: ${TABLE}.product ;;
  }

  dimension: fee_rate {
    type: number
    sql: ${TABLE}.fee_rate ;;
  }

  dimension: fee_rate_usd {
    type: number
    value_format_name: usd
    sql: ${fee_rate} / ${fx_rates.rate_usd} ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: captured_count {
    type: number
    sql: ${TABLE}.captured_count ;;
  }

  dimension: captured_amount {
    type: number
    sql: ${TABLE}.captured_amount ;;
  }

  dimension: captured_amount_usd {
    type: number
    value_format_name: usd
    sql: ${captured_amount} / ${fx_rates.rate_usd} ;;
  }

  dimension: eligible_transactions_count {
    type: number
    sql: ${TABLE}.eligible_transactions_count ;;
  }

  dimension: eligible_transactions_amount {
    type: number
    sql: ${TABLE}.eligible_transactions_amount ;;
  }

  dimension: eligible_transactions_amount_usd {
    type: number
    value_format_name: usd
    sql: ${eligible_transactions_amount} / ${fx_rates.rate_usd} ;;
  }

  dimension: non_eligible_transactions_count {
    type: number
    sql: ${TABLE}.non_eligible_transactions_count ;;
  }

  dimension: non_eligible_transactions_amount {
    type: number
    sql: ${TABLE}.non_eligible_transactions_amount ;;
  }

  dimension: non_eligible_transactions_amount_usd {
    type: number
    value_format_name: usd
    sql: ${non_eligible_transactions_amount} / ${fx_rates.rate_usd} ;;
  }

  dimension: total_fee_in_local_currency {
    type: number
    sql: ${TABLE}.total_fee_in_local_currency ;;
  }

  dimension: cb_insurance_expense_in_local_currency {
    type: number
    sql: ${TABLE}.cb_insurance_expense_in_local_currency ;;
  }

  dimension: total_fee_usd {
    description: "The total fee in USD, converted using Xendit's standard FX rates"
    value_format_name: "usd"
    type: number
    sql: ${total_fee_in_local_currency} /  ${fx_rates.rate_usd} ;;
  }

  dimension: cb_insurance_expense_usd {
    type: number
    value_format_name: usd
    sql: ${cb_insurance_expense_in_local_currency} /  ${fx_rates.rate_usd} ;;
  }

  measure: revenue_in_local_currency {
    description: "The total fee in local currency. Must be used along with the Currency dimension"
    type: sum
    sql: ${total_fee_in_local_currency} ;;
    required_fields: [currency]
    drill_fields: [
      business_name,
      product,
      fee_rate,
      currency,
      captured_amount,
      total_fee_in_local_currency
    ]
  }
  measure: revenue_usd {
    type: sum
    sql: ${total_fee_usd} ;;
    value_format_name: usd
    drill_fields: [
      business_name,
      product,
      fee_rate,
      currency,
      captured_amount,
      total_fee_in_local_currency
    ]
  }

  set: detail {
    fields: [
      billing_entity,
      billing_month,
      business_id,
      business_name,
      product,
      fee_rate,
      currency,
      captured_count,
      captured_amount,
      eligible_transactions_count,
      eligible_transactions_amount,
      non_eligible_transactions_count,
      non_eligible_transactions_amount,
      total_fee_in_local_currency,
      cb_insurance_expense_in_local_currency
    ]
  }
}
