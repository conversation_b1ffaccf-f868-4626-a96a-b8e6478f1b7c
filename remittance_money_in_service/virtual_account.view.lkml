view: virtual_account {
  sql_table_name: clean__remittance_money_in_service.virtual_account ;;
  drill_fields: [id]
  suggestions: no

  dimension: virtual_account_id {
    type: string
    sql: ${TABLE}.virtual_account_id ;;
  }

  dimension: account_block_end {
    type: number
    sql: ${TABLE}.account_block_end ;;
  }

  dimension: account_block_start {
    type: number
    sql: ${TABLE}.account_block_start ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension_group: expiration {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.expiration_time ;;
  }

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: is_verified {
    type: yesno
    sql: ${TABLE}.is_verified ;;
  }

  dimension: merchant_code {
    type: string
    sql: ${TABLE}.merchant_code ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: risk_score {
    type: string
    sql: ${TABLE}.risk_score ;;
  }

  dimension: risk_score_id {
    type: string
    sql: ${TABLE}.risk_score_id ;;
  }

  dimension: risk_score_reason {
    type: string
    sql: ${TABLE}.risk_score_reason ;;
  }

  dimension: sender_customer_id {
    type: string
    sql: ${TABLE}.sender_customer_id ;;
  }

  dimension: service_id {
    type: string
    sql: ${TABLE}.service_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: virtual_account_name {
    type: string
    sql: ${TABLE}.virtual_account_name ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [virtual_account_id, virtual_account_name]
  }
}
