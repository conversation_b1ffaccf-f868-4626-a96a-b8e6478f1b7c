view: virtual_account_payment_claim {
  sql_table_name: clean__remittance_money_in_service.virtual_account_payment_claim ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: claim_redeemed_transfer_transaction_id {
    type: string
    sql: ${TABLE}.claim_redeemed_transfer_transaction_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: failure_reason {
    type: string
    sql: ${TABLE}.failure_reason ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: recipient_customer_id {
    type: string
    sql: ${TABLE}.recipient_customer_id ;;
  }

  dimension: remittance_collection_virtual_account_payment_id {
    type: string
    sql: ${TABLE}.remittance_collection_virtual_account_payment_id ;;
  }

  dimension: source_of_fund {
    type: string
    sql: ${TABLE}.source_of_fund ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: transfer_purpose {
    type: string
    sql: ${TABLE}.transfer_purpose ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
