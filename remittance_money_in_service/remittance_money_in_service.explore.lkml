include: "/remittance_money_in_service/*.view.lkml"
include: "/businesses/*.view.lkml"
include: "/customer_object/*.view.lkml"


explore: rmi_virtual_account {
  group_label: "Remittance Money In Service"
  label: "Virtual Account"
  from: virtual_account

  join: businesses {
    sql_on: ${businesses.business_id} = ${rmi_virtual_account.business_id} ;;
    relationship: one_to_many
    type :  inner
  }

  join: sender_customer {
    from: end_customer
    sql_on: ${sender_customer.end_customer_id} = ${rmi_virtual_account.sender_customer_id} ;;
    relationship: one_to_many
    type :  inner
  }

  join: virtual_account_payment {
    sql_on: ${rmi_virtual_account.id} = ${virtual_account_payment.remittance_collection_virtual_account_id} ;;
    relationship: one_to_many
    type :  inner
  }

  join: virtual_account_payment_claim {
    sql_on: ${virtual_account_payment.id} = ${virtual_account_payment_claim.remittance_collection_virtual_account_payment_id} ;;
    relationship: one_to_many
    type :  inner
  }

  join: virtual_account_payment_refund {
    sql_on: ${virtual_account_payment.id} = ${virtual_account_payment_refund.remittance_collection_virtual_account_payment_id} ;;
    relationship: one_to_many
    type :  inner
  }
}

explore: rmi_virtual_account_payment {
  group_label: "Remittance Money In Service"
  label: "Virtual Account Payment"
  from: virtual_account_payment
}


explore: rmi_virtual_account_payment_claim {
  group_label: "Remittance Money In Service"
  label: "Virtual Account Payment Claim"
  from: virtual_account_payment_claim
}

explore: rmi_virtual_account_payment_refund{
  group_label: "Remittance Money In Service"
  label: "Virtual Account Payment Refund"
  from: virtual_account_payment_refund
}
