view: virtual_account_payment_refund {
  sql_table_name: clean__remittance_money_in_service.virtual_account_payment_refund ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: account_holder_name {
    type: string
    sql: ${TABLE}.account_holder_name ;;
  }

  dimension: account_number {
    type: string
    sql: ${TABLE}.account_number ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: disbursement_id {
    type: string
    sql: ${TABLE}.disbursement_id ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: failure_reason {
    type: string
    sql: ${TABLE}.failure_reason ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: refund_holding_withdrawal_transaction_id {
    type: string
    sql: ${TABLE}.refund_holding_withdrawal_transaction_id ;;
  }

  dimension: remittance_collection_virtual_account_payment_id {
    type: string
    sql: ${TABLE}.remittance_collection_virtual_account_payment_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id, account_holder_name]
  }
}
