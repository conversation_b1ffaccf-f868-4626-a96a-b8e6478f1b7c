view: virtual_account_payment {
  sql_table_name: clean__remittance_money_in_service.virtual_account_payment ;;
  drill_fields: [id]
  suggestions: no

  dimension: virtual_account_payment_id {
    type: string
    sql: ${TABLE}.virtual_account_payment_id ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: claimed_amount {
    type: number
    sql: ${TABLE}.claimed_amount ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: payment_holding_deposit_transaction_id {
    type: string
    sql: ${TABLE}.payment_holding_deposit_transaction_id ;;
  }

  dimension: remittance_collection_virtual_account_id {
    type: string
    sql: ${TABLE}.remittance_collection_virtual_account_id ;;
  }

  dimension: risk_score {
    type: string
    sql: ${TABLE}.risk_score ;;
  }

  dimension: risk_score_id {
    type: string
    sql: ${TABLE}.risk_score_id ;;
  }

  dimension: risk_score_reason {
    type: string
    sql: ${TABLE}.risk_score_reason ;;
  }

  dimension: sender_customer_id {
    type: string
    sql: ${TABLE}.sender_customer_id ;;
  }

  dimension: sender_name {
    type: string
    sql: ${TABLE}.sender_name ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: transaction_recorded_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.transaction_recorded_timestamp ;;
  }

  dimension_group: transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.transaction_timestamp ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: virtual_account_number {
    type: string
    sql: ${TABLE}.virtual_account_number ;;
  }

  measure: count {
    type: count
    drill_fields: [virtual_account_payment_id, sender_name]
  }
}
