include: "/businesses/businesses.view.lkml"

view: businesses_for_revenue {
  extends: [businesses]

  dimension: authorised_signatory_email {
    type: string
    sql: ${TABLE}.authorised_signatory_email ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_identification_file_id {
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    type: string
    sql: ${TABLE}.authorised_signatory_identification_file_id ;;
    hidden: yes
  }

  dimension: authorised_signatory_identification_file_original_name {
    type: string
    sql: ${TABLE}.authorised_signatory_identification_file_original_name ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_identification_number {
    type: string
    sql: ${TABLE}.authorised_signatory_identification_number ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_name {
    type: string
    sql: ${TABLE}.authorised_signatory_name ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    case_sensitive: no
    hidden: yes
  }

  dimension: authorised_signatory_npwp_file_id {
    type: string
    sql: ${TABLE}.authorised_signatory_npwp_file_id ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_npwp_file_original_name {
    type: string
    sql: ${TABLE}.authorised_signatory_npwp_file_original_name ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_npwp_number {
    type: string
    sql: ${TABLE}.authorised_signatory_npwp_number ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: authorised_signatory_phone_number {
    type: string
    sql: ${TABLE}.authorised_signatory_phone_number ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    tags: ["phone"]
    hidden: yes
  }

  dimension: authorised_signatory_title {
    type: string
    sql: ${TABLE}.authorised_signatory_title ;;
    view_label: "Businesses - KYC documents"
    group_label: "Authorised Signatory"
    hidden: yes
  }

  dimension: bank_account_file_id {
    type: string
    sql: ${TABLE}.bank_account_file_id ;;
    view_label: "Businesses - KYC documents"
    group_label: "Bank Details"
    hidden: yes
  }

  dimension: bank_account_file_original_name {
    type: string
    sql: ${TABLE}.bank_account_file_original_name ;;
    view_label: "Businesses - KYC documents"
    group_label: "Bank Details"
    hidden: yes
  }

  dimension: bank_account_name {
    type: string
    sql: ${TABLE}.bank_account_name ;;
    view_label: "Businesses - KYC documents"
    group_label: "Bank Details"
    hidden: yes
  }

  dimension: bank_account_number {
    type: string
    sql: ${TABLE}.bank_account_number ;;
    view_label: "Businesses - KYC documents"
    group_label: "Bank Details"
    hidden: yes
  }

  dimension: bank_name {
    type: string
    sql: coalesce(${TABLE}.bank_name_ccd, ${TABLE}.bank_name_biz) ;;
    view_label: "Businesses - KYC documents"
    group_label: "Bank Details"
    description: "The bank name a business registered during the onboarding process"
    hidden: yes
  }

  dimension: business_email {
    group_label: "Business Details"
    sql: ${TABLE}.business_email ;;
    description: "The email used for registration"
    drill_fields: [business_detail*]
    hidden: yes
  }

  dimension: has_verified_email {
    description: "Whether a customer has at least one verified user email"
    type: yesno
    sql: ${TABLE}.has_verified_email ;;
    hidden: yes
  }

  dimension: number_of_users {
    description: "How many users(user_ids) a business_id has"
    type: number
    sql: ${TABLE}.number_of_users_per_business_id ;;
    hidden: yes
  }

  dimension: address {
    group_label: "Business Details"
    description: "Geographical address of business"
  }

  dimension: business_due_diligence_pic {
    group_label: "Due Diligence"
    description: "A person who made the business pass business(sales) due diligence"
    hidden: yes
  }

  dimension: mbo_due_diligence_pic {
    group_label: "Due Diligence"
    description: "A person who made the business pass MBO due diligence"
    hidden: yes
  }

  dimension: generated_api_key_in_dev_mode {
    type: yesno
    sql: ${TABLE}.first_api_key_generation_in_dev_mode is not null ;;
    description: "Whether a business generated API key in development mode"
    group_label: "Go Live Status"
    hidden: yes
  }

  dimension: pic_email {
    type: string
    sql: ${TABLE}.pic_email ;;
    description: "The email of business's PIC for us"
    group_label: "Business PIC"
    hidden: yes
  }

  dimension: pic_name {
    type: string
    sql: ${TABLE}.pic_name ;;
    description: "The name of business's PIC for us"
    group_label: "Business PIC"
    case_sensitive: no
    hidden: yes
  }

  dimension: pic_title {
    type: string
    sql: ${TABLE}.pic_title ;;
    group_label: "Business PIC"
    description: "The title of business's PIC for us"
    hidden: yes
  }

  dimension: pic_phone_number {
    type: string
    sql: ${TABLE}.pic_phone_number ;;
    group_label: "Business PIC"
    description: "PIC's phone number"
    hidden: yes
  }

  dimension: preferred_name {
    type: string
    sql: ${TABLE}.preferred_name ;;
    group_label: "Business Details"
    case_sensitive: no
    hidden: yes
  }

  dimension: qualification_score { hidden: yes }

  dimension: is_t4_migrated {
    description: "Whether the customer migrated to T4 or not"
    type: yesno
    sql: ${TABLE}.is_t4_migrated ;;
    hidden: yes
  }

  dimension_group: last_response {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.last_response;;
    hidden: yes
  }

  dimension_group: last_contact {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.last_contact;;
    hidden: yes
  }

  dimension: mbo_notes_latest {
    description: "The latest note that MBO left in Admin Dashboard"
    group_label: "MBO Information"
    sql:get_json_object(trim(both'[]'from mbo_notes_history),'$.notes');;
    hidden: yes
  }

  dimension: mbo_personnel_name {
    description: "The name of MBO who handled the business"
    group_label: "MBO Information"
    hidden: yes
  }

  dimension: total_kyc_doc_resubmission_request_count {
    view_label: "Businesses - Activation data on Business ID level"
    description: "The number of Xendit's KYC document(s) resubmission requests to the customer"
    type: number
    sql: ${TABLE}.total_kyc_doc_resubmission_request_count ;;
    hidden: yes
  }

  dimension: total_kyc_doc_resubmission_count {
    view_label: "Businesses - Activation data on Business ID level"
    description: "The number of document(s) resubmissions from the customer"
    type: number
    sql: ${TABLE}.total_kyc_doc_resubmission_count ;;
    hidden: yes
  }

  dimension: kyc_verification_status {
    group_label: "Go Live Status"
    sql: ${TABLE}.kyc_verification_status ;;
    drill_fields: [business_detail*]
    case_sensitive: no
    suggestions: ["AWAITING_DATA", "DATA_PROVIDED", "FAILED", "INVALID", "PASSED", "PENDING"]
    hidden: yes
  }

  dimension: go_live_reason {
    group_label: "Go Live Status"
    sql: ${TABLE}.go_live_reason ;;
    case_sensitive: no
    suggestions: [
      "BAU",
      "FAILED_KYC",
      "FOUND_ALTERNATIVE",
      "FRAUD",
      "FRAUD_ACCOUNT_TAKEOVER",
      "FRAUD_CREDIT_CARD",
      "FRAUD_EWALLET",
      "FRAUD_OTHERS",
      "FRAUD_PHISHING",
      "FRAUD_PROMO_ABUSE",
      "FRAUD_VIRTUAL_ACCOUNT",
      "INELIGIBLE",
      "LOST_INTEREST",
      "OTHER",
      "POST_INVESTIGATION",
      "UNRESPONSIVE"
    ]
    hidden: yes
  }

  dimension: kyc_verification_reason {
    group_label: "Go Live Status"
    sql: ${TABLE}.kyc_verification_reason ;;
    case_sensitive: no
    suggestions: ["BAD_DOCUMENT", "BLACKLIST", "BUSINESS_MODEL", "FAILED_KYC", "FAILED_RESUBMISSION", "FRAUD", "INACTIVE_WEBSITE", "OTHER", "SUSPECTED_FRAUD"]
    hidden: yes
  }

  dimension: go_live_notes {
    group_label: "Go Live Status"
    sql: ${TABLE}.go_live_notes ;;
    case_sensitive: no
    hidden: yes
  }

  dimension: kyc_verification_notes {
    group_label: "Go Live Status"
    sql: ${TABLE}.kyc_verification_notes ;;
    case_sensitive: no
    hidden: yes
  }

  dimension_group: first_kyc_doc_resubmission_request {
    view_label: "Businesses - Activation data on Business ID level"
    description: "Time when Xendit requested the customer to submit KYC document(s) for the first time"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.first_kyc_doc_resubmission_request ;;
    hidden: yes
  }

  dimension_group: first_kyc_doc_resubmission {
    view_label: "Businesses - Activation data on Business ID level"
    description: "Time when the customer re-submitted KYC document(s) for the first time upon Xendit's request"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.first_kyc_doc_resubmission ;;
    hidden: yes
  }

  dimension_group: last_kyc_doc_resubmission_request {
    view_label: "Businesses - Activation data on Business ID level"
    description: "Time when Xendit requested the customer to submit KYC document(s) lastly"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.last_kyc_doc_resubmission_request ;;
    hidden: yes
  }

  dimension_group: last_kyc_doc_resubmission {
    view_label: "Businesses - Activation data on Business ID level"
    description: "Time when the customer re-submitted KYC document(s) lastly upon Xendit's request"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    sql: ${TABLE}.last_kyc_doc_resubmission ;;
    hidden: yes
  }

  dimension: have_signed_contract {
    group_label: "Go Live Status"
    description: "An identifier on whether a business has signed contract"
    type: yesno
    sql: ${signed_contract_time} is not null ;;
    hidden: yes
  }

  dimension: instant_activation_status {
    description: "To show instant activation status"
    type: string
    sql: ${TABLE}.instant_activation_status;;
    suggestions: ["AWAITING_GOV_VALIDATION", "AWAITING_OCR_RESULT", "FAILED", "SUCCESS"]
  }

  dimension: withholding_period_end_reminder_sent {
    description: "On the date where the release period ends at 9 am JKT time, we should be able to notify the merchant via emails, telling them that they can ask to withdraw the money"
    type: yesno
    group_label: "Instant Activation"
    sql:  ${TABLE}.withholding_period_end_reminder_sent;;
    hidden: yes
  }

  dimension: instant_activation_declined_withhold_money_stopped {
    description: "To be able to cancel money holding period"
    type: yesno
    group_label: "Instant Activation"
    sql:  ${TABLE}.instant_activation_declined_withhold_money_stopped;;
    hidden: yes
  }

  dimension: instant_activation_attempts {
    description: "Stores how many times user has attempted to complete instant activation"
    type: number
    group_label: "Instant Activation"
    sql: ${TABLE}.instant_activation_attempts;;
    hidden: yes
  }

  dimension_group: instant_activation_declined_withhold_money_until {
    description: "To indicate that the merchant will be able to withhold money from that date."
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.instant_activation_declined_withhold_money_until;;
    hidden: yes
  }

  dimension_group: micro_ecommerce_lifted_at {
    description: "Micro ecommerce limits status updated time"
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.micro_ecommerce_lifted_at ;;
    hidden: yes
  }


  dimension_group: micro_ecommerce_applied_at {
    description: "Micro ecommerce applied status updated time"
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.micro_ecommerce_applied_at ;;
    hidden: yes
  }

  dimension:  consent_acceptance {
    description: "Consent acceptance"
    type: yesno
    sql: ${TABLE}.consent_acceptance;;
    hidden: yes
  }

  dimension:  service_agreement_type {
    description: "Service agreement type"
    type: string
    sql: ${TABLE}.service_agreement_type;;
    suggestions: ["ELECTRONIC_CONSENT", "PKS"]
    hidden: yes
  }

  dimension: account_manager_email {
    description: "Xendit email of business's account manager"
    group_label: "Sales Team Information"
    type: string
    sql: ${TABLE}.account_manager_email;;
    hidden: yes
  }

  dimension: last_contacted_pool_am {
    type: string
    group_label: "Sales Team Information"
    sql: ${TABLE}.last_contacted_pool_am ;;
    hidden: yes
  }

  dimension: account_manager_name {
    description: "Name of business's account manager"
    group_label: "Sales Team Information"
    type: string
    sql: ${TABLE}.account_manager_name ;;
    hidden: yes
  }
}
