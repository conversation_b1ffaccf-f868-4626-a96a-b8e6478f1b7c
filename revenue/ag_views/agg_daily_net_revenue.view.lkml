view: agg_daily_net_revenue {
  sql_table_name: clean__de_test.daily_net_revenue_first ;;
  suggestions: no

  dimension: account_relation_id {
    type: string
    sql: ${TABLE}.account_relation_id ;;
  }
  dimension: amortization_end_date {
    type: string
    sql: ${TABLE}.amortization_end_date ;;
  }
  dimension: amortization_id {
    type: string
    sql: ${TABLE}.amortization_id ;;
  }
  dimension: amortization_start_date {
    type: string
    sql: ${TABLE}.amortization_start_date ;;
  }
  dimension: bucket {
    type: string
    sql: ${TABLE}.bucket ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  dimension: channel_adjusted {
    type: string
    sql: ${TABLE}.channel_adjusted ;;
  }
  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }
  dimension: cogs_base_fee {
    type: number
    sql: ${TABLE}.cogs_base_fee ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_cost_structure {
    type: string
    sql: ${TABLE}.cogs_cost_structure ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_fixed_fee {
    type: number
    sql: ${TABLE}.cogs_fixed_fee ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_is_current_rate {
    type: yesno
    sql: ${TABLE}.cogs_is_current_rate ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_pct_cost {
    type: number
    sql: ${TABLE}.cogs_pct_cost ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension_group: cogs_rates_effective_end {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.cogs_rates_effective_end_date ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension_group: cogs_rates_effective_end_month {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.cogs_rates_effective_end_month ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension_group: cogs_rates_effective_start {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.cogs_rates_effective_start_date ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension_group: cogs_rates_effective_start_month {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.cogs_rates_effective_start_month ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_tier_1_cost {
    type: number
    sql: ${TABLE}.cogs_tier_1_cost ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_tier_1_end {
    type: number
    sql: ${TABLE}.cogs_tier_1_end ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_tier_2_cost {
    type: number
    sql: ${TABLE}.cogs_tier_2_cost ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_tier_2_end {
    type: number
    sql: ${TABLE}.cogs_tier_2_end ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: cogs_tier_3_cost {
    type: number
    sql: ${TABLE}.cogs_tier_3_cost ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: commercial_model {
    type: string
    sql: ${TABLE}.commercial_model ;;
  }
  dimension_group: created_utc {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.created_utc ;;
  }
  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }
  dimension: days_elapsed {
    type: string
    sql: ${TABLE}.days_elapsed ;;
  }
  dimension: deduction_type {
    type: string
    sql: ${TABLE}.deduction_type ;;
  }
  dimension_group: dt {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }
  dimension_group: dt_month {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.dt_month ;;
  }
  dimension: fees {
    type: number
    sql: ${TABLE}.fees ;;
  }
  dimension: integration_type {
    type: string
    sql: ${TABLE}.integration_type ;;
  }
  dimension: is_switcher {
    type: string
    sql: ${TABLE}.is_switcher ;;
  }
  dimension: is_tpi_transaction {
    type: string
    sql: ${TABLE}.is_tpi_transaction ;;
  }
  dimension: is_web_or_app {
    type: string
    sql: ${TABLE}.is_web_or_app ;;
  }
  dimension: loan_id {
    type: string
    sql: ${TABLE}.loan_id ;;
  }
  dimension: money_flow {
    type: string
    sql: ${TABLE}.money_flow ;;
  }
  dimension: net_revenue {
    type: number
    sql: ${TABLE}.net_revenue ;;
  }
  dimension: partner_name {
    type: string
    sql: ${TABLE}.partner_name ;;
  }
  dimension: platform_fees_pct {
    type: number
    sql: ${TABLE}.platform_fees_pct ;;
  }
  dimension: processing_platform {
    type: string
    sql: ${TABLE}.processing_platform ;;
  }
  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }
  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }
  dimension: routing {
    type: string
    sql: ${TABLE}.routing ;;
  }
  dimension: sender_bank_code {
    type: string
    sql: ${TABLE}.sender_bank_code ;;
  }
  dimension: sender_bank_code_adjusted {
    type: string
    sql: ${TABLE}.sender_bank_code_adjusted ;;
  }
  dimension: statement_generation_type {
    type: string
    sql: ${TABLE}.statement_generation_type ;;
  }
  dimension: tier {
    type: string
    sql: ${TABLE}.tier ;;
  }
  dimension: tier_cost {
    type: yesno
    sql: ${TABLE}.tier_cost ;;
  }
  dimension: total_base_cost {
    type: number
    sql: ${TABLE}.total_base_cost ;;
  }
  dimension: total_cost {
    type: number
    sql: ${TABLE}.total_cost ;;
  }
  dimension: total_pct_cost {
    type: number
    sql: ${TABLE}.total_pct_cost ;;
  }
  dimension: total_platform_cost {
    type: number
    sql: ${TABLE}.total_platform_cost ;;
  }
  dimension: tpi_platform_name {
    type: string
    sql: ${TABLE}.tpi_platform_name ;;
  }
  dimension: transaction_amount {
    type: number
    sql: ${TABLE}.transaction_amount ;;
  }
  dimension: transaction_count {
    type: number
    sql: ${TABLE}.transaction_count ;;
  }
  dimension: transaction_type_cogs {
    type: string
    sql: ${TABLE}.transaction_type_cogs ;;
    required_access_grants: [global__can_view_cogs]
  }
  dimension: xendit_entity {
    type: string
    sql: ${TABLE}.xendit_entity ;;
  }
  dimension: xendit_rate_final {
    type: number
    sql: ${TABLE}.xendit_rate_final ;;
  }
  measure: count {
    type: count
    drill_fields: [channel_name, tpi_platform_name, partner_name]
  }
}
