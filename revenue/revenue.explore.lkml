include: "//central-models-dbr/revenue/revenue.explore"
include: "//central-models-dbr/revenue/_revenue_dqc_explores/revenue_test_explores.explore"
include: "/growth/sales_attribution.view.lkml"
include: "/salesforce/ndt_salesforce_account.view.lkml"

explore: +revenue {
################################################
# Aggregate tables {
################################################
aggregate_table: _rollup_sales_by_day  {
  materialization: {
    sql_trigger_value: select count(1) from ${daily_revenue.SQL_TABLE_NAME} ;;
  }
  query: {
    dimensions: [
      revenue.created_utc_date, revenue.created_utc_month, revenue.created_local_month,
      revenue.product_type,
      sales_attribution.sf_rollup_ultimate_parent_brand, sales_attribution.sf_rollup_ultimate_parent_id, sales_attribution.sf_rollup_owner_email, sales_attribution.sales_team,
      businesses.is_internal, businesses.is_soft_deleted_account
    ]
    measures: [revenue.revenue_usd, revenue.take_rate]
    filters: []
    timezone: Asia/Jakarta
  }
}
################################################
# Aggregate tables }
################################################
join: sales_attribution {
  view_label: "Sales Attribution Live"
  sql_on: ${businesses.business_id} = ${sales_attribution.business_id};;
  type: left_outer
  relationship: one_to_one
}

join: ndt_salesforce_account {
  view_label: "Salesforce Record"
  sql_on: ${businesses.business_id} = ${ndt_salesforce_account.business_id};;
  type: left_outer
  relationship: one_to_one
}
}

explore: +revenue_rework {
################################################
# Aggregate tables {
################################################
  hidden: yes
  label: "Revenue (Rework)"
  aggregate_table: _rollup_sales_by_day  {
    materialization: {
      sql_trigger_value: select count(1) from ${daily_revenue_rework.SQL_TABLE_NAME} ;;
    }
    query: {
      dimensions: [
        revenue_rework.created_utc_date, revenue_rework.created_utc_month, revenue_rework.created_local_month,
        revenue_rework.product_type,
        sales_attribution.sf_rollup_ultimate_parent_brand, sales_attribution.sf_rollup_ultimate_parent_id, sales_attribution.sf_rollup_owner_email, sales_attribution.sales_team,
        businesses.is_internal, businesses.is_soft_deleted_account
      ]
      measures: [revenue_rework.revenue_usd, revenue_rework.take_rate]
      filters: []
      timezone: Asia/Jakarta
    }
  }
################################################
# Aggregate tables }
################################################
  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${businesses.business_id} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: one_to_one
  }

  join: ndt_salesforce_account {
    view_label: "Salesforce Record"
    sql_on: ${businesses.business_id} = ${ndt_salesforce_account.business_id};;
    type: left_outer
    relationship: one_to_one
  }

}

explore: +net_revenue {
################################################
# Aggregate tables {
################################################
  hidden: yes
  label: "Net Revenue (Dev)"
  aggregate_table: _rollup_sales_by_day  {
    materialization: {
      sql_trigger_value: select count(1) from ${daily_net_revenue.SQL_TABLE_NAME} ;;
    }
    query: {
      dimensions: [
        net_revenue.created_utc_date, net_revenue.created_utc_month, net_revenue.created_local_month,
        net_revenue.product_type,
        sales_attribution.sf_rollup_ultimate_parent_brand, sales_attribution.sf_rollup_ultimate_parent_id, sales_attribution.sf_rollup_owner_email, sales_attribution.sales_team,
        businesses.is_internal, businesses.is_soft_deleted_account
      ]
      measures: [net_revenue.revenue_usd, net_revenue.net_revenue_usd, net_revenue.take_rate]
      filters: []
      timezone: Asia/Jakarta
    }
  }
################################################
# Aggregate tables }
################################################
  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${businesses.business_id} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: one_to_one
  }

  join: ndt_salesforce_account {
    view_label: "Salesforce Record"
    sql_on: ${businesses.business_id} = ${ndt_salesforce_account.business_id};;
    type: left_outer
    relationship: one_to_one
  }

}
