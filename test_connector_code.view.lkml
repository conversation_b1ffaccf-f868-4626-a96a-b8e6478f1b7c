
view: test_connector_code {
  derived_table: {
    sql: select t.id,coalesce(json_extract(te.payment_snapshot,'$.connector_code'),json_extract(te.payment_snapshot,'$.connector_code')) as connector_code from hive.clean__transaction_service_v4.transaction t
      inner join hive.clean__transaction_service_v4.transaction_event te
      on t.id = te.transaction_id
      where t.id in 
      ('6a8061c2-8d86-4cf2-935d-d35e71da6d34') ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: connector_code {
    type: string
    sql: ${TABLE}.connector_code ;;
  }

  set: detail {
    fields: [
        id,
	connector_code
    ]
  }
}
