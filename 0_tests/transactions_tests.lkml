include: "/transactions/transactions.explore"

################################################
# P0: Unique and Not Null {
################################################

test: pk_is_unique_transactions {
  explore_source: transaction_backbone {
    column: reference {}
    column: count {}
    sorts: [transaction_backbone.count: desc]
    limit: 1
  }
  assert: transaction_reference_is_unique {
    expression: ${transaction_backbone.count} = 1 ;;
  }
}

test: pk_is_not_null_transactions {
  explore_source: transaction_backbone {
    column: reference {}
    sorts: [transaction_backbone.reference: desc]
    limit: 1
  }
  assert: transaction_reference_is_not_null {
    expression: NOT is_null(${transaction_backbone.reference}) ;;
  }
}

################################################
# P0: Unique and Not Null }
################################################

################################################
# P1: Joins Tests {
################################################

test: joins_are_one_to_one_transactions {
  explore_source: transaction_backbone {
    column: transaction_backbone_count {field:transaction_backbone.count}
    column: revenue_view_count {field: revenue.count}
  }
  assert: txn_backbone_revenue_counts_are_equal {
    expression:  ${transaction_backbone.count} >= ${revenue.count} ;;
  }
}

################################################
# P1: Joins Tests }
################################################

################################################
# P2: Transaction Counts by Year {
################################################

# The following should be moved to DBT and are hence commented out here:
# NB: Round TPV to nearest 10 USD to account for floating point precision differences

# test: historic_txns_is_accurate_2022 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2022"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression: ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(***********.09/10) ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = ********* ;;
#   }
# }

# test: historic_txns_is_accurate_2021 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2021"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression:ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(***********.09/10) ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = ********* ;;
#   }
# }

# test: historic_txns_is_accurate_2020 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2020"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression: ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(**********.33/10)  ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = ******** ;;
#   }
# }

# test: historic_txns_is_accurate_2019 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2019"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression: ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(**********.84/10) ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = ******** ;;
#   }
# }

# test: historic_txns_is_accurate_2018 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2018"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression: ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(**********.51/10) ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = ******** ;;
#   }
# }

# test: historic_txns_is_accurate_2017 {
#   explore_source: transaction_backbone {
#     column: tpv_usd {}
#     column: count {}
#     filters: [
#       businesses.is_internal: "",
#       businesses.is_soft_deleted_account: "",
#       transaction_backbone.status: "COMPLETED",
#       transaction_backbone.date_partition: "2017"
#     ]
#     limit: 1
#   }
#   assert: tpv_is_expected {
#     expression: ceiling(${transaction_backbone.tpv_usd}/10) = ceiling(*********.91/10) ;;
#   }
#   assert: transaction_count_is_expected {
#     expression: ${transaction_backbone.count} = 1413189 ;;
#   }
# }

################################################
# P2: Transaction Counts by Year }
################################################

################################################
# P3: View Specific Tests {
################################################

test: columns_values_are_as_expected_transactions {
  explore_source: transaction_backbone {
    column: total_uncategorised_product_type_count {}
    column: total_uncategorised_money_flow_count {}
    column: total_currencies_used {}
    filters: [
      businesses.is_internal: "",
      businesses.is_soft_deleted_account: "",
    ]
    limit: 1
  }
  assert: product_type_is_not_uncategorised {
    expression: ${transaction_backbone.total_uncategorised_product_type_count} = 0;;
  }
  # assert: money_flow_is_not_uncatagorised {
  #   expression: ${transaction_backbone.total_uncategorised_money_flow_count} = 1;;
  # }
  # the result return 7
  # assert: txns_only_have_old_currencies {
  #   expression: ${transaction_backbone.total_currencies_used} = 8;;
  # }
}


################################################
# P3: View Specific Tests }
################################################
