view: growth_metrics_xp_master {

  sql_table_name:
      {% if period_unit._parameter_value == '_28days' %} ${xp_retention_analysis_28d_backbone.SQL_TABLE_NAME}
      {% elsif period_unit._parameter_value == 'month' %} ${xp_retention_analysis_monthly_backbone.SQL_TABLE_NAME}
      {% elsif period_unit._parameter_value == 'week' %} ${xp_retention_analysis_weekly_backbone.SQL_TABLE_NAME}
      {% else %} ${xp_retention_analysis_quarterly_backbone.SQL_TABLE_NAME}
      {% endif %} ;;



  parameter: period_unit {
    description: "Select period window for retention analysis"
    type: unquoted
    default_value: "_28days"
    allowed_value: {
      label: "Weekly Window"
      value: "week"
    }
    allowed_value: {
      label: "28 Days Window"
      value: "_28days"
    }
    allowed_value: {
      label: "Monthly Window"
      value: "month"
    }
    allowed_value: {
      label: "Quarterly Window"
      value: "quarter"
    }
  }

  dimension: compound_primary_key {
    primary_key: yes
    hidden: yes
    type: string
    sql: CONCAT(${TABLE}.xp_master_account, ' ', cast(${TABLE}.transaction_period_index_asc as varchar)) ;;
  }

  dimension: xp_master_account {
    type: string
    sql: ${TABLE}.xp_master_account ;;
  }

  dimension: transaction_period_started {
    type: date
    sql: ${TABLE}.transaction_period_started ;;
    allow_fill: no
    convert_tz: no
  }

  dimension: how_many_periods_ago {
    hidden: yes
    type: number
    sql: ${TABLE}.how_many_periods_ago ;;
  }

  dimension: active_status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: is_active {
    type: yesno
    sql: ${TABLE}.status IN ('New Active', 'Retained Active', 'Re-activated') ;;
  }

  dimension: transaction_period_index_asc {
    type: number
    sql: ${TABLE}.transaction_period_index_asc ;;
  }

  dimension: transaction_period_index_desc {
    type: number
    sql: ${TABLE}.transaction_period_index_desc ;;
  }

  dimension: product_types_used {
    type: string
    sql: ${TABLE}.product_types_used ;;
  }

  dimension: the_number_of_products_used {
    type: number
    sql: ${TABLE}.the_number_of_products_used ;;
  }

  dimension: the_number_of_active_subaccounts {
    type: number
    sql: ${TABLE}.the_number_of_active_subaccounts ;;
  }

  dimension: tpv_usd {
    type: number
    sql: ${TABLE}.tpv_usd ;;
  }

  dimension: transaction_count {
    type: number
    sql: ${TABLE}.transaction_count ;;
  }

  dimension_group: first_transaction_timestamp_in_the_period {
    type: time
    timeframes: [time, date, day_of_month]
    sql: ${TABLE}.first_transaction_timestamp_in_the_period ;;
    convert_tz: no
  }

  measure: sum_of_tpv_usd {
    description: "Actual TPV from the transaction period. Use it with Active Status"
    value_format_name: "usd"
    type: sum
    sql: ${TABLE}.tpv_usd ;;
    drill_fields: [xp_master_account, businesses.internal_name, businesses.business_name, tpv_usd]
  }

  measure: sum_of_transaction_count {
    type: sum
    sql: ${TABLE}.transaction_count ;;
    drill_fields: [xp_master_account, businesses.internal_name, businesses.business_name, transaction_count]
  }

  measure: sum_of_active_subaccounts {
    type: sum
    sql: ${TABLE}.the_number_of_active_subaccounts ;;
    drill_fields: [xp_master_account, businesses.internal_name, businesses.business_name, the_number_of_active_subaccounts]
  }

  measure: count {
    label: "Business Count"
    type: count_distinct
    sql: ${TABLE}.xp_master_account ;;
    drill_fields: [xp_master_account, businesses.internal_name, businesses.business_name]
  }

  measure: retained_active_business_count {
    type: count_distinct
    sql: CASE WHEN  ${TABLE}.status = 'Retained Active' THEN ${TABLE}.xp_master_account ELSE NULL END;;
    hidden: yes
  }

  measure: churned_business_count {
    type: count_distinct
    sql: CASE WHEN ${TABLE}.status = 'Churned' THEN ${TABLE}.xp_master_account ELSE NULL END;;
    hidden: yes
  }

  # measure: monthly_active_customer_retention_rate {
  #   description: "Ratio of previous month active Business IDs retained to current month"
  #   type: number
  #   value_format_name: percent_2
  #   sql: cast(${retained_active_business_count} as double)/(${retained_active_business_count} + ${churned_business_count}) ;;
  # }
}
