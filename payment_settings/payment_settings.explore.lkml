include: "/payment_settings/*.view.lkml"
include: "/businesses/*.view.lkml"


explore: merchants_explore {
  group_label: "Payment Settings"
  label: "Merchants"
  from: merchants

  join: businesses {
    sql_on: ${businesses.business_id} = ${merchants_explore.business_id} ;;
    relationship: one_to_many
    type :  inner
  }
}

explore: merchant_routing_rules_explore {
  group_label: "Payment Settings"
  label: "Merchant Routing Rules"
  from: merchant_routing_rules
  join: connections {
    sql_on: ${merchant_routing_rules_explore.connection_id} = ${connections.id} ;;
    relationship: one_to_one
    type: inner
  }
}

explore: connection_explore {
  group_label: "Payment Settings"
  label: "Connections"
  from: connections
}

explore: channel_config_explore {
  group_label: "Payment Settings"
  label: "Channel Configs"
  from: channel_configs
}
