view: channel_configs {
  sql_table_name: clean__payment_settings.channel_configs ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: activation_fields {
    type: string
    sql: ${TABLE}.activation_fields ;;
  }
  dimension: activation_policy {
    type: string
    sql: ${TABLE}.activation_policy ;;
  }
  dimension: brands {
    type: string
    sql: ${TABLE}.brands ;;
  }
  dimension: cards {
    type: string
    sql: ${TABLE}.cards ;;
  }
  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }
  dimension: connection_alias {
    type: string
    sql: ${TABLE}.connection_alias ;;
  }
  dimension: connection_template_id {
    type: string
    sql: ${TABLE}.connection_template_id ;;
  }
  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }
  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }
  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }
  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }
  dimension: direct_debit {
    type: string
    sql: ${TABLE}.direct_debit ;;
  }
  dimension: ewallet {
    type: string
    sql: ${TABLE}.ewallet ;;
  }
  dimension: instant_activation_restrictions {
    type: string
    sql: ${TABLE}.instant_activation_restrictions ;;
  }
  dimension: max_amount {
    type: number
    sql: ${TABLE}.max_amount ;;
  }
  dimension: merchant_template_id {
    type: string
    sql: ${TABLE}.merchant_template_id ;;
  }
  dimension: min_amount {
    type: number
    sql: ${TABLE}.min_amount ;;
  }
  dimension: partner_name {
    type: string
    sql: ${TABLE}.partner_name ;;
  }
  dimension: payment_acceptance_model {
    type: string
    sql: ${TABLE}.payment_acceptance_model ;;
  }
  dimension: pm_type {
    type: string
    sql: ${TABLE}.pm_type ;;
  }
  dimension: qr_code {
    type: string
    sql: ${TABLE}.qr_code ;;
  }
  dimension: settlement_detail {
    type: string
    sql: ${TABLE}.settlement_detail ;;
  }
  dimension: settlement_time {
    type: number
    sql: ${TABLE}.settlement_time ;;
  }
  dimension: sharing_policy {
    type: string
    sql: ${TABLE}.sharing_policy ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: transacting_entity {
    type: string
    sql: ${TABLE}.transacting_entity ;;
  }
  dimension: update_id {
    type: string
    sql: ${TABLE}.update_id ;;
  }
  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  dimension: virtual_account {
    type: string
    sql: ${TABLE}.virtual_account ;;
  }
  measure: count {
    type: count
    drill_fields: [id, partner_name]
  }
}
