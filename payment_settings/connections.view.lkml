view: connections {
  sql_table_name: clean__payment_settings.connections ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: access_code {
    type: string
    sql: ${TABLE}.access_code ;;
  }
  dimension: acquiring_bank_mid {
    type: string
    sql: ${TABLE}.acquiring_bank_mid ;;
  }
  dimension: acquiring_bank_name {
    type: string
    sql: ${TABLE}.acquiring_bank_name ;;
  }
  dimension: aggregator_id {
    type: string
    sql: ${TABLE}.aggregator_id ;;
  }
  dimension: aggregator_name {
    type: string
    sql: ${TABLE}.aggregator_name ;;
  }
  dimension: alias {
    type: string
    sql: ${TABLE}.alias ;;
  }
  dimension: base_url {
    type: string
    sql: ${TABLE}.base_url ;;
  }
  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }
  dimension: merchant_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.merchant_id ;;
  }
  dimension: operator_id {
    type: string
    sql: ${TABLE}.operator_id ;;
  }
  dimension: partner_name {
    type: string
    sql: ${TABLE}.partner_name ;;
  }
  dimension_group: reachable {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.reachable_at ;;
  }
  dimension: sales_org_id {
    type: string
    sql: ${TABLE}.sales_org_id ;;
  }
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
	id,
	partner_name,
	acquiring_bank_name,
	aggregator_name,
	merchants.id,
	merchant_routing_rules.count
	]
  }

}
