view: merchants {
  sql_table_name: clean__payment_settings.merchants ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: business_name {
    type: string
    sql: ${TABLE}.business_name ;;
  }

  dimension: industry_sector {
    type: string
    sql: ${TABLE}.industry_sector ;;
  }

  dimension: mcc {
    type: string
    sql: ${TABLE}.mcc ;;
  }

  dimension: signing_entity {
    type: string
    sql: ${TABLE}.signing_entity ;;
  }

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }

  dimension: card_status {
    type: string
    sql: ${TABLE}.cards.status ;;
  }

  dimension: card_mcc {
    type: string
    sql: ${TABLE}.cards.mcc ;;
  }

  dimension: card_industry_sector {
    type: string
    sql: ${TABLE}.cards.industry_sector ;;
  }

  dimension_group: card_created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.cards.created_at ;;
  }

  dimension_group: card_updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.cards.updated_at ;;
  }

  dimension: card_features {
    type: string
    sql: ${TABLE}.cards.features ;;
  }

  dimension: card_api_version {
    type: string
    sql: ${TABLE}.cards.api_version ;;
  }

  dimension: card_pci_compliance_level {
    type: string
    sql: ${TABLE}.cards.pci_compliance_level ;;
  }

  dimension: card_integration_model {
    type: string
    sql: ${TABLE}.cards.integration_model ;;
  }

  dimension: card_allowed_card_types {
    type: string
    sql: ${TABLE}.cards.allowed_card_types ;;
  }

  dimension: card_allowed_card_brands {
    type: string
    sql: ${TABLE}.cards.allowed_card_brands ;;
  }

  dimension: card_authentication_policy {
    type: string
    sql: ${TABLE}.cards.authentication_policy ;;
  }

  dimension: card_fds_policy {
    type: string
    sql: ${TABLE}.cards.fds_policy ;;
  }

  dimension: card_avs_policy {
    type: string
    sql: ${TABLE}.cards.avs_policy ;;
  }

  dimension: card_dynamic_3ds_status {
    type: string
    sql: ${TABLE}.cards.dynamic_3ds_status ;;
  }

  dimension: card_virtual_terminal_status {
    type: string
    sql: ${TABLE}.cards.virtual_terminal_status ;;
  }

  dimension: card_flow {
    type: string
    sql: ${TABLE}.cards.flow ;;
  }

  dimension: card_redirect_flow_callback_url {
    type: string
    sql: ${TABLE}.cards.redirect_flow_callback_url ;;
  }

  measure: count {
    type: count
  }
}
