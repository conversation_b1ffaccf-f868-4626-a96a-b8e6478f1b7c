view: merchant_routing_rules {
  sql_table_name: clean__payment_settings.merchant_routing_rules ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension_group: activated_at {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.activated_at ;;
  }
  dimension: active {
    type: yesno
    sql: ${TABLE}.active ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  dimension: cards {
    type: string
    sql: ${TABLE}.cards ;;
  }
  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }
  dimension: connection_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.connection_id ;;
  }
  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }
  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }
  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }
  dimension_group: deactivated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.deactivated_at ;;
  }
  dimension: direct_debit {
    type: string
    sql: ${TABLE}.direct_debit ;;
  }
  dimension: ewallet {
    type: string
    sql: ${TABLE}.ewallet ;;
  }
  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }
  dimension: external_source {
    type: string
    sql: ${TABLE}.external_source ;;
  }
  dimension: kind {
    type: string
    sql: ${TABLE}.kind ;;
  }
  dimension: migrated {
    type: yesno
    sql: ${TABLE}.migrated ;;
  }
  dimension: original_id {
    type: string
    sql: ${TABLE}.original_id ;;
  }
  dimension: payment_acceptance_model {
    type: string
    sql: ${TABLE}.payment_acceptance_model ;;
  }
  dimension: pm_type {
    type: string
    sql: ${TABLE}.pm_type ;;
  }
  dimension: qr_code {
    type: string
    sql: ${TABLE}.qr_code ;;
  }
  dimension: record_version {
    type: number
    sql: ${TABLE}.record_version ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: transacting_entity {
    type: string
    sql: ${TABLE}.transacting_entity ;;
  }
  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }
  dimension: card_payment_mode {
    type: string
    sql: ${TABLE}.cards.payment_mode ;;
  }

  dimension: card_priority {
    type: number
    sql: ${TABLE}.cards.priority ;;
  }

  dimension: card_mid_label {
    type: string
    sql: ${TABLE}.cards.mid_label ;;
  }

  dimension: card_features {
    type: string
    sql: ${TABLE}.cards.features ;;
  }

  dimension: card_supported_issuers {
    type: string
    sql: ${TABLE}.cards.supported_issuers ;;
  }

  dimension: card_supported_mcc {
    type: string
    sql: ${TABLE}.cards.supported_mcc ;;
  }

  dimension: card_supported_card_types {
    type: string
    sql: ${TABLE}.cards.supported_card_types ;;
  }

  dimension: card_supported_card_brands {
    type: string
    sql: ${TABLE}.cards.supported_card_brands ;;
  }

  dimension: card_supported_card_bins {
    type: string
    sql: ${TABLE}.cards.supported_card_bins ;;
  }

  dimension: card_cof_types {
    type: string
    sql: ${TABLE}.cards.cof_types ;;
  }

  dimension: card_transaction_mode {
    type: string
    sql: ${TABLE}.cards.transaction_mode ;;
  }

  dimension: card_email {
    type: string
    sql: ${TABLE}.cards.email ;;
  }

  dimension: card_secret_key {
    type: string
    sql: ${TABLE}.cards.secret_key ;;
  }

  dimension: card_aggregator_id {
    type: string
    sql: ${TABLE}.cards.aggregator_id ;;
  }

  dimension: card_sales_org_id {
    type: string
    sql: ${TABLE}.cards.sales_org_id ;;
  }

  dimension: card_fds_site_id {
    type: string
    sql: ${TABLE}.cards.fds_site_id ;;
  }

  dimension: card_fds_assessment_policy {
    type: string
    sql: ${TABLE}.cards.fds_assessment_policy ;;
  }

  dimension: card_fds_steps {
    type: string
    sql: ${TABLE}.cards.fds_steps ;;
  }

  dimension: card_merchant_id {
    type: string
    sql: ${TABLE}.cards.merchant_id ;;
  }

  dimension: card_merchant_key {
    type: string
    sql: ${TABLE}.cards.merchant_key ;;
  }

  dimension: card_acceptor_key {
    type: string
    sql: ${TABLE}.cards.acceptor_key ;;
  }

  dimension: card_enrollment_key {
    type: string
    sql: ${TABLE}.cards.enrollment_key ;;
  }

  dimension: card_external_shop_id {
    type: string
    sql: ${TABLE}.cards.external_shop_id ;;
  }

  dimension: card_payment_term {
    type: string
    sql: ${TABLE}.cards.payment_term ;;
  }

  measure: count {
    type: count
    drill_fields: [id, connections.id, connections.partner_name, connections.acquiring_bank_name, connections.aggregator_name]
  }
}
