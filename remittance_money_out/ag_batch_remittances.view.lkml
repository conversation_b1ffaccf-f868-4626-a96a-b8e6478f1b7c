view: ag_batch_remittances {
  sql_table_name: clean__batch_remittance_service_live.batch_remittances ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension_group: approved {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.approved_at ;;
  }

  dimension: approver_id {
    type: string
    sql: ${TABLE}.approver_id ;;
  }

  dimension: approver_name {
    type: string
    sql: ${TABLE}.approver_name ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: completed_items_amount {
    type: number
    sql: ${TABLE}.completed_items_amount ;;
  }

  dimension: completed_items_count {
    type: number
    sql: ${TABLE}.completed_items_count ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: deleted_items_amount {
    type: number
    sql: ${TABLE}.deleted_items_amount ;;
  }

  dimension: deleted_items_count {
    type: number
    sql: ${TABLE}.deleted_items_count ;;
  }

  dimension: excel_meta_data {
    type: string
    sql: ${TABLE}.excel_meta_data ;;
  }

  dimension: failed_items_amount {
    type: number
    sql: ${TABLE}.failed_items_amount ;;
  }

  dimension: failed_items_count {
    type: number
    sql: ${TABLE}.failed_items_count ;;
  }

  dimension: file_id {
    type: string
    sql: ${TABLE}.file_id ;;
  }

  dimension: is_reported {
    type: yesno
    sql: ${TABLE}.is_reported ;;
  }

  dimension: items_amount {
    type: number
    sql: ${TABLE}.items_amount ;;
  }

  dimension: items_count {
    type: number
    sql: ${TABLE}.items_count ;;
  }

  dimension: reference {
    type: string
    sql: ${TABLE}.reference ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: uploader_id {
    type: string
    sql: ${TABLE}.uploader_id ;;
  }

  dimension: uploader_name {
    type: string
    sql: ${TABLE}.uploader_name ;;
  }

  measure: count {
    type: count
    drill_fields: [id, uploader_name, approver_name, batch_remittance_item_inputs.count]
  }
}
