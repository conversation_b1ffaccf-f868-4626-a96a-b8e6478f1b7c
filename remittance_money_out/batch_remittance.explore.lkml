include: "/remittance_money_out/*.view.lkml"
include: "/businesses/*.view.lkml"

explore: batch_remittances {
  from: ag_batch_remittances
  group_label: "PG Transactions"
  label: "Batch Remittances"

  always_filter: {
    filters: [businesses.is_internal: "no", businesses.is_soft_deleted_account: "no"]
  }

  join: businesses {
    sql_on: ${businesses.business_id} = ${batch_remittances.business_id} ;;
    relationship: one_to_many
    type :  inner
  }

  join: batch_remittance_item_inputs {
    from: ag_batch_remittance_item_inputs
    sql_on: ${batch_remittance_item_inputs.batch_remittance_id} = ${batch_remittances.id} ;;
    relationship: many_to_one
    type: inner
  }

}
