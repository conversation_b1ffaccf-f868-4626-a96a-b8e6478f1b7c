view: remittance {
  sql_table_name: clean__remittance_money_out_service.remittance ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: assessment_results {
    type: string
    sql: ${TABLE}.assessment_results ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: destination_amount {
    type: number
    sql: ${TABLE}.destination_amount ;;
  }

  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: idempotency_key {
    type: string
    sql: ${TABLE}.idempotency_key ;;
  }

  dimension: ledger_id {
    type: string
    sql: ${TABLE}.ledger_id ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: origin_amount {
    type: number
    sql: ${TABLE}.origin_amount ;;
  }

  dimension: origin_currency {
    type: string
    sql: ${TABLE}.origin_currency ;;
  }

  dimension: purpose_code {
    type: string
    sql: ${TABLE}.purpose_code ;;
  }

  dimension: recipient_customer_id {
    type: string
    sql: ${TABLE}.recipient_customer_id ;;
  }

  dimension: recipient_customer_object {
    type: string
    sql: ${TABLE}.recipient_customer_object ;;
  }

  dimension: recipient_name {
    type: string
    sql: ${TABLE}.recipient_name ;;
  }

  dimension: recipient_type {
    type: string
    sql: ${TABLE}.recipient_type ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: relationship {
    type: string
    sql: ${TABLE}.relationship ;;
  }

  dimension: remittance_type {
    type: string
    sql: ${TABLE}.remittance_type ;;
  }

  dimension: risk_score {
    type: string
    sql: ${TABLE}.risk_score ;;
  }

  dimension: sender_customer_id {
    type: string
    sql: ${TABLE}.sender_customer_id ;;
  }

  dimension: sender_customer_object {
    type: string
    sql: ${TABLE}.sender_customer_object ;;
  }

  dimension: sender_name {
    type: string
    sql: ${TABLE}.sender_name ;;
  }

  dimension: sender_type {
    type: string
    sql: ${TABLE}.sender_type ;;
  }

  dimension: source_of_fund {
    type: string
    sql: ${TABLE}.source_of_fund ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id, recipient_name, sender_name, disbursement_detail.count, remittance_event.count]
  }
}
