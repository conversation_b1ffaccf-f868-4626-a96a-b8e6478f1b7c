view: rmo_risk_assessment {
  sql_table_name: transform__remittance_dev.rmo_risk_assess ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: reference_id {
    type: number
    sql: ${TABLE}.reference_id ;;
  }

  dimension: compliance_ut_elapsed {
    type: number
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

  dimension_group: rmo_disbursing_timestamp {
    type: time
    timeframes:[raw,
      time,
      date,
      week,
      month,
      quarter,
      year]
    sql: ${TABLE}.rmo_disbursing_timestamp ;;
  }

  measure:compliance_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

  measure:compliance_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

  measure:compliance_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

  measure:compliance_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

  measure:compliance_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.compliance_ut_elapsed ;;
  }

}
