view: rmo_api_gw_logs_pdt {
  derived_table: {
    interval_trigger: "6 hours"
    explore_source: api_gw_logs {
      column: reference_id {}
      column: path_group {}
      column: request {}
      column: id {}
      column: last_modified_time {}
      column: request_timestemp {}
      column: method {}
      filters: {
        field: api_gw_logs.last_modified_time
        value: "2 months"
      }
      filters: {
        field: api_gw_logs.method
        value: "POST"
      }
      filters: {
        field: api_gw_logs.path_group
        value: "\"/remittance_payouts\""
      }
    }
  }
  dimension: reference_id {
    description: ""
  }
  dimension: path_group {
    description: ""
  }
  dimension: request {
    description: ""
  }
  dimension: id {
    description: ""
  }
  dimension_group: last_modified_time {
    description: ""
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.last_modified_time ;;
  }
  dimension_group: api_request_time {
    description: ""
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql:from_unixtime(cast(${TABLE}.request_timestemp as BIGINT)/1000)  ;;
  }
  dimension: method {
    description: ""
  }
}
