view: rmo_api_gw_performance_pdt {
  derived_table: {
    explore_source: e2e_rmo {
      column: reference_id { field: rmo_api_gw_logs_pdt.reference_id }
      column: api_request_time_time { field: rmo_api_gw_logs_pdt.api_request_time_time }
      column: created_time {}
      filters: {
        field: e2e_rmo.created_time
        value: "2 months"
      }
      filters: {
        field: rmo_api_gw_logs_pdt.api_request_time_time
        value: "NOT NULL"
      }
    }
  }
  dimension: reference_id {
    label: "Api Gateway Logs Reference ID"
    description: ""
  }
  dimension_group: api_request_time_time {
    label: "Api Gateway Logs API Request Time Time"
    description: ""
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
  }
  dimension_group: created_time {
    description: ""
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
  }
  measure: elapsed {
    description: ""
    type: number
    sql: date_diff(second, (rmo_api_gw_performance_pdt.api_request_time_time AT TIME ZONE 'Asia/Jakarta'), (rmo_api_gw_performance_pdt.created_time AT TIME ZONE 'Asia/Jakarta')) ;;
  }
}
