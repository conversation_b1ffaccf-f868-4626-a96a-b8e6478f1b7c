view: payout_api_logs {

  sql_table_name: transform__remittance_dev.payout_api_logs  ;;
  suggestions: no

  # Define your dimensions and measures here, like this:
  dimension: reference_id {
    type: number
    sql: ${TABLE}.reference_id ;;
  }


  dimension: payout_e2e_ut_elapsed {
    type: number
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  dimension: payout_api_ut_elapsed {
    description: "The date when each user last ordered"
    type: number
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  dimension_group: rmo_created {
    type: time
    timeframes:[raw,
      time,
      date,
      week,
      month,
      quarter,
      year]
    sql: ${TABLE}.rmo_created ;;
  }

  measure:payout_e2e_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_e2e_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_e2e_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_e2e_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_e2e_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_e2e_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.payout_e2e_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_10_pct  {
    type: percentile
    percentile: 10
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }

  measure:payout_api_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.payout_api_ut_elapsed ;;
  }
}
