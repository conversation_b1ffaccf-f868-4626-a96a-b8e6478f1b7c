include: "/remittance_money_out/e2e_rmo/*.view.lkml"
include: "/remittance_money_out/*.view.lkml"


explore: e2e_rmo {
  group_label: "Remittance Money Out"
  label: "E2E Remittance Money Out"
  from: remittance

  join: rmo_api_gw_logs_pdt{
    from: rmo_api_gw_logs_pdt
    view_label: "Api Gateway Logs"
    sql_on: ${e2e_rmo.reference_id}=${rmo_api_gw_logs_pdt.reference_id} and ${rmo_api_gw_logs_pdt.last_modified_time_raw} < date_add('second',30,${e2e_rmo.created_raw}) and ${rmo_api_gw_logs_pdt.last_modified_time_raw} > date_add('second',-10,${e2e_rmo.created_raw}) ;;
    relationship: one_to_one
    type:  left_outer
  }

  join: rmo_webhook_pdt{
    from: rmo_webhook_pdt
    view_label: "Webhook"
    sql_on: ${e2e_rmo.reference_id}=${rmo_webhook_pdt.reference_id} ;;
    relationship: one_to_many
    type:  left_outer
  }

}
