view: rmo_webhook {

  sql_table_name: transform__remittance_dev.rmo_webhook ;;
  suggestions: no

  dimension: disbursing_ut_elapsed {
    type: number
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension_group: rmo_disbursing_timestamp {
    type: time
    timeframes: [date, week, month, year]
    sql: ${TABLE}.rmo_disbursing_timestamp ;;
  }

  dimension_group: webhook_created {
    type: time
    timeframes: [date, week, month, year]
    sql: ${TABLE}.webhook_created ;;
  }

  measure:disbursing_ut_elapsed_99_pct  {
    type: percentile
    percentile: 99
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }

  measure:disbursing_ut_elapsed_95_pct  {
    type: percentile
    percentile: 95
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }

  measure:disbursing_ut_elapsed_90_pct  {
    type: percentile
    percentile: 90
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }

  measure:disbursing_ut_elapsed_min  {
    type: min
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }

  measure:disbursing_ut_elapsed_max  {
    type: max
    sql: ${TABLE}.disbursing_ut_elapsed ;;
  }
}
