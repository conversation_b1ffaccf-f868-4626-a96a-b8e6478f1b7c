include: "/webhook/*.view.lkml"



view: rmo_webhook_pdt {
  derived_table: {
    explore_source:  webhook_service {
      column: payload {}
      column: type {}
      column: status { field: webhook_events.status }
      column: created_time { field: webhook_events.created_time }
      column: rmo_id {field:webhook_service.rmo_id}
      column: reference_id {field:webhook_service.reference_id}
      filters: {
        field: webhook_service.type
        value: "\"remittance_payout.succeeded\",\"remittance_payout.failed\""
      }
      filters: {
        field: webhook_events.status
        value: "COMPLETED"
      }
    }
    interval_trigger: "6 hours"
  }
  dimension: payload {
    description: ""
  }
  dimension: type {
    description: ""
  }
  dimension: rmo_id {
    description: ""
  }
  dimension: reference_id {
    description: ""
  }
  dimension: webhook_created_time {
    description: ""
    sql: ${TABLE}.created_time ;;
    type: date_time
  }
}


view: +ext_webhooks {

  dimension: rmo_id {
    description: ""
    sql: get_json_object(${TABLE}.payload, '$.id') ;;
  }
  dimension: reference_id {
    description: ""
    sql: get_json_object(${TABLE}.payload, '$.reference_id') ;;
  }
}
