# The name of this view in Looker is "Compliance Detail"
view: compliance_detail {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__remittance_money_out_service.compliance_detail ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Automatic Compliance Reasons" in Explore.

  dimension: automatic_compliance_reasons {
    type: string
    sql: ${TABLE}.automatic_compliance_reasons ;;
  }

  dimension: compliance_action {
    type: string
    sql: ${TABLE}.compliance_action ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  dimension: reasons {
    type: string
    sql: ${TABLE}.reasons ;;
  }

  dimension: recipient_matched_terrorist_data {
    type: string
    sql: ${TABLE}.recipient_matched_terrorist_data ;;
  }

  dimension: remittance_id {
    type: string
    sql: ${TABLE}.remittance_id ;;
  }

  dimension: risk_score {
    type: string
    sql: ${TABLE}.risk_score ;;
  }

  dimension: sender_matched_terrorist_data {
    type: string
    sql: ${TABLE}.sender_matched_terrorist_data ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }
  measure: count {
    type: count
    drill_fields: [id]
  }
}
