view: callback_request {
  sql_table_name: clean__remittance_money_out_service.callback_request ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: bypass_retry_logic {
    type: yesno
    sql: ${TABLE}.bypass_retry_logic ;;
  }

  dimension: callback_setting_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.callback_setting_id ;;
  }

  dimension: callback_type {
    type: string
    sql: ${TABLE}.callback_type ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: event {
    type: string
    sql: ${TABLE}.event ;;
  }

  dimension: product_id {
    type: string
    sql: ${TABLE}.product_id ;;
  }

  dimension: request_payload {
    type: string
    sql: ${TABLE}.request_payload ;;
  }

  dimension: response_payload {
    type: string
    sql: ${TABLE}.response_payload ;;
  }

  dimension: response_status {
    type: string
    sql: ${TABLE}.response_status ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: url {
    type: string
    sql: ${TABLE}.url ;;
  }

  measure: count {
    type: count
    drill_fields: [id, callback_setting.id]
  }
}
