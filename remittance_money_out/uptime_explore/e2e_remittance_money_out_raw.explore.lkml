include: "/remittance_money_out/*.view.lkml"
include: "/Api_Gateway/*.view.lkml"

explore: remittance_e2e {
  group_label: "Remittance Uptime"
  label: "Remittance Money Out E2E"
  from: remittance

  join: api_gw_logs{
    from: api_gw_logs
    view_label: "Api Gateway Logs"
    sql_on: ${remittance_e2e.reference_id}=${api_gw_logs.reference_id} and ${api_gw_logs.path_group}='/remittance_payouts';;
    relationship: one_to_one
    type:  full_outer
  }
}
