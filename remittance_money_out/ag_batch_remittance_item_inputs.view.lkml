view: ag_batch_remittance_item_inputs {
  sql_table_name: clean__batch_remittance_service_live.batch_remittance_item_inputs ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    type: string
    sql: ${TABLE}.amount ;;
  }

  dimension: batch_remittance_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.batch_remittance_id ;;
  }

  dimension: batch_remittance_item_id {
    type: string
    sql: ${TABLE}.batch_remittance_item_id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: purpose_code {
    type: string
    sql: ${TABLE}.purpose_code ;;
  }

  dimension: recipient_account_details_account_code {
    type: string
    sql: ${TABLE}.recipient_account_details_account_code ;;
  }

  dimension: recipient_account_details_account_holder_name {
    type: string
    sql: ${TABLE}.recipient_account_details_account_holder_name ;;
  }

  dimension: recipient_account_details_account_number {
    type: string
    sql: ${TABLE}.recipient_account_details_account_number ;;
  }

  dimension: recipient_address_city {
    type: string
    sql: ${TABLE}.recipient_address_city ;;
  }

  dimension: recipient_address_country_code {
    type: string
    sql: ${TABLE}.recipient_address_country_code ;;
  }

  dimension: recipient_customer_type {
    type: string
    sql: ${TABLE}.recipient_customer_type ;;
  }

  dimension: recipient_email {
    type: string
    sql: ${TABLE}.recipient_email ;;
  }

  dimension: recipient_mobile_number {
    type: string
    sql: ${TABLE}.recipient_mobile_number ;;
  }

  dimension: recipient_name {
    type: string
    sql: ${TABLE}.recipient_name ;;
  }

  dimension: recipient_phone_number {
    type: string
    sql: ${TABLE}.recipient_phone_number ;;
  }

  dimension: sender_address_city {
    type: string
    sql: ${TABLE}.sender_address_city ;;
  }

  dimension: sender_address_country_code {
    type: string
    sql: ${TABLE}.sender_address_country_code ;;
  }

  dimension: sender_customer_type {
    type: string
    sql: ${TABLE}.sender_customer_type ;;
  }

  dimension: sender_name {
    type: string
    sql: ${TABLE}.sender_name ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
      id,
      sender_name,
      recipient_name,
      recipient_account_details_account_holder_name,
      batch_remittances.id,
      batch_remittances.uploader_name,
      batch_remittances.approver_name
    ]
  }
}
