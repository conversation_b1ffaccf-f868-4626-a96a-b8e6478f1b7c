view: disbursement_detail {
  sql_table_name: clean__remittance_money_out_service.disbursement_detail ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: channel_properties_account_holder_name {
    type: string
    sql: ${TABLE}.channel_properties_account_holder_name ;;
  }

  dimension: channel_properties_account_number {
    type: string
    sql: ${TABLE}.channel_properties_account_number ;;
  }

  dimension: channel_properties_beneficiary_id_name {
    type: string
    sql: ${TABLE}.channel_properties_beneficiary_id_name ;;
  }

  dimension: channel_properties_disbursement_code {
    type: string
    sql: ${TABLE}.channel_properties_disbursement_code ;;
  }

  dimension: channel_properties_expires_at {
    type: string
    sql: ${TABLE}.channel_properties_expires_at ;;
  }

  dimension: channel_type {
    type: string
    sql: ${TABLE}.channel_type ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: disbursement_service_id {
    type: string
    sql: ${TABLE}.disbursement_service_id ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: remittance_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.remittance_id ;;
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
      id,
      channel_properties_account_holder_name,
      channel_properties_beneficiary_id_name,
      remittance.id,
      remittance.recipient_name,
      remittance.sender_name
    ]
  }
}
