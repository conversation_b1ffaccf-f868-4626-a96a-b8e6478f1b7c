view: migrations {
  sql_table_name: clean__remittance_money_out_service.migrations ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: timestamp {
    type: number
    sql: ${TABLE}."timestamp" ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name]
  }
}
