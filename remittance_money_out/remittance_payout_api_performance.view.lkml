# If necessary, uncomment the line below to include explore_source.
# include: "e2e_remittance_money_out.explore.lkml"

view: remittance_payout_api_performance {
  derived_table: {
    sql:
      SELECT
        rmo.reference_id,
        rmo.created,
        logs.request
      FROM
        clean__remittance_money_out_service.remittance rmo
      left join
        clean__api_logs.logs logs
      on
        rmo.reference_id=logs.reference_id and ${api_gw_logs.last_modified_raw} < date_add('second',30,${remittance_e2e.created_raw}) and ${api_gw_logs.last_modified_raw} > date_add('second',-10,${remittance_e2e.created_raw}) and   ${api_gw_logs.path_group}='/remittance_payouts'
      GROUP BY
      customer_id ;;
    # interval_trigger: "24 hours"
  }


  dimension: reference_id {
    description: ""
  }
}
