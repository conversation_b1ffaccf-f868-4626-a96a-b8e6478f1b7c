view: billing_statement_stats {
  derived_table: {
    sql:
      with billingStatements as (
        select
            statement_generation_type,
            billing_end_date,
            concat(date_format(billing_end_date, 'yyyy'), '-',date_format(billing_end_date, 'MM'), '-01') as billing_month,
            rounding,
            total_fees_amount,
            user_id as business_id
        from clean__xendit_billing_service.billingstatements
      ), billingStatementsV2 as (
        select
            statement_generation_type,
            billing_month,
            rounding,
            fee_amount_total as total_fees_amount,
            business_id
        from clean__xendit_billing_service.billingstatementv2
        where billing_statement_publishing_status = 'PRODUCTION'
      ), allbillingstatements as (
        select
            bs.statement_generation_type,
            bs.billing_month,
            bs.rounding,
            bs.total_fees_amount,
            bs.business_id
        from billingStatements bs
        union
        select
            bs2.statement_generation_type,
            bs2.billing_month,
            bs2.rounding,
            bs2.total_fees_amount,
            bs2.business_id
        from billingStatementsV2 bs2
      )
      select * from allbillingstatements
      ;;
  }
  dimension: statement_generation_type {
    type: string
    sql: ${TABLE}.statement_generation_type ;;
  }
  dimension: billing_month {
    type: string
    sql: ${TABLE}.billing_month ;;
  }
  dimension: rounding {
    type: number
    sql: ${TABLE}.rounding ;;
  }
  dimension: total_fees_amount {
    type: number
    sql: ${TABLE}.total_fees_amount ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
}
