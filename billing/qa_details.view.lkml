include: "./billing.explore.lkml"

view: qa_details {
  derived_table: {
    explore_source: qa_summary {
      derived_column: pk {
        sql: row_number() over () ;;
      }
      column: business_id {}
      column: billing_month_string {}
      column: report {}
      column: actual_billed_amount { field: billing_statement_audit_billed_amount.actual_billed_amount }
      column: created_timestamp_ba { field: billing_statement_audit_billed_amount.created_timestamp }
      column: currency { field: billing_statement_audit_billed_amount.currency }
      column: expected_billed_amount { field: billing_statement_audit_billed_amount.expected_billed_amount }
      column: failure_reason_ba { field: billing_statement_audit_billed_amount.failure_reason }
      column: fee_percentage { field: billing_statement_audit_billed_amount.fee_percentage }
      column: flat_fee { field: billing_statement_audit_billed_amount.flat_fee }
      column: payment_channel { field: billing_statement_audit_billed_amount.payment_channel }
      column: transaction_type { field: billing_statement_audit_billed_amount.transaction_type }
      column: txn_amount { field: billing_statement_audit_billed_amount.txn_amount }
      column: txn_count { field: billing_statement_audit_billed_amount.txn_count }

      column: actual_rounding { field: billing_statement_audit_calculation.actual_rounding }
      column: actual_subtotal { field: billing_statement_audit_calculation.actual_subtotal }
      column: actual_total { field: billing_statement_audit_calculation.actual_total }
      column: actual_vat { field: billing_statement_audit_calculation.actual_vat }
      column: created_timestamp_calc { field: billing_statement_audit_calculation.created_timestamp }
      column: expected_rounding { field: billing_statement_audit_calculation.expected_rounding }
      column: expected_subtotal { field: billing_statement_audit_calculation.expected_subtotal }
      column: expected_total { field: billing_statement_audit_calculation.expected_total }
      column: expected_vat { field: billing_statement_audit_calculation.expected_vat }
      column: failure_reason_calc { field: billing_statement_audit_calculation.failure_reason }

      column: channel_name { field: billing_statement_audit_product_count.channel_name }
      column: created_timestamp_product { field: billing_statement_audit_product_count.created_timestamp }
      column: failure_reason_product { field: billing_statement_audit_product_count.failure_reason }
      column: quantity { field: billing_statement_audit_product_count.quantity }
      column: type { field: billing_statement_audit_product_count.type }
    }
  }
  dimension: pk {
    primary_key: yes
    hidden: yes
  }
  dimension: business_id {
    hidden: yes
  }
  dimension: billing_month_string {
    hidden: yes
  }
  dimension: report {
    hidden: yes
  }
  dimension: audit_created_timestamp {
    description: "Timestamp of audit creation. Stored as a string in unknown timezone"
    type: date_time
    convert_tz: no
    sql: to_timestamp(date_format(
    coalesce(
     ${TABLE}.created_timestamp_ba, ${TABLE}.created_timestamp_calc,${TABLE}.created_timestamp_product), 'dd/MM/yyyy, hh:mm a'),'dd/MM/yyyy, hh:mm a');;
  }
  dimension: audit_failure_reason {
    sql: coalesce(${TABLE}.failure_reason_ba, ${TABLE}.failure_reason_calc, ${TABLE}.failure_reason_product) ;;
  }
  dimension: currency {
    group_label: "Billed amount audit details"
  }
  dimension: fee_percentage {
    group_label: "Billed amount audit details"
  }
  dimension: flat_fee {
    group_label: "Billed amount audit details"
  }
  dimension: payment_channel {
    group_label: "Billed amount audit details"
  }
  dimension: transaction_type {
    group_label: "Billed amount audit details"
  }
  dimension: channel_name {
    group_label: "Product audit details"
  }
  dimension: type {
    group_label: "Product audit details"
    description: "The product type for the audit (e.g., Invoice, Disbursement)"
  }
  dimension: subtotals_internally_consistent {
    group_label: "Billing calculation audit details"
    type: yesno
    sql: ${TABLE}.actual_subtotal = ${TABLE}.expected_subtotal ;;
  }
  dimension: rounding_matches {
    group_label: "Billing calculation audit details"
    type: yesno
    sql: ${TABLE}.actual_rounding = ${TABLE}.expected_rounding ;;
  }
  dimension: total_matches {
    group_label: "Billing calculation audit details"
    type: yesno
    sql: ${TABLE}.actual_total = ${TABLE}.expected_total ;;
  }
  dimension: vat_matches {
    group_label: "Billing calculation audit details"
    type: yesno
    sql: ${TABLE}.actual_vat = ${TABLE}.expected_vat ;;
  }

  measure: audit_count {
    type: count
  }
  measure: actual_billed_amount {
    group_label: "Billed amount audit details"
    type: sum
    sql: cast(${TABLE}.actual_billed_amount as double) ;;
    required_fields: [currency]
  }
  measure: expected_billed_amount {
    group_label: "Billed amount audit details"
    type: sum
    sql: cast(${TABLE}.expected_billed_amount as double) ;;
    required_fields: [currency]
  }
  measure: txn_amount {
    group_label: "Billed amount audit details"
    type: sum
    sql: cast(${TABLE}.txn_amount as double) ;;
    required_fields: [currency]
  }
  measure: txn_count {
    group_label: "Billed amount audit details"
    type: sum
    sql: cast(${TABLE}.txn_count as double) ;;
    required_fields: [currency]
  }
  measure: actual_rounding {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.actual_rounding as double) ;;
  }
  measure: actual_subtotal {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.actual_subtotal as double) ;;
  }
  measure: actual_total {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.actual_total as double) ;;
  }
  measure: actual_vat {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.actual_vat as double) ;;
  }
  measure: expected_rounding {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.expected_rounding as double) ;;
  }
  measure: expected_subtotal {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.expected_subtotal as double) ;;
  }
  measure: expected_total {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.expected_total as double) ;;
  }
  measure: expected_vat {
    group_label: "Billing calculation audit details"
    type: sum
    sql: cast(${TABLE}.expected_vat as double) ;;
  }
  measure: quantity {
    group_label: "Product audit details"
    type: sum
    sql: cast(${TABLE}.quantity as double) ;;
  }

  set: failure_details {
    fields: [
      report
      , audit_failure_reason
      , audit_count
      , actual_billed_amount
      , expected_billed_amount
      , actual_total
      , expected_total
      , actual_subtotal
      , expected_subtotal
      , actual_rounding
      , expected_rounding
      , actual_vat
      , expected_vat
      , quantity
    ]
  }
}
