include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
include: "qa_summary.view.lkml"

view: billing_statements_consolidated {
  derived_table: {
    sql: select
    -- Note that this currently only contains Indonesia data
        -- Basic columns that overlap with billing statement V2
        id
        , created
        , due_date_term
        , email_sent
        , faktur_pajak_file_id
        , file_id
        , identifier_number
        , is_soft_deleted
        , payer_email
        , payment_term
        , rounding
        , should_send_email
        , statement_generation_type
        , status
        , status_updated
        , updated
        , user_entity
        -- Remapped columns
        , date_trunc('month', billing_end_date) as billing_month
        , split(regexp_replace(finance_emails, '[\\[\\]"]', ''), ',') AS finance_emails
        , subtotal_fees_amount as amount_billed
        , user_id as business_id
        , 'V1' as statement_generation
        , 'IDR' as billing_currency
        , case
            when status = 'PAID' then status_updated
            else null
          end as paid_date
        --, (fees_paid_amount - vat_paid) as amount_outstanding -- not ideal, TODO: 1. Not sure if this calculation accurate for v1 or not
        , total_fees_amount/(1 + (case when created < (date '2022-04-01') then 0.1 else 0.11 end)) as amount_outstanding
          -- assuming v1 is only for Indonesia. It's not ideal since VAT was 10% in 2021, TODO: 1. Need to propagate vat_rate to table schema in data lake. 2. may want to consider storing direct deducted and indirect deduction amount and it's VAT's in the table.
        , total_fees_amount as amount_to_pay
        , case when status = 'DELETED' then status
          when is_soft_deleted = true then 'DELETED'
          else 'PRODUCTION' end as publishing_status
        -- Detailed revenue data removed
      from clean__xendit_billing_service.billingstatements
      union all
      select
        -- Basic columns that overlap with billing statements
        bv2.id
        , bv2.created
        , bv2.due_date_term
        , bv2.email_sent
        , bv2.faktur_pajak_file_id
        , bv2.file_id
        , bv2.identifier_number
        , bv2.is_soft_deleted
        , bv2.payer_email
        , bv2.payment_term
        , bv2.rounding
        , bv2.should_send_email
        , bv2.statement_generation_type
        , bv2.status
        , bv2.status_updated
        , bv2.updated
        , bv2.user_entity
        -- Remapped columns
        , cast(bv2.billing_month as date)
        , bv2.finance_emails
        , bv2.billed_amount_total as amount_billed
        , bv2.business_id
        , 'V2' as statement_generation
        , case
            when bv2c.is_single_currency_bill then bv2.biller.currency
            else null
          end as billing_currency
        , case
            when bv2.status = 'PAID' then bv2.status_updated
            else null
          end as paid_date
       , bv2.fee_amount_total /(1+ (case bv2.country_of_operation
              when 'Indonesia' then 0.11
              when  'Philippines' then 0.12
              when  'Thailand' then 0.11
              when  'Viet Nam' then 0.1
              when  'Regional Remittance' then 0.03
              else 0.11
            end) -- not ideal, TODO: 1. Need to propagate vat_rate to table schema in data lake. 2. may want to consider storing direct deducted and indirect deduction amount and it's VAT's in the table.
          ) as amount_outstanding
        -- Detailed revenue data removed
        , bv2.fee_amount_total as amount_to_pay
        , bv2.billing_statement_publishing_status as publishing_status
      from clean__xendit_billing_service.billingstatementv2 as bv2
      join ${billingstatementv2_currencies.SQL_TABLE_NAME} as bv2c on bv2.id = bv2c.id
       ;;
      sql_trigger_value: select max(ts) from (
      select max(created) as ts from clean__xendit_billing_service.billingstatementv2
      union select max(created) from clean__xendit_billing_service.billingstatements);;
  }

  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension_group: created {
    type: time
    timeframes: [raw, time, hour_of_day, date, day_of_month, day_of_week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }
  dimension_group: to_create {
    description: "Time from the end of month Jakarta time until billing statement was generated"
    type: duration
    intervals: [hour, day]
    # sql_start: to_utc_timestamp(cast(year(${billing_raw}) as string) ||'-'||cast(month(${billing_raw}) as string)||'-01',"UTC");;
    sql_start: to_timestamp(concat(cast(last_day(billing_statements_consolidated.billing_month) + interval '1' day as timestamp),'Asia/Jakarta')) ;;
    sql_end: ${created_raw};;
  }
  dimension_group: payment_due {
    type: time
    timeframes: [raw, date, month, quarter, year]
    sql: ${TABLE}.due_date_term ;;
  }
  dimension: is_email_sent {
    type: yesno
    sql: ${TABLE}.email_sent ;;
  }
  dimension: faktur_pajak_file_id {
    type: string
    sql: ${TABLE}.faktur_pajak_file_id ;;
  }
  dimension: file_id {
    type: string
    sql: ${TABLE}.file_id ;;
  }
  dimension: identifier_number {
    type: string
    sql: ${TABLE}.identifier_number ;;
  }
  dimension: is_soft_deleted {
    type: yesno
    sql: ${TABLE}.is_soft_deleted ;;
  }
  dimension: payer_email {
    type: string
    sql: ${TABLE}.payer_email ;;
  }
  dimension: payment_term {
    type: number
    sql: ${TABLE}.payment_term ;;
  }
  dimension: should_send_email {
    type: yesno
    sql: ${TABLE}.should_send_email ;;
  }
  dimension: statement_generation_type {
    type: string
    sql: ${TABLE}.statement_generation_type ;;
    order_by_field: statement_generation_type_ordered
  }
  dimension: statement_generation_type_ordered {
    description: "Places the manual generation type last given it is a different class of statement"
    hidden: yes
    type: string
    case: {
      when: {
        label:"Manual generation"
        sql: ${TABLE}.statement_generation_type = 'MANUAL_GENERATION' ;;
      }
      else: "Automated generation"
    }
    alpha_sort: yes
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
    case_sensitive: no
    suggestable: yes
    suggest_persist_for: "168 hours"
  }

  dimension: publishing_status {
    type: string
    sql: ${TABLE}.publishing_status ;;
  }
  dimension_group: status_updated {
    type: time
    timeframes: [raw, time, hour_of_day, date, day_of_month, day_of_week, month, quarter, year]
    sql: ${TABLE}.status_updated ;;
  }
  dimension_group: updated {
    type: time
    timeframes: [raw, time, hour_of_day, date, day_of_month, day_of_week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }
  dimension: user_entity {
    type: string
    sql: ${TABLE}.user_entity ;;
  }
  dimension_group: billing {
    type: time
    convert_tz: no
    timeframes: [raw, date, month, quarter, year]
    sql: ${TABLE}.billing_month ;;
  }
  dimension: billing_month_string {
    type: string
    hidden: yes
    sql:date_format(${billing_raw}, 'yyyy-MM-dd') ;;
  }
  dimension: finance_emails {
    type: string
    sql: ${TABLE}.finance_emails ;;
  }
  dimension: billing_currency {
    group_label: "Amount"
    type: string
    sql: ${TABLE}.billing_currency ;;
  }
  dimension: amount_rounding {
    group_label: "Amount"
    type: number
    sql: ${TABLE}.rounding ;;
    required_fields: [billing_currency]
  }
  dimension: amount_billed {
    group_label: "Amount"
    type: number
    sql: ${TABLE}.amount_billed ;;
    required_fields: [billing_currency]
  }
  dimension: amount_billed_usd {
    group_label: "Amount"
    label: "Amount Billed (USD)"
    description: "Billed amounts in USD. May be null if more than one currency was on a bill"
    value_format_name: "usd"
    type: number
    sql: ${TABLE}.amount_billed /  ${fx_rates.rate_usd} ;;
  }
  dimension: amount_outstanding {
    group_label: "Amount"
    type: number
    sql: ${TABLE}.amount_outstanding ;;
    required_fields: [billing_currency]
    value_format_name: decimal_2
  }
  dimension: amount_outstanding_usd {
    group_label: "Amount"
    label: "Amount Outstanding (USD)"
    description: "Unpaid amounts in USD. May be null if more than one currency was on a bill"
    value_format_name: "usd"
    type: number
    sql: ${TABLE}.amount_outstanding /  ${fx_rates.rate_usd} ;;
  }
  dimension: amount_to_pay {
    group_label: "Amount"
    type: number
    sql: ${TABLE}.amount_to_pay ;;
    required_fields: [billing_currency]
    value_format_name: decimal_2
  }
  dimension: amount_to_pay_usd {
    group_label: "Amount"
    label: "Amount to Pay (USD)"
    description: "Invoice amount to be paid by customer"
    value_format_name: "usd"
    type: number
    sql: ${TABLE}.amount_to_pay /  ${fx_rates.rate_usd} ;;
  }
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }
  dimension: statement_generation {
    type: string
    sql: ${TABLE}.statement_generation ;;
  }
  dimension_group: paid {
    type: time
    timeframes: [raw, time, hour_of_day, date, day_of_month, day_of_week, month, quarter, year]
    sql: ${TABLE}.paid_date ;;
  }
  dimension_group: unpaid {
    # @TODO check this as paid date != bill creation date + payment term
    type: duration
    intervals: [day, week, month]
    sql_start: ${created_raw} ;;
    sql_end: case when ${paid_raw} is not null then ${paid_raw} else now() end ;;
  }
  dimension: duration_unpaid_tiers {
    type: tier
    style: integer
    tiers: [0,7,14,30,60,90]
    sql: ${days_unpaid} ;;
  }
  dimension: paid_on_time {
    description: "Returns `true` if paid before the due date, `null` if unpaid but before the due date and false otherwise"
    type: string
    sql: cast(case when ${status} = 'PAID' then (${paid_raw} <= ${payment_due_raw})
    when ${status} = 'UNPAID' and now() >= ${payment_due_raw} then false
    else null end as string) ;;
  }
  dimension: has_audit {
    description: "Will be Yes if there is at least one audit result for this billing statement"
    type: yesno
    sql: ${qa_summary.number_of_audits_for_billing_month} > 0 ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }
  measure: count_not_deleted {
    type: count
    filters: [status: "-DELETED"]
    drill_fields: [detail*]
  }
  measure: count_paid_on_time {
    type: count
    filters: [
      status: "PAID"
      , paid_on_time: "true"
    ]
    drill_fields: [detail*]
  }
  measure: percent_paid_on_time {
    value_format_name: percent_1
    type: number
    sql: cast(${count_paid_on_time} as double) / nullif(${count_not_deleted},0) ;;
    link: {
      label: "View by count"
      url: "/explore/{{_model._name}}/{{_explore._name}}?fields={{_view._name}}.durations_by_count*"
    }
    link: {
      label: "View by value"
      url: "/explore/{{_model._name}}/{{_explore._name}}?fields={{_view._name}}.durations_by_value*"
    }
  }
  measure: revenue {
    value_format_name: decimal_2
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_billed} ;;
    drill_fields: [detail*]
  }
  measure: revenue_usd {
    value_format_name: "usd"
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_billed_usd} ;;
    drill_fields: [detail*]
    required_fields: []
  }

  measure: revenue_outstanding {
    value_format_name: decimal_2
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_outstanding} ;;
    drill_fields: [detail*]
  }
  measure: revenue_outstanding_usd {
    value_format_name: "usd"
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_outstanding_usd} ;;
    drill_fields: [detail*]
    required_fields: []
  }
  measure: volume_to_pay{
    value_format_name: decimal_2
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_to_pay} ;;
    drill_fields: [detail*]
  }
  measure: volume_to_pay_usd {
    value_format_name: "usd"
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_to_pay_usd} ;;
    drill_fields: [detail*]
    required_fields: []
  }

  measure: revenue_paid_direct_usd {
    value_format_name: "usd"
    type: sum
    filters: [status: "-DELETED"]
    sql: ${amount_billed_usd} - ${amount_outstanding_usd} ;;
    drill_fields: [detail*]
    required_fields: []
  }

  measure: revenue_usd_paid_on_time {
    hidden: yes
    type: sum
    filters: [
      status: "PAID"
      , paid_on_time: "true"
    ]
    sql: ${amount_billed_usd} ;;
  }
  measure: percent_paid_on_time_by_value {
    value_format_name: percent_1
    type: number
    sql: cast(${revenue_usd_paid_on_time} as double) / nullif(${revenue_usd},0) ;;
    link: {
      label: "Drill into payment durations by count"
      url: "/explore/{{_model._name}}/{{_explore._name}}?fields={{_view._name}}.durations_by_count*"
    }
    link: {
      label: "Drill into payment durations by value"
      url: "/explore/{{_model._name}}/{{_explore._name}}?fields={{_view._name}}.durations_by_value*"
    }
  }
  measure: average_of_hours_to_create {
    description: "The average time taken to create a billing statement"
    type: average
    sql: ${hours_to_create};;
  }
  measure: count_with_audit {
    type: count
    hidden: yes
    filters: [has_audit: "Yes"]
  }
  measure: audit_coverage {
    type: number
    value_format_name: percent_1
    sql: cast(${count_with_audit} as double) / nullif(${count},0) ;;
    drill_fields: [
      has_audit
      , count
    ]
  }

  set: durations_by_count {
    fields: [
      duration_unpaid_tiers
      , count
    ]
  }
  set: durations_by_value {
    fields: [
      duration_unpaid_tiers
      , revenue_usd
    ]
  }

  set: detail {
    fields: [
      id,
      file_id,
      faktur_pajak_file_id,
      identifier_number,
      business_id,
      billing_month,
      created_time,
      updated_time,
      status_updated_time,
      payment_term,
      payment_due_date,
      status,
      is_soft_deleted,
      user_entity,
      payer_email,
      should_send_email,
      is_email_sent,
      finance_emails,
      statement_generation_type,
      statement_generation,
      amount_billed,
      amount_rounding,
      billing_currency
    ]
  }
}
