include: "/billing/promotion/ag_promo_reimbursement.view.lkml"

view: +ag_promo_reimbursement {

  dimension: transaction_type {
    type: string
    sql: json_extract_scalar(replace(replace(transaction_details, '['), ']'), '$.product_transaction_type') ;;
  }

  dimension: transaction_channel {
    type: string
    sql: json_extract_scalar(replace(replace(transaction_details, '['), ']'), '$.product_transaction_channel') ;;
  }

  dimension: transaction_total_amount {
    type: number
    sql: cast(json_extract_scalar(replace(replace(transaction_details, '['), ']'), '$.transaction_total_amount') as double) ;;
  }

  dimension: transaction_total_count {
    type: number
    sql: cast(json_extract_scalar(replace(replace(transaction_details, '['), ']'), '$.transaction_total_count') as double);;
  }
}
