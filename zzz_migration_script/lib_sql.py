def is_valid_databricks_sql(sql_clause):
    invalid_patterns = [
        "cross join", 
        "unnest(", 
        "json_extract_scalar",
        "array_agg"
    ]

    # Check if any invalid pattern is present in the SQL clause
    for pattern in invalid_patterns:
        if pattern in sql_clause.lower():
            return False

    # If none of the invalid patterns are found, consider it a valid Databricks SQL clause
    return True


import sqlglot

def translate_trino_to_spark(sql):
    try:
        res = sqlglot.transpile(sql, read='presto', write='spark', pretty=True)[0]
        return res
    except sqlglot.errors.ParseError as e:
        print(e.errors)
   
    return None