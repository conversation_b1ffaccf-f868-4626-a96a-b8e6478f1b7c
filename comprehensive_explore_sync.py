#!/usr/bin/env python3
"""
Comprehensive .explore.lkml file synchronization script
Compares all .explore.lkml files between source and DBR repositories
"""

import os
import subprocess
import difflib
from pathlib import Path

# Repository paths
SOURCE_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def get_explore_files(repo_path):
    """Get all .explore.lkml files in a repository"""
    result = subprocess.run(
        ["find", repo_path, "-name", "*.explore.lkml"],
        capture_output=True, text=True
    )
    files = result.stdout.strip().split('\n') if result.stdout.strip() else []
    # Convert to relative paths
    relative_files = []
    for file in files:
        if file:
            rel_path = os.path.relpath(file, repo_path)
            relative_files.append(rel_path)
    return sorted(relative_files)

def read_file_content(repo_path, relative_path):
    """Read file content safely"""
    try:
        full_path = os.path.join(repo_path, relative_path)
        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        return f"ERROR: Could not read file - {str(e)}"

def compare_files(source_content, dbr_content):
    """Compare two file contents and return differences"""
    if source_content == dbr_content:
        return None, "IDENTICAL"
    
    # Check for structural differences vs content differences
    source_lines = source_content.splitlines()
    dbr_lines = dbr_content.splitlines()
    
    diff = list(difflib.unified_diff(
        source_lines, dbr_lines,
        fromfile='source', tofile='dbr',
        lineterm='', n=3
    ))
    
    if not diff:
        return None, "IDENTICAL"
    
    # Categorize differences
    has_include_diff = any('include:' in line for line in diff if line.startswith(('+', '-')))
    has_explore_diff = any('explore:' in line for line in diff if line.startswith(('+', '-')))
    has_description_diff = any('description:' in line for line in diff if line.startswith(('+', '-')))
    has_join_diff = any('join:' in line for line in diff if line.startswith(('+', '-')))
    has_sql_diff = any(any(sql_keyword in line for sql_keyword in ['sql_on:', 'sql_where:', 'sql:', 'get_json_object', 'json_extract_scalar', 'instr', 'STRPOS']) for line in diff if line.startswith(('+', '-')))
    
    diff_type = []
    if has_include_diff:
        diff_type.append("INCLUDES")
    if has_explore_diff:
        diff_type.append("EXPLORES")
    if has_description_diff:
        diff_type.append("DESCRIPTIONS")
    if has_join_diff:
        diff_type.append("JOINS")
    if has_sql_diff:
        diff_type.append("SQL")
    
    return diff, " | ".join(diff_type) if diff_type else "OTHER"

def main():
    print("🔍 COMPREHENSIVE .EXPLORE.LKML SYNCHRONIZATION ANALYSIS")
    print("=" * 70)
    
    # Get all explore files
    source_files = get_explore_files(SOURCE_REPO)
    dbr_files = get_explore_files(DBR_REPO)
    
    print(f"📊 Source repository: {len(source_files)} files")
    print(f"📊 DBR repository: {len(dbr_files)} files")
    
    # Find missing files
    missing_in_dbr = set(source_files) - set(dbr_files)
    extra_in_dbr = set(dbr_files) - set(source_files)
    common_files = set(source_files) & set(dbr_files)
    
    print(f"\n📋 MISSING IN DBR: {len(missing_in_dbr)} files")
    for file in sorted(missing_in_dbr):
        print(f"  ❌ {file}")
    
    print(f"\n📋 EXTRA IN DBR: {len(extra_in_dbr)} files")
    for file in sorted(extra_in_dbr):
        print(f"  ➕ {file}")
    
    print(f"\n📋 COMMON FILES: {len(common_files)} files")
    
    # Compare common files
    differences = []
    identical_count = 0
    
    for file in sorted(common_files):
        source_content = read_file_content(SOURCE_REPO, file)
        dbr_content = read_file_content(DBR_REPO, file)
        
        diff, diff_type = compare_files(source_content, dbr_content)
        
        if diff_type == "IDENTICAL":
            identical_count += 1
        else:
            differences.append({
                'file': file,
                'diff_type': diff_type,
                'diff': diff
            })
    
    print(f"\n📊 COMPARISON RESULTS:")
    print(f"  ✅ Identical files: {identical_count}")
    print(f"  🔄 Files with differences: {len(differences)}")
    
    # Group differences by type
    diff_by_type = {}
    for item in differences:
        diff_type = item['diff_type']
        if diff_type not in diff_by_type:
            diff_by_type[diff_type] = []
        diff_by_type[diff_type].append(item['file'])
    
    print(f"\n📋 DIFFERENCES BY TYPE:")
    for diff_type, files in sorted(diff_by_type.items()):
        print(f"  🔧 {diff_type}: {len(files)} files")
        for file in files[:5]:  # Show first 5 files
            print(f"    - {file}")
        if len(files) > 5:
            print(f"    ... and {len(files) - 5} more")
    
    # Save detailed report
    with open('explore_sync_report.txt', 'w') as f:
        f.write("COMPREHENSIVE .EXPLORE.LKML SYNCHRONIZATION REPORT\n")
        f.write("=" * 60 + "\n\n")
        
        f.write(f"Source files: {len(source_files)}\n")
        f.write(f"DBR files: {len(dbr_files)}\n")
        f.write(f"Missing in DBR: {len(missing_in_dbr)}\n")
        f.write(f"Extra in DBR: {len(extra_in_dbr)}\n")
        f.write(f"Identical: {identical_count}\n")
        f.write(f"Different: {len(differences)}\n\n")
        
        if missing_in_dbr:
            f.write("MISSING IN DBR:\n")
            for file in sorted(missing_in_dbr):
                f.write(f"  - {file}\n")
            f.write("\n")
        
        if extra_in_dbr:
            f.write("EXTRA IN DBR:\n")
            for file in sorted(extra_in_dbr):
                f.write(f"  - {file}\n")
            f.write("\n")
        
        f.write("FILES WITH DIFFERENCES:\n")
        for item in differences:
            f.write(f"\n{item['file']} ({item['diff_type']}):\n")
            f.write("-" * 50 + "\n")
            if item['diff']:
                for line in item['diff'][:20]:  # First 20 lines of diff
                    f.write(f"{line}\n")
                if len(item['diff']) > 20:
                    f.write(f"... ({len(item['diff']) - 20} more lines)\n")
    
    print(f"\n📄 Detailed report saved to: explore_sync_report.txt")
    
    return differences, missing_in_dbr, extra_in_dbr

if __name__ == "__main__":
    main()
