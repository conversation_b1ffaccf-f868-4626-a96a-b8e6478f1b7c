view: ag_cards_cybersource_apicalls {
  derived_table: {
    sql: with agg as (
          select cybersource_merchant_id as merchant_id, 'authentications' as type, substring(masked_card_number, 1, 6) as bin, date_trunc('month', a.created) as date_month
          from clean__credit_card_production.authenticatedcreditcardtokens a
          where date_trunc('month', a.created) > date('2022-01-01')
          and a.processor_type in ('CYBERSOURCE', 'CYBS_REST')
      union all
          select a.cybersource_merchant_id, 'authorizations', substring(c.masked_card_number, 1, 6) as bin, date_trunc('month', a.created) as date_month
          from clean__credit_card_production.authorizations a
          left join clean__credit_card_production.creditcardcharges c on a.id = c.authorization_id
          where date_trunc('month', a.created) > date('2022-01-01')
          and c.credit_card_payment_channel in ('CYBERSOURCE', 'CYBS_REST')
      union all
          select c.merchant_id, 'refunds', substring(c.masked_card_number, 1, 6) as bin, date_trunc('month', r.created) as date_month
          from clean__credit_card_production.creditcardrefunds r
          join clean__credit_card_production.creditcardcharges c on c.id = r.credit_card_charge_id
          where date_trunc('month', r.created) > date('2022-01-01')
          and c.credit_card_payment_channel in ('CYBERSOURCE', 'CYBS_REST')
      union all
          select merchant_id, 'decision_manager', payment_card_bin as bin, date_trunc('month', created_at) as date_month
          from clean__cybersource_service.decision_manager
          where date_trunc('month', created_at) > date('2022-01-01')
      union all
          select merchant_id, 'tokenizations', substring(masked_card_number, 1, 6) as bin, date_trunc('month', created) as date_month
          from clean__credit_card_production.creditcardtokens
          where date_trunc('month', created) > date('2022-01-01')
          and token_type != 'XENDIT'
      )
      select date_month, a.merchant_id, a.type, bin.card, count(*) as total
      from agg a
      left join clean__xendit_fraud_static_data_provider.bin as bin on a.bin = bin.bin_number
      -- where merchant_id in (
      --'bii009090152', 'bii009090157', 'xendit', 'xendit_001990520000',
      --                      'xendit_bii009090172', 'xendit_bii009090181', 'xendit_cashbac',
      --                     'xendit_ctv_agg', 'xendit_paperid', 'xendit_pc5', 'xendit_travelio',
      --                     'xendit_yummycorp', 'xendit_zomato'
      -- )
      where (
        merchant_id in (
          '**********',
          '1783296_D',
          '4968960_D',
          '4988795_D',
          '5089014_D',
          '5858676_D',
          'allianz_cybersource',
          'bii009090152',
          'bii009090157',
          'bni_amex_test',
          'cashbac_bni_ins_6mo',
          'cashbac_bri_ins_3mo',
          'cashbac_bri_ins_6mo',
          'fdx_cpg_id_idr',
          'garuda',
          'garuda_dm_idr',
          'garuda_mcp',
          'garuda_mob',
          'garudamiles_citi',
          'hlidcybersource',
          'jafra54321',
          'paybuddy_bca',
          'paybuddy_id',
          'paybuddy_id2',
          'ptdompet_bri',
          'tiket',
          'tiket_12mo',
          'tiket_3ds_12mo',
          'tiket_3ds_3mo',
          'tiket_3ds_6mo',
          'tiket_3ds_ecomm',
          'tiket_3mo',
          'tiket_6mo',
          'tiket_ecomm',
          'tiketinstallment12',
          'tiketinstallment3',
          'tiketinstallment6',
          'tokopedia_bni_ins3',
          'tokopedia_preauth_3ds',
          'tokopedia_purchase_3ds',
          'traveloka',
          'traveloka3ds',
          'travelokabay_thb',
          'travelokabay3ds_thb',
          'travelokabni_idr_in12',
          'travelokabni_idr_in3',
          'travelokabni_idr_in6',
          'travelokabni_idr_phone',
          'travelokabni3ds_idr_in12',
          'travelokabni3ds_idr_in3',
          'travelokabni3ds_idr_in6',
          'travelokabni3ds_idr_phone',
          'travelokabni3ds_idr_supplier',
          'travelokabri_idr',
          'travelokabri3ds_idr',
          'travelokabri3ds2',
          'travelokacimb',
          'travelokacimb3ds',
          'travelokakasikorn_thb',
          'travelokakasikorn3ds_thb',
          'travelokakasikorn3ds_thb_in3',
          'travelokakbank3ds_thb_supplier',
          'travelokamandiri',
          'travelokamandiri_idr_aff',
          'travelokamandiri_idr_corp',
          'travelokamandiri3ds',
          'travelokamaybank_myr',
          'travelokamaybank3ds_myr',
          'travelokamaybank3ds_myr_in12',
          'travelokamaybank3ds_myr_in6',
          'travelokamaybank3ds_myr_vendor',
          'travelokaocbc_aud',
          'travelokaocbc_eur',
          'travelokaocbc3ds_aud',
          'travelokaocbc3ds_eur',
          'travelokaocbc3ds_sgd',
          'travelokaocbc3ds_usd',
          'travelokapaymaya_php',
          'travelokapaymaya3ds_php',
          'travelokavcb3ds_vnd',
          'travelokavietin_vnd',
          'travelokavietin3ds_vnd',
          'travelokavietin3ds_vnd_vendor',
          'travelokawirecard',
          'travelokawirecard3ds',
          'usana_indonesia',
          'xdt_afamily',
          'xen_orami',
          'xendit',
          'xendit_001990520000',
          'xendit_airfrov',
          'xendit_airpaz',
          'xendit_berrybenka',
          'xendit_bii009090166',
          'xendit_bii009090172',
          'xendit_bii009090173',
          'xendit_bii009090181',
          'xendit_blibli_bni_ins12mo',
          'xendit_blibli_bni_ins24mo',
          'xendit_blibli_bni_ins6mo',
          'xendit_blibli_bri',
          'xendit_blibli_bri_ins12mo',
          'xendit_blibli_bri_ins18mo',
          'xendit_blibli_bri_ins3mo',
          'xendit_blibli_bri_ins6mo',
          'xendit_blibli_ins3mo',
          'xendit_blibli_mandiri_reg',
          'xendit_blibli18bln',
          'xendit_blibliipg2',
          'xendit_bms',
          'xendit_bni_acm_ins_12mo',
          'xendit_bni_acm_ins_3mo',
          'xendit_bni_acm_ins_6mo',
          'xendit_bni_ecom_ins_12mo',
          'xendit_bni_ecom_ins_3mo',
          'xendit_bni_ecom_ins_6mo',
          'xendit_boga',
          'xendit_boga_dinasti_bni',
          'xendit_boga_intipangn_bni',
          'xendit_boga_mandiri_bni',
          'xendit_boga_prima_bni',
          'xendit_boga_wijaya_bni',
          'xendit_bookmyshow',
          'xendit_bouncestreetasia',
          'xendit_bri_acm_ins_12mo',
          'xendit_bri_acm_ins_3mo',
          'xendit_bri_acm_ins_6mo',
          'xendit_bri_ecom_ins_12mo',
          'xendit_bri_ecom_ins_3mo',
          'xendit_bri_ecom_ins_6mo',
          'xendit_bri_jewelry',
          'xendit_buka_bni_12mo',
          'xendit_buka_bni_18mo',
          'xendit_buka_bni_24mo',
          'xendit_buka_bni_3mo',
          'xendit_buka_bni_6mo',
          'xendit_buka_bni_full',
          'xendit_buka_bri_12mo',
          'xendit_buka_bri_3mo',
          'xendit_buka_bri_6mo',
          'xendit_buka_bri_full_non3ds',
          'xendit_buka_bri_fullpayment',
          'xendit_buka_cimb_12mo',
          'xendit_buka_cimb_24mo',
          'xendit_buka_cimb_3mo',
          'xendit_buka_cimb_6mo',
          'xendit_buka_cimb_full',
          'xendit_cashbac',
          'xendit_cashbac_bni',
          'xendit_cashbac_bni_installment',
          'xendit_cimb_vts_test',
          'xendit_cn910090481',
          'xendit_ctv_agg',
          'xendit_dlocalairlines',
          'xendit_dlocalapplications',
          'xendit_dlocalbooks',
          'xendit_dlocalcomputernetwork',
          'xendit_dlocalequipment',
          'xendit_dlocalgames',
          'xendit_dlocallargedigital',
          'xendit_dlocalmarketplaces',
          'xendit_dlocalmiscellaneous',
          'xendit_dlocalmulticategory',
          'xendit_dlocalshotel',
          'xendit_dlocaltravelagencies',
          'xendit_esb_bri',
          'xendit_fuse_mandiri_12mo',
          'xendit_fuse_mandiri_3mo',
          'xendit_fuse_mandiri_6mo',
          'xendit_fuse_mandiri_reg',
          'xendit_halodoc_bni',
          'xendit_halosis_bri_full',
          'xendit_happyfresh_001369790',
          'xendit_happyfresh_001369815',
          'xendit_happyfresh_2d',
          'xendit_happyfresh_3ds',
          'xendit_harvestcakes',
          'xendit_ifg_bni_3ds',
          'xendit_ifg_bni_recurring',
          'xendit_keikpop',
          'xendit_lemonilo',
          'xendit_mandiri_test',
          'xendit_mggroup_cimb',
          'xendit_paperid',
          'xendit_paperid_bni',
          'xendit_pasporsehat',
          'xendit_pc5',
          'xendit_pc5_2ds',
          'xendit_recurring',
          'xendit_storesircloipg',
          'xendit_testing',
          'xendit_travelio',
          'xendit_yummycorp',
          'xendit_yummycorp_bri',
          'xendit_zomato'
        )
        or merchant_id is null
      )
      group by 1, 2, 3, 4
      order by 1, 2, 3, 4
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: date_month {
    type: time
    sql: ${TABLE}.date_month ;;
  }

  dimension: merchant_id {
    type: string
    sql: ${TABLE}.merchant_id ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: card {
    type: string
    sql: ${TABLE}.card ;;
  }

  dimension: total {
    type: number
    sql: ${TABLE}.total ;;
  }

  set: detail {
    fields: [date_month_time, merchant_id, type, card, total]
  }
}
