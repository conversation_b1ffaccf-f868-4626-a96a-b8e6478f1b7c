view: billing_summary {
  sql_table_name: transform__datamarts.billing_summary ;;
  suggestions: no

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: data_last_updated {
    type: string
    sql: ${TABLE}.data_last_updated ;;
  }

  dimension: date_range {
    type: string
    sql: ${TABLE}.date_range ;;
  }

  dimension: fees_paid_amount_total {
    type: number
    sql: ${TABLE}.fees_paid_amount_total ;;
  }

  dimension: is_vat_enabled {
    type: yesno
    sql: ${TABLE}.is_vat_enabled ;;
  }

  dimension: quantity {
    type: number
    sql: ${TABLE}.quantity ;;
  }

  dimension: rounding_fees_paid_amount {
    type: number
    sql: ${TABLE}.rounding_fees_paid_amount ;;
  }

  dimension: sum_amount {
    type: number
    sql: ${TABLE}.sum_amount ;;
  }

  dimension: sum_fees {
    type: number
    sql: ${TABLE}.sum_fees ;;
  }

  dimension: sum_vat {
    type: number
    sql: ${TABLE}.sum_vat ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
