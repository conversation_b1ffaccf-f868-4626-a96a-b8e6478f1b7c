view: billing_rates {
  sql_table_name: clean__xendit_billing_service.billingrates ;;
  suggestions: no

  dimension: bank_account_data_request_rates {
    group_label: "Data products"
    type: number
    sql: ${TABLE}.bank_account_data_request_rates ;;
  }

  dimension: business_id {
    primary_key: yes
    hidden: yes
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: credit_card_rates {
    group_label: "Credit card rates"
    type: string
    sql: ${TABLE}.credit_card_rates ;;
  }

  dimension: disbursement_per_bank_rates {
    group_label: "Disbursement rates"
    type: string
    sql: ${TABLE}.disbursement_per_bank_rates ;;
  }

  dimension: disbursement_rates {
    group_label: "Disbursement rates"
    type: string
    sql: ${TABLE}.disbursement_rates ;;
  }

  dimension: disbursement_rates_type {
    group_label: "Disbursement rates"
    type: string
    sql: ${TABLE}.disbursement_rates_type ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: events {
    type: string
    sql: ${TABLE}.events ;;
  }

  dimension: ewallet_rates {
    group_label: "eWallet rates"
    type: string
    sql: ${TABLE}.ewallet_rates ;;
  }

  dimension: is_vat_enabled {
    type: yesno
    sql: ${TABLE}.is_vat_enabled ;;
  }

  dimension: retail_outlet_per_type_rates {
    group_label: "Retail outlet rates"
    type: string
    sql: ${TABLE}.retail_outlet_per_type_rates ;;
  }

  dimension: retail_outlet_rates {
    group_label: "Retail outlet rates"
    type: string
    sql: ${TABLE}.retail_outlet_rates ;;
  }

  dimension: should_create_monthly_billing_statement {
    type: yesno
    sql: ${TABLE}.should_create_monthly_billing_statement ;;
  }

  dimension: should_deduct_monthly {
    type: yesno
    sql: ${TABLE}.should_deduct_monthly ;;
  }

  dimension: switching_virtual_account_rates {
    group_label: "Virtual bank account rates"
    type: string
    sql: ${TABLE}.switching_virtual_account_rates ;;
  }

  dimension: unique_amount_rates {
    group_label: "Virtual bank account rates"
    type: string
    sql: ${TABLE}.unique_amount_rates ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  dimension: v {
    type: number
    sql: ${TABLE}.v ;;
  }

  dimension: vat_percentage {
    type: number
    sql: ${TABLE}.vat_percentage ;;
  }

  dimension: virtual_account_per_bank_rates {
    group_label: "Virtual bank account rates"
    type: string
    sql: ${TABLE}.virtual_account_per_bank_rates ;;
  }

  dimension: virtual_account_rates {
    group_label: "Virtual bank account rates"
    type: string
    sql: ${TABLE}.virtual_account_rates ;;
  }

  dimension: virtual_account_rates_type {
    group_label: "Virtual bank account rates"
    type: string
    sql: ${TABLE}.virtual_account_rates_type ;;
  }
}
