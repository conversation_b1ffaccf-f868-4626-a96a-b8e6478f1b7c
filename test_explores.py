#!/usr/bin/env python3
"""
Test script to validate .explore.lkml files for syntax and structure
"""

import os
import subprocess
import re
from pathlib import Path

DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def get_all_explore_files():
    """Get all .explore.lkml files in DBR repository"""
    result = subprocess.run(
        ["find", DBR_REPO, "-name", "*.explore.lkml"],
        capture_output=True, text=True
    )
    files = result.stdout.strip().split('\n') if result.stdout.strip() else []
    return [f for f in files if f]

def validate_file_syntax(file_path):
    """Basic syntax validation for LookML files"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check for basic LookML structure
        if not content.strip():
            issues.append("Empty file")
            return issues
        
        # Check for balanced braces
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            issues.append(f"Unbalanced braces: {open_braces} open, {close_braces} close")
        
        # Check for proper include statements
        include_lines = [line.strip() for line in content.split('\n') if line.strip().startswith('include:')]
        for line in include_lines:
            if not (line.endswith('"') and '"' in line[8:]):
                issues.append(f"Malformed include: {line}")
        
        # Check for explore definitions
        explore_matches = re.findall(r'explore:\s*([+]?\w+)', content)
        if not explore_matches:
            # Some files might only have includes, that's okay
            pass
        
        # Check for proper central-models-dbr references
        if '//central-models/' in content and '//central-models-dbr/' not in content:
            issues.append("Still contains old central-models references")
        
        return issues
        
    except Exception as e:
        return [f"Error reading file: {str(e)}"]

def test_sample_files():
    """Test a sample of files for more detailed validation"""
    sample_files = [
        "salesforce/salesforce.explore.lkml",
        "businesses/businesses.explore.lkml", 
        "transactions/transactions.explore.lkml",
        "growth/growth_team_dashboard.explore.lkml",
        "revenue/revenue.explore.lkml"
    ]
    
    print("🧪 DETAILED TESTING OF SAMPLE FILES:")
    print("-" * 50)
    
    for file in sample_files:
        file_path = os.path.join(DBR_REPO, file)
        if os.path.exists(file_path):
            issues = validate_file_syntax(file_path)
            if issues:
                print(f"  ❌ {file}:")
                for issue in issues:
                    print(f"    - {issue}")
            else:
                print(f"  ✅ {file}: OK")
        else:
            print(f"  ⚠️  {file}: File not found")

def main():
    print("🧪 COMPREHENSIVE .EXPLORE.LKM FILE TESTING")
    print("=" * 60)
    
    explore_files = get_all_explore_files()
    print(f"📊 Testing {len(explore_files)} .explore.lkml files...")
    
    total_files = len(explore_files)
    files_with_issues = 0
    total_issues = 0
    
    print(f"\n📋 SYNTAX VALIDATION RESULTS:")
    
    for file_path in explore_files:
        relative_path = os.path.relpath(file_path, DBR_REPO)
        issues = validate_file_syntax(file_path)
        
        if issues:
            files_with_issues += 1
            total_issues += len(issues)
            print(f"  ❌ {relative_path}:")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print(f"  ✅ {relative_path}: OK")
    
    print(f"\n📊 TESTING SUMMARY:")
    print(f"  📁 Total files tested: {total_files}")
    print(f"  ✅ Files without issues: {total_files - files_with_issues}")
    print(f"  ❌ Files with issues: {files_with_issues}")
    print(f"  🐛 Total issues found: {total_issues}")
    
    if files_with_issues == 0:
        print(f"\n🎉 ALL FILES PASSED VALIDATION!")
    else:
        print(f"\n⚠️  {files_with_issues} files need attention")
    
    # Test sample files in detail
    print(f"\n" + "=" * 60)
    test_sample_files()
    
    print(f"\n✅ TESTING COMPLETE!")

if __name__ == "__main__":
    main()
