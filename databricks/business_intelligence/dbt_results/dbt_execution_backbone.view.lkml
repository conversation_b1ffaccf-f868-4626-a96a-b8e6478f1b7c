include: "/business_intelligence/dbt_results/ag_views/dbt_run_results.view.lkml"

view: dbt_execution_backbone {

  ## Results of all dbt executions
  extends: [dbt_run_results]
  parameter: team_name {}

  dimension: pk_id {
    type: string
    description: "PK as a concatenation of model_execution_id and order of id execution"
    sql: model_execution_id || model_invocation_reverse_index ;;
    primary_key: yes
  }

  dimension: execution_duration {
    type: number
    sql: ${TABLE}.execution_time ;;
    description: "Execution duration in seconds"
    value_format: "0\" seconds\""
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
    description: "Name of resource as configured in DBT"
  }

  dimension: filter_for_team {
    type: yesno
    sql: {{ team_name._parameter_value }} in ${owner};;
  }

  dimension: resource_type {
    type: string
    sql: ${TABLE}.resource_type ;;
    description: "Refers to whether the resource was a Model or a Test"
  }

  dimension: rows_affected {
    hidden: yes
    type: number
    sql: ${TABLE}.rows_affected ;;
  }

  dimension: full_refresh {
    type: yesno
    sql: ${TABLE}.full_refresh ;;
    hidden: yes
  }

  dimension: is_full_refresh {
    type: yesno
    sql: ${full_refresh} ;;
    description: "Whether the model run was a full refresh"
  }

  # dimension: schema {
  #   type: string
  #   sql: regexp_extract(schema_name, 'transform__'|| {{team_name._parameter_value}} ||'_(.*?)(_dbt_|$)', 1)  ;;
  # }

  # dimension: model_and_field {
  #   type: string
  #   sql: regexp_extract(${name}, '(?<='||{{team_name._parameter_value}} ||'_).*') ;;
  #   description: "Name of the model being run"
  # }

  dimension: test_type {
    type: string
    sql: regexp_extract(name, '^(.*?)(?=_' || {{team_name._parameter_value}} || ')')  ;;
  }

  dimension: model_type {
    type: string
    sql: case
          when ${schema_name} like 'transform__%' then 'transform'
          when ${schema_name} like 'sources__%' then 'dbt_source'
          when ${schema_name} like 'dbt_seed%' then 'dbt_seed'
          when ${schema_name} like '%_dbt_test__audit%' then 'singular_dbt_test'
    else 'unknown' end;;
  }

  dimension_group: execution_start {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql:to_utc_timestamp(${TABLE}.execute_started_at,"UTC");;
  }

  dimension: execute_started_at {
    type: string
    sql: ${TABLE}.execute_started_at ;;
    hidden: yes
  }

  dimension_group: execution_complete {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql:to_utc_timestamp(${TABLE}.execute_completed_at,"UTC");;
  }

  dimension: execute_completed_at {
    type: string
    sql: ${TABLE}.execute_completed_at ;;
    hidden: yes
  }

  dimension: compile_started_at {
    type: string
    sql: ${TABLE}.compile_started_at ;;
    hidden: yes
  }

  dimension_group: compile_start {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql:to_utc_timestamp(${TABLE}.compile_started_at,"UTC") ;;
  }

  dimension: compile_completed_at {
    type: string
    sql: ${TABLE}.compile_completed_at ;;
    hidden: yes
  }

  dimension_group: compile_complete {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql:to_utc_timestamp(${TABLE}.compile_completed_at,"UTC");;
  }

  measure: latest_execution_completion_time {
    type: date_raw
    sql: max(${execution_complete_time}) ;;
    description: "Latest Execution Completion Datetime"
  }

  measure: average_execution_time {
    group_label: "Execution Duration"
    type: average
    sql: ${execution_time} ;;
    description: "Average time taken to execute the command in seconds"
  }

  measure: execution_duration_p99 {
    group_label: "Execution Duration"
    type: percentile
    percentile: 99
    sql: ${execution_duration} ;;
    value_format: "0.0 \"seconds\""
  }

  measure: execution_duration_p95 {
    group_label: "Execution Duration"
    type: percentile
    percentile: 95
    sql: ${execution_duration};;
    value_format: "0.0 \"seconds\""
  }

  measure: execution_duration_p90 {
    group_label: "Execution Duration"
    type: percentile
    percentile: 90
    sql: ${execution_duration};;
    value_format: "0.0 \"seconds\""
  }

  measure: execution_duration_p75 {
    group_label: "Execution Duration"
    type: percentile
    percentile: 75
    sql: ${execution_duration};;
    value_format: "0.0 \"seconds\""
  }

  measure: duration_p50 {
    group_label: "Execution Duration"
    type: percentile
    percentile: 50
    sql: ${execution_duration};;
    value_format: "0.0 \"seconds\""
  }

  measure: model_count {
    description: "Total unique number of models"
    type: count_distinct
    sql: ${unique_id} ;;
  }
}
