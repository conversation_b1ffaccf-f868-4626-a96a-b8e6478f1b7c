
include: "/businesses/behavior/*.view.lkml"
include: "/businesses/behavior/*/*.view.lkml"

explore: merchant_frontend_activity {
  from: biz_activity
  group_label: "PG Businesses"
  description: "Behavioral data on a merchant's frontend activity as seen on Xendit's web and mobile apps"

  always_filter: {
    filters: [businesses.is_internal: "no"]
  }

  query: 30days_since_first_transaction_behavior {
    dimensions: [businesses.country_of_operation, businesses.segment]
    description: "UI activity for merchants (per segment and country) in first 30 days after first transaction"
    measures: [aft_first_txn_30day_ui_activity.first_30day_avg_sessions, aft_first_txn_30day_ui_activity.invoice_affinity]
    filters: [
      business_facts.first_transaction_date: "NOT NULL",
      businesses.is_internal: "no",
      businesses.is_platform_subaccount: "No"
    ]
  }

  query: sessions_and_users_by_platform {
    description: "Number of mobile / web sessions and users per business"
    dimensions: [businesses.business_id, businesses.business_name]
    measures: [mobile_sessions, mobile_users, web_sessions, web_users]
    filters: [
      businesses.is_internal: "no",
      businesses.is_platform_subaccount: "No"
    ]
  }

  # Other explores can be joined too. These NEED NOT be at the same granularity as 1 row = 1 business ID

  # Following explore is 1:1
  join: aft_first_txn_30day_ui_activity {
    view_label: "30 Day UI activity after 1st Txn"
    type: left_outer
    relationship: one_to_one
    sql_on: ${merchant_frontend_activity.business_id} = ${aft_first_txn_30day_ui_activity.business_id} ;;
  }

  # Following explore is 1:M
  join: user_login_activity {
    view_label: "User Login Activity"
    type: left_outer
    relationship: one_to_many
    sql_on: ${merchant_frontend_activity.business_id} = ${user_login_activity.business_id} ;;
  }


  # Other business attributes can be joined as desired
  join: businesses {
    from: businesses
    type: left_outer
    sql_on: ${businesses.business_id} = ${merchant_frontend_activity.business_id};;
    relationship: one_to_one
  }

  join: business_marketing_attribution {
    from: business_marketing_attribution
    view_label: "Businesses - Marketing Attribution"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_marketing_attribution.business_id};;
    relationship: one_to_one
  }

  join : business_facts {
    from: business_facts
    view_label: "Business facts - By Business ID"
    type: left_outer
    sql_on: ${businesses.business_id} = ${business_facts.business_id} ;;
    relationship: one_to_one
  }

  join: pdt_channel_activation_timestamp {
    view_label: "Channel Activation Timestamps"
    type: left_outer
    sql_on: ${merchant_frontend_activity.business_id} = ${pdt_channel_activation_timestamp.business_id} ;;
    relationship: one_to_one
  }

  join: business_campaign_activity {
    view_label: "Campaign Activity"
    type: left_outer
    sql_on: ${merchant_frontend_activity.business_id} = ${business_campaign_activity.business_id} ;;
    relationship: one_to_one
  }

}
