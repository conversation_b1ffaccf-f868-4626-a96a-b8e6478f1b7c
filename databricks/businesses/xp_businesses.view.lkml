include: "businesses_base.view"
include: "xp_master_sub_account_mapping.view"
include: "ag_views/dim_masteraccount_facts.view"

view: xp_businesses {
  extends: [businesses_base]

  derived_table: {
    sql:
      SELECT b.*, cast(m.first_transaction_date as timestamp) as master_account_first_transaction
      FROM ${businesses_base.SQL_TABLE_NAME} b
      LEFT JOIN ${dim_masteraccount_facts.SQL_TABLE_NAME} m
      ON b.business_id = m.master_account_id
      WHERE is_platform_masteraccount ;;
  }

  set: xp_master_metrics {
    fields: [
      has_subaccount,
      subaccount_count,
      first_transaction_on_platform_level_time,
      first_transaction_on_platform_level_date,
      first_transaction_on_platform_level_week,
      first_transaction_on_platform_level_month,
      first_transaction_on_platform_level_quarter,
      first_transaction_on_platform_level_year
    ]
  }

  set: xp_master_fields {
    fields: [
      pk1_business_id,
      business_id,
      business_name,
      internal_name,
      business_email,
      website,
      segment,
      customer_segment,
      industry_sector,
      parent_industry_sector,
      entity_type,
      xendit_entity,
      sales_rep_name,
      account_manager_name,
      country_of_incorporation,
      country_of_operation,
      is_internal,
      count_internal_name,
      created_time,
      created_date,
      created_week,
      created_month,
      created_quarter,
      created_year,
      handover_time,
      handover_date,
      handover_week,
      handover_month,
      handover_quarter,
      handover_year,
      count
    ]
  }

  dimension: has_subaccount {
    description: "Whether the business is xenPlatform mater account or not"
    type: yesno
    sql: ${business_id} IN (SELECT distinct master_account_id FROM ${xp_master_sub_account_mapping.SQL_TABLE_NAME}) ;;
  }

  dimension_group: first_transaction_on_platform_level {
    description: "first transaction time on a master account level"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.master_account_first_transaction ;;
  }

  dimension: subaccount_count {
    type: number
    sql: (select count(*) from transform__business_intelligence.dim_businesses b where b.master_account_id = ${business_id});;
  }
}
