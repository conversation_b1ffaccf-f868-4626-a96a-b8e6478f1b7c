view: businesses_base {
    sql_table_name: transform__business_intelligence.dim_businesses ;;

################################################
# Primary keys {
################################################
    dimension: pk1_business_id {
      primary_key: yes
      hidden: yes
      sql: ${business_id} ;;
    }
################################################
# Primary keys }
################################################

################################################
# Parameters {
################################################
  parameter: selected_business {
    description: "Use this field to select a business for comparison vs its peers, in combination with the is_selected dimension"
    suggestable: yes
    suggest_dimension: pk1_business_id
    type: string
  }
################################################
# Parameters }
################################################

################################################
# Sets {
################################################
    set: business_identifiers {
      fields: [
        business_id,
        business_name,
        internal_name,
        business_email,
        website,
        segment,
        industry_sector
      ]
    }
################################################
# Sets }
################################################

################################################
# Dimensions {
################################################
    dimension: business_id {
      group_label: "Business Details"
      case_sensitive: yes
      link: {
        label: "Open in Admin Dashboard"
        url: "https://admin-dashboard.tidnex.com/customer-management/{{ value }}"
      }
      sql: ${TABLE}.business_id ;;
    }
    dimension: xendit_entity {
      group_label: "Business Details"
      description: "Xendit group entity where this business belongs to(Xendit VS Instamoney)"
      sql: ${TABLE}.entity_adjusted ;;
      suggestions: ["xendit", "instamoney"]
    }

    dimension: master_account_id {
      group_label: "Business Details"
      description: "The business ID of the xenplatform master account(if any)"
      label: "Business ID: xenPlatform Master"
    }

    dimension: is_platform_masteraccount {
      description: "Whether the business is xenPlatform mater account or not"
      type: yesno
      sql: ${TABLE}.is_platform_masteraccount ;;
    }

    dimension: internal_name {
      group_label: "Business Details"
      description: "The name we use to identify Business Name on Logo level"
      # In case a customer onboarded via another vendor, we put the vendor's name first, and customer's name later with '|' in between.
      # For example, an internal name can be "jubelio | midira beauty", if Midira beauty onboarded via Jubelio.
      # In internal name, we want to get only the vendor name, so that's why we are getting values up to | only.
      sql: lower(trim(regexp_extract(${TABLE}.internal_name,'^([^\|]+)',1))) ;;
      case_sensitive: no
    }

    dimension: business_name {
      group_label: "Business Details"
      sql: lower(${TABLE}.business_name) ;;
      # coalesce(${TABLE}.name_ccd, ${TABLE}.name_biz) ;;
      case_sensitive: no
    }

    dimension: business_email {
      group_label: "Business Details"
      sql: ${TABLE}.email ;;
      description: "The email used for registration"
    }

    dimension: country_of_incorporation {
      group_label: "Business Details"
      sql: lower(${TABLE}.country_of_incorporation_biz)  ;;
      case_sensitive: no
      description: "In which country a business is founded"
      suggestions: ["australia","belgium","brazil","british virgin island","china","cyprus","czech republic","digital marketing","finland","foreign",
        "france","hk, china","hong kong","hong kong (china)","hong kong china","hong kong, china","idonesia","indonesia","japan","kuwait",
        "malaysia","netherlands","payments","philippines","portugal","singapore","singapore & indonesia","switzerland","taiwan","taiwan (roc)",
        "taiwan, province of china","thailand","united kingdom","united states"]
    }

    dimension: country_of_operation {
      group_label: "Business Details"
      sql: ${TABLE}.country_of_operation ;;
      suggestions: ["indonesia", "philippines", "malaysia", "thailand", "vietnam", "singapore"]
    }

    dimension: entity_type {
      group_label: "Business Details"
      type: string
      sql: CASE WHEN ${country_of_operation} = 'philippines' THEN replace(replace(${TABLE}.entity_type_ccd, 'PH:', 'Philippines '), 'Foreign:', 'Foreign ')
                WHEN ${TABLE}.entity_type_ccd IN ('Indonesian Individual', 'Indonesian Entity', 'Foreign Individual', 'Foreign Entity') THEN ${TABLE}.entity_type_ccd
                WHEN ${country_of_incorporation} = 'indonesia' AND upper(${TABLE}.company_type_biz)  IN ('PT', 'CV', 'INDONESIAN_ENTITY', 'YAYASAN', 'PMA', 'KOPERASI') THEN 'Indonesian Entity'
                WHEN ${country_of_incorporation} IN ('indonesia', 'indoensia', 'indonsia') AND ${TABLE}.company_type_biz IN ('INDIVIDUALS', 'individuals', 'INDIVIDUAL') THEN 'Indonesian Individual'
                WHEN ${TABLE}.company_type_biz IN ('INDIVIDUALS', 'individuals', 'INDIVIDUAL') THEN 'Foreign Individual'
                WHEN upper(${TABLE}.company_type_biz)  IN ('LIMITED LIABILITY COMPANY', 'LIMITED LIABILITY', 'LIMITED LIABILITY PARTNERSHIP', 'SOLE PROPRIETORSHIP', 'FOREIGN_ENTITY') THEN 'Foreign Entity'
           ELSE 'Others' END ;;
      description: "Whether a business is entity or individual, and local or foreign"
      suggestions: ["Foreign Corporation", "Foreign Entity", "Foreign Individual", "Indonesian Entity", "Indonesian Individual", "Others", "Philippines Corporation",
        "Philippines Individual", "Philippines Partnership", "Philippines Sole Proprietorship"]
    }

    dimension: segment {
      description: "Enterprise, SME or Individual"
      type: string
      sql: CASE WHEN ${entity_type} LIKE '%Individual%' THEN 'Individual'
                WHEN ${TABLE}.segment is not null then  ${TABLE}.segment
                ELSE 'SME' END ;;
      suggestions: ["Enterprise", "Individual", "SME"]
    }

    dimension: customer_segment{
      description: "Enterprise, MM, SME or Individual"
      type: string
      #sql: ${TABLE}.businesses_segment ;;
      sql: CASE WHEN ${entity_type} LIKE '%Individual%' THEN 'Individual'
                WHEN ${TABLE}.ccd_segment is not null then  ${TABLE}.ccd_segment
                WHEN ${TABLE}.segment is not null then  ${TABLE}.segment
                ELSE 'SME' END ;;
      suggestions: ["Enterprise", "MM", "Individual", "SME"]
    }

    dimension: industry_sector {
      group_label: "Industry Sector"
      description: "Subset of the industry which the business operates in"
      case_sensitive: no
      sql: ${TABLE}.industry_sector ;;
    }

    dimension: parent_industry_sector {
      group_label: "Industry Sector"
      description: "Industries that businesses operate in like digital products, entertainment, non-profit etc"
      label: "Parent Industry Sector"
      case_sensitive: no
      sql: coalesce(lower(${TABLE}.parent_industry_sector), 'others')  ;;
      suggestions: ["digital products", "entertainment", "financial services", "non-profit", "others", "property", "restaurants", "retail", "services",
        "travel and hospitality"]
    }

    dimension: sales_rep_name {
      description: "Name of Sales person who handled the business"
      group_label: "Sales Team Information"
      type: string
      sql: ${TABLE}.sales_rep_name ;;
    }
    dimension: account_manager_name {
      description: "Name of business's account manager"
      group_label: "Sales Team Information"
      type: string
      case_sensitive: no
      sql: ${TABLE}.account_manager_name ;;
    }

    dimension: website {
      group_label: "Business Details"
      description: "The website of business"
      case_sensitive: no
      sql: ${TABLE}.website ;;
      html: "<a href={{value}} target="_blank">{{value}}</a>" ;;
    }

    dimension: is_internal {
      description: "An identifier for internal test accounts"
      type: yesno
      sql: ${TABLE}.is_internal = true ;;
    }

    dimension: is_selected {
      description: "Whether the business has been selected for comparison using the selected_business parameter"
      type: yesno
      sql: ${pk1_business_id} = {% parameter selected_business %} ;;
    }

    dimension_group: created {
      view_label: "Businesses - Activation data on Business ID level"
      description: "Time when the customer account is created - dashboard Sign-up moment"
      type: time
      timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    }

    dimension_group: updated {
      description: "Time when business was last updated"
      type: time
      timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
    }

    dimension_group: handover {
      group_label: "Sales Handover Date"
      description: "The handover date from Sales to Account Management"
      type: time
      timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
      sql: ${TABLE}.handover ;;
    }
################################################
# Dimensions }
################################################

################################################
# Measures {
################################################
    measure: count {
      group_label: "Count by Business ID"
      type: count_distinct
      sql:  ${TABLE}.business_id ;;
      drill_fields: [business_identifiers*]
    }

    measure: count_internal_name {
      group_label: "Count by Internal Name"
      label: "Business Count Internal Name"
      type: count_distinct
      sql: coalesce(${internal_name}, ${business_name});;
      drill_fields: [business_identifiers*]
    }
################################################
# Measures }
################################################
}
