view: incident_root_causes {
  derived_table: {
    sql: SELECT
        DATE_TRUNC('month', CAST(incident_date AS DATE)) AS incident_month,
        r.root_cause,
        i.severity_level,
        i.id as id,
        i.incident_name as incident_name,
        i.incident_date as incident_date,
        i.postmortem_link as postmortem_link
      FROM ${incidents.SQL_TABLE_NAME} AS i
      INNER JOIN ${root_causes.SQL_TABLE_NAME} AS r
      ON i.id = r.incident_id ;;
  }

  dimension_group: incident_month {
    type: time
    sql: ${TABLE}.incident_month ;;
    datatype: date
    timeframes: [date, month, year]
  }

  dimension_group: incident_date {
    type: time
    sql: ${TABLE}.incident_date ;;
    datatype: date
    timeframes: [date, month, year]
  }

  dimension: root_cause {
    type: string
    sql: ${TABLE}.root_cause ;;
  }

  dimension: severity_level {
    type: string
    sql: ${TABLE}.severity_level ;;
  }

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: incident_name {
    type: string
    sql: ${TABLE}.incident_name ;;
  }

  dimension: postmortem_link {
    type: string
    sql: ${TABLE}.postmortem_link ;;

    link: {
      label: "{{ value }}"
      url: "{{ value }}"
    }
  }

  dimension: is_internal_root_cause {
    type: number
    sql: CASE
      WHEN (${TABLE}.root_cause = 'LOGIC_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'DEPENDENCY_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'DEPLOYMENT_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'MANUAL_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'INFRA_CONFIGURATION_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'INFRA_CAPACITY_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'DATABASE_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'SCALABILITY_DESIGN_PROBLEM') THEN 1
      ELSE 0
    END ;;
  }

  dimension: is_external_root_cause {
    type: number
    sql: CASE
      WHEN (${TABLE}.root_cause = 'UNPLANNED_PARTNER_DOWNTIME') THEN 1
      WHEN (${TABLE}.root_cause = 'PARTNER_CAPACITY_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'UNPLANNED_PLATFORM_DOWNTIME') THEN 1
      WHEN (${TABLE}.root_cause = 'NETWORK_ERROR') THEN 1
      WHEN (${TABLE}.root_cause = 'EXPOSED_SECRET_ERROR') THEN 1
      ELSE 0
    END ;;
  }

  dimension: is_unknown_root_cause {
    type: number
    sql: CASE
      WHEN (${TABLE}.root_cause = 'OTHER') THEN 1
      ELSE 0
    END ;;
  }

  dimension: root_cause_category {
    type: string
    sql: CASE
      WHEN (${TABLE}.root_cause = 'LOGIC_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'DEPENDENCY_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'DEPLOYMENT_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'MANUAL_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'INFRA_CONFIGURATION_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'INFRA_CAPACITY_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'DATABASE_ERROR') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'SCALABILITY_DESIGN_PROBLEM') THEN 'Internal Root Cause'
      WHEN (${TABLE}.root_cause = 'UNPLANNED_PARTNER_DOWNTIME') THEN 'External Root Cause'
      WHEN (${TABLE}.root_cause = 'PARTNER_CAPACITY_ERROR') THEN 'External Root Cause'
      WHEN (${TABLE}.root_cause = 'UNPLANNED_PLATFORM_DOWNTIME') THEN 'External Root Cause'
      WHEN (${TABLE}.root_cause = 'NETWORK_ERROR') THEN 'External Root Cause'
      WHEN (${TABLE}.root_cause = 'EXPOSED_SECRET_ERROR') THEN 'External Root Cause'
      WHEN (${TABLE}.root_cause = 'OTHER') THEN 'Unknown Root Cause'
      ELSE 'Unknown Root Cause'
    END ;;
  }

  measure: total {
    type: count
    drill_fields: [root_causes_details*]
  }

  measure: number_of_internal_root_cause {
    type: sum
    sql: ${is_internal_root_cause} ;;
    filters: [
      is_internal_root_cause: "1",
    ]
    drill_fields: [root_causes_details*]
  }

  measure: number_of_external_root_cause {
    type: sum
    sql: ${is_external_root_cause} ;;
    filters: [
      is_external_root_cause: "1",
    ]
    drill_fields: [root_causes_details*]
  }

  measure: number_of_unknown_root_cause {
    type: sum
    sql: ${is_unknown_root_cause} ;;
    filters: [
      is_unknown_root_cause: "1",
    ]
    drill_fields: [root_causes_details*]
  }

  set: root_causes_details {
    fields: [
      id,
      incident_name,
      incident_date_date,
      postmortem_link,
      root_cause,
      severity_level,
    ]
  }
}