view: affected_incidents {
  derived_table: {
    sql: SELECT DISTINCT ap.incident_id as incident_id, ipt.team_name as team_name
      FROM ${affected_products.SQL_TABLE_NAME} AS ap
      INNER JOIN ${incidents_product_teams.SQL_TABLE_NAME} as ipt
      ON ap.product = ipt.product ;;
  }

  dimension: incident_id {
    type: string
    sql: ${TABLE}.incident_id ;;
  }

  dimension: team_name {
    type: string
    sql: ${TABLE}.team_name ;;
  }

  dimension: primary_key {
    primary_key: yes
    sql: CONCAT(${TABLE}.incident_id, ${TABLE}.team_name) ;;
  }
}