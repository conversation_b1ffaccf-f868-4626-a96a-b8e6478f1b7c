view: weekly_combined {
  # Or, you could make this view a derived table, like this:
  derived_table: {
    sql:

      WITH combined_table AS(
      SELECT a.business_id,
             a.transaction_period_started,
             a.product_types_used,
             a.the_number_of_products_used,
             a.segment_by_interface_usage,
             a.status,
             a.first_transaction_timestamp_in_the_period,
             a.transaction_period_index_asc,
             a.transaction_period_index_desc,
             b.tpv_usd,
             b.transaction_count

      FROM ${retention_analysis_weekly_backbone.SQL_TABLE_NAME} a
      LEFT JOIN ${retention_analysis_weekly_transactions.SQL_TABLE_NAME} b
      ON a.business_id = b.business_id AND a.transaction_period_started = b.transaction_week)

      SELECT business_id,
      transaction_period_started,
      product_types_used,
      the_number_of_products_used,
      segment_by_interface_usage,
      status,
      first_transaction_timestamp_in_the_period,
      transaction_period_index_asc,
      transaction_period_index_desc,
      tpv_usd,
      transaction_count
      FROM combined_table
      WHERE status != 'Churned'
      UNION ALL
      SELECT a.business_id,
      a.transaction_period_started,
      a.product_types_used,
      a.the_number_of_products_used,
      a.segment_by_interface_usage,
      a.status,
      a.first_transaction_timestamp_in_the_period,
      a.transaction_period_index_asc,
      a.transaction_period_index_desc,
      b.tpv_usd,
      b.transaction_count
      from combined_table a
      left join combined_table b on a.business_id = b.business_id and a.transaction_period_index_asc - 1 = b.transaction_period_index_asc
      WHERE a.status = 'Churned'
      ;;
  }
}
