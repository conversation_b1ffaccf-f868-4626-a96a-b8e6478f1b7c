view: xp_subaccount_growth_accounting_28d {
  sql_table_name: transform__looker_pdt_migration.xp_subaccount_growth_accounting_28d ;;
  suggestions: no

  dimension: how_many_periods_ago {
    type: number
    sql: ${TABLE}.how_many_periods_ago ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: the_number_of_subaccounts {
    type: number
    sql: ${TABLE}.the_number_of_subaccounts ;;
  }

  dimension_group: transaction_period_started {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.transaction_period_started ;;
  }

  dimension: xp_master_account {
    type: string
    sql: ${TABLE}.xp_master_account ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
