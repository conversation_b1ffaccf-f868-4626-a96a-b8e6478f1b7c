{"id": "bd17a0c3-d553-4321-a505-1357a7696f6a", "charge": {"id": "66d3e67c-0098-4f8a-bf53-7779a18c26d4", "amount": 1013.86, "basket": null, "status": "INACTIVE", "actions": {"qr_checkout_string": null, "mobile_web_checkout_url": "https://payments.gcash.com/gcash-cashier-web/1.2.1/index.html#/confirm?bizNo=20250305121212800110170315337156506&timestamp=1741169724090&sign=qc0YWqh2QpcqebjqLT5pnw86pP0AAMfWezTJN3t4VlXPGZuw4WL%2BzLwFHDBcgFLNczpRxLn9RFaLw8HVhNKlmaz8X7P2i9EM3LhHtctDfbDiAYSy6P4ATmqIIzeKHMiXa2%2BHECjl%2BMm3KC5zkwmDpStRsCVWphULokSw5SWP3EsP7zWbrJev%2BpUby%2F0IepuB0FDJGYkaUkS%2BkzG0FhjWLmIMEGQ5zZmvAruMvuO3v7kfb%2BR97ovFauSrnMJveVIG0aeals8GABuq5iaDLai9o3%2FHr4Z6HhcjR0BjXeo%2BGUS6F%2B1oh%2BDSwNXquCQRPvWn%2FTbqxlaHeK5w%2Ber2x0xFzw%3D%3D&merchantName=Billease&orderAmount=1013.86&pdCode=51051000101000100001&merchantid=217020000067389375136&queryInterval=10000&qrcode=GCSHWPV220250305121212800110170315337156506,217020000067389375136&expiryTime=898", "desktop_web_checkout_url": "https://payments.gcash.com/gcash-cashier-web/1.2.1/index.html#/confirm?bizNo=20250305121212800110170315337156506&timestamp=1741169724090&sign=qc0YWqh2QpcqebjqLT5pnw86pP0AAMfWezTJN3t4VlXPGZuw4WL%2BzLwFHDBcgFLNczpRxLn9RFaLw8HVhNKlmaz8X7P2i9EM3LhHtctDfbDiAYSy6P4ATmqIIzeKHMiXa2%2BHECjl%2BMm3KC5zkwmDpStRsCVWphULokSw5SWP3EsP7zWbrJev%2BpUby%2F0IepuB0FDJGYkaUkS%2BkzG0FhjWLmIMEGQ5zZmvAruMvuO3v7kfb%2BR97ovFauSrnMJveVIG0aeals8GABuq5iaDLai9o3%2FHr4Z6HhcjR0BjXeo%2BGUS6F%2B1oh%2BDSwNXquCQRPvWn%2FTbqxlaHeK5w%2Ber2x0xFzw%3D%3D&merchantName=Billease&orderAmount=1013.86&pdCode=51051000101000100001&merchantid=217020000067389375136&queryInterval=10000&qrcode=GCSHWPV220250305121212800110170315337156506,217020000067389375136&expiryTime=898", "mobile_deeplink_checkout_url": null}, "created": "2025-03-05T10:15:23.867187Z", "updated": "2025-03-05T10:15:43.862094Z", "currency": "PHP", "customer": null, "metadata": null, "business_id": "5f06b4393a73531402cea11a", "capture_now": true, "customer_id": "19fb2b10-2205-4ccf-a720-2340546fc573", "callback_url": "https://api.fdfc.io/cor-xendit-api/xendit/payments/callback", "channel_code": "PH_GCASH", "reference_id": "********", "request_source": "API_GATEWAY", "checkout_method": "ONE_TIME_PAYMENT", "channel_metadata": {"ovo_cash_amount": null, "ovo_point_amount": null}, "internal_metadata": null, "payment_method_id": "", "channel_properties": {"internal": {"redirection_url": {"cancel_redirect_url": "https://ewallet-service-live.xendit.co/redirect?token=7d30df911cf6ed52d974120bcc5f449ad59a9d04bb4fa2d8bba58f898709a93aacdd7ed34360fd6e3bb1d27c7a4a9411255b9eab6b519cf1eab509177132eb", "failure_redirect_url": "https://ewallet-service-live.xendit.co/redirect?token=1f89cce3aba559c2dd70b874800b92257be6b25139ef79c7e8bc859a35bd48d9a5596f63336f0e6dfb9d7b1b46d98e79f0ba906a7d61cebc854e9a3c7704dcf9", "pending_redirect_url": "https://ewallet-service-live.xendit.co/redirect?token=955e649d6a7d18f505e529535d099f53399f56277b13cb809237e6aed3f29b1edf82967dcd5b4021896b53448e7d27306cba93008a613f6f3c3f0c0e1a82ae36", "success_redirect_url": "https://ewallet-service-live.xendit.co/redirect?token=39b0d3942436329e5c2683485c23ee4c1dd6edf2236626cf7465ab041048ce85dc931601bd1e14cd6bd517e3257ff5e5c1d2d486d311af87c47f1776ff565f86"}}, "failure_redirect_url": "https://billease.ph/account/paynow/failed", "success_redirect_url": "https://billease.ph/account/paynow/success/?payment_id=********"}, "is_redirect_required": true, "payer_charged_amount": null, "shipping_information": null, "connector_reference_id": "cv428ep4ruekvut35m40", "payer_charged_currency": null}, "status": "COMPLETED", "capture": {"id": "bd17a0c3-d553-4321-a505-1357a7696f6a", "amount": 1013.86, "status": "SUCCEEDED", "created": "2025-03-05T10:15:43.851864Z", "paid_at": "2025-03-05T10:15:43.776Z", "updated": "2025-03-05T10:15:43.851864Z", "charge_id": "66d3e67c-0098-4f8a-bf53-7779a18c26d4", "voided_at": null, "fee_txn_id": null, "vat_amount": null, "channel_code": "PH_GCASH", "failure_code": null, "payment_mode": "DIRECT", "billing_amount": null, "holding_txn_id": null, "payment_detail": null, "settlement_date": "2025-03-07T10:15:43.776Z", "total_fee_amount": null, "settlement_txn_id": null, "partner_receipt_id": "20250305121212800110170315337156506", "payer_captured_amount": null, "payer_captured_currency": null}, "void_rules": {"allowed": true, "expires_at": "2025-03-05T15:50:00Z"}, "client_type": "API_GATEWAY", "channel_code": "PH_GCASH", "refund_rules": {"allowed": true, "expires_at": "2025-09-01T10:15:43.851864Z", "partial_refund_allowed": true, "partial_refund_allowed_at": "2025-03-05T10:15:43.851864Z"}, "internal_metadata": null, "accounting_reconciliation_identifiers": {"country": "PH", "event_date": "2025-03-05T10:15:23.867187Z", "partner_name": "GCASH", "reference_id": "cv428ep4ruekvut35m40", "connector_code": "GCASH", "settlement_bank_name": "UBP", "settlement_bank_account_number": "************"}}