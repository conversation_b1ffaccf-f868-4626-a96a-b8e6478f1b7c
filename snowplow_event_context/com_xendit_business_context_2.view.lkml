view: com_xendit_business_context_2 {
  sql_table_name: clean__s3_snowplow.com_xendit_business_context_2 ;;
  suggestions: no

  dimension: account_type {
    type: string
    sql: ${TABLE}.account_type ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: email {
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: entity {
    type: string
    sql: ${TABLE}.entity ;;
  }

  dimension: live_status {
    type: string
    sql: ${TABLE}.live_status ;;
  }

  dimension: root_id {
    type: string
    sql: ${TABLE}.root_id ;;
  }

  dimension_group: root_tstamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.root_tstamp ;;
  }

  dimension: xenplatform_master_account_id {
    type: string
    sql: ${TABLE}.xenplatform_master_account_id ;;
  }

  measure: count {
    type: count
    drill_fields: []
  }
}
