include: "//central-models-dbr/0_assets/fx_rates.view"
include: "//central-models-dbr/businesses/businesses.view"
include: "./*.view.lkml"

explore: rdl_consolidated {
  group_label: "Escrow - RDL"
  label: "RDL Consolidated"
  from: rdl_consolidated
  always_filter: {
    filters: [businesses.is_internal: "no",
      businesses.is_soft_deleted_account: "no",
      rdl_consolidated.internal_status: "RDL_TRANSFER_COMPLETED, SETTLED, REVERSED",
      rdl_consolidated.created_date: "13 months"]
  }

  join: fx_rates {
    relationship: many_to_one
    sql_on: case when {{fx_rates.use_current_year_fx._parameter_value}} then year(CURRENT_DATE) else ${rdl_consolidated.created_year} end = ${fx_rates.year}
    and ${rdl_consolidated.currency} = ${fx_rates.currency} ;;
  }

  join: businesses {
    sql_on: ${businesses.business_id} = ${rdl_consolidated.business_id} ;;
    relationship: many_to_one
    type :  inner
  }
}
