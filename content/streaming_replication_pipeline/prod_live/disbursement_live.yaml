source_group: data_engineering
registra_type: STREAMING_REPLICATION
source_name: disbursement_live
data_config:
  xendit-withdrawal-service:
    tables:
      directdisbursements:
        topic: production.live.public.payouts.cdc.xendit-withdrawal-service.directdisbursements
        kafka_bootstrap_servers: /STREAMING/PRODUCT_LIVE_KAFKA_BOOTSTRAP_SERVERS
        kafka_sasl_auth: /STREAMING/PRODUCT_LIVE_KAFKA_AUTH
        kafka_sasl_mechanism: SCRAM-SHA-512
        kafka_security_protocol: sasl_ssl
        schema_type: json
        schema_str: '{"fields":[{"metadata":{},"name":"__v","nullable":true,"type":"long"},{"metadata":{},"name":"_id","nullable":true,"type":"string"},{"metadata":{},"name":"account_holder_name","nullable":true,"type":"string"},{"metadata":{},"name":"account_number","nullable":true,"type":"string"},{"metadata":{},"name":"amount","nullable":true,"type":"long"},{"metadata":{},"name":"bank_code","nullable":true,"type":"string"},{"metadata":{},"name":"bank_reference","nullable":true,"type":"string"},{"metadata":{},"name":"bca_switcher_disbursement","nullable":true,"type":"boolean"},{"metadata":{},"name":"channel_code","nullable":true,"type":"string"},{"metadata":{},"name":"completed_transaction_id","nullable":true,"type":"string"},{"metadata":{},"name":"created","nullable":true,"type":"string"},{"metadata":{},"name":"disbursement_description","nullable":true,"type":"string"},{"metadata":{},"name":"disbursement_fulfillment_type","nullable":true,"type":"string"},{"metadata":{},"name":"disbursement_id","nullable":true,"type":"string"},{"metadata":{},"name":"entity","nullable":true,"type":"string"},{"metadata":{},"name":"external_id","nullable":true,"type":"string"},{"metadata":{},"name":"is_instant","nullable":true,"type":"boolean"},{"metadata":{},"name":"ledger_account_id","nullable":true,"type":"string"},{"metadata":{},"name":"processor_holder_name","nullable":true,"type":"string"},{"metadata":{},"name":"sender_bank_code","nullable":true,"type":"string"},{"metadata":{},"name":"should_direct_write_to_t4","nullable":true,"type":"boolean"},{"metadata":{},"name":"should_prioritize_disbursement","nullable":true,"type":"boolean"},{"metadata":{},"name":"signing_entity","nullable":true,"type":"string"},{"metadata":{},"name":"status","nullable":true,"type":"string"},{"metadata":{},"name":"status_updated","nullable":true,"type":"long"},{"metadata":{},"name":"transacting_entity","nullable":true,"type":"string"},{"metadata":{},"name":"transaction_id","nullable":true,"type":"string"},{"metadata":{},"name":"transaction_sequence","nullable":true,"type":"string"},{"metadata":{},"name":"updated","nullable":true,"type":"string"},{"metadata":{},"name":"user_id","nullable":true,"type":"string"},{"metadata":{},"name":"uuid","nullable":true,"type":"string"},{"metadata":{},"name":"xendit_fee_user_id","nullable":true,"type":"string"},{"metadata":{},"name":"xenplatform_platform_id","nullable":true,"type":"string"}],"type":"struct"}'
        upsert_key: ["_id"]
  disbursement-service:
    tables:
      disbursement:
        topic: production.live.public.payouts.cdc.disbursement-service.disbursement
        kafka_bootstrap_servers: /STREAMING/PRODUCT_LIVE_KAFKA_BOOTSTRAP_SERVERS
        kafka_sasl_auth: /STREAMING/PRODUCT_LIVE_KAFKA_AUTH
        kafka_sasl_mechanism: SCRAM-SHA-512
        kafka_security_protocol: sasl_ssl
        schema_type: json
        schema_str: '{"fields":[{"metadata":{},"name":"payload","nullable":true,"type":{"fields":[{"metadata":{},"name":"account_holder_name","nullable":true,"type":"string"},{"metadata":{},"name":"account_name","nullable":true,"type":"string"},{"metadata":{},"name":"account_number","nullable":true,"type":"string"},{"metadata":{},"name":"account_type","nullable":true,"type":"string"},{"metadata":{},"name":"amount","nullable":true,"type":"string"},{"metadata":{},"name":"beneficiary","nullable":true,"type":"string"},{"metadata":{},"name":"callback_status","nullable":true,"type":"string"},{"metadata":{},"name":"callback_url","nullable":true,"type":"string"},{"metadata":{},"name":"channel_category","nullable":true,"type":"string"},{"metadata":{},"name":"channel_code","nullable":true,"type":"string"},{"metadata":{},"name":"client_type","nullable":true,"type":"string"},{"metadata":{},"name":"connector_code","nullable":true,"type":"string"},{"metadata":{},"name":"connector_reference","nullable":true,"type":"string"},{"metadata":{},"name":"created","nullable":true,"type":"long"},{"metadata":{},"name":"currency","nullable":true,"type":"string"},{"metadata":{},"name":"description","nullable":true,"type":"string"},{"metadata":{},"name":"disbursement_routing_id","nullable":true,"type":"string"},{"metadata":{},"name":"email_bcc","nullable":true,"type":"string"},{"metadata":{},"name":"email_cc","nullable":true,"type":"string"},{"metadata":{},"name":"email_to","nullable":true,"type":"string"},{"metadata":{},"name":"entity","nullable":true,"type":"string"},{"metadata":{},"name":"estimated_arrival_time","nullable":true,"type":"long"},{"metadata":{},"name":"expires_at","nullable":true,"type":"string"},{"metadata":{},"name":"failure_code","nullable":true,"type":"string"},{"metadata":{},"name":"id","nullable":true,"type":"string"},{"metadata":{},"name":"idempotency_key","nullable":true,"type":"string"},{"metadata":{},"name":"internal_metadata","nullable":true,"type":"string"},{"metadata":{},"name":"ledger_account_id","nullable":true,"type":"string"},{"metadata":{},"name":"metadata","nullable":true,"type":"string"},{"metadata":{},"name":"partner_details","nullable":true,"type":"string"},{"metadata":{},"name":"payout_code","nullable":true,"type":"string"},{"metadata":{},"name":"purpose_code","nullable":true,"type":"string"},{"metadata":{},"name":"recipient_given_names","nullable":true,"type":"string"},{"metadata":{},"name":"recipient_surname","nullable":true,"type":"string"},{"metadata":{},"name":"reference_id","nullable":true,"type":"string"},{"metadata":{},"name":"sender","nullable":true,"type":"string"},{"metadata":{},"name":"sending_entity","nullable":true,"type":"string"},{"metadata":{},"name":"signing_entity","nullable":true,"type":"string"},{"metadata":{},"name":"source_of_funds","nullable":true,"type":"string"},{"metadata":{},"name":"status","nullable":true,"type":"string"},{"metadata":{},"name":"transacting_entity","nullable":true,"type":"string"},{"metadata":{},"name":"transaction_id","nullable":true,"type":"string"},{"metadata":{},"name":"transaction_management","nullable":true,"type":"string"},{"metadata":{},"name":"type","nullable":true,"type":"string"},{"metadata":{},"name":"updated","nullable":true,"type":"long"},{"metadata":{},"name":"user_id","nullable":true,"type":"string"},{"metadata":{},"name":"version","nullable":true,"type":"long"}],"type":"struct"}}],"type":"struct"}'
        upsert_key: ["payload.id"]
        stream_source_type: MONGO_CDC_NO_OP
  
