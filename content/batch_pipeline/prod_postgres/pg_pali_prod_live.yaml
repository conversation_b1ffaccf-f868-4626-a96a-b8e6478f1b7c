registra_type: REPLICATION
data_config:
  payment_account_linking_intent_service:
    tables:
      intent:
        team: recurring
        backfill_filters:
        - backfill_id: updated
          backfill_days: 30
      linking_history:
        team: recurring
        etl_mode: FULL_RELOAD
        is_paginated: true
        pagination_key: id
source_group: prod_postgres
source_name: pg_pali_prod_live
source_type: postgres
cron_schedule: 0 18 * * *
