source_group: prod_postgres
registra_type: REPLICATION
source_name: PG_XENDIT_TPI_SERVICE
source_type: postgres
cron_schedule: 0 18 * * *
data_config:
  xendit-store-tools-live:
    tables:
      item_option_values:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      item_options:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      item_variants:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      item_variants_item_option_values:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      items:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      store_channel_syncs:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
      store_channels:
        etl_mode: FULL_RELOAD
        archive_config:
          mode: STATIC
        schema_configs:
          fields_to_remove: ['data']
