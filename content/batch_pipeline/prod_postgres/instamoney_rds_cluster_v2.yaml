registra_type: REPLICATION
source_name: instamoney_rds_cluster_v2
source_type: postgres
source_group: prod_postgres
cron_schedule: 0 18 * * *
data_config:
  rdl-service:
    tables:
      rdl_disbursements:
        backfill_filters:
          - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
            - col_name: 'created'
              col_alias: 'dt'
              col_cast_method: built_in.to_timestamp
              type: 'APPEND'
        dqc_key: created
      rdl_payments:
        backfill_filters:
          - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
            - col_name: 'created'
              col_alias: 'dt'
              col_cast_method: built_in.to_timestamp
              type: 'APPEND'
