registra_type: REPLICATION
data_config:
  fraud_ops_production:
    tables:
      bank_acc_response:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: response_at
            col_alias: response_at
            col_cast_method: general.second_to_timestamp
            type: IN_PLACE
      va_invoice_response:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: response_at
            col_alias: response_at
            col_cast_method: general.second_to_timestamp
            type: IN_PLACE
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_timestamp
            type: IN_PLACE
      ekata:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      kyc_blacklist:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: updated_at
            col_alias: updated_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      risk_score:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      va_blacklist:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      virtual_account:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      risk_evaluation_individual:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: created
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: updated
            col_alias: updated
            col_cast_method: general.second_to_date
            type: IN_PLACE
      risk_evaluation_merchant_access:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: created
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: updated
            col_alias: updated
            col_cast_method: general.second_to_date
            type: IN_PLACE
      merchant_event:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: updated_at
            col_alias: updated_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: event_received_time
            col_alias: event_received_time
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: go_live_date
            col_alias: go_live_date
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: registration_date
            col_alias: registration_date
            col_cast_method: general.second_to_date
            type: IN_PLACE
      ewallet_validator:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      va_client_response:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: response_at
            col_alias: response_at
            col_cast_method: general.second_to_timestamp
            type: IN_PLACE
      blacklist_check:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      internal_blacklist:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: registration_date
            col_alias: registration_date
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: updated_at
            col_alias: updated_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      fraud_detection:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: updated_at
            col_alias: updated_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: registration_date
            col_alias: registration_date
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: go_live_date
            col_alias: go_live_date
            col_cast_method: general.second_to_date
            type: IN_PLACE
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      identity_risk:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      external_biz_name:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      internal_device_fp_blacklist:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      bank_account_blacklist:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      country:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      bank_validation:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: created_at
            col_cast_method: general.second_to_date
            type: IN_PLACE
      risk_evaluation_service:
        etl_mode: FULL_RELOAD
      risk_evaluation_plan_to_service:
        etl_mode: FULL_RELOAD
  engr_ops_prod:
    tables:
      persons:
        etl_mode: FULL_RELOAD
        schema_configs:
          source_column_transformations:
          - col_name: level
            col_cast_expr: level::varchar
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
      teams:
        backfill_filters:
        - backfill_id: updated_at
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
      roles:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
      person_teams:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
      ptrs:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
  merchant-verification-service:
    tables:
      suspicion:
        backfill_filters:
        - backfill_id: created_at
      screening_event_details:
        backfill_filters:
          - backfill_id: updated_at
      merchant_scan:
        team: fraud-risk-operations
        backfill_filters:
        - backfill_id: updated_at
source_group: prod_postgres
source_name: PG_OPS_AUTOMATION_PRODUCTION_LIVE_NEW
source_type: postgres
cron_schedule: 0 18 * * *
