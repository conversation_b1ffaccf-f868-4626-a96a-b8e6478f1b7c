registra_type: REPLICATION
data_config:
  globalprime:
    tables:
      merchant_info:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - bank_account
        - citizen_id
        - name
        - name_tax
        - surname
        - secret_key
        - token
        - public_key
        - secret_key_new
        allow_pii_access_to:
          - "gbp-thailand"
      merchant_info_additional:
        backfill_filters:
        - backfill_id: update_date
        upsert_key:
        - merchant_id
      payment_infomation: # typo in gb database table name
        schema_configs:
          lakehouse_column_transformations_udf:
            - col_name: create_by
              col_alias: create_by
              col_cast_method: built_in.to_timestamp
              type: IN_PLACE
            - col_name: create_date
              col_alias: create_date
              col_cast_method: built_in.to_timestamp
              type: IN_PLACE
            - col_name: update_by
              col_alias: update_by
              col_cast_method: built_in.to_timestamp
              type: IN_PLACE
            - col_name: update_date
              col_alias: update_date
              col_cast_method: built_in.to_timestamp
              type: IN_PLACE
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - customer_email
        - customer_name
        - customer_telephone
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - ptx_id
      transfer_due_of_manual_history:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - acc_holder_name
        - bank_account
        - customer_email
        allow_pii_access_to:
          - "gbp-thailand"
      scb_host_2_host:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - password
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - ptx_id
      payment_transaction_directdebit:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - account_token
        - account_no
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - ptx_id
      payment_transaction_mobile_banking:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
        - mobile_no
        - payer_account_name
        - payer_account_number
        - payee_account_number
        - payer_name
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - ptx_id
      payment_transaction: # need manual replication from 2023-01-01
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - customer_email
          - customer_name
          - customer_ip_address
        allow_pii_access_to:
          - "gbp-thailand"
      gateway_channel:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - private_key
          - cert_file_password
          - merchant_token
          - password
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - code
      true_wallet:
        etl_mode: FULL_RELOAD
      md_description:
        etl_mode: FULL_RELOAD
      xendit_business_merchant_mapping:
        etl_mode: FULL_RELOAD
      payment_resp_rt:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - customer_email
          - cvv2
          - invoice_no
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - ptx_id
      users:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - password
          - phone_number
          - name
          - parent_username
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - username
      cogs_configuration:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - partner
          - payment_channel
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - id
      transfer_due_of_day:
        backfill_filters:
        - backfill_id: update_date
        upsert_key:
        - id
      payment_transaction_qr_visa:
        backfill_filters:
        - backfill_id: create_date
        pii_columns:
          - consumer_pan
          - consumer_name
          - other_phone_number
          - other_email_address
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - id
      payment_transaction_qr_wechat:
        backfill_filters:
        - backfill_id: update_date
        upsert_key:
        - id
      gateway_channel_directdebit:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - app_secret
          - partner_key
          - signature_key
          - encryption_key
          - decryption_key
          - tax_id
          - bay_api_key
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - code
      cimbgateway:
        backfill_filters:
        - backfill_id: update_date
        pii_columns:
          - encrypted_proxyid
          - senderid
        allow_pii_access_to:
          - "gbp-thailand"
        upsert_key:
        - id
      gateway_info:
        etl_mode: FULL_RELOAD
      bank:
        etl_mode: FULL_RELOAD
      business_type:
        etl_mode: FULL_RELOAD
      partner_info:
        backfill_filters:
        - backfill_id: update_date
        upsert_key:
        - id
      workflow:
        backfill_filters:
        - backfill_id: create_date
        upsert_key:
        - id
source_group: prod_postgres
source_name: gbp_main_prod_cluster
source_type: postgres
cron_schedule: 0 18 * * *
