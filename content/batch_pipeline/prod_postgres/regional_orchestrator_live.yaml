registra_type: REPLICATION
data_config:
  regional-orchestrator-live:
    tables:
      transaction_events:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      fee:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      batch_trade_schedule:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      batch_trade_instructions:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      trade_instructions:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      merchant_bank_accounts:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        upsert_key:
        - business_id
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      merchant_accounts:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        upsert_key:
        - business_id
      merchant_xendit_beneficiaries:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        upsert_key:
        - business_id
        - currency_code
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      transactions:
        team: regional
        backfill_filters:
        - backfill_id: updated_at
        dqc_key: created_at
        source_dqc_configs:
          run_quality_check: false
          include_last_x_days: 7
      conversions:
        team: regional-product
        backfill_filters:
        - backfill_id: updated_at
      internal_trades:
        team: regional-product
        backfill_filters:
        - backfill_id: updated_at
source_group: prod_postgres
source_name: regional_orchestrator_live
source_type: postgres
cron_schedule: 0 18 * * *
