source_group: prod_postgres
registra_type: REPLICATION
source_name: CONVOY_WEBHOOK_SYSTEM
source_type: postgres
cron_schedule: 0 18 * * *
data_config:
  convoy-webhook-system:
    tables:
      convoy.convoy_events_view:
        backfill_filters:
        - backfill_id: updated_at
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created_at
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND