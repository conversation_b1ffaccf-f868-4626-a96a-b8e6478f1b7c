registra_type: REPLICATION
source_name: PG_API_CRYPTOGRAPHIC_PROD_LIVE
source_type: postgres
source_group: prod_postgres
cron_schedule: 0 18 * * *
data_config:
  api-cryptograhic-signatures-live:
    tables:
      xnd_asymmetric_keys:
        backfill_filters:
        - backfill_id: created
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
