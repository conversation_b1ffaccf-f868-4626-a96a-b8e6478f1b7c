registra_type: REPLICATION
data_config:
  payment-method-service:
    tables:
      payment_method:
        etl_mode: FULL_RELOAD
  payment-settings:
    tables:
      merchant_routing_rules:
        tbl_name_alias: merchant_routing_rules_dev
        team: central-payments
        backfill_filters:
        - backfill_id: updated_at
source_group: prod_postgres
source_name: payment_settings
is_active: false
source_type: postgres
cron_schedule: 0 18 * * *
