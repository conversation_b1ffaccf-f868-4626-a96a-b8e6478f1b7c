registra_type: REPLICATION
source_name: remittance_rds_cluster_de_priority
source_type: postgres
source_group: prod_postgres_priority
cron_schedule: 0 18 * * *
data_config:
  remittance-money-in-service:
    tables:
      virtual_account:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
        alert:
          airflow:
          - channel: recon-auto-alert
            subscribers:
              users:
              - <EMAIL>
              - <EMAIL>
              usergroups:
              - recon-auto-onshift
      virtual_account_payment:
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
        alert:
          airflow:
          - channel: recon-auto-alert
            subscribers:
              users:
              - <EMAIL>
              - <EMAIL>
              usergroups:
              - recon-auto-onshift
