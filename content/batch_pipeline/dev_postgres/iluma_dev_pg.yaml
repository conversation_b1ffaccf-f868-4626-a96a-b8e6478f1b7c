registra_type: REPLICATION
source_group: dev_postgres
source_name: ILUMA_DEV_PG
source_type: postgres
cron_schedule: 0 18 * * *
data_config:
  identity_service_v2_dev:
    tables:
      client:
        team: iluma
        etl_mode: FULL_RELOAD
      end_customer:
        team: iluma
        grant_read_to:
        - credit-card-user
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
        upsert_key:
        - end_customer_id
        pii_columns:
        - surname_non_roman
        - place_of_birth
        - date_of_birth
        - surname
        - given_names
        - phone_number
        - mobile_number
        - email
        - given_names_non_roman
        - surname_non_roman
        - place_of_birth
        - date_of_birth
        - gender
        - employment
        - occupation
        - mother_maiden_name
        allow_pii_access_to:
        - "iluma-team"
        - "pii-users"
