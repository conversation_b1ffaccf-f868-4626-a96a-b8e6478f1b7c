registra_type: REPLICATION
source_name: PG_BANKING_PROD_DEV
source_type: postgres
source_group: dev_postgres
cron_schedule: 0 18 * * *
data_config:
  bank-dev:
    tables:
      baas.accounts:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        run_quality_check: true
        upsert_key: [ 'id' ]
        schema_configs:
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
      baas.customers:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
      angkas.customers:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
      billease.customers:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
      billease.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
      xpi.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
      xri.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
      jitta.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
      tala.accounts:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
      tala.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
      flowaccount.statement_entries:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL
        upsert_key:
        - id
        - direction
  ach-adapter-dev:
    tables:
      phbananafintech.transactions:
        backfill_filters:
          - backfill_id: updated
        data_format: DELTA
        etl_mode: INCREMENTAL