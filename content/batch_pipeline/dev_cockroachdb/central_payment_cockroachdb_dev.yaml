registra_type: REPLICATION
source_name: central_payment_cockroachdb_dev
source_type: cockroachdb
source_group: dev_cockroachdb
cron_schedule: 0 15 * * *
data_config:
  unified_payments_data_dev:
    tables:
      refund_requests:
        etl_mode: FULL_RELOAD
        team: payments-api
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        scheduling:
          num_workers: 0
      receipts:
        etl_mode: FULL_RELOAD
        team: payments-api
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        scheduling:
          num_workers: 0
      payment_requests:
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        backfill_filters:
        - backfill_id: updated
        upsert_key:
        - charge_id
        dqc_key: created
        scheduling:
          num_workers: 0
      notifications:
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        backfill_filters:
        - backfill_id: updated
          backfill_days: 2
        scheduling:
          num_workers: 0
        run_quality_check: false
      captures:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
          scheduling:
            num_workers: 0
      cards_payment_methods:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
          scheduling:
            num_workers: 0
      channel_info:
        etl_mode: FULL_RELOAD
        scheduling:
          num_workers: 0
      cryptocurrency_payment_methods:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        scheduling:
          num_workers: 0
      direct_bank_transfers_payment_methods:
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        upsert_key:
        - payment_method_id
        archive_config:
          mode: STATIC
      direct_debits_payment_methods:
        run_quality_check: false
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        upsert_key:
        - payment_method_id
        scheduling:
          num_workers: 0
      ewallets_payment_methods:
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        upsert_key:
        - payment_method_id
        scheduling:
          num_workers: 0
        run_quality_check: false
      over_the_counter_payment_methods:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        scheduling:
          num_workers: 0
      payment_channels_webhook:
        etl_mode: FULL_RELOAD
        scheduling:
          num_workers: 0
      payment_methods:
        backfill_filters:
        - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        upsert_key:
        - id
        dqc_key: created
        archive_config:
          mode: STATIC
      qr_codes_payment_methods:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: built_in.to_timestamp
            type: APPEND
        scheduling:
          num_workers: 0
      virtual_accounts_payment_methods:
        etl_mode: FULL_RELOAD
        schema_configs:
          lakehouse_column_transformations_udf:
          - col_name: created
            col_alias: dt
            col_cast_method: general.datetime_to_date
            type: APPEND
        scheduling:
          num_workers: 0
