registra_type: REPLICATION
source_name: cc_cluster_staging
source_type: mongo
source_group: dev_mongo
cron_schedule: 0 15 * * *
data_config:
  credit-card-staging:
    tables:
      authenticatedcreditcardtokens:
        dqc_key: created
        run_quality_check: false
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        schema_configs:
          fields_to_remove: ['id']  # dropping the original ID column, using `_id` as `id`
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
      authorizations:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_casts:
            - col_name: amount
              cast_to: long
            - col_name: risk_score
              cast_to: double
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        run_quality_check: false
        grant_read_to: ['credit-card-user']
        backfill_filters:
          - backfill_id: updated
      bindatas:
        team: credit-card
        etl_mode: FULL_RELOAD
        grant_read_to: ['credit-card-user']
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
      creditcardcharges:
        dqc_key: created
        run_quality_check: false
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to:
        - credit-card-user
        - risk-automation-team
        schema_configs:
          fields_to_remove: ['id']
          schema_file_column_casts:
            - col_name: authorization_response
              cast_to: string
            - col_name: order
              cast_to: string
            - col_name: settlement_date
              cast_to: string
          lakehouse_column_transformations_udf:
            - col_name: settlement_date
              col_alias: settlement_date
              col_cast_method: general.mongo_str_to_ts
              type: IN_PLACE
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
      creditcardrefunds:
        team: credit-card
        run_quality_check: false
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
      creditcardtokens:
        team: credit-card
        run_quality_check: false
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
