registra_type: REPLICATION
source_name: ATLAS_CLUSTER_SHARD_04
source_type: mongo
source_group: prod_mongo
cron_schedule: 0 18 * * *
data_config:
  xendit-go-live-service:
    tables:
      bcavaonboardings:
        archive_config:
          mode: STATIC
        backfill_filters:
          - backfill_id: updated
        partition_keys:
          - order: 1
            col_name: "created"
            col_type: "timestamp"
            col_alias: "dt"
            col_cast_method: general.datetime_to_date
      companydetails:
        archive_config:
          mode: STATIC
        backfill_filters:
          - backfill_id: updated
        partition_keys:
          - order: 1
            col_name: "created"
            col_type: "timestamp"
            col_alias: "dt"
            col_cast_method: general.datetime_to_date
      onboardinginformations:
        archive_config:
          mode: STATIC
        backfill_filters:
          - backfill_id: updated
        partition_keys:
          - order: 1
            col_name: "created"
            col_type: "timestamp"
            col_alias: "dt"
            col_cast_method: general.datetime_to_date
      ovoonboardings:
        archive_config:
          mode: STATIC
        backfill_filters:
          - backfill_id: updated
        partition_keys:
          - order: 1
            col_name: "created"
            col_type: "timestamp"
            col_alias: "dt"
            col_cast_method: general.datetime_to_date
