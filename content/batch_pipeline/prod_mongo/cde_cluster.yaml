registra_type: REPLICATION
source_name: CDE_CLUSTER
source_type: mongo
source_group: prod_mongo
cron_schedule: 0 18 * * *
data_config:
  cards-cimb-connector-live:
    tables:
      cimbtransactions:
        team: credit-card
        grant_read_to: ['credit-card-user']
        backfill_filters:
          - backfill_id: updated
        schema_configs:
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
  credit-card-production:
    tables:
      authenticatedcreditcardtokens:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        dqc_key: created
      authorizations:
        schema_configs:
          fields_to_remove: ['id']
          schema_file_column_casts:
            - col_name: merchant_rules_context
              cast_to: string
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
            backfill_days: 10
        grant_read_to:
        - credit-card-user
        - finops-automation-team
        dqc_key: created
      bindatas:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to:
        - credit-card-user
        - finops-automation-team
      creditcardrefunds:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
            backfill_days: 2
        grant_read_to:
        - credit-card-user
        - financial-planning-and-analysis-team
        - finance_automation-team
        - finops-automation-team
      creditcardtokens:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        dqc_key: created
      networktokens:
        schema_configs:
          fields_to_remove: ['id']
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        dqc_key: created
        pii_columns:
          - network_token_number
          - processor_response
        allow_pii_access_to:
          - "credit-card-user"
      tvlkeantransaction:
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to:
        - credit-card-user
        - finops-team
        - billing-team
        reload_schema: True
        schema_configs:
          schema_file_column_casts:
            - col_name: response_payload
              cast_to: string
          lakehouse_column_transformations_udf:
            - col_name: created
              col_alias: dt
              col_cast_method: general.datetime_to_date
              type: APPEND
  cybersource-production:
    tables:
      transactions:
        dqc_key: created
        partition_keys:
          - order: 1
            col_name: 'created'
            col_type: 'timestamp'
            col_alias: 'dt'
            col_cast_method: general.datetime_to_date
        team: credit-card
        backfill_filters:
          - backfill_id: updated
        grant_read_to: ['credit-card-user']
        archive_config:
          mode: STATIC