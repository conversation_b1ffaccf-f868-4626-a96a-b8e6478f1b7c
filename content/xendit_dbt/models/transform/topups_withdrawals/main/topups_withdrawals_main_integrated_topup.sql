{{ config(
    alias="integrated_topup"
) }}

SELECT 
  ta.id,
  SUBSTRING(bi.currency, 1, 2) AS country,
  bi.currency,
  'TOPUP' AS product_type,
  'TOPUP' AS product_sub_type,
  'MONEY_IN' AS money_flow,
  bi.channel_type AS topup_type,
  ta.topup_channel_identifier AS topup_identifier,
  ta.transaction_id,
  tc.business_id AS merchant_id,
  null AS transacting_entity,
  bi.channel_code AS topup_channel,
  bi.account_number,
  ta.amount,
  ta.status,
  ta.created,
  ta.updated,
  ta.created AS triggered
FROM {{ source('clean__regional_topup_service', 'topup_activity') }} ta
LEFT JOIN {{ source('clean__regional_topup_service', 'topup_channel') }} tc ON ta.topup_channel_id = tc.id
LEFT JOIN {{ source('clean__regional_topup_service', 'bank_information') }} bi ON tc.bank_information_id = bi.id
UNION ALL
SELECT
  dd.id,
  SUBSTRING(dd.currency, 1, 2) AS country,
  dd.currency AS currency,
  'TOPUP' AS product_type,
  'TOPUP' AS product_sub_type,
  'MONEY_IN' AS money_flow,
  'DIRECT_TOPUP' AS topup_type,
  dd.uuid AS topup_identifier,
  dd.transaction_id,
  dd.user_id AS merchant_id,
  null AS transacting_entity,
  'N/A' AS topup_channel,
  'N/A' AS account_number,
  dd.amount,
  dd.status,
  dd.created,
  dd.updated,
  dd.created AS triggered
FROM {{ source('clean__xendit_deposit_service', 'directdeposits') }} dd
UNION ALL
SELECT 
  vdd.id,
  'ID' AS country,
  'IDR' AS currency,
  'TOPUP' AS product_type,
  'TOPUP' AS product_sub_type,
  'MONEY_IN' AS money_flow,
  'VIRTUAL_ACCOUNT' AS topup_type,
  vdd.external_id AS topup_identifier,
  vdd.transaction_id,
  vdd.user_id AS merchant_id,
  CONCAT('ID_', vdd.bank_code) AS topup_channel,
  vdd.account_number,
  null AS transacting_entity,
  vdd.amount,
  vdd.status,
  vdd.created,
  vdd.created,
  ip.created AS triggered
FROM {{ source('clean__xendit_deposit_service', 'virtualaccountdirectdeposits') }} vdd
JOIN {{ ref('virtual_accounts_main_integrated_payment') }} ip ON vdd.payment_id = ip.id
