{{ config(
    alias="tickets"
) }}

WITH transform_tickets AS (
select t.*,
             CASE 
               WHEN t.custom_fields.`*************` = "ktp_validator__temporary_error_on_data_sources" THEN "ktp_biometrics__intermittent_error"
               WHEN t.custom_fields.`*************` = "ewallet_validator__merchant_issue" THEN "ewallet_validator__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "bank_name_validator__timeout_issue_on_merchant" THEN "bank_name_validator__customer_mistake_or_issue"
               ELSE t.custom_fields.`*************`
               END as iluma_reason_code_raw,
             CASE
               WHEN t.custom_fields.`*************` = "im_retail_outlet__partner_limitation_or_issue" THEN "im_retail_outlet__partner_issue"
               WHEN t.custom_fields.`*************` = "im_retail_outlet__xendit_api_gateway_issue" THEN "im_retail_outlet__product_system_issue"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_callback_tab_on_dashboard" THEN "im_api__lack_of_understanding_on_webhook_tab_on_dashboard"
               WHEN t.custom_fields.`*************` = "im_api__unable_to_generate_callback_report_on_dashboard" THEN "im_api__unable_to_generate_webhook_report_on_dashboard"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_xendit_callback_url" THEN "im_api__lack_of_understanding_on_xendit_webhook_url"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_xendit_callback_ip_address" THEN "im_api__lack_of_understanding_on_xendit_webhook_ip_address"
               WHEN t.custom_fields.`*************` = "im_api__outdated_library_version" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__callback_url_setting_issue" THEN "im_api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_ip_whitelist_feature" THEN "im_api__lack_of_understanding_on_ip_allowlist_feature"
               WHEN t.custom_fields.`*************` = "im_disbursement__customer_mistake" THEN "im_disbursement__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "im_batch_disbursement__customer_mistake" THEN "im_batch_disbursement__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "im_escrow_&_rdl__customer_mistake" THEN "im_escrow_&_rdl__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "im_billing__internal_system_issue" THEN "im_billing__bug_system_issue"
               WHEN t.custom_fields.`*************` = "im_api__customer_needs_of_using_particular_library" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_in_xendit_use_case" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__incomplete_documentation" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__wrong_error_code_and_description_in_api_reference" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__wrong_parameter_and_description_in_api_reference" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__unauthenticated_api_by_customer" THEN "im_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "im_api__current_product_improvement" THEN "im_api__:new_product_feature_request"
               WHEN t.custom_fields.`*************` = "im_api__customer's_server_issue" THEN "im_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "im_api__connection_issue_from_customer_side" THEN "im_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "im_api__unstable_merchant_server_or_url" THEN "im_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "im_api__ip_address_in_callback_url" THEN "im_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_xendit_callback_url_standard" THEN "im_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_callback_url_setup" THEN "im_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "im_api__unable_to_set_callback_url_on_dashboard" THEN "im_api__callback_url_setting_issue"
               WHEN t.custom_fields.`*************` = "im_api__unable_to_set_callback_url_on_dashboard" THEN "im_api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "im_api__break_in_business_service" THEN "im_api__business_service_issue"
               WHEN t.custom_fields.`*************` = "im_api__customer_needs_of_adding_ip_address" THEN "im_api__lack_of_understanding_on_ip_whitelist_feature"
               WHEN t.custom_fields.`*************` = "im_api__customer_requests_to_whitelist_xendit_ip_address" THEN "im_api__lack_of_understanding_on_xendit_callback_ip_address"
               WHEN t.custom_fields.`*************` = "im_api__lack_of_understanding_on_api_key_creation" THEN "im_api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*************` = "im_api__incorrect_api_key_permission" THEN "im_api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*************` = "im_retail_outlet__break_in_deposit_service" THEN "im_retail_outlet__break_in_webhook_service"
               WHEN t.custom_fields.`*************` = "im_retail_outlet__system_issue" THEN "im_retail_outlet__partner_limitation_or_issue"
               WHEN t.custom_fields.`*************` = "im_retail_outlet__traffic_due_to_deployment" THEN "im_retail_outlet__xendit_api_gateway_issue"
               WHEN t.custom_fields.`*************` = "im_disbursement__unknown_status" THEN "im_disbursement__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "im_batch_disbursement__unknown_status" THEN "im_batch_disbursement__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "im_withdrawal__unknown_status" THEN "im_withdrawal__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "im_batch_disbursement__customer_insufficient_balance" THEN "im_batch_disbursement__failed_insufficient_balance"
               WHEN t.custom_fields.`*************` = "im_disbursement__customer_insufficient_balance" THEN "im_disbursement__failed_insufficient_balance"
               WHEN t.custom_fields.`*************` = "im_remittance__lack_of_understanding_on_refund" THEN "im_remittance__lack_of_understanding_on_reversal"
               WHEN t.custom_fields.`*************` = "im_billing__miscalculation_by_xendit_team" THEN "im_billing__different_calculation_between_billing_and_finance_team"
               ELSE t.custom_fields.`*************`
               END as im_reason_code_raw,
             CASE
               WHEN t.custom_fields.`*************` = "ewallet__lack_of_understanding_on_ewallet_creation" THEN "ewallet__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "qr_code__lack_of_understanding_on_qr_code_creation" THEN "qr_code__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "ewallet__unable_to_locate_transaction_in_callback_tab" THEN "ewallet__lack_of_understanding_on_callback_status_checking"
               WHEN t.custom_fields.`*************` = "qr_code__unable_to_locate_transaction_in_callback_tab" THEN "qr_code__lack_of_understanding_on_callback_status_checking"
               WHEN t.custom_fields.`*************` = "ewallet__unable_to_resend_callback_from_dashboard" THEN "ewallet__lack_of_understanding_on_resend_callback_feature"
               WHEN t.custom_fields.`*************` = "qr_code__unable_to_resend_callback_from_dashboard" THEN "qr_code__lack_of_understanding_on_resend_callback_feature"
               WHEN t.custom_fields.`*************` = "paylater__refund_feature_unavailable" THEN "paylater__lack_of_understanding_on_refund_feature"
               WHEN t.custom_fields.`*************` = "ewallet__ewallet_service_error" THEN "ewallet__intermittent_error"
               WHEN t.custom_fields.`*************` = "qr_code___qr_service_down" THEN "qr_code__intermittent_error"
               WHEN t.custom_fields.`*************` = "ewallet__error_on_partner" THEN "ewallet__partner_issue"
               WHEN t.custom_fields.`*************` = "qr_code__error_on_partner" THEN "qr_code__partner_issue"
               WHEN t.custom_fields.`*************` = "paylater__error_on_partner" THEN "paylater__partner_issue"
               WHEN t.custom_fields.`*************` = "qr_code__end_user's_apps_or_internet_connection_issue" THEN "qr_code__lack_of_understanding_about_failure_reason"
               WHEN t.custom_fields.`*************` = "retail_outlet__partner_limitation_or_issue" THEN "retail_outlet__partner_issue"
               WHEN t.custom_fields.`*************` = "retail_outlet__xendit_api_gateway_issue" THEN "retail_outlet__product_system_issue"
               WHEN t.custom_fields.`*************` = "xenplatform__lack_of_understanding_on_sub_account_customization" THEN "xenplatform__lack_of_understanding_on_sub_account_dashboard_access"
               WHEN t.custom_fields.`*************` = "xenplatform__platform_fee_issue" THEN "xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "xenplatform__transfer_feature_issue" THEN "xenplatform__fund_transfer_issue"
               WHEN t.custom_fields.`*************` = "xenplatform__xenplatorm_service_error" THEN "xenplatform__xenplatform_service_error"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_callback_tab_on_dashboard" THEN "api__lack_of_understanding_on_webhook_tab_on_dashboard"
               WHEN t.custom_fields.`*************` = "api__unable_to_generate_callback_report_on_dashboard" THEN "api__unable_to_generate_webhook_report_on_dashboard"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_xendit_callback_url" THEN "api__lack_of_understanding_on_xendit_webhook_url"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_xendit_callback_ip_address" THEN "api__lack_of_understanding_on_xendit_webhook_ip_address"
               WHEN t.custom_fields.`*************` = "api__outdated_library_version" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__callback_url_setting_issue" THEN "api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "api__rate-limit_related" THEN "api__rate-limit_related"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_ip_whitelist_feature" THEN "api__lack_of_understanding_on_ip_allowlist_feature"
               WHEN t.custom_fields.`*************` = "cards__customer_request" THEN "cards__difficult_to_find_transaction_on_dashboard"
               WHEN t.custom_fields.`*************` = "cards__lack_of_understanding_on_minimum_and_maximum_amount" THEN "cards__lack_of_understanding_on_cards_rules_and_limitation"
               WHEN t.custom_fields.`*************` = "subscription__unactivated_optional_3ds_on_cards" THEN "subscription__unactivated_merchant_initiated_transaction_on_cards"
               WHEN t.custom_fields.`*************` = "payout__current_product_improvement" THEN "payout_link__current_product_improvement"
               WHEN t.custom_fields.`*************` = "payout__new_product_feature_request" THEN "payout_link__new_product_feature_request"
               WHEN t.custom_fields.`*************` = "payout__unidentified_reason" THEN "payout_link__unidentified_reason"
               WHEN t.custom_fields.`*************` = "payout__lack_of_understanding_on_product_feature" THEN "payout_link__lack_of_understanding_on_product_feature"
               WHEN t.custom_fields.`*************` = "disbursement__issue_or_bug_on_test_mode" THEN "disbursement__bug_system_issue"
               WHEN t.custom_fields.`*************` = "disbursement__customer_mistake" THEN "disbursement__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "batch_disbursement__customer_mistake" THEN "batch_disbursement__customer_mistake_or_issue"
               WHEN t.custom_fields.`*************` = "product_activation__product_activation_bugs" THEN "product_activation__bug_system_issue"
               WHEN t.custom_fields.`*************` = "product_activation__product_activation_service_error" THEN "product_activation__product_system_issue"
               WHEN t.custom_fields.`*************` = "billing__internal_system_issue" THEN "billing__bug_system_issue"
               WHEN t.custom_fields.`*************` = "xenplatform__lack_of_understanding_on_product_feature" THEN "xenplatform__lack_of_understanding_on_product_feature_and_capabilities"
               WHEN t.custom_fields.`*************` = "xenplatform__lack_of_understanding_on_charging_fee_to_sub_account" THEN "xenplatform__lack_of_understanding_on_fee_rule_feature"
               WHEN t.custom_fields.`*************` = "xenplatform__lack_of_understanding_on_sub_account_creation_via_api" THEN "xenplatform__lack_of_understanding_on_sub_account_creation"
               WHEN t.custom_fields.`*************` = "api__customer_needs_of_using_particular_library" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_in_xendit_use_case" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__incomplete_documentation" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__wrong_error_code_and_description_in_api_reference" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__wrong_parameter_and_description_in_api_reference" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__unauthenticated_api_by_customer" THEN "api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*************` = "api__current_product_improvement" THEN "api__new_product_feature_request"
               WHEN t.custom_fields.`*************` = "api__customer's_server_issue" THEN "api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "api__connection_issue_from_customer_side" THEN "api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "api__unstable_merchant_server_or_url" THEN "api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*************` = "api__ip_address_in_callback_url" THEN "api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_xendit_callback_url_standard" THEN "api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_callback_url_setup" THEN "api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*************` = "api__unable_to_set_callback_url_on_dashboard" THEN "api__callback_url_setting_issue"
               WHEN t.custom_fields.`*************` = "api__unable_to_set_callback_url_on_dashboard" THEN "api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "api__break_in_business_service" THEN "api__business_service_issue"
               WHEN t.custom_fields.`*************` = "api__customer_needs_of_adding_ip_address" THEN "api__lack_of_understanding_on_ip_whitelist_feature"
               WHEN t.custom_fields.`*************` = "api__customer_requests_to_whitelist_xendit_ip_address" THEN "api__lack_of_understanding_on_xendit_callback_ip_address"
               WHEN t.custom_fields.`*************` = "api__lack_of_understanding_on_api_key_creation" THEN "api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*************` = "api__incorrect_api_key_permission" THEN "api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*************` = "api__ssl_certification_issue" THEN "api__website_certification_issue_on_merchant_side"
               WHEN t.custom_fields.`*************` = "api__xendit_api_gateway_issue" THEN "api__api_gateway_issue"
               WHEN t.custom_fields.`*************` = "retail_outlet__traffic_due_to_deployment" THEN "retail_outlet__xendit_api_gateway_issue"
               WHEN t.custom_fields.`*************` = "cards__customer_request" THEN "cards__difficult_to_find_transaction_on_dashboard"
               WHEN t.custom_fields.`*************` = "cards__lack_of_understanding_on_minimum_and_maximum_amount" THEN "cards__lack_of_understanding_on_cards_rules_and_limitation"
               WHEN t.custom_fields.`*************` = "disbursement__unknown_status" THEN "disbursement__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "batch_disbursement__unknown_status" THEN "batch_disbursement__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "withdrawal__unknown_status" THEN "withdrawal__unknown_status_-_within_internal_sla"
               WHEN t.custom_fields.`*************` = "batch_disbursement__customer_insufficient_balance" THEN "batch_disbursement__failed_insufficient_balance"
               WHEN t.custom_fields.`*************` = "disbursement__customer_insufficient_balance" THEN "disbursement__failed_insufficient_balance"
               WHEN t.custom_fields.`*************` = "remittance_payout__lack_of_understanding_on_reversal" THEN "remittance_payout__lack_of_understanding_on_reversal"
               WHEN t.custom_fields.`*************` = "remittance_payout__unavailable_transaction_status_in_dashboard" THEN "remittance_payout__transaction_completed_or_failed"
               ELSE t.custom_fields.`*************`
               END as reason_code_raw,
             CASE
               WHEN t.custom_fields.`*********9753` = "ph_ewallet__ewallet_service_error" THEN "ph_ewallet__intermittent_error"
               WHEN t.custom_fields.`*********9753` = "ph_ewallet__error_on_partner" THEN "ph_ewallet__partner_issue"
               WHEN t.custom_fields.`*********9753` = "ph_paylater__error_on_partner" THEN "ph_paylater__partner_issue"
               WHEN t.custom_fields.`*********9753` = "ph_retail_outlet__partner_limitation_or_issue" THEN "ph_retail_outlet__partner_issue"
               WHEN t.custom_fields.`*********9753` = "ph_retail_outlet__xendit_api_gateway_issue" THEN "ph_retail_outlet__product_system_issue"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__lack_of_understanding_on_sub_account_customization" THEN "ph_xenplatform__lack_of_understanding_on_sub_account_dashboard_access"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__platform_fee_issue" THEN "ph_xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__transfer_feature_issue" THEN "ph_xenplatform__fund_transfer_issue"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__xenplatorm_service_error" THEN "ph_xenplatform__xenplatform_service_error"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_callback_tab_on_dashboard" THEN "ph_api__lack_of_understanding_on_webhook_tab_on_dashboard"
               WHEN t.custom_fields.`*********9753` = "ph_api__unable_to_generate_callback_report_on_dashboard" THEN "ph_api__unable_to_generate_webhook_report_on_dashboard"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_xendit_callback_url" THEN "ph_api__lack_of_understanding_on_xendit_webhook_url"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_xendit_callback_ip_address" THEN "ph_api__lack_of_understanding_on_xendit_webhook_ip_address"
               WHEN t.custom_fields.`*********9753` = "ph_api__outdated_library_version" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__callback_url_setting_issue" THEN "ph_api__webhook_service_issue"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_ip_whitelist_feature" THEN "ph_api__lack_of_understanding_on_ip_allowlist_feature"
               WHEN t.custom_fields.`*********9753` = "ph_cards__customer_request" THEN "ph_cards__difficult_to_find_transaction_on_dashboard"
               WHEN t.custom_fields.`*********9753` = "ph_cards__lack_of_understanding_on_minimum_and_maximum_amount" THEN "ph_cards__lack_of_understanding_on_cards_rules_and_limitation"
               WHEN t.custom_fields.`*********9753` = "ph_subscription__unactivated_optional_3ds_on_cards" THEN "ph_subscription__unactivated_merchant_initiated_transaction_on_cards"
               WHEN t.custom_fields.`*********9753` = "ph_product_activation__product_activation_service_error" THEN "ph_product_activation__product_system_issue"
               WHEN t.custom_fields.`*********9753` = "ph_billing__internal_system_issue" THEN "ph_billing__bug_system_issue"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__lack_of_understanding_on_charging_fee_to_sub_account" THEN "ph_xenplatform__lack_of_understanding_on_fee_rule_feature"
               WHEN t.custom_fields.`*********9753` = "ph_xenplatform__lack_of_understanding_on_sub_account_creation_via_api" THEN "ph_xenplatform__lack_of_understanding_on_sub_account_creation"
               WHEN t.custom_fields.`*********9753` = "ph_api__customer_needs_of_using_particular_library" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_in_xendit_use_case" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__incomplete_documentation" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__wrong_error_code_and_description_in_api_reference" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__wrong_parameter_and_description_in_api_reference" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__unauthenticated_api_by_customer" THEN "ph_api__lack_of_understanding_on_integration"
               WHEN t.custom_fields.`*********9753` = "ph_api__current_product_improvement" THEN "ph_api__new_product_feature_request"
               WHEN t.custom_fields.`*********9753` = "ph_api__customer's_server_issue" THEN "ph_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*********9753` = "ph_api__connection_issue_from_customer_side" THEN "ph_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*********9753` = "ph_api__unstable_merchant_server_or_url" THEN "ph_api__unstable_merchant_website_or_connection"
               WHEN t.custom_fields.`*********9753` = "ph_api__ip_address_in_callback_url" THEN "ph_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_xendit_callback_url_standard" THEN "ph_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_callback_url_setup" THEN "ph_api__lack_of_understanding_on_xendit_callback_url"
               WHEN t.custom_fields.`*********9753` = "ph_api__unable_to_set_callback_url_on_dashboard" THEN "ph_api__callback_url_setting_issue"
               WHEN t.custom_fields.`*********9753` = "ph_api__unable_to_set_callback_url_on_dashboard" THEN "ph_api__webhook_service_issue"
               WHEN t.custom_fields.`*********9753` = "ph_api__break_in_business_service" THEN "ph_api__business_service_issue"
               WHEN t.custom_fields.`*********9753` = "ph_api__customer_needs_of_adding_ip_address" THEN "ph_api__lack_of_understanding_on_ip_whitelist_feature"
               WHEN t.custom_fields.`*********9753` = "ph_api__customer_requests_to_whitelist_xendit_ip_address" THEN "ph_api__lack_of_understanding_on_xendit_callback_ip_address"
               WHEN t.custom_fields.`*********9753` = "ph_api__lack_of_understanding_on_api_key_creation" THEN "ph_api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*********9753` = "ph_api__incorrect_api_key_permission" THEN "ph_api__lack_of_understanding_on_api_key"
               WHEN t.custom_fields.`*********9753` = "ph_api__ssl_certification_issue" THEN "ph_api__website_certification_issue_on_merchant_side"
               WHEN t.custom_fields.`*********9753` = "ph_api__xendit_api_gateway_issue" THEN "ph_api__api_gateway_issue"
               WHEN t.custom_fields.`*********9753` = "ph_retail_outlet__traffic_due_to_deployment" THEN "ph_retail_outlet__xendit_api_gateway_issue"
               WHEN t.custom_fields.`*********9753` = "ph_cards__customer_request" THEN "ph_cards__difficult_to_find_transaction_on_dashboard"
               WHEN t.custom_fields.`*********9753` = "ph_cards__lack_of_understanding_on_minimum_and_maximum_amount" THEN "ph_cards__lack_of_understanding_on_cards_rules_and_limitation"
               ELSE t.custom_fields.`*********9753`
               END as ph_reason_code_raw,
             CASE
               WHEN t.custom_fields.`7205277737241` = "issue__ktp_validator" THEN "issue__ktp_biometrics"
               ELSE t.custom_fields.`7205277737241`
               END as iluma_ticket_type_raw,
             CASE 
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__callback_error_from_xendit_side" THEN "issue__im_api__webhook_error_from_xendit_side"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__callback_url_setting_issue" THEN "issue__im_api__webhook_url_setting_issue"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__callback_tab_not_working" THEN "issue__im_api__webhook_service_issue"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__callback_error_from_merchant_side" THEN "question__im_api__webhook_error_from_merchant_side"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__xendit_callback_ip_address" THEN "question__im_api__xendit_webhook_ip_address"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__callback_url" THEN "question__im_api__webhook_url"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__callback_tab_on_dashboard" THEN "question__im_api__webhook_tab_on_dashboard"
               WHEN t.custom_fields.`7196800852889` = "task__im_api__callback_report_generation" THEN "task__im_api__webhook_report_generation"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__rate_limit" THEN "question__im_api__rate_limit"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__ip_whitelist" THEN "question__im_api__ip_allowlist"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__api__request_forbidden_error_response" THEN "question__im_api__4xx_error_api_response"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__incomplete_parameters_or_endpoints_in_api_docs" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__incomplete_error_response_or_error_code_in_api_docs" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__wrong_code_example" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__missing_code_example" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_go_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_javascript_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_laravel_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_node_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_php_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__documentation_for_ruby_language" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__use_case_and_parameter_explanation" THEN "question__im_api__api_integration"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__xendit_api_integration" THEN "question__im_api:api_integration"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__missing_response" THEN "issue__im_api__non_4xx_error_api_response"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__missing_callback" THEN "issue__im_api__callback_error_from_xendit_side"
               WHEN t.custom_fields.`7196800852889` = "issue__im_api__unable_to_set_up_callback_url" THEN "issue__im_api__callback_url_setting_issue"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__non_2xx_error_response_from_callback_url" THEN "question__im_api__callback_error_from_merchant_side"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__callback_xendit_ip_to_whitelist" THEN "question__im_api__xendit_callback_ip_address"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__addition_of_whitelisted_api" THEN "question__im_api__ip_whitelist"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__api_key_creation" THEN "question__im_api__api_key"
               WHEN t.custom_fields.`7196800852889` = "question__im_api__callback_url_set-up" THEN "question__im_api__callback_url"
               WHEN t.custom_fields.`7196800852889` = "task__im_api__addition_of_callback_url" THEN "question__im_api__callback_url"
               WHEN t.custom_fields.`7196800852889` = "task__im_remittance__money_in_transaction_status_checking" THEN "question__im_remittance__money_in_transaction_status_checking"
               WHEN t.custom_fields.`7196800852889` = "task__im_remittance__money_out_transaction_status_checking" THEN "question__im_remittance__money_out_transaction_status_checking"
               WHEN t.custom_fields.`7196800852889` = "question__im_dashboard__team_member_addition" THEN "question__im_dashboard__team_member_management"
               WHEN t.custom_fields.`7196800852889` = "question__im_billing__pph_23_withholding_tax_slip_submission" THEN "question__im_billing__pph_23_withholding_tax_information"
               ELSE t.custom_fields.`7196800852889`
               END as im_ticket_type_raw,
             CASE
               WHEN t.custom_fields.`*************` = "question__ewallet__ewallet_creation" THEN "question__ewallet__integration_related"
               WHEN t.custom_fields.`*************` = "question__qr_code__qr_code_creation" THEN "question__qr_code__integration_related"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__double_fund_deduction" THEN "issue__xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__fee_rules_creation_error" THEN "issue__xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__late_platform_fee_deduction" THEN "issue__xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__undeducted_platform_fee_to_master_account" THEN "issue__xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__for-user-id_issue" THEN "issue__xenplatform__sub_account_transaction_creation_error"
               WHEN t.custom_fields.`*************` = "question__xenplatform__custom_sub_account" THEN "question__xenplatform__owned_sub_account"
               WHEN t.custom_fields.`*************` = "issue__api__callback_error_from_xendit_side" THEN "issue__api__webhook_error_from_xendit_side"
               WHEN t.custom_fields.`*************` = "issue__api__callback_url_setting_issue" THEN "issue__api__webhook_url_setting_issue"
               WHEN t.custom_fields.`*************` = "issue__api__callback_tab_not_working" THEN "issue__api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "question__api__callback_error_from_merchant_side" THEN "question__api__webhook_error_from_merchant_side"
               WHEN t.custom_fields.`*************` = "question__api__xendit_callback_ip_address" THEN "question__api__xendit_webhook_ip_address"
               WHEN t.custom_fields.`*************` = "question__api__callback_url" THEN "question__api__webhook_url"
               WHEN t.custom_fields.`*************` = "question__api__callback_tab_on_dashboard" THEN "question__api__webhook_tab_on_dashboard"
               WHEN t.custom_fields.`*************` = "task__api__callback_report_generation" THEN "task__api__webhook_report_generation"
               WHEN t.custom_fields.`*************` = "question__api__ip_whitelist" THEN "question__api__ip_allowlist"
               WHEN t.custom_fields.`*************` = "feature_request__payout" THEN "feature_request__payout_link"
               WHEN t.custom_fields.`*************` = "issue__:payout__dashboard_unexpected_error" THEN "issue__:payout_link__dashboard_unexpected_error"
               WHEN t.custom_fields.`*************` = "issue__:payout__other" THEN "issue__:payout_link__other"
               WHEN t.custom_fields.`*************` = "question__payout__payout_link_creation" THEN "question__payout_link__payout_link_creation"
               WHEN t.custom_fields.`*************` = "question__payout__payout_claim" THEN "question__payout_link__payout_link_claim"
               WHEN t.custom_fields.`*************` = "question__payout__locked_payout_links" THEN "question__payout_link__locked_payout_links"
               WHEN t.custom_fields.`*************` = "question__payout__other" THEN "question__payout_link__other"
               WHEN t.custom_fields.`*************` = "task__payout__other" THEN "task__payout_link__other"
               WHEN t.custom_fields.`*************` = "issue__xenplatform__individual_unable_to_create_managed_sub_account" THEN "question__xenplatform__managed_sub_account"
               WHEN t.custom_fields.`*************` = "question__xenplatform__platform_fee_rule" THEN "question__xenplatform__fee_rule_feature"
               WHEN t.custom_fields.`*************` = "issue__api__api__request_forbidden_error_response" THEN "question__api__4xx_error_api_response"
               WHEN t.custom_fields.`*************` = "issue__api__missing_response" THEN "issue__api__non_4xx_error_api_response"
               WHEN t.custom_fields.`*************` = "issue:api__missing_callback" THEN "issue__api__callback_error_from_xendit_side"
               WHEN t.custom_fields.`*************` = "issue__api__unable_to_set_up_callback_url" THEN "issue__api__callback_url_setting_issue"
               WHEN t.custom_fields.`*************` = "issue__api__incomplete_parameters_or_endpoints_in_api_docs" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__api__incomplete_error_response_or_error_code_in_api_docs" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__api__wrong_code_example" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__api__missing_code_example" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_go_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_javascript_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_laravel_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_node_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_php_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__documentation_for_ruby_language" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__use_case_and_parameter_explanation" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__xendit_api_integration" THEN "question__api__api_integration"
               WHEN t.custom_fields.`*************` = "question__api__non_2xx_error_response_from_callback_url" THEN "question__api__callback_error_from_merchant_side"
               WHEN t.custom_fields.`*************` = "question__api__callback_xendit_ip_to_whitelist" THEN "question__api__xendit_callback_ip_address"
               WHEN t.custom_fields.`*************` = "question__api__addition_of_whitelisted_api" THEN "question__api__ip_whitelist"
               WHEN t.custom_fields.`*************` = "question__api__api_key_creation" THEN "question__api__api_key"
               WHEN t.custom_fields.`*************` = "question__api__callback_url_set-up" THEN "question__api__callback_url"
               WHEN t.custom_fields.`*************` = "task__api__addition_of_callback_url" THEN "question__api__callback_url"
               WHEN t.custom_fields.`*************` = "question__tpi__unable_to_disconnect_the_integration" THEN "issue__tpi__unable_to_disconnect_the_integration"
               ELSE t.custom_fields.`*************`
               END as ticket_type_raw,
             CASE
               WHEN t.custom_fields.`*************` = "issue__ph_ewallet__ewallet_creation_failure" THEN "issue__ph_ewallet__unable_to_create_payment"
               WHEN t.custom_fields.`*************` = "issue__ph_xenplatform__double_fund_deduction" THEN "issue__ph_xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__ph_xenplatform__fee_rules_creation_error" THEN "issue__ph_xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__ph_xenplatform__late_platform_fee_deduction" THEN "issue__ph_xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__ph_xenplatform__undeducted_platform_fee_to_master_account" THEN "issue__ph_xenplatform__platform_fee_and_fee_rule_issue"
               WHEN t.custom_fields.`*************` = "issue__ph_xenplatform__for-user-id_issue" THEN "issue__ph_xenplatform__sub_account_transaction_creation_error"
               WHEN t.custom_fields.`*************` = "question__ph_xenplatform__custom_sub_account" THEN "question__ph_xenplatform__owned_sub_account"
               WHEN t.custom_fields.`*************` = "issue__ph_api__callback_error_from_xendit_side" THEN "issue__ph_api__webhook_error_from_xendit_side"
               WHEN t.custom_fields.`*************` = "issue__ph_api__callback_url_setting_issue" THEN "issue__ph_api__webhook_url_setting_issue"
               WHEN t.custom_fields.`*************` = "issue__ph_api__callback_tab_not_working" THEN "issue__ph_api__webhook_service_issue"
               WHEN t.custom_fields.`*************` = "question__ph_api__callback_error_from_merchant_side" THEN "question__ph_api__webhook_error_from_merchant_side"
               WHEN t.custom_fields.`*************` = "question__ph_api__xendit_callback_ip_address" THEN "question__ph_api__xendit_webhook_ip_address"
               WHEN t.custom_fields.`*************` = "question__ph_api__callback_url" THEN "question__ph_api__webhook_url"
               WHEN t.custom_fields.`*************` = "question__ph_api__callback_tab_on_dashboard" THEN "question__ph_api__webhook_tab_on_dashboard"
               WHEN t.custom_fields.`*************` = "task__ph_api__callback_report_generation" THEN "task__ph_api__webhook_report_generation"
               WHEN t.custom_fields.`*************` = "issue__ph_api__customer_cannot_simulate_payment_in_test_mode" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__customer_request_is_not_found_in_their_dashboard" THEN "question__ph_api__api_log_on_dashboard"
               WHEN t.custom_fields.`*************` = "question__ph_api__ip_whitelist" THEN "question__ph_api__ip_allowlist"
               WHEN t.custom_fields.`*************` = "issue__ph_cards__declined_card" THEN "question__ph_cards___declined_card"
               WHEN t.custom_fields.`*************` = "issue__ph_cards__failed_to_pay_cards_with_invoice" THEN "question__ph_cards___failed_to_pay_cards_with_invoice"
               WHEN t.custom_fields.`*************` = "question__ph_xenplatform__platform_fee_rule" THEN "question__ph_xenplatform__fee_rule_feature"
               WHEN t.custom_fields.`*************` = "issue__ph_api__api__request_forbidden_error_response" THEN "question__ph_api__4xx_error_api_response"
               WHEN t.custom_fields.`*************` = "issue__ph_api__incomplete_parameters_or_endpoints_in_api_docs" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__incomplete_error_response_or_error_code_in_api_docs" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__wrong_code_example" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__missing_code_example" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_go_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_javascript_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_laravel_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_c#_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_phyton_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "question__ph_api__documentation_for_node_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "question__ph_api__documentation_for_php_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "question__ph_api__documentation_for_ruby_language" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "queston__ph_api__documentation_for_unsupported_library" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__use_case_and_parameter_explanation" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "question__ph_api__xendit_api_integration" THEN "question__ph_api__api_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_api__missing_response" THEN "issue__ph_api__non_4xx_error_api_response"
               WHEN t.custom_fields.`*************` = "issue__ph_api__missing_callback" THEN "issue__ph_api__callback_error_from_xendit_side"
               WHEN t.custom_fields.`*************` = "issue__ph_api__unable_to_set_up_callback_url" THEN "issue__ph_api__callback_url_setting_issue"
               WHEN t.custom_fields.`*************` = "question__ph_api__non_2xx_error_response_from_callback_url" THEN "question__ph_api__callback_error_from_merchant_side"
               WHEN t.custom_fields.`*************` = "question__ph_api__callback_xendit_ip_to_whitelist" THEN "question__ph_api__xendit_callback_ip_address"
               WHEN t.custom_fields.`*************` = "question__ph_api__addition_of_whitelisted_api" THEN "question__ph_api__ip_whitelist"
               WHEN t.custom_fields.`*************` = "question__ph_api__api_key_creation" THEN "question__ph_api__api_key"
               WHEN t.custom_fields.`*************` = "question__ph_api__callback_url_set-up" THEN "question__ph_api__callback_url"
               WHEN t.custom_fields.`*************` = "task__ph_api__addition_of_callback_url" THEN "question__ph_api__callback_url"
               WHEN t.custom_fields.`*************` = "issue__ph_cards__declined_card" THEN "question__ph_cards___declined_card"
               WHEN t.custom_fields.`*************` = "issue__ph_cards__failed_to_pay_cards_with_invoice" THEN "question__ph_cards___failed_to_pay_cards_with_invoice"
               WHEN t.custom_fields.`*************` = "question__ph_tpi__unable_to_disconnect_the_integration" THEN "issue__ph_tpi__unable_to_disconnect_the_integration"
               WHEN t.custom_fields.`*************` = "issue__ph_dashboard__dashboard_unexpected_error" THEN "issue__ph_dashboard__dashboard_unexpected_error_when_login"
               ELSE t.custom_fields.`*************`
               END as ph_ticket_type_raw,
             CASE
               WHEN coalesce(custom_fields.`**************`,custom_fields.`35552830650009`,custom_fields.`5739921335449`) = 'nex__question__spam__no_context' then 'question__spam__no_context' 
               WHEN coalesce(custom_fields.`**************`,custom_fields.`35552830650009`,custom_fields.`5739921335449`) = 'question__nex__spam__no_context' then 'question__spam__no_context'
               ELSE coalesce(custom_fields.`**************`,custom_fields.`35552830650009`,custom_fields.`5739921335449`)
               END as nex_ticket_type_raw,
            CASE  
               WHEN coalesce(custom_fields.`41423798831641`,custom_fields.`35552914763545`,custom_fields.`5740428081305`) = 'nex__spam__nex__no_context' then 'spam__no_context' 
               WHEN coalesce(custom_fields.`41423798831641`,custom_fields.`35552914763545`,custom_fields.`5740428081305`) = 'nex__spam__double_ticket' then 'spam__no_context'
               ELSE coalesce(custom_fields.`41423798831641`,custom_fields.`35552914763545`,custom_fields.`5740428081305`)
               END as nex_reason_code_raw
      from {{ source('clean__zendesk', 'tickets') }} t
),

zendesk_user AS (
SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY id) AS count
    FROM {{ source('clean__zendesk', 'users') }}
),

ticket_sentiment AS (
  SELECT
      ticket_id,
       SUM(CASE
               WHEN sentiment = 'Positive' THEN 1
               WHEN sentiment = 'Neutral' THEN 0
               WHEN sentiment = 'Negative' THEN -1
               ELSE 0
            END) AS summed_value,
          string(array_agg(concat(actors, ' : ', conversation))) AS concatenated_body,
        COUNT(DISTINCT event_id) AS event_count
    FROM
        transform__cs_openai_results.zendesk_ticket_sentiments
    WHERE
        actors <> 'admin'
    and actors <> 'agent'  
    GROUP BY
        ticket_id
)


select 
  transform_tickets.*,
  ticket_sentiment.concatenated_body as body,
  CASE
      WHEN  ticket_sentiment.event_count > 0 THEN
          CASE
              WHEN ( ticket_sentiment.summed_value /  ticket_sentiment.event_count) < -0.5 THEN 'Negative'
              WHEN ( ticket_sentiment.summed_value /  ticket_sentiment.event_count) >= -0.5 AND ( ticket_sentiment.summed_value /  ticket_sentiment.event_count) <= 0.5 THEN 'Neutral'
              WHEN ( ticket_sentiment.summed_value /  ticket_sentiment.event_count) > 0.5 THEN 'Positive'
              ELSE NULL
          END
      ELSE NULL
  END AS sentiment,
  CASE
      WHEN  ticket_sentiment.event_count > 0 THEN 
      ( ticket_sentiment.summed_value /  ticket_sentiment.event_count) 
      ELSE NULL 
  END as sentiment_value_average,
  u.email AS requester_email,
  u.name AS requester_name,
  u.organization_id AS requester_organization_id,
  u.phone AS requester_phone,
  u.role AS requester_role,
  cast(transform_tickets.created_at as TIMESTAMP) AS created_at_new,
  replace(split(custom_fields.`************` ,"__")[0],"_"," ") as escalation,
  replace(split(custom_fields.`************` ,"__")[1],"_"," ") as solution,
  custom_fields.`************` as affected_transaction,
  split(ticket_type_raw,"__")[0] as ticket_type,
  split(ticket_type_raw,"__")[1] as ticket_type_product,
  split(ticket_type_raw,"__")[2] as ticket_type_detail,
  custom_fields.`*************` as recipient_bank,
  custom_fields.`*************` as ph_recipient_bank,
  split(reason_code_raw,"__")[0] as reason_code_product,
  split(reason_code_raw,"__")[1] as reason_code_detail,
  split(custom_fields.`*************`,"__")[0] as partner_type,
  split(custom_fields.`*************`,"__")[1] as partner_detail,
  split(custom_fields.`**************`,"__")[0] as im_partner_type,
  split(custom_fields.`**************`,"__")[1] as im_partner_detail,
  split(custom_fields.`*************`,"__")[0] as solution_type,
  split(custom_fields.`*************`,"__")[1] as solution_detail,
  split(nex_ticket_type_raw,"__")[0] as nex_ticket_type,
  split(nex_ticket_type_raw,"__")[1] as nex_ticket_type_product,
  split(nex_ticket_type_raw,"__")[2] as nex_ticket_type_detail,
  split(nex_reason_code_raw,"__")[0] as nex_reason_code_product,
  split(nex_reason_code_raw,"__")[1] as nex_reason_code_detail,
  split(custom_fields.`**************`,"__")[0] as nex_partner_type,
  split(custom_fields.`**************`,"__")[1] as nex_partner_detail,
  split(ph_ticket_type_raw,"__")[0] as ph_ticket_type,
  split(ph_ticket_type_raw,"__")[1] as ph_ticket_type_product,
  split(ph_ticket_type_raw,"__")[2] as ph_ticket_type_detail,
  split(ph_reason_code_raw,"__")[0] as ph_reason_code_product,
  split(ph_reason_code_raw,"__")[1] as ph_reason_code_detail,
  split(custom_fields.`7197877751577`,"__")[0] as ph_partner_type,
  split(custom_fields.`7197877751577`,"__")[1] as ph_partner_detail,
  split(im_ticket_type_raw,"__")[0] as im_ticket_type,
  split(im_ticket_type_raw,"__")[1] as im_ticket_type_product,
  split(im_ticket_type_raw,"__")[2] as im_ticket_type_detail,
  split(im_reason_code_raw,"__")[0] as im_reason_code_product,
  split(im_reason_code_raw,"__")[1] as im_reason_code_detail,
  split(iluma_ticket_type_raw,"__")[0] as iluma_ticket_type,
  split(iluma_ticket_type_raw,"__")[1] as iluma_ticket_type_product,
  split(iluma_ticket_type_raw,"__")[2] as iluma_ticket_type_detail,
  split(iluma_reason_code_raw,"__")[0] as iluma_reason_code_product,
  split(iluma_reason_code_raw,"__")[1] as iluma_reason_code_detail,
  split(custom_fields.`8428991937561`,"__")[0] as dpcss_reason_code_product,
  split(custom_fields.`8428991937561`,"__")[1] as dpcss_reason_code_detail,
  split(custom_fields.`4496192660889`,"__")[0] as dp_reason_code_product,
  split(custom_fields.`4496192660889`,"__")[1] as dp_reason_code_detail,
  split(custom_fields.`17214275417113`,"__")[0] as payex_ticket_type,
  split(custom_fields.`17214275417113`,"__")[1] as payex_ticket_type_product,
  split(custom_fields.`17214275417113`,"__")[2] as payex_ticket_type_detail,
  split(custom_fields.`17215226291993`,"__")[0] as payex_reason_code_product,
  split(custom_fields.`17215226291993`,"__")[1] as payex_reason_code_detail,
  replace(split(custom_fields.`17215424586137` ,"__")[0],"_"," ") as payex_provider_type,
  replace(split(custom_fields.`17215424586137` ,"__")[1],"_"," ") as payex_provider_name,
  split(custom_fields.`19128280053785`,"__")[0] as th_ticket_type,
  split(custom_fields.`19128280053785`,"__")[1] as th_ticket_type_product,
  split(custom_fields.`19128280053785`,"__")[2] as th_ticket_type_detail,
  split(custom_fields.`19128365325209`,"__")[0] as th_reason_code_product,
  split(custom_fields.`19128365325209`,"__")[1] as th_reason_code_detail,
  replace(split(custom_fields.`19129030422553` ,"__")[0],"_"," ") as th_partner_type,
  replace(split(custom_fields.`19129030422553` ,"__")[1],"_"," ") as th_partner_detail,
  split(custom_fields.`19127455052697`,"__")[0] as vn_ticket_type,
  split(custom_fields.`19127455052697`,"__")[1] as vn_ticket_type_product,
  split(custom_fields.`19127455052697`,"__")[2] as vn_ticket_type_detail,
  split(custom_fields.`19128358583193`,"__")[0] as vn_reason_code_product,
  split(custom_fields.`19128358583193`,"__")[1] as vn_reason_code_detail,
  replace(split(custom_fields.`19128816372505` ,"__")[0],"_"," ") as vn_partner_type,
  replace(split(custom_fields.`19128816372505` ,"__")[1],"_"," ") as vn_partner_detail,
  split(custom_fields.`15873361436569`,"__")[0] as app_ticket_type,
  split(custom_fields.`15873361436569`,"__")[1] as app_ticket_type_product,
  split(custom_fields.`15873361436569`,"__")[2] as app_ticket_type_detail,
  split(custom_fields.`15101982023193`,"__")[0] as app_reason_code_product,
  split(custom_fields.`15101982023193`,"__")[1] as app_reason_code_detail,
  replace(split(custom_fields.`15874055547417` ,"__")[0],"_"," ") as app_provider_type,
  replace(split(custom_fields.`15874055547417` ,"__")[1],"_"," ") as app_provider_name,
  split(custom_fields.`41330581602457`,"__")[0] as gbp_ticket_type,
  split(custom_fields.`41330581602457`,"__")[1] as gbp_ticket_type_product,
  split(custom_fields.`41330581602457`,"__")[2] as gbp_ticket_type_detail,
  split(custom_fields.`41330801281433`,"__")[0] as gbp_reason_code_product,
  split(custom_fields.`41330801281433`,"__")[1] as gbp_reason_code_detail,
  replace(split(custom_fields.`43673015668505` ,"__")[0],"_"," ") as xi_partner_type,
  replace(split(custom_fields.`43673015668505` ,"__")[1],"_"," ") as xi_partner_detail,
  split(custom_fields.`43673137542681`,"__")[0] as xi_ticket_type,
  split(custom_fields.`43673137542681`,"__")[1] as xi_ticket_type_product,
  split(custom_fields.`43673137542681`,"__")[2] as xi_ticket_type_detail,
  split(custom_fields.`43673291798553`,"__")[0] as xi_reason_code_product,
  split(custom_fields.`43673291798553`,"__")[1] as xi_reason_code_detail,
  replace(split(custom_fields.`25202097954457` ,"__")[0],"_"," ") as my_partner_type,
  replace(split(custom_fields.`25202097954457` ,"__")[1],"_"," ") as my_partner_detail,
  split(custom_fields.`25119154488473`,"__")[0] as my_ticket_type,
  split(custom_fields.`25119154488473`,"__")[1] as my_ticket_type_product,
  split(custom_fields.`25119154488473`,"__")[2] as my_ticket_type_detail,
  split(custom_fields.`25122367071385`,"__")[0] as my_reason_code_product,
  split(custom_fields.`25122367071385`,"__")[1] as my_reason_code_detail,
  custom_fields.`46579921915161` as frt_condition

from transform_tickets
LEFT JOIN zendesk_user u on requester_id = u.id and u.count = 1
LEFT JOIN ticket_sentiment on transform_tickets.id = ticket_sentiment.ticket_id 
