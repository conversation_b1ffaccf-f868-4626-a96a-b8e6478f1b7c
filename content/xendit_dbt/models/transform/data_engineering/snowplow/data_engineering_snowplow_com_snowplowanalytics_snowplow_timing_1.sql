{{ config(
    alias="com_snowplowanalytics_snowplow_timing_1",
    materialized="incremental",
    unique_key=['root_id']
) }}

WITH parsed_json AS (
    SELECT
        root_id,
        root_tstamp,
        dt
    FROM {{ ref('data_engineering_snowplow_parsed_context_unstruct') }}
    WHERE schema = 'iglu:com.snowplowanalytics.snowplow/timing/jsonschema/1-0-0'
)

SELECT
    root_id,
    root_tstamp,
    dt
FROM parsed_json
WHERE dt IS NOT NULL
    {% if is_incremental() %}
        AND dt > CURRENT_DATE - INTERVAL '10' DAYS
    {% endif %}
