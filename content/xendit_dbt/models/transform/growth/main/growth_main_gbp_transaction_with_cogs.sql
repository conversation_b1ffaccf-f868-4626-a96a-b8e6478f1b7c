{{ config(
    alias="gbp_transaction_with_cogs"
) }}

WITH
  parsed_pt AS (
    SELECT
      pt.*,
      CASE pt.payment_type
        WHEN 'C' THEN CASE WHEN pt.pay_type = 'I' THEN 'CARD_INSTALLMENT' ELSE 'CARD_FULL_PAYMENT' END
        WHEN 'Y' THEN 'ALIPAY'
        WHEN 'Q' THEN 'QR'
        WHEN 'V' THEN 'QR_VISA'
        WHEN 'M' THEN CASE WHEN pt.gwc_directdebit_code IS NOT NULL THEN 'DIRECT_DEBIT' ELSE 'MOBILE_BANKING' END
        WHEN 'B' THEN 'BILL_PAYMENT'
        WHEN 'S' THEN 'SHOPEE_PAY'
        WHEN 'W' THEN 'WECHAT'
        WHEN 'T' THEN 'TRUE_MONEY'
        WHEN 'L' THEN 'LINE_PAY'
        ELSE NULL
      END AS payment_channel,
      CASE 
        WHEN length(pt.payment_card_no) = 16 THEN
          CASE 
            WHEN left(pt.payment_card_no, 1) = '4' THEN 'VISA'
            WHEN left(pt.payment_card_no, 2) BETWEEN '51' AND '55' 
              OR left(pt.payment_card_no, 4) BETWEEN '2221' AND '2720' THEN 'MASTERCARD'  -- Standard ranges
            WHEN left(pt.payment_card_no, 2) = '35' THEN 'JCB'
            WHEN left(pt.payment_card_no, 2) = '62' THEN 'UNIONPAY'
            ELSE NULL
          END
        WHEN length(pt.payment_card_no) = 15 AND left(pt.payment_card_no, 2) IN ('34', '37') THEN 'AMEX'
        ELSE NULL
      END AS card_scheme,
      CASE pt.payment_type
        WHEN 'C' THEN 
        CASE gc.gwif_id
          WHEN 'A01' THEN 'AMEX_LEGACY'
          WHEN 'AX01' THEN 'AMEX_MPGS'
          WHEN 'B04' THEN 'KBANK'
          WHEN 'B05' THEN 'KBANK'
          WHEN 'B06' THEN 'KBANK'
          WHEN 'B07' THEN 'KBANK'
          WHEN 'B08' THEN 'KBANK'
          WHEN 'BBL01' THEN 'BBL'
          WHEN 'BBL02' THEN 'BBL'
          WHEN 'KS04' THEN 'BAY'
          WHEN 'KS05' THEN 'BAY'
          WHEN 'KS06' THEN 'BAY'
          WHEN 'K04' THEN 'KTC'
          WHEN 'K05' THEN 'KTC'
          WHEN 'KTC01' THEN 'KTC'
          WHEN 'S04' THEN 'SCB'
          WHEN 'T04' THEN 'TTB'
          WHEN 'UPI01' THEN 'GBP_UPI'
          WHEN 'JCB01' THEN 'GBP_JCB'
          WHEN 'JCB02' THEN 'GBP_JCB'
          WHEN 'JCB03' THEN 'GBP_JCB'
          ELSE NULL
        END
        WHEN 'M' THEN 
          CASE WHEN pt.gwc_directdebit_code IS NULL THEN
            CASE pt.mobile_bank
            WHEN 'BBL' THEN 'BBL'
            WHEN 'KBA' THEN 'KBANK'
            WHEN 'BAY' THEN 'BAY'
            WHEN 'SCB' THEN 'SCB'
            WHEN 'KTB' THEN 'KTB'
            ELSE NULL
          END
          ELSE
          CASE pt.gwc_directdebit_code
            WHEN 'K01DDKTB' THEN 'KTB'
            WHEN 'DDRS0001' THEN 'SCB'
            WHEN 'DDRKS0001' THEN 'BAY'
            WHEN 'DDRKBANK0001' THEN 'KBANK'
            WHEN 'DDPS0001' THEN 'SCB'
            WHEN 'DDPKS0001' THEN 'BAY'
            WHEN 'B01DDBBL' THEN 'BBL'
            ELSE NULL
          END
        END
        WHEN 'Q' THEN 
          CASE 
            WHEN pt.bpc_code IN ('Q042', 'Q016') 
              OR (pt.bpc_code = 'Q040' AND pt.create_by != 'Static Qr')
              OR (pt.create_by = 'Static Qr' AND pt.customer_name IS NOT NULL)
              OR pt.bpc_code IS NULL THEN 'BBL'
            ELSE 'SCB'
          END
        WHEN 'B' THEN 'SCB'
        WHEN 'S' THEN 'SHOPEE_PAY'
        WHEN 'W' THEN 'WECHAT'
        WHEN 'T' THEN 'TRUE_MONEY'
        WHEN 'L' THEN 'LINE_PAY'
        ELSE NULL
      END AS match_partner,
      nullif(prr.pay_month, '') AS installment_month,
      CASE WHEN pt.payment_type = 'C' THEN pt.settle_due ELSE pt.payment_date END AS transaction_date_adjusted,
      prr.approval_code,
      prr.debit_type,
      ma.mcc_jcb,
      ma.currency
    FROM {{ source('clean__globalprime', 'payment_transaction') }} pt
    LEFT JOIN {{ source('clean__globalprime', 'gateway_channel') }} gc ON pt.gwc_code = gc.code
    LEFT JOIN {{ source('clean__globalprime', 'gateway_channel_directdebit') }} gcd on pt.gwc_directdebit_code = gcd.code
    LEFT JOIN {{ source('clean__globalprime', 'payment_resp_rt') }} prr ON pt.id = prr.ptx_id
    LEFT JOIN {{ source('clean__globalprime', 'merchant_info_additional') }} ma ON pt.merchant_id = ma.merchant_id
    WHERE pt.status = 'S'
  ),
  pt_qr_bbl_count AS (
    SELECT 
      date_trunc('month', pt.create_date) AS month,
      count(*) AS sum_qr_bbl
    FROM {{ source('clean__globalprime', 'payment_transaction') }} pt
    WHERE pt.status = 'S'
      AND pt.payment_type = 'Q'
      AND (
        pt.bpc_code IN ('Q042', 'Q016')
        OR (pt.bpc_code = 'Q040' AND pt.create_by != 'Static Qr')
        OR (pt.bpc_code = 'Q040' AND pt.create_by = 'Static Qr' AND pt.customer_name IS NOT NULL)
        OR pt.bpc_code IS NULL
      )
      AND pt.create_date >= add_months(date_trunc('month', current_date()), -6)
      AND pt.create_date < add_months(date_trunc('month', current_date()), 1)
    GROUP BY date_trunc('month', pt.create_date)
  ),
  pt_by_cogs AS (
    SELECT
      parsed_pt.*,
      parsed_pt.match_partner as partner,
      cc.acquirer_merchant_id,
      cc.mdr,
      cc.rate_type,
      coalesce(gc.merchant_id, '') AS gc_merchant_id,
      coalesce(gc.mpi_merchant_id, '') AS gc_mpi_merchant_id,
      row_number() OVER (
        PARTITION BY parsed_pt.id
        ORDER BY
          (cc.gbp_merchant_id = parsed_pt.merchant_id) DESC,
          CASE 
            WHEN cc.acquirer_merchant_id = gc.merchant_id THEN 1
            WHEN cc.acquirer_merchant_id = gc.mpi_merchant_id THEN 2
            WHEN cc.acquirer_merchant_id = ltrim('0', gc.mpi_merchant_id) THEN 3
            ELSE 4
          END,
          (cc.card_type = CASE WHEN parsed_pt.debit_type = 'D' THEN 'DEBIT' ELSE 'CREDIT' END) DESC,
          (cc.card_type = 'ALL') DESC,
          (cc.location_card = CASE WHEN parsed_pt.location_card = 'D' THEN 'LOCAL' WHEN parsed_pt.location_card = 'I' THEN 'FOREIGN' END) DESC,
          (cc.location_card = 'ALL') DESC,
          (cc.card_scheme = parsed_pt.card_scheme) DESC,
          (cc.card_scheme = 'ALL') DESC,
          cc.start_date DESC,
          cc.create_date DESC
      ) AS priority
    FROM parsed_pt
    LEFT JOIN {{ source('clean__globalprime', 'gateway_channel') }} gc ON parsed_pt.gwc_code = gc.code
    LEFT JOIN {{ source('clean__globalprime', 'cogs_configuration') }} cc ON 
      parsed_pt.payment_channel = cc.payment_channel
      AND (
        coalesce(cc.acquirer_merchant_id, '') = ''
        OR cc.acquirer_merchant_id IN (gc.merchant_id, gc.mpi_merchant_id, ltrim('0', gc.mpi_merchant_id))
      )
      AND (parsed_pt.installment_month IS NULL OR cc.installment_month = cast(parsed_pt.installment_month AS decimal))
      AND (parsed_pt.match_partner IS NULL OR cc.partner = parsed_pt.match_partner)
      AND (
        parsed_pt.payment_type != 'C'
        OR parsed_pt.location_card IS NULL
        OR (parsed_pt.location_card = 'D' AND cc.location_card IN ('LOCAL', 'ALL'))
        OR (parsed_pt.location_card = 'I' AND cc.location_card IN ('FOREIGN', 'ALL'))
      )
      AND parsed_pt.transaction_date_adjusted >= coalesce(cc.start_date, parsed_pt.transaction_date_adjusted)
      AND parsed_pt.transaction_date_adjusted <= coalesce(cc.end_date, parsed_pt.transaction_date_adjusted)
  ),
  pt_payout AS (
    SELECT
      tmh.id AS gbp_internal_transaction_id,
      tmh.merchant_id AS gbp_merchant_id,
      tmh.amount AS amount_local,
      tmh.transfer_rate AS fee,
      0.0 AS vat,
      'PAY_OUT' AS payment_method,
      cast(null as string) AS card_scheme,
      cast(null as string) AS installment_month,
      cast(null as string) AS gbp_internal_acquirer_merchant_id,
      tmh.reference_no,
      cast(null as string) AS payment_card_no,
      cast(null as string) AS authorize_code,
      cc.payment_channel,
      cc.acquirer_merchant_id,
      cc.mdr,
      CASE cc.rate_type
        WHEN 'F' THEN 'Fix Rate'
        WHEN 'P' THEN 'Percent Rate'
        ELSE ''
      END AS partner_rate_type,
      CASE cc.rate_type
        WHEN 'F' THEN cc.mdr
        ELSE (cc.mdr / 100.0) * tmh.amount
      END AS cogs_local,
      try_divide(
        tmh.transfer_rate - CASE cc.rate_type
          WHEN 'F' THEN cc.mdr
          ELSE (cc.mdr / 100.0) * tmh.amount
        END,
        tmh.amount
      ) * 10000 AS net_take_rate_bps,
      tmh.transfer_rate - CASE cc.rate_type
        WHEN 'F' THEN cc.mdr
        ELSE (cc.mdr / 100.0) * tmh.amount
      END AS net_revenue_local,
      tmh.transfer_date AS create_date,
      tmh.transfer_date AS transaction_date,
      'Disbursement' AS transfer_type,
      ma.currency AS gbp_merchant_currency
    FROM {{ source('clean__globalprime', 'transfer_due_of_manual_history') }} tmh
    LEFT JOIN {{ source('clean__globalprime', 'merchant_info_additional') }} ma ON tmh.merchant_id = ma.merchant_id
    CROSS JOIN {{ source('clean__globalprime', 'cogs_configuration') }} cc
    WHERE cc.payment_channel = 'PAYOUT' 
      AND tmh.result_message = 'Success'
      AND (tmh.transfer_date >= coalesce(cc.start_date, tmh.transfer_date))
      AND (tmh.transfer_date <= coalesce(cc.end_date, tmh.transfer_date))
  )

SELECT
  pt_by_cogs.id AS gbp_internal_transaction_id,
  pt_by_cogs.merchant_id AS gbp_merchant_id,
  pt_by_cogs.amount2 AS amount_local,
  pt_by_cogs.rate AS fee,
  pt_by_cogs.rate * 0.07 AS vat,
  pt_by_cogs.payment_channel AS payment_method,
  pt_by_cogs.card_scheme,
  pt_by_cogs.installment_month,
  concat(pt_by_cogs.gc_merchant_id, '|', pt_by_cogs.gc_mpi_merchant_id) AS gbp_internal_acquirer_merchant_id,
  pt_by_cogs.merchant_inv2 AS reference_no,
  pt_by_cogs.payment_card_no,
  pt_by_cogs.approval_code AS authorize_code,
  pt_by_cogs.partner AS payment_channel,
  pt_by_cogs.acquirer_merchant_id,
  CASE 
    WHEN pt_by_cogs.payment_channel = 'QR' AND pt_by_cogs.partner = 'BBL' 
    THEN try_divide(pt_by_cogs.mdr, pt_qr_bbl_count.sum_qr_bbl)
    ELSE pt_by_cogs.mdr
  END AS mdr,
  CASE pt_by_cogs.rate_type
    WHEN 'F' THEN 'Fix Rate'
    WHEN 'P' THEN 'Percent Rate'
    ELSE ''
  END AS partner_rate_type,
  CASE
    WHEN pt_by_cogs.payment_channel = 'QR' AND pt_by_cogs.partner = 'BBL' 
    THEN try_divide(pt_by_cogs.mdr, pt_qr_bbl_count.sum_qr_bbl)
    WHEN pt_by_cogs.rate_type = 'F' THEN pt_by_cogs.mdr
    ELSE (pt_by_cogs.mdr / 100.0) * pt_by_cogs.amount2
  END AS cogs_local,
  try_divide(
    pt_by_cogs.rate - CASE
      WHEN pt_by_cogs.payment_channel = 'QR' AND pt_by_cogs.partner = 'BBL' 
      THEN try_divide(pt_by_cogs.mdr, pt_qr_bbl_count.sum_qr_bbl)
      WHEN pt_by_cogs.rate_type = 'F' THEN pt_by_cogs.mdr
      ELSE (pt_by_cogs.mdr / 100.0) * pt_by_cogs.amount2
    END,
    pt_by_cogs.amount2
  ) * 10000 AS net_take_rate_bps,
  pt_by_cogs.rate - CASE
    WHEN pt_by_cogs.payment_channel = 'QR' AND pt_by_cogs.partner = 'BBL' 
    THEN try_divide(pt_by_cogs.mdr, pt_qr_bbl_count.sum_qr_bbl)
    WHEN pt_by_cogs.rate_type = 'F' THEN pt_by_cogs.mdr
    ELSE (pt_by_cogs.mdr / 100.0) * pt_by_cogs.amount2
  END AS net_revenue_local,
  pt_by_cogs.create_date,
  pt_by_cogs.transaction_date_adjusted AS transaction_date,
  cast(null as string) AS transfer_type,
  pt_by_cogs.currency AS gbp_merchant_currency
FROM pt_by_cogs
LEFT JOIN pt_qr_bbl_count 
  ON date_trunc('month', pt_by_cogs.transaction_date_adjusted) = pt_qr_bbl_count.month
WHERE pt_by_cogs.priority = 1

UNION ALL

SELECT * FROM pt_payout;
