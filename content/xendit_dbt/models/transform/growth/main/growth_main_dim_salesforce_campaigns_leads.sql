{{ config(
    alias="dim_salesforce_campaigns_leads",
    tags = ["campaigns"]
) }}

with campaigns_leads_source 
as 
(select * from
{{source('clean__salesforce','leads')}}
)

, campaigns_leads as
(
select

id as leadid
,ownerid
,business_id__c as associated_leadid_business_id__c
,mql_date__c as associated_leadid_mql_date
,working_date__c as associated_leadid_working_date

,converted_date__c as associated_leadid_converted_date
,disqualified_date__c as associated_leadid_account_disqualified_date
,leadsource as associated_leadid_status
,lead_score__c as associated_leadid_lead_score__c
,isconverted as associated_leadid_isconverted
,convertedopportunityid as associated_leadid_convertedopportunityid

,convertedcontactid as associated_leadid_convertedcontactid
,convertedaccountid as associated_leadid_convertedaccountid

from
campaigns_leads_source 
)

select * from campaigns_leads