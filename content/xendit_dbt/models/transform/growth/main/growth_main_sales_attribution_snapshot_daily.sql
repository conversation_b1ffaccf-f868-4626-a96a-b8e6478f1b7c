{{ config(
    alias="sales_attribution_snapshot_daily"
) }}

WITH
-- https://xendit-prod.cloud.databricks.com/sql/editor/d3dd7b6c-d482-4084-a43d-722462d4bcd4?o=3626732626433765

user_split_rows AS (
  SELECT 
    id
    ,email
    ,TRIM(history_item) AS history_item
  FROM {{ source('clean__salesforce', 'users') }}
  LATERAL VIEW explode(split(division_history__c, ';')) AS history_item
  WHERE division_history__c IS NOT NULL
)
,user_split_column AS (
  SELECT 
    id
    ,email
    ,TRIM(REGEXP_EXTRACT(history_item, '^(.*)_(\d{4}-\d{2}-\d{2})$', 1)) AS division
    ,REGEXP_EXTRACT(history_item, '^(.*)_(\d{4}-\d{2}-\d{2})$', 2) AS snapshot_date
  FROM user_split_rows
)
,user_minmax AS (
    SELECT
        id
        ,email
        ,CAST(MIN(t1.snapshot_date) AS DATE) AS min_date
        ,CAST(CURRENT_DATE AS DATE) AS max_date
    FROM user_split_column t1
    GROUP BY 1,2
)
,user_filldates AS (
    SELECT 
        id
        ,email
        ,EXPLODE(SEQUENCE(min_date, max_date, INTERVAL 1 DAY)) AS snapshot_date
    FROM user_minmax
)
,user_with_gaps AS (
    SELECT
        d.snapshot_date,
        d.id,
        d.email,
        i.division,
        COUNT(i.division) OVER (
            PARTITION BY d.id ORDER BY d.snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS fill_group
    FROM user_filldates d
    LEFT JOIN user_split_column i
        ON d.snapshot_date = i.snapshot_date
        AND d.id = i.id
)
,user_history AS (
    -- Forward-fill missing values using FIRST_VALUE() within each group
    SELECT 
        snapshot_date,
        id,
        email,
        FIRST_VALUE(division) OVER (
            PARTITION BY id, fill_group ORDER BY snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS division
    FROM user_with_gaps
)

---- bid history ---
,sf_acchistory_initial_bid_part1 AS (
    -- Extract latest value for each account and field
    SELECT
        from_unixtime(t2.createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
        t2.createddate as createddate,
        t2.id as id,
        t2.accountid AS sf_id,
        t2.field,
        t2.newvalue AS value
    FROM (
        -- taking the max value in the same date for each owner, cpm, and secondary acc owner
        SELECT
            accountid,
            field,
            from_unixtime(createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
            max(createddate) AS max_createddate
            ,max(id) as max_id
        FROM {{ source('clean__salesforce', 'accounthistory') }}
        WHERE field IN ('Business_ID__c')
        GROUP BY 1,2,3
    ) t1
    LEFT JOIN {{ source('clean__salesforce', 'accounthistory') }} t2
        ON t1.accountid = t2.accountid
        AND t1.max_createddate = t2.createddate
        AND t1.field = t2.field
        AND t1.max_id = t2.id
)
,sf_acchistory_bid_initial_part2 AS (
  SELECT DISTINCT
    *
  FROM sf_acchistory_initial_bid_part1

  UNION

  SELECT DISTINCT
      t1.snapshot_date,
      t2.createddate as createddate,
      t2.id as id,
      t2.accountid AS sf_id,
      t2.field,
      t2.oldvalue AS value
  FROM (
      -- taking the max value in the same date for each owner, cpm, and secondary acc owner
      SELECT
          t1.accountid,
          t1.field,
          from_unixtime(t2.createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
          min(t1.createddate) AS min_createddate
          ,min(t1.id) as min_id
      FROM {{ source('clean__salesforce', 'accounts') }} t2
      LEFT JOIN {{ source('clean__salesforce', 'accounthistory') }} t1 ON t1.accountid=t2.id
      LEFT JOIN (SELECT sf_id, min(createddate) as snapshot_date FROM sf_acchistory_initial_bid_part1 GROUP BY 1) t3 ON t1.id=t3.sf_id
      WHERE from_unixtime(t2.createddate / 1000, 'yyyy-MM-dd')!=t3.snapshot_date AND t1.field IN ('Business_ID__c')
      GROUP BY 1,2,3
  ) t1
  LEFT JOIN {{ source('clean__salesforce', 'accounthistory') }} t2
      ON t1.accountid = t2.accountid
      AND t1.min_createddate = t2.createddate
      AND t1.field = t2.field
      AND t1.min_id = t2.id
)
,sf_acchistory_bid_initial AS (
    SELECT DISTINCT * FROM sf_acchistory_bid_initial_part2
    
    UNION ALL
    
    SELECT
        from_unixtime(t1.createddate / 1000, 'yyyy-MM-dd') as snapshot_date
        ,t1.createddate
        ,NULL AS id
        ,t1.id AS accountid
        ,'Business_ID__c' AS field
        ,t1.business_id__c AS business_id
    FROM {{ source('clean__salesforce', 'accounts') }} t1
    LEFT JOIN sf_acchistory_bid_initial_part2 t2 ON t1.id=t2.sf_id
    WHERE t2.sf_id IS NULL
)
,sf_acchistory_bid_minmax AS (
    SELECT
        sf_id
        ,CAST(MIN(t1.snapshot_date) AS DATE) AS min_date
        ,CAST(CURRENT_DATE AS DATE) AS max_date
    FROM sf_acchistory_bid_initial t1
    GROUP BY 1
)
,sf_acchistory_bid_filldates AS (
    SELECT 
        sf_id
        ,EXPLODE(SEQUENCE(min_date, max_date, INTERVAL 1 DAY)) AS snapshot_date
    FROM sf_acchistory_bid_minmax
)
,sf_acchistory_bid_with_gaps AS (
    SELECT
        d.snapshot_date,
        d.sf_id,
        i.value,
        COUNT(i.value) OVER (
            PARTITION BY d.sf_id ORDER BY d.snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS fill_group
    FROM sf_acchistory_bid_filldates d
    LEFT JOIN sf_acchistory_bid_initial i
        ON d.snapshot_date = i.snapshot_date
        AND d.sf_id = i.sf_id
)
,sf_acchistory_bid_history AS (
    -- Forward-fill missing values using FIRST_VALUE() within each group
    SELECT 
        snapshot_date,
        sf_id,
        FIRST_VALUE(value) OVER (
            PARTITION BY sf_id, fill_group ORDER BY snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS business_id
    FROM sf_acchistory_bid_with_gaps
)

---- source from salesforce account history table --------------------------------------------------------------------------
,sf_acchistory_initial_bfr AS (
    -- Extract latest value for each account and field per day
    SELECT
        from_unixtime(t2.createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
        t2.accountid AS sf_id,
        t2.field,
        t2.newvalue AS value
    FROM (
        -- taking the max value in the same date for each owner, cpm, and secondary acc owner
        SELECT
            accountid,
            field,
            from_unixtime(createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
            max(createddate) AS max_createddate
        FROM {{ source('clean__salesforce', 'accounthistory') }}
        WHERE field IN ('Owner', 'CP_Manager__c','Secondary_Account_Owner__c') AND datatype = 'EntityId'
        GROUP BY 1,2,3
    ) t1
    LEFT JOIN {{ source('clean__salesforce', 'accounthistory') }} t2
        ON t1.accountid = t2.accountid
        AND t1.max_createddate = t2.createddate
        AND t1.field = t2.field
    -- LEFT JOIN sfid_testing t3 ON t2.accountid=t3.sf_id
    WHERE datatype = 'EntityId'
    -- AND t3.sf_id IS NOT NULL
)

,sf_acchistory_initial AS (
    SELECT * FROM sf_acchistory_initial_bfr

    UNION ALL
    
    -- Extract earliest value for each account and field on the earliest acc history
    SELECT
        from_unixtime(t4.createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
        t2.accountid AS sf_id,
        t2.field,
        t2.oldvalue AS value
    FROM (
        -- taking the max value in the same date for each owner, cpm, and secondary acc owner
        SELECT
            accountid,
            field,
            from_unixtime(createddate / 1000, 'yyyy-MM-dd') AS snapshot_date,
            min(createddate) AS min_createddate
        FROM {{ source('clean__salesforce', 'accounthistory') }}
        WHERE field IN ('Owner', 'CP_Manager__c','Secondary_Account_Owner__c') AND datatype = 'EntityId'
        GROUP BY 1,2,3
    ) t1
    LEFT JOIN (
        SELECT
            t1.sf_id as accountid,
            t1.field,
            min(t1.snapshot_date) AS min_date
        FROM sf_acchistory_initial_bfr t1
        GROUP BY 1,2
     ) t3 
        ON t1.accountid=t3.accountid 
        AND t1.field=t3.field 
        AND t1.snapshot_date=t3.min_date
    LEFT JOIN {{ source('clean__salesforce', 'accounthistory') }} t2
        ON t3.accountid = t2.accountid
        AND t3.min_date=from_unixtime(t2.createddate / 1000, 'yyyy-MM-dd')
        AND t3.field = t2.field
        AND t1.min_createddate = t2.createddate
    LEFT JOIN {{ source('clean__salesforce', 'accounts') }} t4
        ON t2.accountid=t4.id
    -- LEFT JOIN sfid_testing t3 ON t2.accountid=t3.sf_id
    WHERE datatype = 'EntityId'
    -- AND t3.sf_id IS NOT NULL
)

,sf_acchistory_range AS (
    -- Extract min snapshot_date and ensure max_date extends to today
    SELECT
        t1.sf_id,
        t1.field,
        CAST(min(t1.snapshot_date) AS DATE) AS min_date,
        CAST(CURRENT_DATE AS DATE) AS max_date
    FROM sf_acchistory_initial t1
    GROUP BY 1,2,4
),
sf_acchistory_filldates AS (
    -- Generate continuous date sequence until today
    SELECT 
        sf_id, 
        field, 
        EXPLODE(SEQUENCE(min_date, max_date, INTERVAL 1 DAY)) AS snapshot_date
    FROM sf_acchistory_range
),
sf_acchistory_with_gaps AS (
    -- Join generated dates with existing account history
    SELECT
        d.snapshot_date,
        d.sf_id,
        d.field,
        i.value,
        SUM(CASE WHEN i.value IS NOT NULL THEN 1 ELSE 0 END) 
            OVER (PARTITION BY d.sf_id, d.field ORDER BY d.snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) 
            AS group_id
    FROM sf_acchistory_filldates d
    LEFT JOIN sf_acchistory_initial i
        ON d.snapshot_date = i.snapshot_date
        AND d.sf_id = i.sf_id
        AND d.field = i.field
),
sf_acchistory_combined AS (
    -- Fill missing values by carrying forward the last known value
    SELECT
        snapshot_date,
        sf_id,
        field,
        MAX(value) OVER (
            PARTITION BY sf_id, field, group_id
            ORDER BY snapshot_date ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS value
    FROM sf_acchistory_with_gaps
)
,sf_acchistory_before_final AS (
    SELECT
        t1.snapshot_date
        ,t1.sf_id
        ,t1.field
        ,t1.value
        ,t2.email
        ,CONCAT_WS(' ', t2.firstname, t2.middlename, t2.lastname) AS names
        ,coalesce(t3.division,t2.division) as division
        ,t2.department as team
        ,t2.country
    FROM sf_acchistory_combined t1
    LEFT JOIN {{ source('clean__salesforce', 'users') }} t2 ON t1.value=t2.id
    LEFT JOIN user_history t3 ON t1.value=t3.id AND t1.snapshot_date=t3.snapshot_date
)
,sf_acchistory_pre AS (
    SELECT
        t1.snapshot_date
        ,t1.sf_id
        ,t2.value as owner_id
        ,t3.value as cpm_id
        ,t4.value as sec_owner_id
        ,t2.team as team_assignment
        ,t2.division as account_division
        ,t2.team as team_owner
        ,t2.email as owner_email
        ,t3.email as cpm_email
        ,t4.email as sec_owner_email
        ,t2.names as owner_name
        ,t3.names as cpm_name
        ,t4.names as sec_owner_name
        ,t2.division as owner_division
        ,t3.division as cpm_division
        ,t4.division as sec_owner_division
        ,t2.team as owner_team
        ,t3.team as cpm_team
        ,t4.team as sec_owner_team
        ,t2.country as owner_country
        ,t3.country as cpm_country
        ,t4.country as sec_owner_country
        ,t5.business_id as business_id
    FROM (SELECT DISTINCT snapshot_date, sf_id FROM sf_acchistory_before_final) t1
    LEFT JOIN sf_acchistory_before_final t2 ON t1.snapshot_date=t2.snapshot_date AND t1.sf_id=t2.sf_id AND t2.field='Owner'
    LEFT JOIN sf_acchistory_before_final t3 ON t1.snapshot_date=t3.snapshot_date AND t1.sf_id=t3.sf_id AND t3.field='CP_Manager__c'
    LEFT JOIN sf_acchistory_before_final t4 ON t1.snapshot_date=t4.snapshot_date AND t1.sf_id=t4.sf_id AND t4.field='Secondary_Account_Owner__c'
    LEFT JOIN sf_acchistory_bid_history t5 ON t1.sf_id=t5.sf_id AND t1.snapshot_date=t5.snapshot_date
)
,acc_without_acc_history AS (
  SELECT
    t1.*, EXPLODE(SEQUENCE(cast(from_unixtime(t1.createddate/1000, 'yyyy-MM-dd') as date), CAST(CURRENT_DATE AS DATE), INTERVAL 1 DAY)) AS snapshot_date
  FROM {{ source('clean__salesforce', 'accounts') }} t1
  LEFT JOIN sf_acchistory_pre t2 ON t1.id=t2.sf_id
  WHERE cast(from_unixtime(t1.createddate/1000, 'yyyy-MM-dd') as date)>=date('2025-01-01')
  AND t2.sf_id IS NULL
)
,sf_acc_history AS (
  SELECT
      t1.snapshot_date
      ,t1.id as sf_id
      ,t1.ownerid as owner_id
      ,t1.cp_manager__c as cpm_id
      ,t1.secondary_account_owner__c as sec_owner_id
      ,t3.department as team_assignment
      ,coalesce(t6.division,t3.division) as account_division
      ,t3.department as team_owner
      ,t3.email as owner_email
      ,t4.email as cpm_email
      ,t5.email as sec_owner_email
      ,CONCAT_WS(' ', t3.firstname, t3.middlename, t3.lastname) as owner_name
      ,CONCAT_WS(' ', t4.firstname, t4.middlename, t4.lastname) as cpm_name
      ,CONCAT_WS(' ', t5.firstname, t5.middlename, t5.lastname) as sec_owner_name
      ,coalesce(t6.division,t3.division) as owner_division
      ,coalesce(t7.division,t4.division) as cpm_division
      ,coalesce(t8.division,t5.division) as sec_owner_division
      ,t3.department as owner_team
      ,t4.department as cpm_team
      ,t5.department as sec_owner_team
      ,t3.country as owner_country
      ,t4.country as cpm_country
      ,t5.country as sec_owner_country
      ,t9.business_id as business_id
  FROM acc_without_acc_history t1
  -- LEFT JOIN sf_acchistory_pre t2 ON t1.id=t2.sf_id
  LEFT JOIN {{ source('clean__salesforce', 'users') }} t3 ON t3.id=t1.ownerid
  LEFT JOIN {{ source('clean__salesforce', 'users') }} t4 ON t4.id=t1.cp_manager__c
  LEFT JOIN {{ source('clean__salesforce', 'users') }} t5 ON t5.id=t1.secondary_account_owner__c
  LEFT JOIN user_history t6 ON t6.id=t1.ownerid AND t1.snapshot_date=t6.snapshot_date
  LEFT JOIN user_history t7 ON t7.id=t1.cp_manager__c AND t1.snapshot_date=t7.snapshot_date
  LEFT JOIN user_history t8 ON t8.id=t1.secondary_account_owner__c AND t1.snapshot_date=t8.snapshot_date
  LEFT JOIN sf_acchistory_bid_history t9 ON t1.id=t9.sf_id AND t1.snapshot_date=t9.snapshot_date
  -- WHERE cast(from_unixtime(t1.createddate/1000, 'yyyy-MM-dd') as date)>=date('2025-01-01')
  -- AND t2.sf_id IS NULL

  UNION ALL

  SELECT * FROM sf_acchistory_pre WHERE snapshot_date>=date('2025-01-01')
)

----------------------------------------------------------------------------------------------------------------------------

,xpm AS (
    SELECT DISTINCT 
        t1.business_id AS master_business_id
        , t1.sf_id AS sf_master_id
        , year(t1.snapshot_date) AS year
        , quarter(t1.snapshot_date) AS quarter
        , month(t1.snapshot_date) AS month
        , t1.snapshot_date
        --cp
        , t1.cpm_id AS sf_master_cp_manager_id
        , t1.cpm_name AS sf_master_cp_manager_name
        , t1.cpm_email AS sf_master_cp_manager_email
        --account owner
        , t1.owner_id AS sf_master_owner_id
        , t1.owner_name AS sf_master_owner_name
        , t1.owner_email AS sf_master_owner_email
        , t1.sec_owner_id AS sf_master_secondary_owner_id
        , t1.sec_owner_name AS sf_master_secondary_owner_name
        , t1.sec_owner_email AS sf_master_secondary_owner_email
        --team
        , t1.owner_division AS sf_master_owner_division
        , t1.team_owner AS sf_master_team_owner
        , t1.account_division AS sf_master_acc_owner_division
        , t1.owner_country AS sf_master_acc_owner_country
        , t1.team_assignment AS sf_master_acc_team_assignment__c
    FROM sf_acc_history t1
    WHERE t1.business_id IN 
    (
        SELECT DISTINCT master_acc_business
        FROM {{ source('clean__xendit_platform_service_live', 'xenplatformrelationships') }}
        WHERE master_acc_business IS NOT NULL
    )
)
,ag_business AS (
    SELECT 
        *
        , EXPLODE(SEQUENCE(DATE(created), DATE(CURRENT_DATE), INTERVAL 1 DAY)) AS effective_date
    FROM (
        SELECT DISTINCT 
            t1.business_id as business_id
            , t1.country_of_operation
            , t1.industry_sector
            , DATE(t1.created) AS created
        FROM {{ ref('business_intelligence_main_dim_businesses') }} t1
    )
)
,ag_attribution_initial AS (
    SELECT
        
        --basic information
        t1.business_id AS business_id
        ,t4.sf_id AS sf_id
        
        --cp
        , t4.cpm_id AS sf_cp_manager_id
        , t4.cpm_name AS sf_cp_manager_name
        , t4.cpm_email AS sf_cp_manager_email
        , coalesce(t3.sf_master_cp_manager_id, t4.cpm_id) AS sf_rollup_cp_manager_id
        , coalesce(t3.sf_master_cp_manager_name, t4.cpm_name) AS sf_rollup_cp_manager_name
        , coalesce(t3.sf_master_cp_manager_email, t4.cpm_email) AS sf_rollup_cp_manager_email
        
        --account owner
        , t4.owner_id AS sf_owner_id
        , t4.owner_name AS sf_owner_name
        , t4.owner_email AS sf_owner_email
        , coalesce(t3.sf_master_owner_id, t4.owner_id) AS sf_rollup_owner_id
        , coalesce(t3.sf_master_owner_name, t4.owner_name) AS sf_rollup_owner_name
        , coalesce(t3.sf_master_owner_email, t4.owner_email) AS sf_rollup_owner_email
        , t4.sec_owner_id AS sf_secondary_owner_id
        , t4.sec_owner_name AS sf_secondary_owner_name
        , t4.sec_owner_email AS sf_secondary_owner_email
        , coalesce(t3.sf_master_secondary_owner_id, t4.sec_owner_id) AS sf_rollup_secondary_owner_id
        , coalesce(t3.sf_master_secondary_owner_name, t4.sec_owner_name) AS sf_rollup_secondary_owner_name
        , coalesce(t3.sf_master_secondary_owner_email, t4.sec_owner_email) AS sf_rollup_secondary_owner_email
        
        --team
        , t4.owner_division AS sf_owner_division
        , t4.team_owner AS sf_team_owner
        , t4.account_division AS sf_acc_owner_division
        , coalesce(t3.sf_master_owner_division, t4.owner_division) AS sf_rollup_owner_division
        , coalesce(t3.sf_master_team_owner, t4.team_owner) AS sf_rollup_team_owner
        , coalesce(t3.sf_master_acc_owner_division, t4.account_division) AS sf_rollup_acc_owner_division
        , t4.team_assignment AS sf_team_assignment
        , coalesce(t3.sf_master_acc_team_assignment__c, t4.team_assignment) AS sf_rollup_team_assignment
        , t4.owner_country AS sf_acc_owner_country
        , coalesce(t3.sf_master_acc_owner_country, t4.owner_country) AS sf_rollup_acc_owner_country
        --hybrid team
        , CASE 
            WHEN lower(t1.country_of_operation) = 'indonesia' AND lower(t1.industry_sector) IN ('crypto', 'cryptocurrency exchange')
                THEN 'ID Crypto'
            WHEN lower(t1.country_of_operation) = 'indonesia' AND coalesce(t3.sf_master_owner_email, t4.owner_email) = '<EMAIL>'
                THEN 'ID Pool AM'
            WHEN coalesce(t3.sf_master_owner_email, t4.owner_email) = '<EMAIL>'
                THEN 'Pool AM'
            WHEN coalesce(lower(t3.sf_master_acc_team_assignment__c), lower(t4.team_assignment)) = 'remittance'
                THEN "Regional Remittance"
            WHEN coalesce(lower(t3.sf_master_acc_team_assignment__c), lower(t4.team_assignment)) = 'cn'
                THEN "Regional Chinese"
            WHEN coalesce(lower(t3.sf_master_acc_team_assignment__c), lower(t4.team_assignment)) = 'fs'
                THEN "ID Financial Services"
            WHEN coalesce(t3.sf_master_owner_email, t4.owner_email) = '<EMAIL>' AND coalesce(lower(t3.sf_master_acc_team_assignment__c), lower(t4.team_assignment)) = 'am'
                THEN 'ID AM - Dedicated'
            WHEN coalesce(t3.sf_master_owner_email, t4.owner_email) = '<EMAIL>'
                THEN 'PH OB'
            WHEN lower(t1.country_of_operation) = 'philippines' AND (coalesce(t3.sf_master_owner_email, t4.owner_email) IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>') OR coalesce(t3.sf_master_owner_email, t4.owner_email) IS NULL)
                THEN 'PH IB + Pool AM'
            WHEN lower(t1.country_of_operation) = 'indonesia' AND (coalesce(t3.sf_master_owner_email, t4.owner_email) IN ('<EMAIL>', '<EMAIL>', '<EMAIL>') OR coalesce(t3.sf_master_owner_email, t4.owner_email) IS NULL)
                THEN 'ID Self Serve'
            WHEN coalesce(lower(t3.sf_master_owner_division), lower(t4.owner_division)) IN ('sales operations', 'group mgt', 'regional business development & channel partnerships', 'strategy & analytics')
                THEN 'Other'
            ELSE coalesce(t3.sf_master_owner_division, t4.owner_division, "Other")
            END AS sales_team
        
        --effective_date
        , t1.effective_date AS effective_date
        
    FROM (SELECT * FROM ag_business WHERE effective_date>=date('2025-01-01')) t1
    LEFT JOIN {{ source('clean__xendit_platform_service_live', 'xenplatformrelationships') }} t2 
        ON t1.business_id = t2.sub_acc_business
    LEFT JOIN xpm t3
        ON t2.master_acc_business = t3.master_business_id AND t1.effective_date = t3.snapshot_date
    LEFT JOIN sf_acc_history t4
        ON t1.business_id = t4.business_id AND t1.effective_date = t4.snapshot_date
)
,old_snapshot_not_in_new_initial AS (
  SELECT
      t2.*
      ,EXPLODE(SEQUENCE(DATE(t2.effective_date), DATE(CURRENT_DATE), INTERVAL 1 DAY)) AS effective_date_1
  FROM (
    SELECT 
      t1.business_id
      ,t1.sf_id
      ,max(t1.effective_date) as max_date
    FROM transform__growth_main.sales_attribution_snapshot_daily_before_20250101 t1
    LEFT JOIN ag_attribution_initial t2 ON t1.business_id=t2.business_id AND t1.sf_id=t2.sf_id
    WHERE t2.business_id IS NULL AND t2.sf_id IS NULL
    GROUP BY 1,2
  ) t1
  LEFT JOIN transform__growth_main.sales_attribution_snapshot_daily_before_20250101 t2 ON t1.business_id=t2.business_id AND t1.sf_id=t2.sf_id AND t1.max_date=t2.effective_date
)
,old_snapshot_not_in_new AS (
  SELECT
      t2.business_id AS business_id
      ,t1.sf_id
      ,t1.sf_cp_manager_id
      ,t1.sf_cp_manager_name
      ,t1.sf_cp_manager_email
      ,t1.sf_rollup_cp_manager_id
      ,t1.sf_rollup_cp_manager_name
      ,t1.sf_rollup_cp_manager_email
      ,t1.sf_owner_id
      ,t1.sf_owner_name
      ,t1.sf_owner_email
      ,t1.sf_rollup_owner_id
      ,t1.sf_rollup_owner_name
      ,t1.sf_rollup_owner_email
      ,t1.sf_secondary_owner_id
      ,t1.sf_secondary_owner_name
      ,t1.sf_secondary_owner_email
      ,t1.sf_rollup_secondary_owner_id
      ,t1.sf_rollup_secondary_owner_name
      ,t1.sf_rollup_secondary_owner_email
      ,t3.division AS sf_owner_division
      ,t1.sf_team_owner
      ,t3.division AS sf_acc_owner_division
      ,t4.division AS sf_rollup_owner_division
      ,t1.sf_rollup_team_owner
      ,t4.division AS sf_rollup_acc_owner_division
      ,t1.sf_team_assignment
      ,t1.sf_rollup_team_assignment
      ,t1.sf_acc_owner_country
      ,t1.sf_rollup_acc_owner_country
      ,CASE
        WHEN lower(t4.division) IN ('sales operations', 'group mgt', 'regional business development & channel partnerships', 'strategy & analytics')
            THEN 'Other'
        ELSE coalesce(t4.division, "Other")
      END AS sales_team
      ,t1.effective_date_1 as effective_date
  FROM old_snapshot_not_in_new_initial t1
  LEFT JOIN sf_acchistory_bid_history t2 ON t1.sf_id=t2.sf_id AND t1.effective_date_1=t2.snapshot_date
  LEFT JOIN user_history t3 ON t1.sf_owner_id=t3.id AND t1.effective_date_1=t3.snapshot_date
  LEFT JOIN user_history t4 ON t1.sf_rollup_owner_id=t3.id AND t1.effective_date_1=t3.snapshot_date
)
,team AS (
    SELECT DISTINCT 
        t1.team
        , t1.country
    FROM {{ source('clean__google_sheets', 'sales_target_v1') }} t1
)
,ag_attribution AS (
    SELECT 
        t1.*
        ,year(t1.effective_date) AS effective_year
        ,quarter(t1.effective_date) AS effective_quarter
        ,month(t1.effective_date) AS effective_month
        ,CASE 
            WHEN lower(t1.sales_team) = 'other'
                THEN 'Other'
            ELSE coalesce(t2.country, 'Other')
        END AS sales_country
    FROM ag_attribution_initial t1
    LEFT JOIN team t2
        ON t1.sales_team = t2.team

    UNION ALL

    SELECT 
        t1.*
        ,year(t1.effective_date) AS effective_year
        ,quarter(t1.effective_date) AS effective_quarter
        ,month(t1.effective_date) AS effective_month
        ,CASE 
            WHEN lower(t1.sales_team) = 'other'
                THEN 'Other'
            ELSE coalesce(t2.country, 'Other')
        END AS sales_country 
    FROM old_snapshot_not_in_new t1
    LEFT JOIN team t2
        ON t1.sales_team = t2.team
)

SELECT * FROM transform__growth_main.sales_attribution_snapshot_daily_before_20250101

UNION ALL

SELECT DISTINCT
    business_id
    ,sf_id
    ,sf_cp_manager_id
    ,sf_cp_manager_name
    ,sf_cp_manager_email
    ,sf_rollup_cp_manager_id
    ,sf_rollup_cp_manager_name
    ,sf_rollup_cp_manager_email
    ,sf_owner_id
    ,sf_owner_name
    ,sf_owner_email
    ,sf_rollup_owner_id
    ,sf_rollup_owner_name
    ,sf_rollup_owner_email
    ,sf_secondary_owner_id
    ,sf_secondary_owner_name
    ,sf_secondary_owner_email
    ,sf_rollup_secondary_owner_id
    ,sf_rollup_secondary_owner_name
    ,sf_rollup_secondary_owner_email
    ,sf_owner_division
    ,sf_team_owner
    ,sf_acc_owner_division
    ,sf_rollup_owner_division
    ,sf_rollup_team_owner
    ,sf_rollup_acc_owner_division
    ,sf_team_assignment
    ,sf_rollup_team_assignment
    ,sf_acc_owner_country
    ,sf_rollup_acc_owner_country
    ,sales_team
    ,effective_year
    ,effective_quarter
    ,effective_month
    ,effective_date
    ,sales_country
FROM ag_attribution
