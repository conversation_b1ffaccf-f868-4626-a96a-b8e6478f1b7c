WITH source AS (
    SELECT *
    FROM
        {{ source('clean__xendit_escrow_service_live', 'repayments') }}
    WHERE
        disbursement_id IS NOT NULL
),

debit AS (
    SELECT
        id || '-debit' AS id,
        business_id,
        internal_status AS status,
        0 AS credit,
        amount - COALESCE(admin_amount, 0) AS debit,
        disbursement_id,
        NULL::timestamp AS timestamp,
        NULL::string AS virtual_account_number,
        NULL::string AS virtual_account_number_internal,
        NULL::string AS payment_id,
        NULL::boolean AS is_direct,
        created,
        updated
    FROM
        source
)

SELECT * FROM debit
{% if is_incremental() %}
    WHERE updated >= DATE_SUB(CURRENT_DATE(), 7)
{% endif %}
