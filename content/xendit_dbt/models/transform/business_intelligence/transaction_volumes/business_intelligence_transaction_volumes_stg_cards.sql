{{ config(
    alias="stg_cards",
    tags=["v2_launch"],
    unique_key='reference',
    materialized='incremental'
) }}

WITH card_source AS (
    SELECT *
    FROM {{ ref('cards_main_integrated_card_v2') }}
    {% if is_incremental() %}
        WHERE charge_updated >= DATE_TRUNC('month', CURRENT_DATE() - INTERVAL '1' MONTH) AND charge_updated <= DATE_ADD(CURRENT_DATE(), 2)
    {% endif %}
),

invoices AS (
    SELECT *
    FROM {{ source('clean__xendit_invoice_service', 'invoices') }}
),

id_t4 AS (
    SELECT
        id,
        payment_id
    FROM {{ source('clean__tcdb', 'transaction') }}
),

ph_t4 AS (
    SELECT
        id,
        payment_id
    FROM {{ source('clean__transaction_service_v4', 'transaction') }}
),

cards AS (
    SELECT
        ic2.charge_id AS reference,
        ic2.entity,
        ic2.product_type,
        ic2.product_subtype,
        ic2.business_id AS user_id,
        ic2.acquiring_bank_name AS channel_name,
        ic2.charge_created AS created,
        ic2.charge_updated AS updated,
        ic2.charge_status AS status,
        ic2.charge_currency AS currency,
        ic2.money_flow,
        ic2.integration_type,
        ic2.source_client_type AS is_web_or_app,
        ic2.tpi_client_type AS is_tpi_transaction,
        ic2.platform_name AS tpi_platform_name,
        ic2.processing_platform,
        ic2.commercial_model,
        ic2.is_xendit_mid,
        ic2.papi_instrument_id,
        invoices.id AS invoice_id,
        COALESCE(ic2.fee_label, 'DEFAULT') AS label,
        CAST(ic2.charge_amount AS double) AS amount,
        DATE(ic2.dt) AS dt,
        CAST(NULL AS string) AS payout_link_id,
        MAX(ic2.settled) AS settled,
        MAX(COALESCE(id_t4.id, ph_t4.id)) AS transaction_id
    FROM card_source AS ic2
    LEFT JOIN id_t4
        ON (ic2.charge_id = id_t4.payment_id OR id_t4.payment_id = ('cc_' || ic2.charge_id))
    LEFT JOIN ph_t4
        ON (ic2.charge_id = ph_t4.payment_id OR ph_t4.payment_id = ('cc_' || ic2.charge_id))
    LEFT JOIN invoices
        ON (ic2.charge_id = invoices.credit_card_charge_id)
    WHERE
        1 = 1
        AND (
            ic2.client_type != 'OFF_RAILS'
            OR ic2.client_type IS NULL
        )
    --AND ic2.charge_updated is not null
    GROUP BY all
)

/* Outcome */
SELECT DISTINCT *
FROM cards
