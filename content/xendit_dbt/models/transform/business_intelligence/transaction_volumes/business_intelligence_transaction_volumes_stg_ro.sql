{{ config(
    alias="stg_ro",
    tags=["v2_launch"],
    unique_key='reference',
    materialized='incremental'
) }}

WITH id_fpc AS (
    SELECT *
    FROM {{ source('clean__xendit_collections_service', 'fixedpaymentcodes') }}
),

fpcd AS (
    SELECT *
    FROM {{ source('clean__xendit_deposit_service', 'fixedpaymentcodedeposits') }}
),

fpcp AS (
    SELECT *
    FROM {{ source('clean__xendit_collections_service', 'fixedpaymentcodepayments') }}
),

id_invoice AS (
    SELECT *
    FROM {{ source('clean__xendit_collections_service', 'retailoutletcollections') }}
),

ro_ph AS (
    SELECT *
    FROM {{ source('clean__retail_outlet_service', 'payment_slips') }}
),


ro_source AS (
    SELECT *
    FROM transform__transactions.integrated_retail_outlet
    WHERE payment_amount IS NOT NULL
        {% if is_incremental() %}
            AND payment_paid_on >= DATE_TRUNC('month', CURRENT_DATE() - INTERVAL '1' MONTH)
            AND payment_paid_on <= DATE_ADD(CURRENT_DATE(), 2)
        {% endif %}
),

businesses AS (
    SELECT *
    FROM {{ source('clean__xendit_business_service', 'businesses') }}
),

invoices AS (
    SELECT *
    FROM {{ source('clean__xendit_invoice_service', 'invoices') }}
),

tpi_invoices AS (
    SELECT *
    FROM {{ source('clean__xendit_tpi_service', 'invoices') }}
),

tpi AS (
    SELECT *
    FROM tpi_invoices AS tpi
    WHERE
        (tpi.is_live = TRUE OR tpi.is_live IS NULL)
        AND (tpi.request_type = 'CREATE_DIRECT_DEBIT' OR tpi.request_type IS NULL)
),

id_t4 AS (
    SELECT
        settled_at,
        id,
        payment_id
    FROM {{ source('clean__tcdb', 'transaction') }}
    WHERE type = 'RO_PAYMENT'
),

ph_t4 AS (
    SELECT
        settled_at,
        id,
        payment_id,
        status
    FROM {{ source('clean__transaction_service_v4', 'transaction') }}
    WHERE type = 'RO_PAYMENT'
),

ro_base AS (
    SELECT
        ti.primary_key AS reference,
        ti.payment_id,
        ti.payment_code_id,
        b.entity,
        'RETAIL_OUTLET' AS product_type,
        CASE
            WHEN payment_code_id = id_fpc.id THEN 'PAYMENT_CODE_FIXED'
            WHEN payment_code_id = id_invoice.id THEN 'PAYMENT_CODE_NONFIXED'
            WHEN payment_code_id = ro_ph.id THEN 'RO_PH'
        END AS product_subtype,
        ti.business_id AS user_id,
        payment_code_channel_id AS channel_name,
        CAST(payment_amount AS double) AS amount,
        payment_paid_on AS created,
        ti.status_updated AS updated,
        CASE
            WHEN payment_code_id = id_fpc.id THEN id_fpc.status
            WHEN payment_code_id = id_invoice.id THEN id_invoice.status
            WHEN payment_code_id = ro_ph.id THEN ph_t4.status
        END AS status,
        ti.currency,
        'MONEY_IN' AS money_flow,
        CASE
            WHEN invoices.client_type = 'STOREFRONT'
                THEN 'ONLINE_STORE'
            ELSE 'INVOICE'
        END AS integration_type,
        CASE
            WHEN invoices.client_type = 'MOBILE' THEN 'MOBILE APP'
            ELSE 'WEB'
        END AS is_web_or_app,
        CASE
            WHEN tpi.id IS NULL THEN 'NO_TPI'
            ELSE 'TPI'
        END AS is_tpi_transaction,
        tpi.platform_name,
        DATE(payment_paid_on) AS dt,
        CAST(NULL AS string) AS processing_platform,
        CAST(NULL AS string) AS commercial_model,
        CAST(NULL AS string) AS is_xendit_mid,
        ti.papi_instrument_id,
        invoices.id AS invoice_id,
        CAST(NULL AS string) AS payout_link_id,
        ti.label
    FROM ro_source AS ti
    INNER JOIN businesses AS b
        ON (b.id = ti.business_id)
    LEFT JOIN id_fpc
        ON ti.payment_code_id = id_fpc.id
    LEFT JOIN id_invoice
        ON ti.payment_code_id = id_invoice.id
    LEFT JOIN ro_ph
        ON ti.payment_code_id = ro_ph.id
    LEFT JOIN tpi
        ON ti.payment_id = tpi.invoice_id
    LEFT JOIN invoices
        ON ti.payment_id = invoices.id
    LEFT JOIN ph_t4
        ON ph_t4.payment_id = ti.payment_id
),

ro_base_2 AS (
    SELECT
        reference,
        payment_id,
        payment_code_id,
        entity,
        product_type,
        product_subtype,
        user_id,
        channel_name,
        amount,
        created,
        updated,
        CASE
            WHEN status IN ('PENDING', 'PENDING_SETTLEMENT', 'VOIDED', 'SETTLING', 'REVERSED')
                THEN status
            ELSE 'COMPLETED'
        END AS status,
        currency,
        money_flow,
        integration_type,
        is_web_or_app,
        is_tpi_transaction,
        platform_name,
        dt,
        processing_platform,
        commercial_model,
        invoice_id,
        payout_link_id,
        is_xendit_mid,
        papi_instrument_id,
        label
    FROM ro_base
),

fixed AS (
    SELECT *
    FROM ro_base_2
    WHERE product_subtype = 'PAYMENT_CODE_FIXED'
),

nonfixed AS (
    SELECT *
    FROM ro_base_2
    WHERE product_subtype != 'PAYMENT_CODE_FIXED'
),

fixed_ro AS (
    SELECT
        fixed.*,
        id_t4.settled_at AS settled,
        id_t4.id AS transaction_id
    FROM fixed
    LEFT JOIN id_fpc
        ON fixed.payment_code_id = id_fpc.id
    LEFT JOIN fpcp
        ON id_fpc.id = fpcp.fixed_payment_code_id
    LEFT JOIN fpcd
        ON fpcd.fixed_payment_code_payment_id = fpcp.id
    LEFT JOIN id_t4
        ON id_t4.payment_id = fpcd.id OR id_t4.payment_id = fpcd.payment_id OR id_t4.id = fpcd.transaction_id
/* or id_t4.payment_id = ti.payment_id */
                 /* LEFT JOIN ph_t4 */
                 /* ON ph_t4.payment_id = fpcd.id */
                 /* or ph_t4.payment_id = fpcd.payment_id */
                 /* or ph_t4.id = fpcd.transaction_id */
                 /* or ph_t4.payment_id = ti.payment_id */
),

nonfixed_ro AS (
    SELECT
        nonfixed.*,
        COALESCE(id_t4.settled_at, ph_t4.settled_at) AS settled,
        COALESCE(id_t4.id, ph_t4.id) AS transaction_id
    FROM nonfixed
    LEFT JOIN id_t4
        ON id_t4.payment_id = nonfixed.payment_id
    LEFT JOIN ph_t4
        ON ph_t4.payment_id = nonfixed.payment_id
),

ro_union_table AS (
    SELECT *
    FROM nonfixed_ro
    UNION ALL
    SELECT *
    FROM fixed_ro
)

/* Outcome */
SELECT
    reference,
    entity,
    product_type,
    product_subtype,
    user_id,
    channel_name,
    CAST(amount AS double) AS amount,
    created,
    updated,
    CASE
        WHEN status IN ('PENDING', 'PENDING_SETTLEMENT', 'VOIDED', 'SETTLING', 'REVERSED')
            THEN status
        ELSE 'COMPLETED'
    END AS status,
    currency,
    money_flow,
    integration_type,
    is_web_or_app,
    is_tpi_transaction,
    platform_name AS tpi_platform_name,
    dt,
    processing_platform,
    commercial_model,
    is_xendit_mid,
    papi_instrument_id,
    invoice_id,
    payout_link_id,
    label,
    MAX(settled) AS settled,
    MAX(transaction_id) AS transaction_id
FROM ro_union_table
GROUP BY ALL
