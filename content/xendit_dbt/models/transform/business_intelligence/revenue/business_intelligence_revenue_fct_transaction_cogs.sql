{{ config(
    alias="fct_transaction_cogs"
)}}
-- Select all records and columns from the primary (IDR) table
SELECT
    dt,
    dt_month,
    business_id,
    account_relation_id,
    xendit_entity,
    product_type,
    product_subtype,
    channel_name,
    channel_adjusted,
    sender_bank_code,
    sender_bank_code_adjusted, -- Column exists in IDR, but not PH
    currency,
    money_flow,
    commercial_model,
    integration_type,
    is_web_or_app,
    processing_platform,
    deduction_type,
    statement_generation_type,
    tpi_platform_name,
    is_tpi_transaction,
    routing,                 -- Column exists in IDR, but not PH
    bucket,                  -- Column exists in IDR, but not PH
    is_switcher,             -- Column exists in IDR, but not PH
    partner_name,            -- Column exists in IDR, but not PH
    transaction_type_cogs,   -- Column exists in IDR, but not PH
    transaction_amount,
    transaction_count,
    fees,
    total_cost,
    net_revenue
FROM
    clean__de_test.daily_net_revenue_first

UNION ALL

-- Select records from the PH table and add NULL for missing columns
SELECT
    dt,
    dt_month,
    business_id,
    account_relation_id,
    xendit_entity,
    product_type,
    product_subtype,
    channel_name,
    channel_adjusted,
    sender_bank_code,
    CAST(NULL AS STRING) AS sender_bank_code_adjusted, -- Add NULL placeholder
    currency,
    money_flow,
    commercial_model,
    integration_type,
    is_web_or_app,
    processing_platform,
    deduction_type,
    statement_generation_type,
    tpi_platform_name,
    is_tpi_transaction,
    CAST(NULL AS STRING) AS routing,                 -- Add NULL placeholder
    CAST(NULL AS STRING) AS bucket,                  -- Add NULL placeholder
    CAST(NULL AS STRING) AS is_switcher,             -- Add NULL placeholder
    CAST(NULL AS STRING) AS partner_name,            -- Add NULL placeholder
    transaction_type AS transaction_type_cogs,       -- Aliasing `transaction_type` to match
    transaction_amount,
    1 AS transaction_count,                          -- Assuming 1 transaction per row for PH data
    fees,
    cogs_amount AS total_cost,                       -- Aliasing `cogs_amount` to match
    net_revenue
FROM
    clean__de_test.daily_net_revenue_first_ph;