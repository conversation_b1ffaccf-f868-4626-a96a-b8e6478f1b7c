{{ config(
    alias="int_product_rate_setting_type2scd_cogs"
)}}


WITH base_data AS (
  SELECT DISTINCT *, created_month AS created_date
  FROM clean__de_test.cogs_rate_check
),

with_changes AS (
  SELECT *,
    LAG(CONCAT_WS('|', tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee, tier_1_end, tier_2_end)) OVER (
      PARTITION BY
        xendit_entity, product_type, sender_bank_code, channel_name,
        transaction_type, routing, cards_is_switcher_yes_no,
        va_is_switching_yes_no, is_switcher
      ORDER BY created_date
    ) AS prev_rate_signature
  FROM base_data
),
mark_change_groups AS (
  SELECT *,
    SUM(CASE
          WHEN prev_rate_signature IS NULL OR
               CONCAT_WS('|', tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee, tier_1_end, tier_2_end) != prev_rate_signature
          THEN 1 ELSE 0
        END
    ) OVER (
      PARTITION BY
        xendit_entity, product_type, sender_bank_code, channel_name,
        transaction_type, routing, cards_is_switcher_yes_no,
        va_is_switching_yes_no, is_switcher
      ORDER BY created_date
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS change_group
  FROM with_changes
),
grouped_scd AS (
  SELECT
    xendit_entity, product_type, sender_bank_code, channel_name,
    transaction_type, routing, cards_is_switcher_yes_no,
    va_is_switching_yes_no, is_switcher, cost_structure,
    tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee,
    tier_1_end, tier_2_end,
    MIN(created_date) AS effective_start_date,
    MAX(created_date) AS effective_end_date
  FROM mark_change_groups
  GROUP BY
    xendit_entity, product_type, sender_bank_code, channel_name,
    transaction_type, routing, cards_is_switcher_yes_no,
    va_is_switching_yes_no, is_switcher, cost_structure,
    tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee,
    tier_1_end, tier_2_end,
    change_group
),
final_scd AS (
  SELECT *,
    DATE_ADD(effective_end_date, 1) AS effective_end_date_plus1,
    ROW_NUMBER() OVER (
      PARTITION BY xendit_entity, product_type, sender_bank_code, channel_name,
                   transaction_type, routing, cards_is_switcher_yes_no,
                   va_is_switching_yes_no, is_switcher
      ORDER BY effective_start_date DESC
    ) = 1 AS is_current
  FROM grouped_scd
)
SELECT
    'IDR' as currency,
  xendit_entity,
  product_type,
  sender_bank_code,
  channel_name,
  transaction_type,
  routing,
  cards_is_switcher_yes_no,
  va_is_switching_yes_no,
  is_switcher,
  cost_structure,
  tier_1_cost,
  tier_2_cost,
  tier_3_cost,
  pct_cost,
  fixed_fee,
  tier_1_end,
  tier_2_end,
  effective_start_date,

  CASE
    WHEN is_current THEN current_date() + INTERVAL 1 DAY
    ELSE effective_end_date_plus1
  END AS effective_end_date,

  is_current,

  DATE_TRUNC('MONTH', effective_start_date) AS effective_start_month,

  CASE
    WHEN is_current THEN DATE_TRUNC('MONTH', ADD_MONTHS(current_date(), 1))
    ELSE DATE_TRUNC('MONTH',
      CASE
        WHEN effective_end_date_plus1 IS NOT NULL THEN effective_end_date_plus1
        ELSE effective_end_date
      END
    )
  END AS effective_end_month

FROM final_scd;
