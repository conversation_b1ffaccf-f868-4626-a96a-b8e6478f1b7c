{{ config(
    alias="daily_net_revenue_first"
)}}

WITH base_data_prep AS (
SELECT
date_trunc('MONTH', fct_transaction_fees.dt) as dt_month,
fct_transaction_fees.dt,
fct_transaction_fees.business_id,
fct_transaction_fees.account_relation_ id,
entity_adjusted AS xendit_entity,
fct_transaction_fees.product_type,
fct_transaction_fees.product_subtype,
fct_transaction_fees.sender_bank_code,
fct_transaction_fees.currency,
fct_transaction_fees.tpi_platform_name,
fct_transaction_fees.money_flow,
fct_transaction_fees.commercial_model,
fct_transaction_fees.integration_type,
fct_transaction_fees.is_web_or_app,
fct_transaction_fees.processing_platform,
fct_transaction_fees.deduction_type,
fct_transaction_fees.statement_generation_type,
fct_transaction_fees.channel_name,
fct_transaction_fees.transaction_type,
fct_transaction_fees.transaction_channel,
cast(dateadd(hour, timezone_adjustment, fct_transaction_fees.created) as date) as created_local,
upper(channel_adjustments.channel_adjusted) as channel_adjusted,
upper(sender_bank_code_adjustments.sender_bank_code_clean) as sender_bank_code_adjusted,
upper(sender_bank_code_adjustments.routing) as routing,

CASE
WHEN transaction_amount <= ******** THEN '0 - 50 mio'
WHEN transaction_amount > ********* THEN '> 100 mio'
ELSE '50 - 100 mio'
END AS bucket,

CASE
WHEN UPPER(commercial_model) = 'SWITCHER' THEN 'Yes'
ELSE 'No'
END AS is_switcher,

CASE
WHEN is_tpi_transaction = 'TPI' THEN 'Yes'
ELSE 'No'
END AS is_tpi_transaction,

CASE
    -- If the Product_Type is 'Disbursement', the partner is the adjusted sender bank.
    WHEN product_type = 'DISBURSEMENT'
    THEN sender_bank_code_adjusted

    -- For all other product types, the partner is the adjusted channel.
    ELSE channel_adjusted
END AS partner_name,


CASE
    -- Rule 1: Non-Disbursement Check
    WHEN product_type <> 'DISBURSEMENT' THEN 'NON-DISBURSEMENT'

    -- Rule 2: Keyword-Based Checks
    WHEN fct_transaction_fees.sender_bank_code LIKE '%FIRECASH%' THEN 'FIRECASH'
    WHEN channel_adjusted = sender_bank_code_adjusted THEN 'INHOUSE'
    WHEN fct_transaction_fees.sender_bank_code LIKE '%BIFAST%' THEN 'BIFAST'
    WHEN fct_transaction_fees.sender_bank_code LIKE '%RTGS%' THEN 'RTGS'

    -- Rule 3: Original "OT" Rule
    WHEN
        fct_transaction_fees.sender_bank_code IN ('SAHABAT_SAMPOERNA', 'ID_BSS', 'BSS', 'ID_PRIMA', 'PRIMA')
        AND bucket IN ('0 - 50 mio', '50 - 100 mio')
    THEN 'OT'

    -- Rule 4: BI-FAST Logic Rule
    WHEN
        Channel_Adjusted IN (SELECT bank_code FROM hive_metastore.clean__de_test.bifast_adjustments) -- Assumes a table `bifast_banks` with a `bank_code` column
        AND Routing = 'IB'
      --  AND (TPV_Local / Count) <= ********* #TODO: Check on this logic
      AND transaction_amount <= *********
    THEN 'BIFAST'

    -- Rule 5: NEW "LLG" Rule for Specific Channels
    WHEN
        fct_transaction_fees.channel_name IN ('BANGKOK', 'DEUTSCHE', 'FAMA', 'BISNIS_INTERNASIONAL')
        AND bucket IN ('0 - 50 mio', '50 - 100 mio')
    THEN 'LLG'

    -- Rule 6: NEW Specific Partner & Channel Rules
    WHEN Channel_Adjusted = 'BNI VA_INT_DISB' AND sender_bank_code_adjusted = 'BNI' THEN 'ESCROW'
    WHEN Channel_Adjusted = 'GOPAY' AND sender_bank_code_adjusted = 'PERMATA' THEN 'GOPAYPBP'
    WHEN Channel_Adjusted = 'SHOPEEPAY' AND sender_bank_code_adjusted = 'PERMATA' THEN 'SHOPEEPAYPBP'
    WHEN Channel_Adjusted = 'OVO' AND sender_bank_code_adjusted = 'PERMATA' THEN 'OVOPBP'
    WHEN fct_transaction_fees.Channel_Name = 'LINKAJA' AND fct_transaction_fees.sender_bank_code IN ('BRI', 'ID_BRI') THEN 'BRILINKAJA'
    WHEN Channel_Adjusted = 'BCA' AND sender_bank_code_adjusted = 'ID_BCA_FIRECASH' THEN 'ID_BCA_FIRECASH'

    -- Rule 7: NEW Default Logic Based on Amount
    WHEN bucket = '0 - 50 mio' THEN 'OT'
    WHEN bucket = '50 - 100 mio' THEN 'LLG'
    ELSE 'RTGS'
END AS transaction_type_cogs,

transaction_amount,
fees-es_fee as fees

FROM transform__business_intelligence_revenue.fct_transaction_fees

LEFT JOIN main.transform__business_intelligence.dim_businesses
    ON fct_transaction_fees.business_id = dim_businesses.business_id

LEFT JOIN clean__de_test.channel_adjustments
    ON upper(fct_transaction_fees.channel_name) =  upper(channel_adjustments.channel_name)

LEFT JOIN clean__de_test.sender_bank_code_adjustments
    ON upper(fct_transaction_fees.sender_bank_code) =  upper(sender_bank_code_adjustments.sender_bank_code)

LEFT JOIN main.dbt_seed.country_timezones
    ON dim_businesses.country_of_operation = country_timezones.country_name

WHERE
fct_transaction_fees.currency='IDR'
),

agg_tpv_count as (
SELECT
dt_month,
dt,
business_id,
account_relation_id,
xendit_entity,
product_type,
product_subtype,
money_flow,
commercial_model,
integration_type,
is_web_or_app,
processing_platform,
deduction_type,
statement_generation_type,

channel_name,

transaction_type,
transaction_channel,
created_local,
channel_adjusted,
sender_bank_code,
COALESCE(sender_bank_code_adjusted, 'Non-Disbursement') sender_bank_code_adjusted,
currency,
is_tpi_transaction,
tpi_platform_name,
COALESCE(routing, 'no routing') routing,
bucket,
is_switcher,
partner_name,
transaction_type_cogs,
sum(transaction_amount) as transaction_amount,
count(1) as transaction_count,
sum(fees) as fees
 FROM base_data_prep
 GROUP BY ALL
),

rates_linking as (
SELECT agg_tpv_count.*,
cast(null as string) as loan_id,
cast(null as string) as amortization_id,
cast(null as string) as amortization_start_date,
cast(null as string) as amortization_end_date,
cast(null as string) as days_elapsed,
dt as created_utc,
0 as xendit_rate_final,
	cog_rates.cogs_cost_structure
    ,cog_rates.cogs_tier_1_cost
    ,cog_rates.cogs_tier_2_cost
    ,cog_rates.cogs_tier_3_cost
    ,cog_rates.cogs_pct_cost
    ,cog_rates.cogs_fixed_fee
    ,cog_rates.cogs_tier_1_end
    ,cog_rates.cogs_tier_2_end
    ,cog_rates.cogs_is_current_rate
    ,cog_rates.cogs_rates_effective_start_date
    ,cog_rates.cogs_rates_effective_end_date
    ,cog_rates.cogs_rates_effective_start_month
    ,cog_rates.cogs_rates_effective_end_month
    ,cog_rates.platform_fees_pct
    ,cog_rates.tier_cost
    ,cog_rates.tier
    ,cog_rates.cogs_base_fee

 FROM agg_tpv_count

 LEFT JOIN
     clean__de_test.cogs_consolidated_monthly_rate_linking cog_rates
 ON  agg_tpv_count.xendit_entity = cog_rates.xendit_entity
 AND agg_tpv_count.dt_month = cog_rates.dt_month
 AND agg_tpv_count.product_type = cog_rates.product_type
 AND agg_tpv_count.product_subtype = cog_rates.product_subtype
 AND UPPER(agg_tpv_count.channel_adjusted) = UPPER(cog_rates.channel_adjusted)
 AND coalesce(agg_tpv_count.sender_bank_code, 'NULL Sender Bank Code') = coalesce(cog_rates.sender_bank_code, 'NULL Sender Bank Code')
 AND COALESCE(agg_tpv_count.sender_bank_code_adjusted, 'Non-Disbursement')  = COALESCE(cog_rates.sender_bank_code_adjusted, 'Non-Disbursement')
 AND agg_tpv_count.currency = cog_rates.currency
 AND COALESCE(agg_tpv_count.routing, 'no routing') = COALESCE(cog_rates.routing, 'no routing')
 AND COALESCE(agg_tpv_count.tpi_platform_name,'NULL TPI Platform Name') = COALESCE(cog_rates.tpi_platform_name, 'NULL TPI Platform Name')
 AND agg_tpv_count.is_switcher = cog_rates.is_switcher
 AND agg_tpv_count.bucket = cog_rates.bucket
 AND agg_tpv_count.transaction_type_cogs = cog_rates.transaction_type_cogs
 AND UPPER(agg_tpv_count.partner_name) = UPPER(cog_rates.partner_name)
 AND agg_tpv_count.is_tpi_transaction = cog_rates.is_tpi_transaction
)

SELECT *,
cogs_base_fee * transaction_count AS total_base_cost,
platform_fees_pct * transaction_amount AS total_platform_cost,

CASE WHEN cogs_cost_structure='Revenue_Share'
THEN cogs_pct_cost * fees
ELSE cogs_pct_cost * transaction_amount END AS total_pct_cost,

CASE WHEN upper(product_type)!='IN-HOUSE TRANSACTION' THEN
total_base_cost + total_platform_cost + total_pct_cost
ELSE 0 END
as total_cost,

CASE WHEN upper(product_type)!='IN-HOUSE TRANSACTION'
THEN fees - total_cost
ELSE fees END as net_revenue
 FROM rates_linking
;