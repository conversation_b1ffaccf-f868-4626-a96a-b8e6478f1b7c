version: 2
models:
- name: business_intelligence_revenue_int_enriched_transactions
  description: >
    Intermediate table that consists of transaction backbone data (for completed transactions),
    enriched with additional metadata from product tables that will be used to calculate revenue.
  meta:
    source_group: revenue
    owner: business_intelligence
  columns:
  - name: reference
    description: Reference ID from transaction backbone
    tests:
    - unique:
        where: "dt >= __two_months_ago__"
    - not_null:
        where: "dt >= __two_months_ago__"
