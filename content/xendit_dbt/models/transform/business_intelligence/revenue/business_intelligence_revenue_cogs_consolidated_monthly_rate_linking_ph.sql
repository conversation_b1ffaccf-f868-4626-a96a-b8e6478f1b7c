{{ config(
    alias="cogs_consolidated_monthly_rate_linking_ph"
)}}

WITH
  -- CTE to pre-process and cast data from the base transactions table
  fees_base AS (
    SELECT
      *,
      TRY_CAST(dt AS TIMESTAMP) AS casted_dt,
      -- Preserve original product_type for filtering and logic before it's redefined
      product_type AS original_product_type
    FROM
      transform__business_intelligence_revenue.fct_transaction_fees
    WHERE
      currency = 'PHP'
      AND product_type NOT IN ('IN-HOUSE TRANSACTION', 'RETAIL_OUTLET')
  ),

  -- CTE to define product and channel logic
  pg_fees AS (
    SELECT
      base.* EXCEPT (product_type, dt, original_product_type, casted_dt),
      base.casted_dt AS dt,
      -- Standardize product_type to match the cost table (e.g., DISBURSEMENT -> REMITTANCE_PAYOUT)
      CASE
        WHEN base.original_product_type = 'DISBURSEMENT' AND base.product_subtype = 'REMITTANCE_PAYOUT'
        THEN 'REMITTANCE_PAYOUT'
        ELSE base.original_product_type
      END AS product_type,
      CASE
        WHEN base.original_product_type = 'CREDIT_CARD'
        THEN COALESCE(ic.mid_label, base.channel_name)
        WHEN base.original_product_type in ('DISBURSEMENT', 'REMITTANCE_PAYOUT')
        THEN base.sender_bank_code
        ELSE base.channel_name
      END AS channel_adjusted
    FROM
      fees_base AS base
    LEFT JOIN
      transform__cards.integrated_card_v2 AS ic
      ON ic.charge_id = base.reference
    WHERE
      -- Filter on the pre-casted and validated timestamp
      base.casted_dt >= '2025-01-01'
  ),

  -- CTE for monthly aggregation
  agg_data AS (
    SELECT
      DATE_TRUNC('month', dt) AS dt_month,
      business_id,
      account_relation_id,
      product_type,
      product_subtype,
      channel_name,
      channel_adjusted,
      transaction_type,
      transaction_channel,
      currency,
      SUM(transaction_amount) AS tpv,
      COUNT(1) AS transaction_count,
      SUM(fees) AS gross_fees
    FROM
      pg_fees
    GROUP BY
      -- Explicitly list grouping columns instead of using non-standard 'GROUP BY ALL'
      dt_month,
      business_id,
      account_relation_id,
      product_type,
      product_subtype,
      channel_name,
      channel_adjusted,
      transaction_type,
      transaction_channel,
      currency
  ),

  -- CTE to pre-cast and clean the cost structure table to avoid repeating functions in joins
  cost_structure AS (
    SELECT
      bid,
      product_type,
      channel_name,
      tier_1_cost,
      tier_2_cost,
      TRY_CAST(effective_start_month AS TIMESTAMP) AS effective_start_ts,
      TRY_CAST(effective_end_month AS TIMESTAMP) AS effective_end_ts
    FROM
      clean__de_test.cost_structure_ph_for_daily_scd
    WHERE
      -- Filter out invalid date ranges once
      TRY_CAST(effective_start_month AS TIMESTAMP) IS NOT NULL
      AND TRY_CAST(effective_end_month AS TIMESTAMP) IS NOT NULL
  ),

  -- CTE to join aggregated data with cost rates
  rate_join AS (
    SELECT
      agg.*,
      -- A single CASE expression handles the special override for a specific business.
      -- Otherwise, it falls back to the specific or generic rate found in the join.
      CASE
        WHEN agg.business_id = '611e0a13044b5240501bce1c'
         AND agg.product_type = 'DIRECT_DEBIT'
         AND agg.dt_month >= '2025-01-01T00:00:00.000+00:00'
        THEN 0
        ELSE COALESCE(scd_specific.tier_1_cost, scd_generic.tier_1_cost)
      END AS fix_fee_rate,
      CASE
        WHEN agg.business_id = '611e0a13044b5240501bce1c'
         AND agg.product_type = 'DIRECT_DEBIT'
         AND agg.dt_month >= '2025-01-01T00:00:00.000+00:00'
        THEN 0
        ELSE COALESCE(scd_specific.tier_2_cost, scd_generic.tier_2_cost)
      END AS pct_fee_rate
    FROM
      agg_data AS agg
    -- Join for generic cost rates (no business ID)
    LEFT JOIN
      cost_structure AS scd_generic
      ON TRIM(agg.product_type) = TRIM(scd_generic.product_type)
      AND TRIM(agg.channel_adjusted) = TRIM(scd_generic.channel_name)
      AND agg.dt_month >= scd_generic.effective_start_ts
      AND agg.dt_month < scd_generic.effective_end_ts
      AND scd_generic.bid IS NULL
    -- Join for business-specific cost rates
    LEFT JOIN
      cost_structure AS scd_specific
      ON TRIM(agg.product_type) = TRIM(scd_specific.product_type)
      AND TRIM(agg.channel_adjusted) = TRIM(scd_specific.channel_name)
      AND scd_specific.bid = agg.business_id
      AND agg.dt_month >= scd_specific.effective_start_ts
      AND agg.dt_month < scd_specific.effective_end_ts
  )
-- Final selection of all columns plus the derived COGS rate type
SELECT
  dt_month,
  business_id,
  account_relation_id,
  product_type,
  product_subtype,
  channel_name,
  channel_adjusted,
  transaction_type,
  transaction_channel,
  currency,
  tpv,
  transaction_count,
  gross_fees,
  fix_fee_rate,
  pct_fee_rate,
  CASE
    -- New: always label credit-card as Combined Fee
    WHEN product_type = 'CREDIT_CARD' THEN 'Combined Fee'
    WHEN fix_fee_rate IS NOT NULL AND pct_fee_rate IS NOT NULL
    THEN
      CASE
        WHEN (fix_fee_rate * transaction_count) > (pct_fee_rate * tpv) THEN 'Fixed Fee'
        ELSE 'Pct Fee'
      END
  END AS cogs_rate_type
FROM
  rate_join;

