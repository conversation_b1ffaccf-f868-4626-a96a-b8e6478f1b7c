{{ config(
    alias="int_product_rate_setting_type2scd_cogs_ph"
)}}

  WITH raw_csv AS (
      SELECT
          product_type, channel_code AS channel_name, bid, industry,
          CAST(tier_1_fixed_rate AS DOUBLE) AS tier_1_cost,
          CAST(tier_2_percentage AS DOUBLE) AS tier_2_cost,
          MAKE_DATE(CAST(year AS INT), CAST(month AS INT), 1) AS created_month
      FROM clean__de_test.cost_structure_ph_for_daily
  ),
  final_scd AS (
      SELECT
          product_type, channel_name, tier_1_cost, tier_2_cost, bid, industry,
          MIN(created_month) AS effective_start_date,
          LEAD(MIN(created_month), 1, '9999-12-31') OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY MIN(created_month)) AS effective_end_date
      FROM (
          SELECT *,
              SUM(CASE WHEN is_new_group = 1 THEN 1 ELSE 0 END) OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY created_month) as change_group
          FROM (
              SELECT *,
                  CASE WHEN CONCAT_WS('|', tier_1_cost, tier_2_cost) != LAG(CONCAT_WS('|', tier_1_cost, tier_2_cost)) OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY created_month) THEN 1 ELSE 0 END as is_new_group
              FROM raw_csv
          )
      )
      GROUP BY product_type, channel_name, tier_1_cost, tier_2_cost, bid, industry, change_group
  )
  SELECT
      product_type, channel_name, tier_1_cost, tier_2_cost, bid, industry,
      DATE_TRUNC('MONTH', effective_start_date) AS effective_start_month,
      DATE_TRUNC('MONTH', effective_end_date) AS effective_end_month
  FROM final_scd;