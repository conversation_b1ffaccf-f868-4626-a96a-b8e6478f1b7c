{{ config(
    alias="cogs_consolidated_monthly_rate_linking"
)}}

INSERT OVERWRITE clean__de_test.cogs_consolidated_monthly_rate_linking
WITH gross_rev AS (
  SELECT *,
    CASE
      WHEN product_type = 'DISBURSEMENT' AND transaction_type IS NULL THEN
        CASE
          WHEN product_subtype = 'DISBURSEMENT_ESCROW' THEN 'ESCROW_MONEY_OUT_EVENT'
          WHEN product_subtype = 'REMITTANCE_PAYOUT' THEN 'REMITTANCE'
          WHEN product_subtype = 'DIRECT_DISBURSEMENT' THEN 'DISBURSEMENT'
          WHEN product_subtype = 'ESCROW_DISBURSEMENT' THEN 'DISBURSEMENT'
          ELSE product_subtype
        END
      ELSE transaction_type
    END AS transaction_type1
  FROM transform__business_intelligence_revenue.fct_transaction_fees
  WHERE 1=1
    AND currency = 'IDR'
    -- AND dt BETWEEN '2025-04-01' and '2025-05-01'
    -- AND business_id = '66c6c3f2223dbd8b4355f3ec'
    -- AND product_type = 'QR_CODE'
),

bucketing_and_txn_type AS (
  SELECT
    date_trunc('MONTH', gross_rev.dt) AS dt_month,
    biz.entity_adjusted AS xendit_entity,
    UPPER(channel_adjustments.channel_adjusted) AS channel_adjusted,
    UPPER(sender_bank_code_adjustments.sender_bank_code_clean) AS sender_bank_code_adjusted,
    UPPER(sender_bank_code_adjustments.routing) AS routing,

    CASE
      WHEN gross_rev.transaction_amount <= ******** THEN '0 - 50 mio'
      WHEN gross_rev.transaction_amount > ********* THEN '> 100 mio'
      ELSE '50 - 100 mio'
    END AS bucket,

    gross_rev.*,

    CASE
      WHEN product_type <> 'DISBURSEMENT' THEN 'NON-DISBURSEMENT'
      WHEN gross_rev.sender_bank_code LIKE '%FIRECASH%' THEN 'FIRECASH'
      WHEN channel_adjustments.channel_adjusted = sender_bank_code_adjusted THEN 'INHOUSE'
      WHEN gross_rev.sender_bank_code LIKE '%BIFAST%' THEN 'BIFAST'
      WHEN gross_rev.sender_bank_code LIKE '%RTGS%' THEN 'RTGS'
      WHEN gross_rev.sender_bank_code IN ('SAHABAT_SAMPOERNA', 'ID_BSS', 'BSS', 'ID_PRIMA', 'PRIMA')
           AND bucket IN ('0 - 50 mio', '50 - 100 mio') THEN 'OT'
      WHEN channel_adjustments.Channel_Adjusted IN (SELECT bank_code FROM hive_metastore.clean__de_test.bifast_adjustments)
           AND Routing = 'IB'
           AND transaction_amount <= ********* THEN 'BIFAST'
      WHEN gross_rev.channel_name IN ('BANGKOK', 'DEUTSCHE', 'FAMA', 'BISNIS_INTERNASIONAL')
           AND bucket IN ('0 - 50 mio', '50 - 100 mio') THEN 'LLG'
      WHEN channel_adjustments.Channel_Adjusted = 'BNI VA_INT_DISB' AND sender_bank_code_adjusted = 'BNI' THEN 'ESCROW'
      WHEN channel_adjustments.Channel_Adjusted = 'GOPAY' AND sender_bank_code_adjusted = 'PERMATA' THEN 'GOPAYPBP'
      WHEN channel_adjustments.Channel_Adjusted = 'SHOPEEPAY' AND sender_bank_code_adjusted = 'PERMATA' THEN 'SHOPEEPAYPBP'
      WHEN channel_adjustments.Channel_Adjusted = 'OVO' AND sender_bank_code_adjusted = 'PERMATA' THEN 'OVOPBP'
      WHEN gross_rev.channel_name = 'LINKAJA' AND gross_rev.sender_bank_code IN ('BRI', 'ID_BRI') THEN 'BRILINKAJA'
      WHEN channel_adjustments.Channel_Adjusted = 'BCA' AND sender_bank_code_adjusted = 'ID_BCA_FIRECASH' THEN 'ID_BCA_FIRECASH'
      WHEN bucket = '0 - 50 mio' THEN 'OT'
      WHEN bucket = '50 - 100 mio' THEN 'LLG'
      ELSE 'RTGS'
    END AS transaction_type_cogs,

    cc_rate_adj.pct_rate AS cc_pct_rate

  FROM gross_rev

  LEFT JOIN main.transform__business_intelligence.dim_businesses biz
    ON biz.business_id = gross_rev.business_id

  LEFT JOIN clean__de_test.channel_adjustments
    ON UPPER(gross_rev.channel_name) = UPPER(channel_adjustments.channel_name)

  LEFT JOIN clean__de_test.sender_bank_code_adjustments
    ON UPPER(gross_rev.sender_bank_code) = UPPER(sender_bank_code_adjustments.sender_bank_code)

  LEFT JOIN clean__de_test.cc_installment_rate_adjustments cc_rate_adj
    ON gross_rev.product_subtype = cc_rate_adj.product_subtype
   AND gross_rev.channel_name = cc_rate_adj.channel_adjusted
),

agg_tpv_count AS (
  SELECT
    dt_month,
    xendit_entity,
    transaction_type_cogs,
    product_subtype,
    product_type,
    channel_adjusted,
    sender_bank_code_adjusted,
    sender_bank_code,
    routing,
    tpi_platform_name,
    commercial_model,
    currency,
    bucket,

    CASE WHEN UPPER(commercial_model) = 'SWITCHER' THEN 'Yes' ELSE 'No' END AS is_switcher,
    CASE WHEN is_tpi_transaction = 'TPI' THEN 'Yes' ELSE 'No' END AS is_tpi_transaction,

    CASE
      WHEN product_type = 'DISBURSEMENT' THEN sender_bank_code_adjusted
      ELSE channel_adjusted
    END AS partner_name,

    SUM(transaction_amount) AS tpv_local,
    COUNT(1) AS transaction_count,
    SUM(fees) AS gross_revenue_local,

    MAX(cc_pct_rate) AS cc_pct_rate

  FROM bucketing_and_txn_type
  GROUP BY ALL
),

rates_linking AS (
  SELECT
    agg_tpv_count.*,
    cogs_rate_type2scd.cost_structure AS cogs_cost_structure,
    cogs_rate_type2scd.tier_1_cost AS cogs_tier_1_cost,
    cogs_rate_type2scd.tier_2_cost AS cogs_tier_2_cost,
    cogs_rate_type2scd.tier_3_cost AS cogs_tier_3_cost,
    cogs_rate_type2scd.fixed_fee AS cogs_fixed_fee,
    cogs_rate_type2scd.tier_1_end AS cogs_tier_1_end,
    cogs_rate_type2scd.tier_2_end AS cogs_tier_2_end,
    cogs_rate_type2scd.is_current AS cogs_is_current_rate,
    cogs_rate_type2scd.effective_start_date AS cogs_rates_effective_start_date,
    cogs_rate_type2scd.effective_end_date AS cogs_rates_effective_end_date,
    cogs_rate_type2scd.effective_start_month AS cogs_rates_effective_start_month,
    cogs_rate_type2scd.effective_end_month AS cogs_rates_effective_end_month,

    CASE
      WHEN agg_tpv_count.product_type = 'CREDIT_CARD' AND agg_tpv_count.product_subtype != 'CC_REGULAR' AND agg_tpv_count.is_switcher = 'No'
      THEN COALESCE(cc_pct_rate, 0)
      ELSE COALESCE(cogs_rate_type2scd.pct_cost, 0)
    END AS cogs_pct_cost,

    CASE
      WHEN tpi_platform_name = 'SHOPIFY2' THEN 0.0035
      WHEN agg_tpv_count.tpi_platform_name = 'WIX' AND agg_tpv_count.product_type = 'CREDIT_CARD' THEN 0.002
      ELSE 0
    END AS platform_fees_pct,

    CASE
      WHEN cogs_cost_structure = 'Tiered' THEN
        CASE
          WHEN (cogs_tier_2_cost + cogs_tier_3_cost = 0) OR ((cogs_tier_2_cost + cogs_tier_3_cost)/2 = cogs_tier_1_cost)
          THEN FALSE ELSE TRUE
        END
      ELSE FALSE
    END AS tier_cost

  FROM agg_tpv_count

  LEFT JOIN clean__de_test.cogs_rate_type2scd
    ON agg_tpv_count.xendit_entity = cogs_rate_type2scd.xendit_entity
    AND agg_tpv_count.product_type = REPLACE(UPPER(cogs_rate_type2scd.product_type), ' ', '_')
    AND agg_tpv_count.transaction_type_cogs = UPPER(cogs_rate_type2scd.transaction_type)
    AND COALESCE(agg_tpv_count.sender_bank_code_adjusted, 'Non-Disbursement') = cogs_rate_type2scd.sender_bank_code
    AND UPPER(agg_tpv_count.channel_adjusted) = UPPER(cogs_rate_type2scd.channel_name)
    AND COALESCE(agg_tpv_count.routing, 'no routing') = cogs_rate_type2scd.routing
    AND agg_tpv_count.is_switcher = cogs_rate_type2scd.is_switcher
    AND dt_month >= cogs_rate_type2scd.effective_start_month
    AND dt_month <= cogs_rate_type2scd.effective_end_month
),

tiers AS (
  SELECT *,
    CASE
      WHEN tier_cost = TRUE THEN
        CASE
          WHEN tpv_local <= cogs_tier_1_end THEN 'tier_1'
          WHEN tpv_local > cogs_tier_2_end THEN 'tier_3'
          ELSE 'tier_2'
        END
      ELSE 'no_tier'
    END AS tier
  FROM rates_linking
)

SELECT * EXCEPT (cc_pct_rate),
  CASE
    WHEN tier_cost = TRUE THEN
      CASE
        WHEN tier = 'tier_1' THEN cogs_tier_1_cost
        WHEN tier = 'tier_2' THEN cogs_tier_2_cost
        WHEN tier = 'tier_3' THEN cogs_tier_3_cost
      END
    ELSE COALESCE(cogs_tier_1_cost, 0)
  END AS cogs_base_fee
FROM tiers
;