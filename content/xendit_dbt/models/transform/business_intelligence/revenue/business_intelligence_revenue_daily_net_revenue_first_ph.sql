{{ config(
    alias="daily_net_revenue_first_ph"
)}}


INSERT OVERWRITE clean__de_test.daily_net_revenue_first_ph
WITH
  pg_fees AS (
    SELECT
      DATE_TRUNC('month', TRY_CAST(fct_transaction_fees.dt AS TIMESTAMP)) AS dt_month,
      fct_transaction_fees.* , -- EXCEPT (product_type)
      CASE
        WHEN fct_transaction_fees.product_type = 'DISBURSEMENT'
         AND fct_transaction_fees.product_subtype = 'REMITTANCE_PAYOUT'
        THEN 'REMITTANCE_PAYOUT'
        ELSE fct_transaction_fees.product_type
      END AS product_type_adjusted,
      CASE
        WHEN fct_transaction_fees.product_type = 'CREDIT_CARD'
        THEN COALESCE(
          integrated_card_v2.mid_label,
          fct_transaction_fees.channel_name
        )
        WHEN fct_transaction_fees.product_type = 'DISBURSEMENT'
        THEN fct_transaction_fees.sender_bank_code
        ELSE fct_transaction_fees.channel_name
      END AS channel_adjusted
    FROM
      transform__business_intelligence_revenue.fct_transaction_fees
      LEFT JOIN transform__cards.integrated_card_v2
        ON integrated_card_v2.charge_id = fct_transaction_fees.reference
    WHERE
      fct_transaction_fees.product_type NOT IN ('IN-HOUSE TRANSACTION', 'RETAIL_OUTLET')
      AND fct_transaction_fees.currency = 'PHP'
      AND TRY_CAST(fct_transaction_fees.dt AS TIMESTAMP) IS NOT NULL
      AND TRY_CAST(fct_transaction_fees.dt AS TIMESTAMP) >= '2025-01-01'
  ),

rates_join AS (
  SELECT * EXCEPT (product_type, product_type_adjusted), product_type_adjusted as product_type
  FROM (
    SELECT
    pg_fees.*,
    fix_fee_rate, pct_fee_rate,
    cogs_rate_type
    FROM
    pg_fees
    -- LEFT JOIN hive_metastore.de_test_lab.ph_intermediate_cogs_agg
    LEFT JOIN clean__de_test.cogs_consolidated_monthly_rate_linking_ph ph_intermediate_cogs_agg
    ON ph_intermediate_cogs_agg.dt_month = pg_fees.dt_month
    AND ph_intermediate_cogs_agg.business_id = pg_fees.business_id
    AND ph_intermediate_cogs_agg.account_relation_id = pg_fees.account_relation_id
    AND ph_intermediate_cogs_agg.product_type = pg_fees.product_type_adjusted
    AND ph_intermediate_cogs_agg.product_subtype = pg_fees.product_subtype
    AND ph_intermediate_cogs_agg.channel_name = pg_fees.channel_name
    AND ph_intermediate_cogs_agg.channel_adjusted = pg_fees.channel_adjusted
    AND ph_intermediate_cogs_agg.transaction_type = pg_fees.transaction_type
    AND ph_intermediate_cogs_agg.transaction_channel = pg_fees.transaction_channel
    AND ph_intermediate_cogs_agg.currency = pg_fees.currency
    WHERE pg_fees.dt_month IS NOT NULL
))


SELECT
  *,
  -- calculate cogs_amount per transaction, now handling Combined Fee
  CASE
    WHEN cogs_rate_type = 'Fixed Fee'    THEN fix_fee_rate
    WHEN cogs_rate_type = 'Pct Fee'      THEN pct_fee_rate * transaction_amount
    WHEN cogs_rate_type = 'Combined Fee' THEN fix_fee_rate + (pct_fee_rate * transaction_amount)
    ELSE NULL
  END AS cogs_amount,
  -- subtract that from the raw fee to get net revenue
  fees
  - CASE
      WHEN cogs_rate_type = 'Fixed Fee'    THEN fix_fee_rate
      WHEN cogs_rate_type = 'Pct Fee'      THEN pct_fee_rate * transaction_amount
      WHEN cogs_rate_type = 'Combined Fee' THEN fix_fee_rate + (pct_fee_rate * transaction_amount)
      ELSE NULL
    END AS net_revenue
FROM
  rates_join;
