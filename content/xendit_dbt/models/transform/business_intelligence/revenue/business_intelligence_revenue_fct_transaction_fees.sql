{{ config(
    alias="fct_transaction_fees",
    materialized="incremental",
    unique_key="reference",
    tags=["revenue_based_on_txn_backbone_v2"]
) }}

with fees as (
    select 
        * 
    from 
        {{ ref('business_intelligence_revenue_fct_txns_with_aggregations') }}
    {% if is_incremental() %}
    where 
        dt >= date_trunc('month', current_date() - interval '1' month)
        and dt <= date_add(current_date(), 2)
    {% endif %}
)

, inhouse_fees as (
    select 
        *
    from
        {{ ref('business_intelligence_revenue_fct_xenplatform_inhouse_transaction_fees') }}
    {% if is_incremental() %}
    where 
        dt >= date_trunc('month', current_date() - interval '1' month)
        and dt <= date_add(current_date(), 2)
    {% endif %}
)

, manual_fees as (
    select
        *
    from 
        {{ ref('business_intelligence_revenue_fct_manual_rate_collections') }}
    {% if is_incremental() %}
    where 
        dt >= date_trunc('month', current_date() - interval '2' month)
        and dt <= date_add(current_date(), 2)
    {% endif %}
)

, fees_excl_manual_transactions as (
    select
        t1.*
    from 
        fees t1
        left join manual_fees t2 on t1.reference = t2.reference
    where
        t2.reference is null
)

, combined_fees as (
    select
        reference
        , business_id
        , account_relation_id
        , product_type
        , product_subtype
        , channel_name
        , transaction_type
        , transaction_channel
        , money_flow
        , created
        , updated
        , settled
        , commercial_model
        , currency
        , integration_type
        , is_web_or_app
        , is_tpi_transaction
        , tpi_platform_name
        , processing_platform
        , papi_instrument_id
        , invoice_id
        , payout_link_id
        , transaction_id
        , sender_bank_code
        , formula
        , transaction_amount
        , tier_1_end
        , tier_2_end
        , tier_1_flat_fee
        , tier_1_fee_percentage
        , tier_2_flat_fee
        , tier_2_fee_percentage
        , tier_3_flat_fee
        , tier_3_fee_percentage
        , deduction_type
        , is_early_settled
        , es_fee
        , pricing_type
        , tier
        , fees
        , statement_generation_type
        , label
        , dt
    from 
        fees_excl_manual_transactions

    union all

    select
        reference
        , business_id
        , account_relation_id
        , product_type
        , product_subtype
        , channel_name
        , cast(null as string) as transaction_type -- left null because we don't map to PRS
        , cast(null as string) as transaction_channel -- left null because we don't map to PRS
        , money_flow
        , created
        , updated
        , settled
        , commercial_model
        , currency
        , integration_type
        , is_web_or_app
        , is_tpi_transaction
        , tpi_platform_name
        , processing_platform
        , papi_instrument_id
        , invoice_id
        , payout_link_id
        , transaction_id
        , sender_bank_code
        , formula
        , transaction_amount
        , tier_1_end
        , tier_2_end
        , tier_1_flat_fee
        , tier_1_fee_percentage
        , tier_2_flat_fee
        , tier_2_fee_percentage
        , tier_3_flat_fee
        , tier_3_fee_percentage
        , deduction_type
        , is_early_settled
        , es_fee
        , pricing_type
        , tier
        , fees
        , statement_generation_type
        , 'DEFAULT' as label
        , dt
    from 
        manual_fees

    union all 

    select 
        reference
        , source_business_id as business_id
        , account_relation_id
        , product_type
        , product_subtype
        , channel_name
        , transaction_type
        , transaction_channel
        , money_flow
        , created
        , updated
        , settled
        , commercial_model
        , currency
        , integration_type
        , is_web_or_app
        , is_tpi_transaction
        , tpi_platform_name
        , processing_platform
        , papi_instrument_id
        , invoice_id
        , payout_link_id
        , transaction_id
        , sender_bank_code
        , formula
        , transaction_amount
        , tier_1_end
        , tier_2_end
        , tier_1_flat_fee
        , tier_1_fee_percentage
        , tier_2_flat_fee
        , tier_2_fee_percentage
        , tier_3_flat_fee
        , tier_3_fee_percentage
        , deduction_type
        , cast(null as boolean) as is_early_settled
        , cast(null as double) as es_fee
        , pricing_type
        , tier
        , fees
        , statement_generation_type
        , 'DEFAULT' as label
        , dt
    from 
        inhouse_fees
)

select * from combined_fees