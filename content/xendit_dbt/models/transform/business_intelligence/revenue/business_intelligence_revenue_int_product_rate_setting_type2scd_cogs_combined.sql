{{ config(
    alias="int_cogs_rate_setting_type2scd_combined"
)}}

-- Combined query for IDR and PHP COGS rate settings
WITH 

-- IDR data processing (from original cogs.sql)
idr_base_data AS (
  SELECT DISTINCT *, created_month AS created_date
  FROM clean__de_test.cogs_rate_check
),

idr_with_changes AS (
  SELECT *,
    LAG(CONCAT_WS('|', tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee, tier_1_end, tier_2_end)) OVER (
      PARTITION BY
        xendit_entity, product_type, sender_bank_code, channel_name,
        transaction_type, routing, cards_is_switcher_yes_no,
        va_is_switching_yes_no, is_switcher
      ORDER BY created_date
    ) AS prev_rate_signature
  FROM idr_base_data
),

idr_mark_change_groups AS (
  SELECT *,
    SUM(CASE
          WHEN prev_rate_signature IS NULL OR
               CONCAT_WS('|', tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee, tier_1_end, tier_2_end) != prev_rate_signature
          THEN 1 ELSE 0
        END
    ) OVER (
      PARTITION BY
        xendit_entity, product_type, sender_bank_code, channel_name,
        transaction_type, routing, cards_is_switcher_yes_no,
        va_is_switching_yes_no, is_switcher
      ORDER BY created_date
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ) AS change_group
  FROM idr_with_changes
),

idr_grouped_scd AS (
  SELECT
    xendit_entity, product_type, sender_bank_code, channel_name,
    transaction_type, routing, cards_is_switcher_yes_no,
    va_is_switching_yes_no, is_switcher, cost_structure,
    tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee,
    tier_1_end, tier_2_end,
    MIN(created_date) AS effective_start_date,
    MAX(created_date) AS effective_end_date
  FROM idr_mark_change_groups
  GROUP BY
    xendit_entity, product_type, sender_bank_code, channel_name,
    transaction_type, routing, cards_is_switcher_yes_no,
    va_is_switching_yes_no, is_switcher, cost_structure,
    tier_1_cost, tier_2_cost, tier_3_cost, pct_cost, fixed_fee,
    tier_1_end, tier_2_end,
    change_group
),

idr_final_scd AS (
  SELECT *,
    DATE_ADD(effective_end_date, 1) AS effective_end_date_plus1,
    ROW_NUMBER() OVER (
      PARTITION BY xendit_entity, product_type, sender_bank_code, channel_name,
                   transaction_type, routing, cards_is_switcher_yes_no,
                   va_is_switching_yes_no, is_switcher
      ORDER BY effective_start_date DESC
    ) = 1 AS is_current
  FROM idr_grouped_scd
),

-- PHP data processing (from cogs_ph.sql)
php_raw_csv AS (
    SELECT
        product_type, channel_code AS channel_name, bid, industry,
        CAST(tier_1_fixed_rate AS DOUBLE) AS tier_1_cost,
        CAST(tier_2_percentage AS DOUBLE) AS tier_2_cost,
        MAKE_DATE(CAST(year AS INT), CAST(month AS INT), 1) AS created_month
    FROM clean__de_test.cost_structure_ph_for_daily
),

php_final_scd AS (
    SELECT
        product_type, channel_name, tier_1_cost, tier_2_cost, bid, industry,
        MIN(created_month) AS effective_start_date,
        LEAD(MIN(created_month), 1, '9999-12-31') OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY MIN(created_month)) AS effective_end_date
    FROM (
        SELECT *,
            SUM(CASE WHEN is_new_group = 1 THEN 1 ELSE 0 END) OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY created_month) as change_group
        FROM (
            SELECT *,
                CASE WHEN CONCAT_WS('|', tier_1_cost, tier_2_cost) != LAG(CONCAT_WS('|', tier_1_cost, tier_2_cost)) OVER (PARTITION BY product_type, channel_name, bid, industry ORDER BY created_month) THEN 1 ELSE 0 END as is_new_group
            FROM php_raw_csv
        )
    )
    GROUP BY product_type, channel_name, tier_1_cost, tier_2_cost, bid, industry, change_group
),

-- IDR records with standard structure
idr_records AS (
  SELECT
    'IDR' as currency,
    xendit_entity,
    product_type,
    sender_bank_code,
    channel_name,
    transaction_type,
    routing,
    cards_is_switcher_yes_no,
    va_is_switching_yes_no,
    is_switcher,
    cost_structure,
    tier_1_cost,
    tier_2_cost,
    tier_3_cost,
    pct_cost,
    fixed_fee,
    tier_1_end,
    tier_2_end,
    effective_start_date,
    CASE
      WHEN is_current THEN current_date() + INTERVAL 1 DAY
      ELSE effective_end_date_plus1
    END AS effective_end_date,
    is_current,
    DATE_TRUNC('MONTH', effective_start_date) AS effective_start_month,
    CASE
      WHEN is_current THEN DATE_TRUNC('MONTH', ADD_MONTHS(current_date(), 1))
      ELSE DATE_TRUNC('MONTH',
        CASE
          WHEN effective_end_date_plus1 IS NOT NULL THEN effective_end_date_plus1
          ELSE effective_end_date
        END
      )
    END AS effective_end_month,
    CAST(NULL AS STRING) AS bid,
    CAST(NULL AS STRING) AS industry
  FROM idr_final_scd
),

-- PHP records mapped to standard structure with NULLs for missing columns
php_records AS (
  SELECT
    'PHP' as currency,
    CAST(NULL AS STRING) AS xendit_entity,
    product_type,
    CAST(NULL AS STRING) AS sender_bank_code,
    channel_name,
    CAST(NULL AS STRING) AS transaction_type,
    CAST(NULL AS STRING) AS routing,
    CAST(NULL AS STRING) AS cards_is_switcher_yes_no,
    CAST(NULL AS STRING) AS va_is_switching_yes_no,
    CAST(NULL AS STRING) AS is_switcher,
    CAST(NULL AS STRING) AS cost_structure,
    tier_1_cost,
    tier_2_cost,
    CAST(NULL AS DOUBLE) AS tier_3_cost,
    CAST(NULL AS DOUBLE) AS pct_cost,
    CAST(NULL AS DOUBLE) AS fixed_fee,
    CAST(NULL AS DOUBLE) AS tier_1_end,
    CAST(NULL AS DOUBLE) AS tier_2_end,
    effective_start_date,
    effective_end_date,
    CASE WHEN effective_end_date = '9999-12-31' THEN TRUE ELSE FALSE END AS is_current,
    DATE_TRUNC('MONTH', effective_start_date) AS effective_start_month,
    DATE_TRUNC('MONTH', effective_end_date) AS effective_end_month,
    bid,
    industry
  FROM php_final_scd
)

-- Union both datasets
SELECT * FROM idr_records
UNION ALL
SELECT * FROM php_records
