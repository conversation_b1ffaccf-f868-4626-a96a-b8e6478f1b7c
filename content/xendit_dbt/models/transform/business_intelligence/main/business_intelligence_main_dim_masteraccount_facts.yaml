version: 2
models:
- name: business_intelligence_main_dim_masteraccount_facts
  description: model to replace pdt table containing transaction information for each master account id. Note that only completed transactions are used for these metrics.
  meta:
    source_group: business_intelligence
    owner: business_intelligence
  columns:
  - name: master_account_id
    description: Primary Key. Identifier of master accounts in Xenplatform.
    tests:
    - unique
    - not_null
  - name: active_status
    description: Shows how active the business is, for example if its active or dormant or reactivated.
  - name: has_more_than_5_lifetime_transactions
    description: Indicates if the business has more than 5 lifetime transactions
  - name: is_active
    description: >
      Indicates if the business is currently active. 
      1 if number of days since latest transaction is less than 28 days before current date
  - name: is_reactivated
    description: >
      Indicates if the business has been reactivated.
      1 if the business had transactions in the current period but not in the prior period.
  - name: is_new_active
    description: >
      Indicates if the business is newly active.
      1 if the business had transactions in the current period and the number of days since the first transaction is less than or equal to 28 days.
  - name: is_churned
    description: >
      Indicates if the business has churned.
      1 if the business had transactions more than 28 days ago and less than or equal to 56 days ago.
  - name: is_dormant
    description: >
      Indicates if the business is dormant.
      1 if the business had transactions more than 56 days ago.
  - name: is_non_starting
    description: >
      Indicates if the business is non-starting.
      1 if the business has not gone live (first_successful_test_transaction is not null) or if the business has gone live but no transactions have occurred.
  - name: first_transaction_date
    description: date of first transaction
  - name: go_live_to_first_transaction
    description: Time difference between going live and the first transaction
  - name: first_month_count
    description: Count of transactions in the first month
  - name: second_month_count
    description: Count of transactions in the second month
  - name: third_month_count
    description: Count of transactions in the third month
  - name: first_18_months_count
    description: Count of transactions in the first 18 months
  - name: first_month_tpv
    description: Total transaction value in USD for the first month
  - name: second_month_tpv
    description: Total transaction value in USD for the second month
  - name: third_month_tpv
    description: Total transaction value in USD for the third month
  - name: first_18_months_tpv
    description: Total transaction value in USD for the first 18 months
  - name: transaction_rank_ones_digit
    description: Last digit of the rank of the business based on lifetime transaction count
  - name: lifetime_transaction_count_rank
    description: Rank of the business based on lifetime transaction count
  - name: tpv_rank_ones_digit
    description: Last digit of the rank of the business based on lifetime TPV in USD
  - name: lifetime_tpv_usd_rank
    description: Rank of the business based on lifetime TPV in USD
  - name: lifetime_transaction_count
    description: Total count of lifetime transactions
  - name: lifetime_tpv_usd
    description: Total lifetime transaction value in USD
