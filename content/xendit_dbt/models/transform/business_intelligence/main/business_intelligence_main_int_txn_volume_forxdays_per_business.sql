{{ config(
    alias="int_txn_volume_forxdays_per_business"
) }}
{% set day_diffs1 = [14, 30, 60, 90, 180] %}
{% set day_diffs2 = [90, 180] %}

-- We want completed transactions only
with transactions_base AS (
  SELECT
    user_id,
    created,
    cast(created as date) as created_date,
    currency,
    amount,
    status,
    is_web_or_app
  FROM
    {{ source('transform__transaction_volumes', 'transaction_backbone') }}
  WHERE
    upper(status) IN (
      'COMPLETED',
      'SETTLED',
      'CAPTURED',
      'SETTLEMENT',
      'CLAIMED',
      'SUCCEEDED',
      'SUCCESS',
      'SUCCESS_COMPLETED',
      'FORCE_PAYMENT',
      'SUCCESSFUL',
      'SETTLING'
    )
)

, transaction_dates_and_tstamps AS (
  SELECT
    user_id,
    first_transaction_tstamp,
    fifth_transaction_tstamp,
    latest_transaction_tstamp,
    cast(first_transaction_tstamp as date) as first_transaction_date,
    cast(fifth_transaction_tstamp as date) as fifth_transaction_date,
    cast(latest_transaction_tstamp as date) as latest_transaction_date
  FROM
    {{ ref('business_intelligence_main_int_ranked_txn_tstamps_per_business') }}
  WHERE
    user_id is not null
)

-- Get Amount since first transaction for all transactions

, amount_since_first_transaction AS (
  SELECT
    t1.user_id
    {% for day in day_diffs1 %}
    , sum(
      CASE
        WHEN t1.created > t2.first_transaction_tstamp
        AND DATEDIFF(t1.created_date, t2.first_transaction_date) <= {{day}}
        THEN (
          CASE WHEN t1.currency = 'IDR' then t1.amount / t3.idr_usd
              WHEN t1.currency = 'PHP' then t1.amount / t3.php_usd
              WHEN t1.currency = 'MYR' then t1.amount / t3.myr_usd
              WHEN t1.currency = 'SGD' then t1.amount / t3.sgd_usd
          END)
      END
    ) as tpv_usd_{{day}}_days_since_first_transaction
    {% endfor %}
  FROM
    transactions_base t1
    LEFT JOIN transaction_dates_and_tstamps t2 on t1.user_id = t2.user_id
    LEFT JOIN {{ source('transform__fpna', 'fx_rates_by_year') }} t3 on year(t1.created_date) = t3.year
  GROUP BY 
    1
)

-- Get Amount since fifth transaction for all transactions
, amount_since_fifth_transaction AS (
  SELECT
    t1.user_id
    {% for day in day_diffs1 %}
    , sum(
      CASE
        WHEN t1.created > t2.fifth_transaction_tstamp
        AND DATEDIFF(t1.created_date, t2.fifth_transaction_date) <= {{day}}
        THEN (
          CASE WHEN t1.currency = 'IDR' then t1.amount / t3.idr_usd
              WHEN t1.currency = 'PHP' then t1.amount / t3.php_usd
              WHEN t1.currency = 'MYR' then t1.amount / t3.myr_usd
              WHEN t1.currency = 'SGD' then t1.amount / t3.sgd_usd
          END)
      END
    ) as tpv_usd_{{day}}_days_since_fifth_transaction
    {% endfor %}
  FROM
    transactions_base t1
    LEFT JOIN transaction_dates_and_tstamps t2 on t1.user_id = t2.user_id
    LEFT JOIN {{ source('transform__fpna', 'fx_rates_by_year') }} t3 on year(t1.created_date) = t3.year
  GROUP BY 
    1
)

-- Get Lifetime TPV and count
, lifetime_txn_activity AS (
  SELECT
    user_id
    , count(*) as lifetime_transaction_count
    , sum( 
        CASE 
            WHEN t1.currency = 'IDR' then t1.amount / t2.idr_usd
            WHEN t1.currency = 'PHP' then t1.amount / t2.php_usd
            WHEN t1.currency = 'MYR' then t1.amount / t2.myr_usd
            WHEN t1.currency = 'SGD' then t1.amount / t2.sgd_usd
        END
    ) as lifetime_tpv_usd
  FROM
    transactions_base t1
    LEFT JOIN {{ source('transform__fpna', 'fx_rates_by_year') }} t2 on year(t1.created_date) = t2.year
  GROUP BY
    1
)

-- TPV in X days Preceding the Latest Transaction per business
, tpv_x_days_since_last_txn AS (
  SELECT 
    t1.user_id
    {% for day in day_diffs2 %}
    , sum( 
      CASE
        WHEN t1.created_date <= t2.latest_transaction_date
        AND DATEDIFF(t2.latest_transaction_date, t1.created_date) <= {{day}}
        THEN (
          CASE WHEN t1.currency = 'IDR' then t1.amount / t3.idr_usd
              WHEN t1.currency = 'PHP' then t1.amount / t3.php_usd
              WHEN t1.currency = 'MYR' then t1.amount / t3.myr_usd
              WHEN t1.currency = 'SGD' then t1.amount / t3.sgd_usd
          END)
      END
    ) as tpv_usd_{{day}}_days_before_latest_transaction
    {% endfor %}
  FROM
    transactions_base t1
    LEFT JOIN transaction_dates_and_tstamps t2 on t1.user_id = t2.user_id
    LEFT JOIN {{ source('transform__fpna', 'fx_rates_by_year') }} t3 on year(t1.created_date) = t3.year
  GROUP BY 
    1
)

-- TPV in X days Preceding today, per business
, tpv_x_days_before_today AS (
  SELECT 
    t1.user_id
    {% for day in day_diffs2 %}
    , sum(
      CASE
        WHEN t1.created_date <= current_date()
        AND DATEDIFF(current_date(), t1.created_date) <= {{day}}
        THEN (
          CASE WHEN t1.currency = 'IDR' then t1.amount / t2.idr_usd
              WHEN t1.currency = 'PHP' then t1.amount / t2.php_usd
              WHEN t1.currency = 'MYR' then t1.amount / t2.myr_usd
              WHEN t1.currency = 'SGD' then t1.amount / t2.sgd_usd
          END)
      END
    ) as tpv_usd_{{day}}_days_before_today
    {% endfor %}
  FROM
    transactions_base t1
    LEFT JOIN transaction_dates_and_tstamps t2 on t1.user_id = t2.user_id
    LEFT JOIN {{ source('transform__fpna', 'fx_rates_by_year') }} t2 on year(t1.created_date) = t2.year
  GROUP BY 
    1
)

SELECT
  a.user_id
  {% for day in day_diffs1 %}
  , b.tpv_usd_{{day}}_days_since_first_transaction
  {% endfor %}
  {% for day in day_diffs1 %}
  , c.tpv_usd_{{day}}_days_since_fifth_transaction
  {% endfor %}
  , d.lifetime_transaction_count
  , d.lifetime_tpv_usd
  {% for day in day_diffs2 %}
  , e.tpv_usd_{{day}}_days_before_latest_transaction
  {% endfor %}
  {% for day in day_diffs2 %}
  , f.tpv_usd_{{day}}_days_before_today
  {% endfor %}
FROM
  transaction_dates_and_tstamps a
  LEFT JOIN amount_since_first_transaction b on a.user_id = b.user_id
  LEFT JOIN amount_since_fifth_transaction c on a.user_id = c.user_id
  LEFT JOIN lifetime_txn_activity d on a.user_id = d.user_id
  LEFT JOIN tpv_x_days_since_last_txn e on a.user_id = e.user_id
  LEFT JOIN tpv_x_days_before_today f on a.user_id = f.user_id
