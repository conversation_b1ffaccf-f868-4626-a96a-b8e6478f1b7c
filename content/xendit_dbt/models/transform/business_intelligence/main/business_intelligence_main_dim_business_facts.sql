{{ config(
    alias="dim_business_facts"
) }}
--https://xenditco.cloud.looker.com/projects/xendit-presto/files/businesses/business_facts.view.lkml


with biz_pdt_activation as
(select * from {{ref('business_intelligence_main_dim_business_product_activation')}}
--where business_id in ('5bfe0e8c97ad396f3b87d9d7', '631a8c3e49ef8e59863d0673' )
--where business_id in ('5e7c4cf957ddd61cbe3f5de8', '5e7cedcdddea021cd156e572', '5eba25ff1821033df291f1d6')
)

,business_details as
(
select * from {{ref('business_intelligence_main_dim_business_txn_details')}}
--where business_id in ('5e7c4cf957ddd61cbe3f5de8', '5e7cedcdddea021cd156e572', '5eba25ff1821033df291f1d6')
)

, transaction_bb as
(select * from transform__transaction_volumes.transaction_backbone
where
--where date(dt) = date('2022-12-21')
--where user_id in ('5bfe0e8c97ad396f3b87d9d7', '631a8c3e49ef8e59863d0673' ) 
--where user_id in ('5e7c4cf957ddd61cbe3f5de8', '5e7cedcdddea021cd156e572', '5eba25ff1821033df291f1d6')
-- (product_subtype = 'VA_ESCROW' and status = 'SETTLED') or
-- (product_subtype = 'VA_FIXED' and status = 'COMPLETED') or 
-- (product_subtype = 'VA_NONFIXED' and status in ('SETTLED', 'COMPLETED')) or 
-- (product_subtype = 'VA_REMITTANCE' and status = 'CLAIMED') or
-- (product_type in ('EWALLET', 'PAYLATER') and status = 'SUCCEEDED') or
-- (product_type = 'CREDIT_CARD' and status = 'CAPTURED') or
-- (status = 'COMPLETED') 
status in ('COMPLETED', 'SETTLED', 'CAPTURED', 'SETTLEMENT', 'CLAIMED', 'SUCCEEDED', 'SUCCESS', 'SUCCESS_COMPLETED', 'FORCE_PAYMENT', 'SUCCESSFUL', 'SETTLING')
)

, fx_rates as
(select * from {{ source('transform__fpna', 'fx_rates_by_year') }})

, transaction_bb_fx as
(select
bb.*
,case when currency = 'IDR' then fx_rates.idr_usd
when currency = 'PHP' then fx_rates.php_usd
when currency = 'MYR' then fx_rates.myr_usd
when currency = 'SGD' then fx_rates.sgd_usd
when currency = 'VND' then fx_rates.vnd_usd
when currency = 'THB' then fx_rates.thb_usd
else 1 end as fx_usd
from transaction_bb bb
left join
fx_rates
on year(bb.dt) = fx_rates.year
)

, transaction_bb_fx_biz as
(select 
bb.*
,bb.created as biz_created
,go_live
,first_successful_test_transaction
,first_test_transaction 
--,round(months_between(bb.created, b.created),0) + 1 months_after_creation
,datediff(month, b.created , bb.created ) + 1 as months_after_creation
,cast(amount as double)/ fx_usd as tpv_usd
--,datediff(day, bb.created, CURRENT_TIMESTAMP()) as num_days_before_today
,TIMESTAMPADD(DAY, -28, DATE_TRUNC('DAY', current_timestamp())) AS this_period_start
,TIMESTAMPADD(DAY, 28, TIMESTAMPADD(DAY, -28, DATE_TRUNC('DAY', current_timestamp()))) AS this_period_end
,TIMESTAMPADD(DAY, -56, DATE_TRUNC('DAY', current_timestamp())) AS prior_period_start
,TIMESTAMPADD(DAY, 56, TIMESTAMPADD(DAY, -56, DATE_TRUNC('DAY', current_timestamp()))) AS prior_period_end
from transaction_bb_fx bb
left join
business_details b
on bb.user_id = b.business_id
)




--select * from transaction_bb_fx_biz
--limit 10



, txn_bb_agg as
(
select
user_id
,ARRAY_JOIN(ARRAY_DISTINCT(SORT_ARRAY(COLLECT_LIST(product_type))), '|', '') as used_product_type
,ARRAY_JOIN(ARRAY_DISTINCT(SORT_ARRAY(COLLECT_LIST(product_subtype))), '|', '') as used_product_subtype
,ARRAY_JOIN(ARRAY_DISTINCT(SORT_ARRAY(COLLECT_LIST(channel_name))), '|', '') as used_channel_name
,count(1) as count
,count(case when UPPER(status) = 'FAILED' then reference end) as failed_transactions
,sum(tpv_usd) as tpv_usd
,count(case when months_after_creation = 1 then reference end) first_month_count
,count(case when months_after_creation = 2 then reference end) second_month_count
,count(case when months_after_creation = 3 then reference end) third_month_count
,count(case when months_after_creation <= 18 then reference end) first_18_months_count
,sum(case when months_after_creation = 1 then tpv_usd end) first_month_tpv
,sum(case when months_after_creation = 2 then tpv_usd end) second_month_tpv
,sum(case when months_after_creation = 3 then tpv_usd end) third_month_tpv
,sum(case when months_after_creation <= 18 then tpv_usd end) first_18_months_tpv
-- ,count(case when num_days_before_today > 28 and num_days_before_today <=56 then reference end) prior_period_transactions
-- ,count(case when num_days_before_today <= 28 then reference end) this_period_transactions
-- ,count(case when num_days_before_today <=56 then reference end) this_and_prior_period_transactions
,SUM(CASE WHEN created >= this_period_start AND created < this_period_end THEN 1 ELSE 0 END) AS this_period_transactions
,SUM(CASE WHEN created >= prior_period_start AND created < this_period_start THEN 1 ELSE 0 END) AS prior_period_transactions
,SUM(CASE WHEN created >= prior_period_start AND created < prior_period_end THEN 1 ELSE 0 END) AS this_and_prior_period_transactions
from transaction_bb_fx_biz
--where dt >= date'2022-12-10'
group by 1
)

--select * from txn_bb_agg limit 10

, biz_level_metrics as
(
select
bt.*
,t.*
,case when fifth_transaction_tstamp is not null then true else false end as has_more_than_5_lifetime_transactions
,rank() over (order by lifetime_transaction_count desc) as lifetime_transaction_count_rank
,rank() over (order by lifetime_tpv_usd desc) as lifetime_tpv_usd_rank
,case when num_days_first_and_latest_transaction >= 30 then true else false end as is_retained_m1
,case when num_days_first_and_latest_transaction >= 90 then true else false end as is_retained_m3
,case when num_days_first_and_latest_transaction >= 180 then true else false end as is_retained_m6
,case when num_days_since_latest_transaction <= 28 then true else false end as is_active
,case when this_period_transactions > 0 and prior_period_transactions = 0 and num_days_since_first_transaction > 28 then true else false end as is_reactivated
,case when this_period_transactions > 0 and num_days_since_first_transaction <= 28 then true else false end as is_new_active
,case when num_days_since_latest_transaction > 28 and num_days_since_latest_transaction <= 56 then true else false end as is_churned
,case when num_days_since_latest_transaction > 56 then true else false end as is_dormant
,unique_product_activation_request
,unique_product_activated
from
business_details bt
left join
txn_bb_agg t
on bt.business_id = t.user_id
left join 
biz_pdt_activation p
on bt.business_id = p.business_id
)

select 
business_id,
created,
go_live,
first_successful_test_transaction,
first_test_transaction,

1 as usd_usd,
used_product_type,
used_product_subtype,
used_channel_name,
count,

tpv_usd,
first_transaction_tstamp as first_transaction,
latest_transaction_tstamp as latest_transaction,
this_period_transactions,
prior_period_transactions,

this_and_prior_period_transactions,
first_month_count,
second_month_count,
third_month_count,
first_18_months_count,

first_month_tpv,
second_month_tpv,
third_month_tpv,
first_18_months_tpv,
go_live_to_first_transaction,

is_active,
is_reactivated,
is_new_active,
is_churned,
is_dormant,

is_non_starting,
has_more_than_5_lifetime_transactions,
lifetime_tpv_usd_rank,
lifetime_transaction_count_rank,
is_retained_m1,

is_retained_m3,
is_retained_m6,
lifetime_transaction_count,
lifetime_tpv_usd,
--new 
case when has_more_than_5_lifetime_transactions = false
 or (first_test_transaction is not null and go_live is null) then true else false end as is_testing,
size(split(used_product_type, ',')) as unique_transaction_products_used_count,

unique_product_activation_request,
unique_product_activated,
9 as unique_transaction_products_offered

from biz_level_metrics 
group by 
1,
2,
3,
4,
5,
6,
7,
8,
9,
10,
11,
12,
13,
14,
15,
16,
17,
18,
19,
20,
21,
22,
23,
24,
25,
26,
27,
28,
29,
30,
31,
32,
33,
34,
35,
36,
37,
38,
39,
40,
41,
42,
43,
44
--limit 10
