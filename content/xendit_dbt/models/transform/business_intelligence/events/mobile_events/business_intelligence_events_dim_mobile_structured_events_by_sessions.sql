{{ config(
    alias="dim_mobile_structured_events_by_sessions",
    tags=["mobile_events"]
) }}

WITH mobile_events AS (
    SELECT * FROM {{ ref('business_intelligence_events_fct_mobile_events') }}
    WHERE event = 'struct'
)

SELECT
    user_id,
    app_id,
    se_category,
    se_action,
    se_label,
    se_property,
    se_value,
    session_id,
    dvce_created_tstamp AS tstamp,
    COUNT(*) AS event_count
FROM mobile_events
GROUP BY ALL
