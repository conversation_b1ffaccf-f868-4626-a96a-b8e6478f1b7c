{{ config(
    alias="web_user_mapping",
) }}

-- noqa: disable=ST05

WITH events AS (
    SELECT * FROM clean__kafka_snowplow.events
),

domain_userid_filter AS (
    SELECT * FROM {{ ref('business_intelligence_events_domain_userid_filter') }}
),

network_userid_filter AS (
    SELECT * FROM {{ ref('business_intelligence_events_network_userid_filter') }}
),

tagged_domains AS (
    SELECT t1.domain_userid
    FROM
        events AS t1
    LEFT JOIN domain_userid_filter AS t2 ON t1.domain_userid = t2.domain_userid
    LEFT JOIN network_userid_filter AS t3 ON t1.network_userid = t3.network_userid
    WHERE user_id IS NOT NULL
        AND t2.domain_userid IS NULL
        AND t3.network_userid IS NULL
        AND t1.platform = 'web'
    GROUP BY t1.domain_userid
),

user_mapping_01 AS (
    SELECT
        t1.domain_userid,
        t2.domain_sessionid,
        t2.network_userid
    FROM
        tagged_domains AS t1
    LEFT JOIN events AS t2 ON t1.domain_userid = t2.domain_userid
    GROUP BY t1.domain_userid, t2.domain_sessionid, t2.network_userid
),

user_mapping_02 AS (
    SELECT
        t1.domain_userid,
        t1.domain_sessionid,
        t1.network_userid
    FROM events AS t1
    INNER JOIN (
        SELECT network_userid
        FROM user_mapping_01
    ) AS t2 ON t1.network_userid = t2.network_userid
    WHERE
        t1.platform = 'web'
    GROUP BY t1.domain_userid, t1.domain_sessionid, t1.network_userid


),

user_mapping_03 AS (
    SELECT DISTINCT *
    FROM
        (
            SELECT * FROM user_mapping_01
            UNION
            SELECT * FROM user_mapping_02
        )
),

latest_userid_prep AS (
    SELECT
        domain_userid,
        MAX(STRUCT(collector_tstamp, dvce_created_tstamp)) AS latest_userid_tstamp
    FROM
        events
    WHERE
        user_id IS NOT NULL
    GROUP BY
        domain_userid
),

latest_userid AS (
    SELECT
        t1.domain_userid,
        t1.user_id
    FROM
        events AS t1
    INNER JOIN latest_userid_prep AS t2
        ON (
            t1.domain_userid = t2.domain_userid
            AND STRUCT(t1.collector_tstamp, t1.dvce_created_tstamp) = t2.latest_userid_tstamp

        )
    GROUP BY t1.domain_userid, t1.user_id
),

user_mapping_04 AS (
    SELECT
        t1.*,
        t2.user_id AS business_id
    FROM
        user_mapping_03 AS t1
    LEFT JOIN latest_userid AS t2 ON t1.domain_userid = t2.domain_userid
)

SELECT * FROM user_mapping_04
