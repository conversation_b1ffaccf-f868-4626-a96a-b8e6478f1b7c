{{ config(
    enabled=false,
    alias="integrated_remittance_old_va",
    partition_by=['dt'],
    materialized="incremental",
    unique_key=['primary_key'],
    tags=["fct_txn_bb", "txn_bb_va"]
) }}

SELECT DISTINCT
  coalesce(va.id, '')||coalesce(payments.id, '')||coalesce(deposits.id, '') AS primary_key,
  va.v AS va_v,
  va.id AS va_id,
  va.account_number AS va_account_number,
  -- va.account_number_varchar AS va_account_number_varchar,
  -- va.actual_expiration_date AS va_actual_expiration_date,
  va.bank_code AS va_bank_code,
  va.created AS va_created,
  va.currency AS va_currency,
  va.description AS va_description,
  -- va.entity AS va_entity,
  va.events AS va_events,
  va.expected_amount AS va_expected_amount,
  va.expiration_date AS va_expiration_date,
  va.external_id AS va_external_id,
  va.is_closed AS va_is_closed,
  va.is_duplicated AS va_is_duplicated,
  -- va.is_forecoffee_problem AS va_is_forecoffee_problem,
  va.is_paid AS va_is_paid,
  va.is_single_use AS va_is_single_use,
  va.merchant_code AS va_merchant_code,
  va.method AS va_method,
  va.name AS va_name,
  -- va.old_bank_code AS va_old_bank_code,
  -- va.old_expected_amount AS va_old_expected_amount,
  -- va.old_expiration_date AS va_old_expiration_date,
  -- va.old_name AS va_old_name,
  -- va.old_owner_id AS va_old_owner_id,
  -- va.old_status AS va_old_status,
  va.owner_id AS va_business_id,
  -- va.reference AS va_reference,
  -- va.should_allow_multiple_payments AS va_should_allow_multiple_payments,
  -- va.should_send_callback AS va_should_send_callback,
  va.status AS va_status,
  va.suggested_amount AS va_suggested_amount,
  -- va.temp_expiration_date AS va_temp_expiration_date,
  -- va.temp_status AS va_temp_status,
  va.type AS va_type,
  va.updated AS va_updated,
  va.xendit_owner_id AS va_xendit_owner_id,

  payments.v AS payments_v,
  payments.id AS ps_id,
  payments.account_number AS payments_account_number,
  payments.amount AS payments_amount,
  payments.bank_code AS payments_bank_code,
  payments.callback_virtual_account_id AS payments_callback_virtual_account_id,
  payments.created AS payments_created,
  payments.currency AS payments_currency,
  payments.external_id AS payments_external_id,
  payments.is_expired_when_paid AS payments_is_expired_when_paid,
  payments.loan_id AS payments_loan_id,
  payments.merchant_code AS payments_merchant_code,
  payments.name AS payments_name,
  -- payments.old_external_id AS payments_old_external_id,
  payments.old_payment_id AS payments_old_payment_id,
  payments.owner_id AS payments_owner_id,
  payments.payment_id AS payments_payment_id,
  payments.sender_name AS payments_sender_name,
  payments.transaction_timestamp AS payments_transaction_timestamp,
  payments.type AS payments_type,
  payments.updated AS payments_updated,
  payments.dt AS payments_dt,

  -- deposits.v AS deposits_v,
  deposits.id AS deposits_id,
  -- deposits.account_number AS deposits_account_number,
  -- deposits.amount AS deposits_amount,
  -- deposits.bank_code AS deposits_bank_code,
  -- deposits.callback_virtual_account_id AS deposits_callback_virtual_account_id,
  -- deposits.callback_virtual_account_payment_id AS deposits_callback_virtual_account_payment_id,
  deposits.created AS deposits_created,
  -- deposits.currency AS deposits_currency,
  -- deposits.events AS deposits_events,
  deposits.virtual_account_number AS deposits_full_account_number,
  CASE
    WHEN deposits.id is NULL then false
    ELSE NULL
  END AS deposits_is_switching,
  -- deposits.old_callback_virtual_account_id AS deposits_old_callback_virtual_account_id,
  -- deposits.old_created AS deposits_old_created,
  -- deposits.old_external_id AS deposits_old_external_id,
  -- deposits.old_payment_id AS deposits_old_payment_id,
  -- deposits.old_status AS deposits_old_status,
  deposits.virtual_account_payment_id AS deposits_payment_id,
  deposits.sender_name AS deposits_sender_name,
  deposits.status AS deposits_status,
  deposits.payment_holding_deposit_transaction_id AS deposits_transaction_id,
  deposits.transaction_timestamp AS deposits_transaction_timestamp,
  deposits.updated AS deposits_updated,
  deposits.business_id AS deposits_user_id,
  deposits.dt AS deposits_dt,
  'DEFAULT' AS label,
  transaction.settled_at AS settled_at,

  date(va.created) AS dt
FROM {{ source('clean__xendit_collections_service', 'callbackvirtualaccounts') }} va
LEFT JOIN {{ source('clean__xendit_collections_service', 'callbackvirtualaccountpayments') }} payments
  ON
    va.id = payments.callback_virtual_account_id
LEFT JOIN {{ source('clean__remittance_money_in_service', 'virtual_account_payment') }} deposits
  ON
    payments.id = deposits.virtual_account_payment_id
LEFT JOIN {{ source('clean__tcdb', 'transaction') }} transaction
  ON
    deposits.id = transaction.reference
    and transaction.type = 'REMITTANCE_VA_PAYMENT_CLAIM'
WHERE 
  va.type = 'REMITTANCE_COLLECTION'
  {% if is_incremental() %}
  and (va.updated >= CURRENT_DATE - INTERVAL '7' days or payments.transaction_timestamp >= CURRENT_DATE - INTERVAL '7' days)
  {% endif %}
