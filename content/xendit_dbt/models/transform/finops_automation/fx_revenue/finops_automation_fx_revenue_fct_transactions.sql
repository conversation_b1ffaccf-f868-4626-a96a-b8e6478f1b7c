{{ config(
    alias="fct_transactions"
) }}


WITH data AS (
    SELECT
        "Philippines" AS country,
        fx_type,
        entity,
        business_id,
        customer,
        transaction_type,
        "PHP" AS currency_local,
        profit_php AS profit_local,
        profit_usd,
        tpv_usd,
        xendit_rate_final,
        CONCAT_WS("_", COALESCE(from_currency, ""), COALESCE(to_currency, "")) AS transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_ph') }}
    UNION ALL
    SELECT
        "Indonesia" AS country,
        fx_type,
        entity,
        business_id,
        customer,
        transaction_type,
        "IDR" AS currency_local,
        total_id_fx_profits_idr AS profit_local,
        total_id_fx_profits_usd AS profit_usd,
        total_id_tpv_usd AS tpv_usd,
        xendit_rate_final,
        CONCAT_WS("_", COALESCE(from_currency, ""), COALESCE(to_currency, "")) AS transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_id') }}
    UNION ALL
    SELECT
        "Thailand" AS country,
        COALESCE(fx_type, "") AS fx_type,
        COALESCE(entity, "") AS entity,
        merchant_bid AS business_id,
        merchant_name AS customer,
        action AS transaction_type,
        "THB" AS currency_local,
        profit_thb AS profit_local,
        profit_usd,
        xendit_rate_final,
        tpv_usd,
        CONCAT_WS("_", COALESCE(from_currency, ""), COALESCE(to_currency, "")) AS transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_th') }}
    UNION ALL
    SELECT
        "Vietnam" AS country,
        COALESCE(fx_type, "") AS fx_type,
        COALESCE(entity, "") AS entity,
        merchant_bid AS business_id,
        merchant_name AS customer,
        action AS transaction_type,
        "VND" AS currency_local,
        profit_vnd AS profit_local,
        profit_usd,
        tpv_usd,
        xendit_rate_final,
        CONCAT_WS("_", COALESCE(from_currency, ""), COALESCE(to_currency, "")) AS transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_vn') }}
    UNION ALL
    SELECT
        "Malaysia" AS country,
        COALESCE(fx_type, "") AS fx_type,
        COALESCE(entity, "") AS entity,
        merchant_bid AS business_id,
        merchant_name AS customer,
        action AS transaction_type,
        "MYR" AS currency_local,
        profit_myr AS profit_local,
        profit_usd,
        tpv_usd,
        xendit_rate_final,
        CONCAT_WS("_", COALESCE("", ""), COALESCE(to_currency, "")) AS transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_my') }}
    UNION ALL
    SELECT
        CASE
            WHEN currency_local = "PHP" THEN "Philippines"
            WHEN currency_local = "IDR" THEN "Indonesia"
            WHEN currency_local = "THB" THEN "Thailand"
            WHEN currency_local = "VND" THEN "Vietnam"
            WHEN currency_local = "MYR" THEN "Malaysia"
            ELSE "Regional"
        END AS country,
        COALESCE(fx_type, "") AS fx_type,
        COALESCE(entity, "") AS entity,
        business_id,
        COALESCE(customer, "") AS customer,
        COALESCE(transaction_type, "") AS transaction_type,
        currency_local,
        profit_local,
        profit_usd,
        tpv_usd,
        xendit_rate_final,
        transaction_channel,
        CAST(date AS date) AS date
    FROM {{ source('transform__fx_revenue', 'fx_transactions_regional_dashboard') }}
)

SELECT
    *,
    CAST(date AS date) AS dt
FROM data
