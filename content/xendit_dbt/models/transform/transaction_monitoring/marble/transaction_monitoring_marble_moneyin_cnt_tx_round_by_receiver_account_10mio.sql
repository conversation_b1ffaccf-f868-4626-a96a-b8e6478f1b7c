{{ config(
    alias="moneyin_cnt_tx_round_by_receiver_account_10mio"
) }}

-- Count of transactions equal 10 million IDR per day grouped by receiver account over last 1 days
WITH money_in AS (
  SELECT
    vap.sender_account_number as account_number,
    vapc.amount AS amount,
    DATE(FROM_UTC_TIMESTAMP(transaction_timestamp, 'Asia/Jakarta')) AS transaction_date
  FROM
    {{ source('clean__remittance_money_in_service', 'virtual_account_payment') }} AS vap
  INNER JOIN
    {{ source('clean__identity_service_v2', 'end_customer') }} AS sd
  ON vap.sender_customer_id = sd.end_customer_id
  INNER JOIN
    {{ source('clean__remittance_money_in_service', 'virtual_account_payment_claim') }} AS vapc
  ON vap.id = vapc.remittance_collection_virtual_account_payment_id 
  INNER JOIN
    {{ source('clean__identity_service_v2', 'end_customer') }} AS ec
  ON vapc.recipient_customer_id = ec.end_customer_id
  WHERE
    FROM_UTC_TIMESTAMP(
      transaction_timestamp, 'Asia/Jakarta'
    ) BETWEEN DATE_SUB(
      CURRENT_TIMESTAMP(), 
      1
    ) 
    AND CURRENT_DATE()
    AND vap.currency = 'IDR'
),
daily_counts AS (
  SELECT
    account_number,
    transaction_date,
    COUNT(*) AS transaction_count
  FROM money_in
  WHERE amount = ********  -- Count only transactions of exactly 10 million IDR
  GROUP BY
    account_number,
    transaction_date
)
SELECT
  uuid() AS object_id,
  current_timestamp() AS updated_at,
  account_number AS receiver_account,
  SUM(transaction_count) AS amount  -- Sum the daily counts
FROM daily_counts
GROUP BY
  account_number
