{{ config(
    alias="moneyout_sum_tx_amt_by_receiver_name_30_days"
) }}

-- sum transaction amount per day grouped by receiver name over last 30 days
WITH money_out AS (
  SELECT
    concat_ws(
        ' ',
        ec.business_name,
        ec.given_name,
        ec.given_names,
        ec.given_names_non_roman,
        ec.middle_name,
        ec.mother_maiden_name,
        ec.surname,
        ec.surname_non_roman,
        ec.trading_name
    ) AS receiver_name,
    r.destination_amount AS amount,
    -- Add date extraction
    DATE(FROM_UTC_TIMESTAMP(r.created, 'Asia/Jakarta')) AS transaction_date
  FROM {{ source('clean__remittance_money_out_service', 'remittance') }} r
  LEFT JOIN  {{ source('clean__identity_service_v2', 'end_customer') }} sd
    ON r.sender_customer_id = concat('cust-', sd.end_customer_id)
  LEFT JOIN {{ source('clean__identity_service_v2', 'end_customer') }} ec
    ON r.recipient_customer_id = concat('cust-', ec.end_customer_id)
  LEFT JOIN {{ source('clean__remittance_money_out_service', 'disbursement_detail') }} AS dd 
    ON r.id = dd.remittance_id
  WHERE
    r.status='SUCCEEDED'
    AND FROM_UTC_TIMESTAMP(
      r.created, 'Asia/Jakarta'
    ) BETWEEN DATE_SUB(
      CURRENT_TIMESTAMP(), 
      30
    ) 
    AND CURRENT_DATE()
    AND r.destination_currency = 'IDR'
)
SELECT
  uuid() AS object_id,
  current_timestamp() AS updated_at,
  receiver_name,
  SUM(daily_sum) AS amount
FROM (
  SELECT
    receiver_name,
    transaction_date,
    SUM(amount) AS daily_sum
  FROM money_out
  GROUP BY
    receiver_name,
    transaction_date
)
GROUP BY
  receiver_name