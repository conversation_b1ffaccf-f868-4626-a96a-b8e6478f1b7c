{{ config(
    alias="moneyin_sum_tx_amt_by_sender_name_30_days"
) }}

-- sum transaction amount per day grouped by sender name over last 30 days
WITH money_in AS (
  SELECT
    concat_ws(
      ' ',
      sd.business_name,
      sd.given_name,
      sd.given_names,
      sd.given_names_non_roman,
      sd.middle_name,
      sd.mother_maiden_name,
      sd.surname,
      sd.surname_non_roman,
      sd.trading_name
    ) AS sender_name,
    vap.amount AS amount,
    -- Add transaction date
    DATE(FROM_UTC_TIMESTAMP(vap.transaction_timestamp, 'Asia/Jakarta')) AS transaction_date
  FROM
    {{ source('clean__remittance_money_in_service', 'virtual_account_payment') }} AS vap
  INNER JOIN
    {{ source('clean__identity_service_v2', 'end_customer') }} AS sd
  ON vap.sender_customer_id = sd.end_customer_id
  INNER JOIN
    {{ source('clean__remittance_money_in_service', 'virtual_account_payment_claim') }} AS vapc
  ON vap.id = vapc.remittance_collection_virtual_account_payment_id 
  INNER JOIN
    {{ source('clean__identity_service_v2', 'end_customer') }} AS ec
  ON vapc.recipient_customer_id = ec.end_customer_id 
  WHERE
    FROM_UTC_TIMESTAMP(
      transaction_timestamp, 'Asia/Jakarta'
    ) BETWEEN DATE_SUB(
      CURRENT_TIMESTAMP(), 
      30
    ) 
    AND CURRENT_DATE()
    AND vap.currency = 'IDR'
),
daily_sum AS (
  -- Calculate daily average for each sender
  SELECT
    sender_name,
    transaction_date,
    SUM(amount) AS daily_sum
  FROM money_in
  GROUP BY
    sender_name,
    transaction_date
)
SELECT
  uuid() AS object_id,
  current_timestamp() AS updated_at,
  sender_name,
  SUM(daily_sum) AS amount
FROM daily_sum
GROUP BY
  sender_name