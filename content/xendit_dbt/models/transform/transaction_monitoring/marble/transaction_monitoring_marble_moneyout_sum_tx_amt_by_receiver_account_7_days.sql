{{ config(
    alias="moneyout_sum_tx_amt_by_receiver_account_7_days"
) }}

-- sum transaction amount per day grouped by receiver account over last 7 days
WITH money_out AS (
  SELECT
    dd.channel_properties_account_number as account_number,
    r.destination_amount AS amount,
    DATE(FROM_UTC_TIMESTAMP(r.created, 'Asia/Jakarta')) AS transaction_date
  FROM {{ source('clean__remittance_money_out_service', 'remittance') }} r
  LEFT JOIN  {{ source('clean__identity_service_v2', 'end_customer') }} sd
    ON r.sender_customer_id = concat('cust-', sd.end_customer_id)
  LEFT JOIN {{ source('clean__identity_service_v2', 'end_customer') }} ec
    ON r.recipient_customer_id = concat('cust-', ec.end_customer_id)
  LEFT JOIN {{ source('clean__remittance_money_out_service', 'disbursement_detail') }} AS dd 
    ON r.id = dd.remittance_id
  WHERE
    r.status='SUCCEEDED'
    AND FROM_UTC_TIMESTAMP(
      r.created, 'Asia/Jakarta'
    ) BETWEEN DATE_SUB(
      CURRENT_TIMESTAMP(), 
      7
    ) 
    AND CURRENT_DATE()
    AND r.origin_currency = 'IDR'
),
daily_sum AS (
  SELECT
    account_number,
    transaction_date,
    SUM(amount) AS daily_sum
  FROM money_out
  GROUP BY
    account_number,
    transaction_date
)
SELECT
  uuid() AS object_id,
  current_timestamp() AS updated_at,
  account_number AS receiver_account,
  SUM(daily_sum) AS amount
FROM daily_sum
GROUP BY
  account_number