{{ config(
    alias="moneyout_cnt_tx_round_by_sender_name_10mio"
) }}

-- Count of transactions equal to 10 million IDR per day grouped by sender name over last 1 days
WITH money_out AS (
  SELECT
    concat_ws(
      ' ',
      sd.business_name,
      sd.given_name,
      sd.given_names,
      sd.given_names_non_roman,
      sd.middle_name,
      sd.mother_maiden_name,
      sd.surname,
      sd.surname_non_roman,
      sd.trading_name
    ) AS sender_name,
    r.origin_amount AS amount,
    DATE(FROM_UTC_TIMESTAMP(r.created, 'Asia/Jakarta')) AS transaction_date
  FROM {{ source('clean__remittance_money_out_service', 'remittance') }} r
  LEFT JOIN  {{ source('clean__identity_service_v2', 'end_customer') }} sd
    ON r.sender_customer_id = concat('cust-', sd.end_customer_id)
  LEFT JOIN {{ source('clean__identity_service_v2', 'end_customer') }} ec
    ON r.recipient_customer_id = concat('cust-', ec.end_customer_id)
  LEFT JOIN {{ source('clean__remittance_money_out_service', 'disbursement_detail') }} AS dd 
    ON r.id = dd.remittance_id
  WHERE
    r.status='SUCCEEDED'
    AND FROM_UTC_TIMESTAMP(
      r.created, 'Asia/Jakarta'
    ) BETWEEN DATE_SUB(
      CURRENT_TIMESTAMP(), 
      1
    ) 
    AND CURRENT_DATE()
    AND r.origin_currency = 'IDR'
),
daily_count AS (
  SELECT
    sender_name,
    transaction_date,
    COUNT(*) AS transaction_count
  FROM money_out
  WHERE amount = 10000000  -- Filter for transactions exactly 10 million IDR
  GROUP BY
    sender_name,
    transaction_date
)
SELECT
  uuid() AS object_id,
  current_timestamp() AS updated_at,
  sender_name,
  SUM(transaction_count) AS amount  -- Sum the daily counts
FROM daily_count
GROUP BY
  sender_name
