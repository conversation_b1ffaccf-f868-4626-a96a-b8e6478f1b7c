version: 2
sources:
- name: clean__disb_routing_service_sgd
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_postgres
    owner: DE Team
  tables:
  - name: route_config
  - name: disbursement_route_history
  - name: disbursement_route
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
