version: 2
sources:
- name: clean__globalprime
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_postgres
    owner: DE Team
  tables:
  - name: payment_transaction
    loaded_at_field: update_date
    columns:
    - name: ptx_id
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: payment_resp_rt
    loaded_at_field: update_date
    columns:
    - name: ptx_id
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: merchant_info_additional
    loaded_at_field: update_date
    columns:
    - name: merchant_id
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: gateway_channel
    loaded_at_field: update_date
    columns:
    - name: code
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: gateway_channel_directdebit
    loaded_at_field: update_date
    columns:
    - name: code
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: cogs_configuration
    loaded_at_field: update_date
    columns:
    - name: id
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
  - name: transfer_due_of_manual_history
    loaded_at_field: update_date
    columns:
    - name: id
      tests:
      - unique:
          where: "update_date >= __two_months_ago__"
