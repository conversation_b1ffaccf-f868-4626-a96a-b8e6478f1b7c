version: 2
sources:
- name: clean__id_disb_bank_connector_prima
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_postgres
    owner: DE Team
  tables:
  - name: disbursement
    loaded_at_field: created
    columns:
    - name: id
      tests:
      - unique
  - name: disbursement_event
    loaded_at_field: created
    columns:
    - name: id
      tests:
      - unique
  - name: destination_channel
    loaded_at_field: created
    columns:
    - name: id
      tests:
      - unique
    freshness: null
