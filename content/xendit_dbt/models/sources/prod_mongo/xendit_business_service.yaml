version: 2
sources:
- name: clean__xendit_business_service
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_mongo
    owner: DE Team
  tables:
  - name: businesssettings
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: callbackrequests
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: bankaccounts
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: automateddailydisbursements
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: philippinesbusinesssettings
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: permissions
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: submerchantflags
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
