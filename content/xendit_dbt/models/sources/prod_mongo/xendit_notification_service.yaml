version: 2
sources:
- name: clean__xendit_notification_service
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_mongo
    owner: DE Team
  tables:
  - name: batchnotifications
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: emails
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: emailrequests
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: templates
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: notifications
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: notificationevents
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: smsrequests
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
    freshness: null
