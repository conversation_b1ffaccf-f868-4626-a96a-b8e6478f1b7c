version: 2
sources:
- name: clean__auth_platform_db
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: dev_postgres
    owner: DE Team
  tables:
  - name: business_banned_products_dev
  - name: schema_migrations_dev
  - name: business_dev
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: product_dev
