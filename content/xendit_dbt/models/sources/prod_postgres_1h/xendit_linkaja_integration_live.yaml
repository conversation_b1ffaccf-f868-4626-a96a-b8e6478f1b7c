version: 2
sources:
- name: clean__xendit_linkaja_integration_live
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_postgres_1h
    owner: DE Team
  tables:
  - name: callback_logs_1_hour_pipeline
    loaded_at_field: created_at
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: checkout_tokens_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: deposits_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: merchants_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: payments_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
    freshness: null
  - name: qris_merchants_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: business_id
      tests:
      - unique
    freshness:
      warn_after:
        count: 15
        period: day
      error_after:
        count: 30
        period: day
  - name: qris_payment_requests_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
  - name: qris_payments_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: id
      tests:
      - unique
  - name: dana_qris_merchants_1_hour_pipeline
    loaded_at_field: updated_at
    columns:
    - name: business_id
      tests:
      - unique
