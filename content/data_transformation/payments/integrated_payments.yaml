registra_type: TRANSFORMATION
source_name: integrated_payments
source_group: payments
# UTC time in cron job
cron_schedule: 0 22 * * *
data_config:
  payments_main:
    tables:
      integrated_direct_debit:
        transformation_mode: DBT
        team: payments
        dbt_detail:
          dbt_model: xendit_dbt.transform.payments.main.payments_main_integrated_direct_debit
        grant_read_to: ['default-user', 'risk_external_vendor']
      integrated_ewallet:
        transformation_mode: DBT
        team: payments
        dbt_detail:
          dbt_model: xendit_dbt.transform.payments.main.payments_main_integrated_ewallet
        grant_read_to: ['default-user', 'risk_external_vendor']
      integrated_paylater:
        transformation_mode: DBT
        team: payments
        dbt_detail:
          dbt_model: xendit_dbt.transform.payments.main.payments_main_integrated_paylater
        grant_read_to: ['default-user', 'risk_external_vendor']
      integrated_qr:
        transformation_mode: DBT
        team: payments
        dbt_detail:
          dbt_model: xendit_dbt.transform.payments.main.payments_main_integrated_qr
        grant_read_to: ['default-user', 'risk_external_vendor']
