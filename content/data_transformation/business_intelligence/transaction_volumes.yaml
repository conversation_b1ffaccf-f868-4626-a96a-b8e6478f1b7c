registra_type: TRANSFORMATION
source_name: transaction_volumes
source_group: business_intelligence
cron_schedule: 0 22 * * *
data_config:
  business_intelligence_transaction_volumes:
    tables:
      # transaction_volumes_v2:
      #   transformation_mode: DBT
      #   team: business_intelligence
      #   dbt_detail:
      #     dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_transaction_volumes_v2
      transaction_volumes_v2_dev:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_transaction_volumes_v2_dev
      # transaction_volumes_v3:
      #   transformation_mode: DBT
      #   team: business_intelligence
      #   dbt_detail:
      #     dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_transaction_volumes_v3
      #   partition_keys:
      #   - order: 1
      #     col_name: 'dt'
      #     col_type: 'date'
      #     col_alias: 'dt'
      ## disable this for now because it already stated in `on-run-end`
      # dbt_results:
      #   transformation_mode: DBT
      #   team: business_intelligence
      #   dbt_detail:
      #     dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_dbt_results
      #   alert:
      #     airflow:
      #       - channel: data-quality-check
      #         subscribers:
      #           usergroups: ["troops-data-bi"]
      stg_cards:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_cards
      stg_disbursement:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_disbursement
      stg_direct_debit:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_direct_debit
      stg_ewallet:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_ewallet
      stg_qr_code:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_qr_code
      stg_virtual_account:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_virtual_account
      stg_ro:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_ro
          source_dqc_enabled: true
      stg_xp:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_xp
      stg_paylater:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_stg_paylater
      fct_transactions_backbone_v2:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_fct_transactions_backbone_v2
        z_order_by: ['user_id']
        alert:
          airflow:
            - channel: lr-dev-dbt-monitoring
              subscribers:
                usergroups: ["troops-data-bi"]
      fct_transaction_backbone:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_fct_transaction_backbone
        z_order_by: ['user_id']
        alert:
          airflow:
            - channel: lr-dev-dbt-monitoring
              subscribers:
                usergroups: ["troops-data-bi"]
      disbursement_sender_bank_code:
        transformation_mode: DBT
        team: business_intelligence
        dbt_detail:
          dbt_model: xendit_dbt.transform.business_intelligence.transaction_volumes.business_intelligence_transaction_volumes_disbursement_sender_bank_code
        alert:
          airflow:
            - channel: lr-dev-dbt-monitoring
              subscribers:
                usergroups: ["troops-data-bi"]