{"fields": [{"metadata": {}, "name": "data", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "firstEventId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "previousSessionId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "sessionId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "sessionIndex", "nullable": true, "type": "long"}, {"metadata": {}, "name": "storageMechanism", "nullable": true, "type": "string"}, {"metadata": {}, "name": "userId", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "hierarchy", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "refParent", "nullable": true, "type": "string"}, {"metadata": {}, "name": "refRoot", "nullable": true, "type": "string"}, {"metadata": {}, "name": "refTree", "nullable": true, "type": {"containsNull": true, "elementType": "string", "type": "array"}}, {"metadata": {}, "name": "rootId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "rootTstamp", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "schema", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "format", "nullable": true, "type": "string"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "vendor", "nullable": true, "type": "string"}, {"metadata": {}, "name": "version", "nullable": true, "type": "string"}], "type": "struct"}}], "type": "struct"}