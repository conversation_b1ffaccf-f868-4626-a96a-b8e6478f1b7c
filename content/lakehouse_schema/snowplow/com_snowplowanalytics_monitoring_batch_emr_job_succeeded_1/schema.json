{"fields": [{"metadata": {}, "name": "hierarchy", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "refParent", "nullable": true, "type": "string"}, {"metadata": {}, "name": "refRoot", "nullable": true, "type": "string"}, {"metadata": {}, "name": "refTree", "nullable": true, "type": {"containsNull": true, "elementType": "string", "type": "array"}}, {"metadata": {}, "name": "rootId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "rootTstamp", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "schema", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "format", "nullable": true, "type": "string"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "vendor", "nullable": true, "type": "string"}, {"metadata": {}, "name": "version", "nullable": true, "type": "string"}], "type": "struct"}}], "type": "struct"}