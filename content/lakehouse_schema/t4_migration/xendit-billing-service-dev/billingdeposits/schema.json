{"fields": [{"metadata": {}, "name": "__v", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "_id", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "oid", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "amount", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "created", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "currency", "nullable": true, "type": "string"}, {"metadata": {}, "name": "events", "nullable": true, "type": {"containsNull": true, "elementType": {"fields": [{"metadata": {}, "name": "created", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "status", "nullable": true, "type": "string"}, {"metadata": {}, "name": "status_updated", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "transaction_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transaction_sequence", "nullable": true, "type": "string"}], "type": "struct"}, "type": "array"}}, {"metadata": {}, "name": "status", "nullable": true, "type": "string"}, {"metadata": {}, "name": "status_updated", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "transaction_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transaction_sequence", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "transcation_sequence", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "updated", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "user_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "uuid", "nullable": true, "type": "string"}], "type": "struct"}