{"fields": [{"metadata": {}, "name": "__v", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "_id", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "oid", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "amount", "nullable": true, "type": "double"}, {"metadata": {}, "name": "bank_account_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "bank_identifier", "nullable": true, "type": "string"}, {"metadata": {}, "name": "bank_reference_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "client_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "created", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "createdAt", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "description", "nullable": true, "type": "string"}, {"metadata": {}, "name": "events", "nullable": true, "type": {"containsNull": true, "elementType": {"fields": [{"metadata": {}, "name": "bank_reference_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "bank_response", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "AcctInqRs", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "InqInfo", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "Account<PERSON><PERSON>", "nullable": true, "type": "string"}, {"metadata": {}, "name": "AccountNumber", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "MsgRsHdr", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "CustRefID", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ResponseTimestamp", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusCode", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusDesc", "nullable": true, "type": "string"}], "type": "struct"}}], "type": "struct"}}, {"metadata": {}, "name": "ErrorCode", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ErrorDescription", "nullable": true, "type": "string"}, {"metadata": {}, "name": "OlXferAddRs", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "MsgRsHdr", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "CustRefID", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ResponseTimestamp", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusCode", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusDesc", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "TrxReffNo", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "OlXferInqRs", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "MsgRsHdr", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "CustRefID", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ResponseTimestamp", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusCode", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusDesc", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "TrxReffNo", "nullable": true, "type": "string"}, {"metadata": {}, "name": "XferInfo", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "BankId", "nullable": true, "type": "string"}, {"metadata": {}, "name": "BankName", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ToA<PERSON>unt", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ToAccountFullName", "nullable": true, "type": "string"}], "type": "struct"}}], "type": "struct"}}, {"metadata": {}, "name": "XferAddRs", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "MsgRsHdr", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "CustRefID", "nullable": true, "type": "string"}, {"metadata": {}, "name": "ResponseTimestamp", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusCode", "nullable": true, "type": "string"}, {"metadata": {}, "name": "StatusDesc", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "TrxReffNo", "nullable": true, "type": "string"}], "type": "struct"}}], "type": "struct"}}, {"metadata": {}, "name": "created", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "event", "nullable": true, "type": "string"}, {"metadata": {}, "name": "failure_code", "nullable": true, "type": "string"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "status", "nullable": true, "type": "string"}, {"metadata": {}, "name": "status_updated", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "updated", "nullable": true, "type": "timestamp"}], "type": "struct"}, "type": "array"}}, {"metadata": {}, "name": "failure_code", "nullable": true, "type": "string"}, {"metadata": {}, "name": "inquiry_customer_reference_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "is_force_credit", "nullable": true, "type": "boolean"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "payment_identifier", "nullable": true, "type": "string"}, {"metadata": {}, "name": "processor_holder_name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "reference", "nullable": true, "type": "string"}, {"metadata": {}, "name": "source_account_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "status", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transfer_customer_reference_number", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transfer_type", "nullable": true, "type": "string"}, {"metadata": {}, "name": "updated", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "updatedAt", "nullable": true, "type": "timestamp"}], "type": "struct"}