{"fields": [{"metadata": {}, "name": "__v", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "_id", "nullable": true, "type": {"fields": [{"metadata": {}, "name": "oid", "nullable": true, "type": "string"}], "type": "struct"}}, {"metadata": {}, "name": "amount", "nullable": true, "type": "integer"}, {"metadata": {}, "name": "created", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "external_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "fixed_payment_code_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "is_different_amount_when_paid", "nullable": true, "type": "boolean"}, {"metadata": {}, "name": "is_expired_when_paid", "nullable": true, "type": "boolean"}, {"metadata": {}, "name": "is_inactive_when_paid", "nullable": true, "type": "boolean"}, {"metadata": {}, "name": "name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "old_payment_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "owner_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "payment_code", "nullable": true, "type": "string"}, {"metadata": {}, "name": "payment_id", "nullable": true, "type": "string"}, {"metadata": {}, "name": "prefix", "nullable": true, "type": "string"}, {"metadata": {}, "name": "retail_outlet_name", "nullable": true, "type": "string"}, {"metadata": {}, "name": "transaction_timestamp", "nullable": true, "type": "timestamp"}, {"metadata": {}, "name": "type", "nullable": true, "type": "string"}, {"metadata": {}, "name": "updated", "nullable": true, "type": "timestamp"}], "type": "struct"}