{"type": "struct", "fields": [{"name": "CreatedById", "type": "string", "nullable": true, "metadata": {}}, {"name": "CreatedDate", "type": "long", "nullable": true, "metadata": {}}, {"name": "DataType", "type": "string", "nullable": true, "metadata": {}}, {"name": "Field", "type": "string", "nullable": true, "metadata": {}}, {"name": "Id", "type": "string", "nullable": true, "metadata": {}}, {"name": "IsDeleted", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "NewvalDate", "type": "string", "nullable": true, "metadata": {}}, {"name": "NewvalFirstName", "type": "string", "nullable": true, "metadata": {}}, {"name": "NewvalLastName", "type": "string", "nullable": true, "metadata": {}}, {"name": "NewValue", "type": "string", "nullable": true, "metadata": {}}, {"name": "OldvalDate", "type": "string", "nullable": true, "metadata": {}}, {"name": "OldvalFirstName", "type": "string", "nullable": true, "metadata": {}}, {"name": "OldvalLastName", "type": "double", "nullable": true, "metadata": {}}, {"name": "OldValue", "type": "string", "nullable": true, "metadata": {}}, {"name": "OpportunityId", "type": "string", "nullable": true, "metadata": {}}]}