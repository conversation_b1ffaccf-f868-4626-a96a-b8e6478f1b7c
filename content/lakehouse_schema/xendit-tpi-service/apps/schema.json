{"fields": [{"name": "_id", "type": {"fields": [{"name": "oid", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "app_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "business_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "can_install", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "category", "type": "string", "nullable": true, "metadata": {}}, {"name": "countries", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "description", "type": "string", "nullable": true, "metadata": {}}, {"name": "developer_info", "type": {"containsNull": true, "elementType": {"fields": [{"name": "label", "type": "string", "nullable": true, "metadata": {}}, {"name": "value", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "documentation_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "has_settings", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "icon", "type": "string", "nullable": true, "metadata": {}}, {"name": "images", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "installation_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "name", "type": "string", "nullable": true, "metadata": {}}, {"name": "settings_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "sort_order", "type": "long", "nullable": true, "metadata": {}}], "type": "struct"}