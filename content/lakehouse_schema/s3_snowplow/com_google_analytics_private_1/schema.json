{"fields": [{"name": "data", "type": {"fields": [{"name": "gid", "type": "string", "nullable": true, "metadata": {}}, {"name": "r", "type": "long", "nullable": true, "metadata": {}}, {"name": "s", "type": "long", "nullable": true, "metadata": {}}, {"name": "u", "type": "string", "nullable": true, "metadata": {}}, {"name": "v", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "hierarchy", "type": {"fields": [{"name": "refParent", "type": "string", "nullable": true, "metadata": {}}, {"name": "refRoot", "type": "string", "nullable": true, "metadata": {}}, {"name": "refTree", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "rootId", "type": "string", "nullable": true, "metadata": {}}, {"name": "rootTstamp", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "schema", "type": {"fields": [{"name": "format", "type": "string", "nullable": true, "metadata": {}}, {"name": "name", "type": "string", "nullable": true, "metadata": {}}, {"name": "vendor", "type": "string", "nullable": true, "metadata": {}}, {"name": "version", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}], "type": "struct"}