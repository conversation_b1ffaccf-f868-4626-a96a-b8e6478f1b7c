{"type": "struct", "fields": [{"name": "__v", "type": "integer", "nullable": true, "metadata": {}}, {"name": "_id", "type": {"type": "struct", "fields": [{"name": "oid", "type": "string", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "business_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "created", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "is_xp_merchant", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "merchant_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "merchant_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "updated", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "xp_master_account_business_id", "type": "string", "nullable": true, "metadata": {}}]}