{"fields": [{"name": "affected_products", "type": {"containsNull": true, "elementType": {"fields": [{"name": "entity_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "products", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "incident_date", "type": "string", "nullable": true, "metadata": {}}, {"name": "incident_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "next_steps", "type": {"containsNull": true, "elementType": {"fields": [{"name": "jira_ticket_created_at", "type": "string", "nullable": true, "metadata": {}}, {"name": "jira_ticket_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "jira_ticket_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "jira_ticket_status", "type": "string", "nullable": true, "metadata": {}}, {"name": "jira_ticket_updated_at", "type": "string", "nullable": true, "metadata": {}}, {"name": "task_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "team_name", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "postmortem_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "reliability_gaps", "type": {"fields": [{"name": "capacity_planning", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "development", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "incident_response", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "monitoring", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "product", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "rca", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "testing_and_release", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "root_causes", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "stats", "type": {"fields": [{"name": "number_of_impacted_customers", "type": "string", "nullable": true, "metadata": {}}, {"name": "number_of_impacted_requests", "type": "string", "nullable": true, "metadata": {}}, {"name": "severity_level", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_customer_detect", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_first_possible_trigger", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_internal_detect", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_rca", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_reconciliation", "type": "string", "nullable": true, "metadata": {}}, {"name": "time_of_recovery", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "statuspage_link", "type": "string", "nullable": true, "metadata": {}}, {"name": "loaded_at", "type": "timestamp", "nullable": false, "metadata": {}}], "type": "struct"}