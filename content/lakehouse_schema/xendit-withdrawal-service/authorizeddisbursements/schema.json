{"type": "struct", "fields": [{"name": "__v", "type": "integer", "nullable": true, "metadata": {}}, {"name": "_id", "type": {"type": "struct", "fields": [{"name": "oid", "type": "string", "nullable": true, "metadata": {}}]}, "nullable": true, "metadata": {}}, {"name": "account_holder_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "account_number", "type": "string", "nullable": true, "metadata": {}}, {"name": "amount", "type": "integer", "nullable": true, "metadata": {}}, {"name": "bank_code", "type": "string", "nullable": true, "metadata": {}}, {"name": "bank_reference", "type": "string", "nullable": true, "metadata": {}}, {"name": "created", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "disbursement_description", "type": "string", "nullable": true, "metadata": {}}, {"name": "disbursement_fulfillment_type", "type": "string", "nullable": true, "metadata": {}}, {"name": "disbursement_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "events", "type": {"type": "array", "elementType": {"type": "struct", "fields": [{"name": "created", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "disbursement_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "failure_code", "type": "string", "nullable": true, "metadata": {}}, {"name": "status", "type": "string", "nullable": true, "metadata": {}}]}, "containsNull": true}, "nullable": true, "metadata": {}}, {"name": "external_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "failure_code", "type": "string", "nullable": true, "metadata": {}}, {"name": "fee_amount", "type": "integer", "nullable": true, "metadata": {}}, {"name": "is_instant", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "managed_user_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "manager_fee_amount", "type": "integer", "nullable": true, "metadata": {}}, {"name": "manager_fee_split", "type": "double", "nullable": true, "metadata": {}}, {"name": "manager_user_fee_transaction_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "manager_user_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "old_manager_user_fee_transaction_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "status", "type": "string", "nullable": true, "metadata": {}}, {"name": "transaction_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "transaction_sequence", "type": "string", "nullable": true, "metadata": {}}, {"name": "updated", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "uuid", "type": "string", "nullable": true, "metadata": {}}, {"name": "xendit_fee_amount", "type": "integer", "nullable": true, "metadata": {}}, {"name": "xendit_fee_transaction_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "xendit_fee_user_id", "type": "string", "nullable": true, "metadata": {}}]}