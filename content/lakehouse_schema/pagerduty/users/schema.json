{"fields": [{"name": "avatar_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "billed", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "color", "type": "string", "nullable": true, "metadata": {}}, {"name": "contact_methods", "type": {"containsNull": true, "elementType": {"fields": [{"name": "address", "type": "string", "nullable": true, "metadata": {}}, {"name": "blacklisted", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "country_code", "type": "long", "nullable": true, "metadata": {}}, {"name": "created_at", "type": "string", "nullable": true, "metadata": {}}, {"name": "device_type", "type": "string", "nullable": true, "metadata": {}}, {"name": "enabled", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "html_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "label", "type": "string", "nullable": true, "metadata": {}}, {"name": "self", "type": "string", "nullable": true, "metadata": {}}, {"name": "send_html_email", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "send_short_email", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "sounds", "type": {"containsNull": true, "elementType": {"fields": [{"name": "file", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "summary", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "coordinated_incidents", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "description", "type": "string", "nullable": true, "metadata": {}}, {"name": "email", "type": "string", "nullable": true, "metadata": {}}, {"name": "html_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "invitation_sent", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "job_title", "type": "string", "nullable": true, "metadata": {}}, {"name": "name", "type": "string", "nullable": true, "metadata": {}}, {"name": "notification_rules", "type": {"containsNull": true, "elementType": {"fields": [{"name": "html_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "self", "type": "string", "nullable": true, "metadata": {}}, {"name": "summary", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "role", "type": "string", "nullable": true, "metadata": {}}, {"name": "self", "type": "string", "nullable": true, "metadata": {}}, {"name": "summary", "type": "string", "nullable": true, "metadata": {}}, {"name": "teams", "type": {"containsNull": true, "elementType": {"fields": [{"name": "html_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "id", "type": "string", "nullable": true, "metadata": {}}, {"name": "self", "type": "string", "nullable": true, "metadata": {}}, {"name": "summary", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "time_zone", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}, {"name": "loaded_at", "type": "timestamp", "nullable": false, "metadata": {}}], "type": "struct"}