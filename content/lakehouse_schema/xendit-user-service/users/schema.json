{"fields": [{"name": "__v", "type": "integer", "nullable": true, "metadata": {}}, {"name": "_id", "type": {"fields": [{"name": "oid", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "account_notifications", "type": {"containsNull": true, "elementType": {"fields": [{"name": "business_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "notify", "type": "string", "nullable": true, "metadata": {}}, {"name": "remind_me", "type": "string", "nullable": true, "metadata": {}}, {"name": "status", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "type": "array"}, "nullable": true, "metadata": {}}, {"name": "created", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "email", "type": "string", "nullable": true, "metadata": {}}, {"name": "email_verification_status", "type": "string", "nullable": true, "metadata": {}}, {"name": "entity", "type": "string", "nullable": true, "metadata": {}}, {"name": "full_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "instant_messaging_consent", "type": {"fields": [{"name": "accepted", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "timestamp", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "is_locked", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "is_phone_number_verified", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "mfa_authentication_secret", "type": {"fields": [{"name": "ascii", "type": "string", "nullable": true, "metadata": {}}, {"name": "base32", "type": "string", "nullable": true, "metadata": {}}, {"name": "hex", "type": "string", "nullable": true, "metadata": {}}, {"name": "otpauth_url", "type": "string", "nullable": true, "metadata": {}}, {"name": "type", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "password", "type": "string", "nullable": true, "metadata": {}}, {"name": "password_expiry_date", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "password_history", "type": {"containsNull": true, "elementType": "string", "type": "array"}, "nullable": true, "metadata": {}}, {"name": "phone_number", "type": "string", "nullable": true, "metadata": {}}, {"name": "pin", "type": "string", "nullable": true, "metadata": {}}, {"name": "preferred_language", "type": "string", "nullable": true, "metadata": {}}, {"name": "registration_source", "type": "string", "nullable": true, "metadata": {}}, {"name": "status", "type": "string", "nullable": true, "metadata": {}}, {"name": "timezone", "type": "string", "nullable": true, "metadata": {}}, {"name": "updated", "type": "timestamp", "nullable": true, "metadata": {}}], "type": "struct"}