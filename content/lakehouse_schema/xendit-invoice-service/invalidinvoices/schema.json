{"fields": [{"name": "__v", "type": "integer", "nullable": true, "metadata": {}}, {"name": "_id", "type": {"fields": [{"name": "oid", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}, "nullable": true, "metadata": {}}, {"name": "amount", "type": "string", "nullable": true, "metadata": {}}, {"name": "batch_payment_link_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "created", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "customer_email", "type": "string", "nullable": true, "metadata": {}}, {"name": "customer_mobile_number", "type": "string", "nullable": true, "metadata": {}}, {"name": "customer_name", "type": "string", "nullable": true, "metadata": {}}, {"name": "description", "type": "string", "nullable": true, "metadata": {}}, {"name": "invoice_external_id", "type": "string", "nullable": true, "metadata": {}}, {"name": "is_deleted", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "is_processed", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "is_validated", "type": "boolean", "nullable": true, "metadata": {}}, {"name": "updated", "type": "timestamp", "nullable": true, "metadata": {}}, {"name": "user_id", "type": "string", "nullable": true, "metadata": {}}], "type": "struct"}