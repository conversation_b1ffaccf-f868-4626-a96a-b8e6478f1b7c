COMPREHENSIVE .EXPLORE.LKML SYNCHRONIZATION REPORT
============================================================

Source files: 231
DBR files: 232
Missing in DBR: 0
Extra in DBR: 1
Identical: 161
Different: 70

EXTRA IN DBR:
  - revenue/myr_revenue.explore.lkml

FILES WITH DIFFERENCES:

account_onboarding/web_account_onboarding_flow.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 include: "/account_onboarding/*.view.lkml"
 

billing/billing.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/account_ownership_changes/business_account_ownership.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/account_ownership_changes/business_account_ownership.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "**/*.view.lkml"
 include: "./_tests/*.lkml"

business_intelligence/bi_metrics/databricks/databricks.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/business_intelligence/bi_metrics/databricks/databricks.explore"
+include: "//central-models-dbr/business_intelligence/bi_metrics/databricks/databricks.explore"
 include: "/hr/employees/ag_data_operating_model_roles.view"
 include: "/hr/employees/ag_persons.view"
 include: "/hr/employees/ag_teams.view"

business_intelligence/bi_metrics/github/github.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/business_intelligence/bi_metrics/github/github.explore"
+include: "//central-models-dbr/business_intelligence/bi_metrics/github/github.explore"
 include: "/hr/employees/ag_data_operating_model_roles.view"
 include: "/hr/employees/ag_persons.view"
 include: "/hr/employees/ag_teams.view"

businesses/businesses.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -3,7 +3,7 @@
 #@TODO:Migration include tests into this file
 #@TODO:Migration rename explores to use base table names and re-reference content
 
-include: "//central-models/businesses/businesses.explore"
+include: "//central-models-dbr/businesses/businesses.explore"
 include: "/dashboard/im_brand_consolidation.view"
 include: "/salesforce/ndt_salesforce_account.view"
 include: "/finance/billing_rates.view"
@@ -11,7 +11,7 @@
 include: "/marketing/business_id_marketing_source.view.lkml"
 
 explore: +businesses {
-  description: "Businesses list from Admin Dashboard and its details (xendit presto)"
+  description: "Businesses list from Admin Dashboard and its details (xendit databricks)"
   join: billing_rates {
     type: left_outer
     sql_on: ${businesses.business_id} = ${billing_rates.business_id} ;;

businesses/product_usage/product_attach_rate.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/businesses/product_usage/product_attach_rate.explore"
+include: "//central-models-dbr/businesses/product_usage/product_attach_rate.explore"
 include: "/finance/billing_rates.view.lkml"
 
 explore: +product_attach_rate {

checkout/checkout_flow_explore.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "/checkout/*.view.lkml"
 include: "/transactions/invoices/*.view.lkml"

checkout/dashboard_invoice_creation.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 include: "/checkout/*.view.lkml"
 

customer_object/customer_object.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
 include: "./*.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 
 explore: customer_object {
   group_label: "Customer Object"

dashboard/events/dashboard_events.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/users.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 include: "/snowplow_event_context/*"
 
 include: "./*.view.lkml"

dashboard/events/page_views.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/users.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 include: "/snowplow_event_context/*"
 
 include: "./*.view.lkml"

dashboard/user_activity/user_activity_last_action_logs.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/users.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 
 include: "./*.view.lkml"
 

dashboard/user_activity/user_activity_logs.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/users.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 
 include: "./*.view.lkml"
 

data_engineering/data_engineering.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -38,7 +38,7 @@
   group_label: "Engineering / Internal"
   label: "Buddy Pipeline Executions"
   from:  pipeline_executions
-  description: "Pipeline execution information from BuddyCI, e.g. build duration, status, and project information"
+  description: "Pipeline execution information from Buddy, e.g. build duration, status, and project information"
 }
 
 

data_product/data_products.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
 include: "/data_product/**/*.view"
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/businesses/businesses.view"
 include: "data_products_aggregate_awareness.lkml"
 include: "/growth/*.view.lkml"
 

databricks/engineering/engineering.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,8 +1,8 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/activation/ag_api_key_credential.view"
-include: "//central-models/transactions/transaction_backbone.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/activation/ag_api_key_credential.view"
+include: "//central-models-dbr/transactions/transaction_backbone.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 include: "/businesses/ag_business_settings.view"
 include: "./datadog/*.view.lkml"
 include: "./**/*.view.lkml"

disbursement/tw/topup_withdrawal.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
-include: "//central-models/businesses/businesses.view.lkml"
-include: "//central-models/businesses/users.view.lkml"
-include: "//central-models/businesses/product_usage/combined*.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/product_usage/combined*.view.lkml"
 include: "/zendesk/ndt_tickets.view.lkml"
 
 explore: topup_withdrawal {

dragonpay/dragonpay.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
 include: "*.view.lkml"
-include: "//central-models/0_assets/fx_rates.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
 
 explore: dragonpay_transactions {
   hidden: yes

engineering/engineering.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,8 +1,8 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/activation/ag_api_key_credential.view"
-include: "//central-models/transactions/transaction_backbone.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/activation/ag_api_key_credential.view"
+include: "//central-models-dbr/transactions/transaction_backbone.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "/businesses/ag_business_settings.view"
 

escrow/rdl_consolidated.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
 include: "./*.view.lkml"
 
 explore: rdl_consolidated {

events/events.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
-include: "//central-models/events/events.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
-include: "//central-models/businesses/business_marketing_attribution.view.lkml"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/business_facts_by_product_type.view"
-include: "//central-models/businesses/accounts_company_relationships.view"
-include: "//central-models/businesses/activation_stage_on_internal_name_level.view"
-include: "//central-models/businesses/xp_businesses.view"
+include: "//central-models-dbr/events/events.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view.lkml"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/business_facts_by_product_type.view"
+include: "//central-models-dbr/businesses/accounts_company_relationships.view"
+include: "//central-models-dbr/businesses/activation_stage_on_internal_name_level.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
 
... (2 more lines)

events/pageviews_sessions_visitors.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,19 +1,19 @@
-include: "//central-models/0_assets/fx_rates.view.lkml"
-include: "//central-models/events/ag_views/event_backbone.view"
-include: "//central-models/events/intermediate_events_views/pageviews.view"
-include: "//central-models/events/intermediate_events_views/sessions.view"
-include: "//central-models/events/ag_views/structured_events.view"
-include: "//central-models/events/intermediate_events_views/visitors.view"
+include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
+include: "//central-models-dbr/events/ag_views/event_backbone.view"
+# include: "//central-models-dbr/events/intermediate_events_views/pageviews.view"
+# include: "//central-models-dbr/events/intermediate_events_views/sessions.view"
+include: "//central-models-dbr/events/ag_views/structured_events.view"
+# include: "//central-models-dbr/events/intermediate_events_views/visitors.view"
 
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/users.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/xp_businesses.view"
... (14 more lines)

events/traffic_events_ndt.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "/events/traffic_events.view.lkml"
-include: "//central-models/events/events.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/events/events.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 
 explore: traffic_events_ndt {
   from:  xendit_site_traffic_ndt

finance/finance.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/businesses.view.lkml"
-include: "//central-models/businesses/business_facts.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/business_facts.view.lkml"
 include: "/finance/*.view.lkml"
 include: "investor_reporting.dashboard"
 

finops/floats.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "/finops/*.view.lkml"
 include: "/finops/**/*.view.lkml"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/businesses/*.view.lkml"
 
 explore: float_configuration_per_bank {
   from: ag_banks

finops/ph_liquidity_reporting/ph_liquidity_reporting.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 include: "./*.view.lkml"
 
 

growth/billing_check.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,10 +1,10 @@
 include: "/growth/*.view.lkml"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 explore: billing_check {
   # hidden: yes
   label: "Billing Statement (GA Checking)"
-  description: "Billing Statement for checking Nett Revenue (xendit presto)"
+  description: "Billing Statement for checking Nett Revenue (xendit databricks)"
   from: billing_check
   }

growth/cp_team_dashboard.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -8,7 +8,7 @@
 explore: cp_team_dashboard {
   hidden: yes
   group_label: "CP Team Dashboard"
-  description: "CP Team Dashboard (xendit presto)"
+  description: "CP Team Dashboard"
   from: cp_team_dashboard
 }
 

growth/cps_dashboard_detail_products.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -9,13 +9,13 @@
 # explore: growth_team_dashboard {
 #   hidden: yes
 #   group_label: "Growth Team Dashboard"
-#   description: "Growth Team Dashboard (xendit presto)"
+#   description: "Growth Team Dashboard (xendit databricks)"
 #   from: growth_team_dashboard
 # }
 explore: cps_dashboard_detail_products {
   hidden: no
   group_label: "Growth Team Dashboard"
-  description: "CPS Dashboard Detail All Products (xendit presto)"
+  description: "CPS Dashboard Detail All Products (xendit databricks)"
   from: cps_dashboard_detail_products
   label: "CPS Dashboard Detail All Products"
 }

growth/growth_team_dashboard.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -9,13 +9,13 @@
 explore: growth_team_dashboard {
   hidden: yes
   group_label: "Growth Team Dashboard"
-  description: "Growth Team Dashboard (xendit presto)"
+  description: "Growth Team Dashboard (xendit databricks)"
   from: growth_team_dashboard
 }
 # explore: cps_dashboard_detail_products {
 #   hidden: no
 #   group_label: "Growth Team Dashboard"
-#   description: "CPS Dashboard Detail All Products (xendit presto)"
+#   description: "CPS Dashboard Detail All Products (xendit databricks)"
 #   # from: cps_dashboard_detail_products
 #   label: "CPS Dashboard Detail All Products"
 # }

growth/marketing_team_dashboard.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -8,7 +8,7 @@
 explore: marketing_team_dashboard {
   hidden: yes
   group_label: "Marketing Team Dashboard"
-  description: "Marketing Team Dashboard (xendit presto)"
+  description: "Marketing Team Dashboard"
   from: marketing_team_dashboard
 }
 

growth/profit_calculation.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 include: "/salesforce/*.view.lkml"
 include: "/growth/*.view.lkml"
 include: "/*/*.view.lkml"
 
 explore: profit_calculation {
   label: "Profit Calculation (NV)"
-  description: "Profit Calculation (NV) (xendit presto)"
+  description: "Profit Calculation (NV) (xendit databricks)"
 
   join : ndt_salesforce_account {
     from: ndt_salesforce_account

growth/revenue_forecast_by_product.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
 include: "/growth/*.view.lkml"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 explore: revenue_forecast_by_product {
   # hidden: yes
   group_label: "Revenue Forecast by Product"
-  description: "Revenue Forecast by Product (xendit presto)"
+  description: "Revenue Forecast by Product (xendit databricks)"
   from: revenue_forecast_by_product
 
   join: businesses {

growth/sales_performance.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/businesses/businesses.view"
 include: "/growth/*.view"
 include: "/salesforce/*.view"
 
 explore: sales_performance {
   #hidden: yes
   group_label: "Sales Performance"
-  description: "Sales Performance (xendit presto)"
+  description: "Sales Performance (xendit databricks)"
   from: sales_performance
 
   join: salesforce_users {
@@ -19,7 +19,7 @@
 explore: sales_performance_snapshot {
   #hidden: yes
   group_label: "Sales Performance"
... (119 more lines)

growth/sales_target.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -6,20 +6,20 @@
 explore: sales_target {
   hidden: yes
   label: "PDT Target"
-  description: "Sales Target (xendit presto)"
+  description: "Sales Target (xendit databricks)"
   from:  sales_target
 }
 
 explore: country_target {
   #hidden: yes
   label: "Country Target"
-  description: "Country P&L Revenue Target (xendit presto)"
+  description: "Country P&L Revenue Target (xendit databricks)"
   from:  country_target
 }
 
 explore: team_target {
... (6 more lines)

growth/sales_team_dashboard.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -8,20 +8,20 @@
 explore: sales_team_dashboard {
   hidden: yes
   group_label: "Sales Team Dashboard"
-  description: "PG Sales Team Dashboard (xendit presto)"
+  description: "PG Sales Team Dashboard"
   from: sales_team_dashboard
 }
 
 explore: xencapital_sales_team_dashboard {
   hidden: yes
   group_label: "Sales Team Dashboard"
-  description: "XenCapital Sales Team Dashboard (xendit presto)"
+  description: "XenCapital Sales Team Dashboard"
   from: xencapital_sales_team_dashboard
 }
 
 explore: solution_team_dashboard {
... (6 more lines)

growth/solution_dashboard.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -5,8 +5,8 @@
 # include: "/businesses/*/*.view.lkml"
 # include: "ag_salesforce_tpv.view.lkml"
 include: "/growth/sales_attribution.view"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 include: "/growth/solution_attribution.view"
 include: "/growth/sf_monthly_transactions.view"
 include: "/marketing/business_id_marketing_source.view.lkml"
@@ -19,7 +19,7 @@
 explore: solution_dashboard {
   hidden: yes
   group_label: "Solution Dashboard"
-  description: "Solution Dashboard (xendit presto)"
+  description: "Solution Dashboard (xendit databricks)"
   from: ndt_solution_dashboard
... (29 more lines)

growth/xencapital_sales.explore.lkml (DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -7,6 +7,6 @@
 explore: xencapital_opportunity {
   #hidden: yes
   group_label: "XenCapital Sales"
-  description: "XenCapital Opportunity (xendit presto)"
+  description: "XenCapital Opportunity"
   from: ag_xencapital_opportunity
 }

interfaces/mobile_app/mobile_app.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
 include: "/interfaces/mobile_app/*.view.lkml"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/businesses/*.view.lkml"
 
 
 explore: mobile_app_login_sessions {

interfaces/mobile_app/mobile_mau.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,7 +1,7 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/businesses/users.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/businesses/users.view"
 
 include: "/interfaces/mobile_app/mobile_mau.view.lkml"
 

marketing/marketing.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -7,8 +7,8 @@
 include: "campaign_revenue.view.lkml"
 include: "ag_marketing_monthly_report.view.lkml"
 include: "/growth/sales_attribution.view"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 include: "marketing.dashboard"
 
 explore: campaign_cost {

merchant_ops/merchant_ops_time_tracker.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/activation/ndt_kyc_verification.view"
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/businesses/activation/ndt_kyc_verification.view"
+include: "//central-models-dbr/businesses/businesses.view"
 
 include: "/merchant_ops/account_activations/**/*.view.lkml"
 include: "/merchant_ops/channel_activations/**/*.view.lkml"

mobile_events/mobile_events.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,7 +1,7 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/businesses/users.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/businesses/users.view"
 
 include: "/mobile_events/*.view.lkml"
 include: "/mobile_events/*/*.view.lkml"

orders/orders.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "/orders/*.view.lkml"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 explore: orders {
   from: orders

revenue/fpna_weekly_net_revenue.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,7 +1,7 @@
-# include: "//central-models/revenue/revenue.explore"
-# include: "//central-models/revenue/_revenue_dqc_explores/revenue_test_explores.explore"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+# include: "//central-models-dbr/revenue/revenue.explore"
+# include: "//central-models-dbr/revenue/_revenue_dqc_explores/revenue_test_explores.explore"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 include: "/revenue/fpna_weekly_net_revenue.view.lkml"
 include: "/revenue/fpna_weekly_net_revenue_split.view.lkml"
 include: "/marketing/business_id_marketing_source.view.lkml"
@@ -11,7 +11,7 @@
   label: "FP&A Weekly Net Revenue"
   group_label: "FP&A Weekly Net Revenue"
   view_label: "FP&A Weekly Net Revenue"
-  description: "FP&A Weekly Net Revenue (xendit presto)"
+  description: "FP&A Weekly Net Revenue (xendit databricks)"
... (12 more lines)

revenue/revenue.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
-include: "//central-models/revenue/revenue.explore"
-include: "//central-models/revenue/_revenue_dqc_explores/revenue_test_explores.explore"
+include: "//central-models-dbr/revenue/revenue.explore"
+include: "//central-models-dbr/revenue/_revenue_dqc_explores/revenue_test_explores.explore"
 include: "/marketing/business_id_marketing_source.view.lkml"
 include: "/growth/sales_attribution.view.lkml"
 include: "/salesforce/ndt_salesforce_account.view.lkml"
 
 explore: +revenue {
-  description: "Revenue (xendit presto)"
+  description: "Revenue (xendit databricks)"
 ################################################
 # Aggregate tables {
 ################################################
@@ -53,7 +53,7 @@
 }
 
... (6 more lines)

sales/hubspot.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "**/*.view.lkml"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 explore: deals  {
   group_label: "Sales / Marketing / CS"

salesforce/salesforce.explore.lkml (INCLUDES | EXPLORES | DESCRIPTIONS | JOINS | SQL):
--------------------------------------------------
--- source
+++ dbr
@@ -17,8 +17,8 @@
 include: "ndt_salesforce_account.view"
 include: "ndt_hygiene_flag.view"
 include: "/growth/sales_attribution.view"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 include: "/growth/solution_attribution.view"
 include: "/growth/sf_monthly_transactions.view"
 include: "/marketing/business_id_marketing_source.view.lkml"
@@ -33,7 +33,7 @@
   group_label: "Salesforce"
   from: ndt_salesforce_account #ag_transform_salesforce_account
   view_label: "Salesforce Record"
-  description: "List of salesforce records (leads + accounts)(xendit presto)"
+  description: "List of salesforce records (leads + accounts)(xendit databricks)"
   hidden: yes
... (252 more lines)

stores/stores.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/*.view.lkml"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/*.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "/stores/**/*.view"
 include: "/orders/*.view"

topups_withdrawals/integrated_topups.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/*.view.lkml"
 include: "/topups_withdrawals/*.view.lkml"
 
 explore: integrated_topups {

topups_withdrawals/integrated_withdrawals.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/*.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/*.view"
 include: "/topups_withdrawals/*.view.lkml"
 
 explore: integrated_withdrawals {

transactions/cards/cards.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,11 +1,11 @@
 include: "/transactions/cards/*.view.lkml"
 include: "/businesses/*.view.lkml"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/businesses/*.view.lkml"
 include: "/payment_settings/*view.lkml"
-include: "//central-models/revenue/ag_views/*.view.lkml"
-include: "//central-models/revenue/revenue.view.lkml"
-include: "//central-models/revenue/daily_revenue.view.lkml"
-include: "//central-models/0_assets/fx_rates.view.lkml"
+include: "//central-models-dbr/revenue/ag_views/*.view.lkml"
+include: "//central-models-dbr/revenue/revenue.view.lkml"
+include: "//central-models-dbr/revenue/daily_revenue.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
 
 explore: credit_cards {
   from:  ag_integrated_card

transactions/cashpay/retail_outlet/retail_outlets.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,16 +1,17 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/xp_businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/transactions/transaction_backbone.view"
-include: "/transactions/cashpay/retail_outlet/ag_integrated_retail_outlet.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/transactions/transaction_backbone.view"
+
+include: "/transactions/cashpay/retail_outlet/integrated_retail_outlet.view"
 
 
... (8 more lines)

transactions/digipay/digipay.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,8 +1,8 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/xp_businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "./ewallet/ag_integrated_ewallet.view"
 include: "./ewallet/ag_ewallet_captures.view"

transactions/disbursements/api_disbursement/live_api_disbursement.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
 
 include: "/transactions/disbursements/api_disbursement/live_api_disbursement.view.lkml"
 include: "/transactions/disbursements/routing/live_disb_routing_service_idr.view.lkml"

transactions/disbursements/batch_disbursement/integrated_batch_disbursement.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/*.view.lkml"
 include: "/transactions/disbursements/batch_disbursement/integrated_batch_disbursement.view.lkml"
 include: "/transactions/disbursements/batch_disbursement/bd_nv_facts.view.lkml"
 

transactions/disbursements/disbursements.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/*.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/*.view"
 include: "/transactions/disbursements/*.view.lkml"
 
 explore: disbursements {

transactions/invoices/invoice.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,7 +1,7 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/xp_businesses.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
 
 include: "/transactions/invoices/*.view.lkml"
 include: "/checkout/*.view.lkml"

transactions/recurring/recurring.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,8 +1,8 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/businesses/xp_businesses.view"
-include: "//central-models/businesses/business_facts.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
 
 include: "ag_recurring_integrated.view"
 

transactions/recurring/revenue.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 
 include: "billed_event.view"
 include: "recurring_plans.view"

transactions/remittances/remittances.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 include: "/transactions/remittances/*"
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/businesses/*.view.lkml"
 
 # STRUCTURAL PARAMETERS
 

transactions/report/report.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,13 +1,13 @@
-include: "//central-models/businesses/businesses.view"
-include: "//central-models/businesses/business_facts.view"
-include: "//central-models/businesses/xp_master_sub_account_mapping.view"
-include: "//central-models/businesses/xp_businesses.view"
-include: "//central-models/businesses/business_marketing_attribution.view"
-include: "//central-models/businesses/activation_stage_on_internal_name_level.view"
+include: "//central-models-dbr/businesses/businesses.view"
+include: "//central-models-dbr/businesses/business_facts.view"
+include: "//central-models-dbr/businesses/xp_master_sub_account_mapping.view"
+include: "//central-models-dbr/businesses/xp_businesses.view"
+include: "//central-models-dbr/businesses/business_marketing_attribution.view"
+include: "//central-models-dbr/businesses/activation_stage_on_internal_name_level.view"
 include: "/growth/sales_attribution.view"
 include: "/xenPlatform/*.lkml"
 include: "/events/traffic_events.view.lkml"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
... (3 more lines)

transactions/transactions.explore.lkml (INCLUDES | DESCRIPTIONS):
--------------------------------------------------
--- source
+++ dbr
@@ -1,8 +1,8 @@
 # @TODO:Migration check all product team tables are referencing the most up to date content
 
-include: "//central-models/transactions/transactions.explore"
-include: "//central-models/businesses/*.view"
-include: "//central-models/0_assets/fx_rates.view"
+include: "//central-models-dbr/transactions/transactions.explore"
+include: "//central-models-dbr/businesses/*.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
 include: "va_partition_passing_to_transaction_backbone.lkml"
 include: "**/*.view.lkml"
 include: "/checkout/*.view.lkml"
@@ -14,7 +14,7 @@
 explore: +transaction_backbone {
   group_label: "PG Transactions"
   label: "Transactions"
-  description: "PG Transactions (xendit presto)"
+  description: "PG Transactions (xendit databricks)"
... (3 more lines)

virtual_account/virtual_account.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/businesses/*.view.lkml"
+include: "//central-models-dbr/businesses/*.view.lkml"
 
 include: "/virtual_account/**/*.view.lkml"
 include: "/businesses/ag_businessinternalinformations.view.lkml"

webhook/webhook.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,4 +1,4 @@
-include: "//central-models/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
 
 include: "/webhook/*.view.lkml"
 

xenCapital/xenCapital_v2.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,6 @@
 include: "/xenCapital/metrics_and_trackers/*"
-include: "/date_scaffold.view.lkml"
+include: "//central-models-dbr/0_assets/date_scaffold.view.lkml"
+include: "//central-models-dbr/0_assets/fx_rates.view.lkml"
 
 explore: drawdown_processing_time_v2 {
   group_label: "xenCapital"

xenPlatform/xenplatform_business.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "/xenPlatform/*.lkml"
-include: "//central-models/businesses/xp_master_sub_account_mapping.view"
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/businesses/xp_master_sub_account_mapping.view"
+include: "//central-models-dbr/businesses/businesses.view"
 
 explore: xenplatform_accountholder {
   group_label: "Xenplatform"

xenShield/xenshield.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,5 +1,5 @@
-include: "//central-models/0_assets/fx_rates.view"
-include: "//central-models/businesses/businesses.view"
+include: "//central-models-dbr/0_assets/fx_rates.view"
+include: "//central-models-dbr/businesses/businesses.view"
 include: "/transactions/cards/ag_integrated_card.view"
 include: "./*.view"
 include: "./ancilia/**.view"

xensavings/xendit_app/xendit_app_events.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,6 +1,6 @@
 include: "/xensavings/xendit_app/*.view.lkml"
 include: "/mobile_events/*.view.lkml"
-include: "//central-models/events/intermediate_events_views/mobile*.view.lkml"
+include: "//central-models-dbr/events/intermediate_events_views/mobile*.view.lkml"
 
 explore: xendit_app_events {
   view_label: "Users"

zendesk/ticket.explore.lkml (INCLUDES):
--------------------------------------------------
--- source
+++ dbr
@@ -1,7 +1,7 @@
 include: "./**/*.view.lkml"
-include: "//central-models/businesses/businesses.view.lkml"
-include: "//central-models/businesses/business_facts.view.lkml"
-include: "//central-models/businesses/users.view.lkml"
+include: "//central-models-dbr/businesses/businesses.view.lkml"
+include: "//central-models-dbr/businesses/business_facts.view.lkml"
+include: "//central-models-dbr/businesses/users.view.lkml"
 include: "/growth/*.view.lkml"
 
 explore: tickets {
