include: "ag_network_userid_filter.view"
include: "ag_domain_userid_filter.view"

view: web_identifier_mapping_01 {
  derived_table: {
    sql: with tagged_domains as
      (select distinct
        t1.domain_userid
      from
        clean__kafka_snowplow.events t1 left join ${domain_userid_filter.SQL_TABLE_NAME} t2
        on t1.domain_userid = t2.domain_userid
        left join ${network_userid_filter.SQL_TABLE_NAME} t3
        on t1.network_userid = t3.network_userid
      where
        user_id IS NOT NULL
        and t2.domain_userid IS NULL
        and t3.network_userid IS NULL
        and t1.platform = 'web'
      )

      select distinct
        t1.domain_userid,
        t2.domain_sessionid,
        t2.network_userid
      from
        tagged_domains t1 left join clean__kafka_snowplow.events t2
        on t1.domain_userid = t2.domain_userid
       ;;
      # datagroup_trigger: views_updated_based_on_time
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: domain_userid {
    type: string
    sql: ${TABLE}.domain_userid ;;
  }

  dimension: domain_sessionid {
    type: string
    sql: ${TABLE}.domain_sessionid ;;
  }

  dimension: network_userid {
    type: string
    sql: ${TABLE}.network_userid ;;
  }

  set: detail {
    fields: [domain_userid, domain_sessionid, network_userid]
  }
}
