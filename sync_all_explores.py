#!/usr/bin/env python3
"""
Comprehensive synchronization script for all .explore.lkml files
Handles includes, explores, descriptions, joins, and SQL differences
"""

import os
import shutil
import re
from pathlib import Path

# Repository paths
SOURCE_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def copy_missing_files():
    """Copy files that are missing in DBR repository"""
    missing_files = [
        "growth/billing_check.explore.lkml",
        "growth/cps_dashboard_detail_products.explore.lkml"
    ]
    
    print("📋 COPYING MISSING FILES:")
    for file in missing_files:
        source_path = os.path.join(SOURCE_REPO, file)
        dbr_path = os.path.join(DBR_REPO, file)
        
        if os.path.exists(source_path):
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(dbr_path), exist_ok=True)
            shutil.copy2(source_path, dbr_path)
            print(f"  ✅ Copied: {file}")
            
            # Update references to use central-models-dbr
            update_central_models_references(dbr_path)
        else:
            print(f"  ❌ Source file not found: {file}")

def update_central_models_references(file_path):
    """Update central-models references to central-models-dbr"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace central-models with central-models-dbr
        updated_content = content.replace('//central-models/', '//central-models-dbr/')
        
        # Update descriptions from presto to databricks
        updated_content = re.sub(
            r'\(xendit presto\)',
            '(xendit databricks)',
            updated_content,
            flags=re.IGNORECASE
        )
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
            
    except Exception as e:
        print(f"  ⚠️  Error updating {file_path}: {e}")

def sync_include_only_files():
    """Sync files that only have include differences"""
    include_only_files = [
        "account_onboarding/web_account_onboarding_flow.explore.lkml",
        "billing/billing.explore.lkml",
        "business_intelligence/bi_metrics/databricks/databricks.explore.lkml",
        "business_intelligence/bi_metrics/github/github.explore.lkml",
        "businesses/product_usage/product_attach_rate.explore.lkml",
        "checkout/checkout_callback_audit.explore.lkml",
        "checkout/checkout_flow_explore.explore.lkml",
        "checkout/dashboard_invoice_creation.explore.lkml",
        "compliance/compliance.explore.lkml",
        "costs/costs.explore.lkml",
        "customer_object/customer_object.explore.lkml",
        "dashboard/events/dashboard_events.explore.lkml",
        "dashboard/events/page_views.explore.lkml",
        "dashboard/events/snowplow_context.explore.lkml",
        "dashboard/metrics/dashboard_datadog_metrics.explore.lkml",
        "dashboard/user_activity/user_activity_last_action_logs.explore.lkml",
        "dashboard/user_activity/user_activity_logs.explore.lkml",
        "dev_platform/t3_audit.explore.lkml",
        "digipay/ewallet_merchant_credentials.explore.lkml",
        "disbursement/tw/topup_withdrawal.explore.lkml",
        "dragonpay/dragonpay.explore.lkml",
        "escrow/metrics/metrics_escrow.explore.lkml",
        "escrow/metrics/metrics_rdl.explore.lkml",
        "escrow/rdl_consolidated.explore.lkml",
        "events/events.explore.lkml",
        "events/pageviews_sessions_visitors.explore.lkml",
        "events/traffic_events_ndt.explore.lkml",
        "finance/finance.explore.lkml",
        "finops/floats.explore.lkml",
        "finops/loan_repayment/manual_transaction.explore.lkml",
        "finops/metrics/finops_efficiency.explore.lkml",
        "finops/ph_liquidity_reporting/ph_liquidity_reporting.explore.lkml",
        "foundation_team/xendit_internal_notifications_metrics.explore.lkml",
        "fraud_ops/transaction_monitoring.explore.lkml",
        "fraud_ops/va_invoice_fraud_monitoring.explore.lkml",
        "fx/metrics/metrics_fx.explore.lkml",
        "hr/hr.explore.lkml",
        "humanity/shifts.explore.lkml",
        "instamoney_performance/RMI_performance.explore.lkml",
        "instamoney_performance/domestic_remittance_payouts_metrics.explore.lkml",
        "instamoney_performance/regional_remittance_payouts_metrics.explore.lkml",
        "interfaces/mobile_app/mobile_app.explore.lkml",
        "interfaces/mobile_app/mobile_mau.explore.lkml",
        "lr_working_folder/business_multigrain_metrics.explore.lkml",
        "lr_working_folder/transaction_backbone_v2_comparison.explore.lkml",
        "marketing/channel_flow.explore.lkml",
        "marketing/marketing.explore.lkml",
        "marketing/marketing_website.explore.lkml",
        "merchant_ops/compliance_audit.explore.lkml",
        "merchant_ops/merchant_ops_time_tracker.explore.lkml",
        "mobile_events/mobile_events.explore.lkml",
        "moneygram_connector/moneygram_connector_load.explore.lkml",
        "moneygram_connector/moneygram_connector_remittance.explore.lkml",
        "nex_business/nex_business.explore.lkml",
        "notifications/notification_service.explore.lkml",
        "orders/orders.explore.lkml",
        "payment_settings/payment_settings.explore.lkml",
        "processing_time/merchantops_time_tracker.explore.lkml",
        "recon/metrics/avg_process_time_metrics.explore.lkml",
        "recon/metrics/daily_metrics.explore.lkml",
        "recon/metrics/monthly_metrics.explore.lkml",
        "recon/metrics/monthly_reconciled_all_txns_metrics.explore.lkml",
        "recon/metrics/monthly_reconciled_metrics.explore.lkml",
        "reconart/transaction.explore.lkml",
        "regional/regional_business_transaction.explore.lkml",
        "remittance_money_in_service/remittance_money_in_service.explore.lkml",
        "remittance_money_out/batch_remittance.explore.lkml",
        "remittance_money_out/e2e_rmo/payout_api_logs.explore.lkml",
        "remittance_money_out/e2e_rmo/rmo_api_gw_performance.explore.lkml",
        "remittance_money_out/e2e_rmo/rmo_compliance.explore.lkml",
        "remittance_money_out/e2e_rmo/rmo_risk_assessment.explore.lkml",
        "remittance_money_out/e2e_rmo/rmo_webhook.explore.lkml",
        "remittance_money_out/uptime_explore/e2e_remittance_money_out_raw.explore.lkml",
        "remittance_service/remittances.explore.lkml",
        "remittance_service/remittances_mongo.explore.lkml",
        "reports/xp_balances.explore.lkml",
        "revenue/fpna_weekly_net_revenue.explore.lkml",
        "stores/stores.explore.lkml",
        "topups_withdrawals/integrated_topups.explore.lkml",
        "topups_withdrawals/integrated_withdrawals.explore.lkml",
        "transactions/PaymentsAPI/paymentMethodView.explore.lkml",
        "transactions/PaymentsAPI/payments_api.explore.lkml",
        "transactions/cards/addons/cards_addons.explore.lkml",
        "transactions/cards/metrics/cards_metrics.explore.lkml",
        "transactions/cards/others/cards_others.explore.lkml",
        "transactions/cashpay/retail_outlet/retail_outlets.explore.lkml",
        "transactions/cashpay/virtual_account/virtual_accounts.explore.lkml",
        "transactions/direct_debit/direct_debit.explore.lkml",
        "transactions/disbursements/api_disbursement/live_api_disbursement.explore.lkml",
        "transactions/disbursements/api_disbursement/live_api_disbursement_direct.explore.lkml",
        "transactions/disbursements/batch_disbursement/integrated_batch_disbursement.explore.lkml",
        "transactions/disbursements/channels/ag_payouts_channels.explore.lkml",
        "transactions/disbursements/core/explores/legacy_connector.explore.lkml",
        "transactions/disbursements/core/explores/ph_instapay_disbursement.explore.lkml",
        "transactions/disbursements/disbursements.explore.lkml",
        "transactions/disbursements/partners/ag_payouts_partners.explore.lkml",
        "transactions/disbursements/partners/legacy_bri_disbursement_connector.explore.lkml",
        "transactions/disbursements/sentry/payouts_tw_sentry.explore.lkml",
        "transactions/escrow/customer_balances.explore.lkml",
        "transactions/escrow/customers.explore.lkml",
        "transactions/escrow/escrow_external_virtual_account_payments.explore.lkml",
        "transactions/escrow/escrow_external_virtual_accounts.explore.lkml",
        "transactions/escrow/escrow_withdrawals.explore.lkml",
        "transactions/escrow/internal_transfers.explore.lkml",
        "transactions/escrow/lender_loan_disbursements.explore.lkml",
        "transactions/escrow/loan_disbursements.explore.lkml",
        "transactions/escrow/repayments.explore.lkml",
        "transactions/escrow_rdl_rdf/rdl_disbursements.explore.lkml",
        "transactions/escrow_rdl_rdf/rdl_payments.explore.lkml",
        "transactions/invoices/invoice.explore.lkml",
        "transactions/recurring/recurring.explore.lkml",
        "transactions/recurring/revenue.explore.lkml",
        "transactions/remittances/remittances.explore.lkml",
        "transactions/third_party_integrations/jurnal/jurnal.explore.lkml",
        "transactions/transaction_monthly_product_usage_lr.explore.lkml",
        "virtual_account/audit_recon/sameday_recon.explore.lkml",
        "virtual_account/audit_recon/t_plus_one_recon.explore.lkml",
        "virtual_account/audit_recon/va_audit_recon_metrics.explore.lkml",
        "virtual_account/audit_recon/va_audit_with_transaction.explore.lkml",
        "virtual_account/va_range_usage.explore.lkml",
        "virtual_account/virtual_account.explore.lkml",
        "webhook/webhook.explore.lkml",
        "xenCapital/early_settlement/early_settlement.explore.lkml",
        "xenCapital/xenCapital_v2.explore.lkml",
        "xenPlatform/xenplatform_business.explore.lkml",
        "xenShield/ancilia/ancilia-dragonpay.explore.lkml",
        "xenconnex/xenconnex.explore.lkml",
        "xenqa/test_stats.explore.lkml",
        "xensavings/xendit_app/xendit_app_events.explore.lkml",
        "zendesk/ticket.explore.lkml",
        "zendesk_ops_guide/ticket_audit.explore.lkml"
    ]
    
    print(f"\n📋 SYNCING INCLUDE-ONLY FILES ({len(include_only_files)} files):")
    for file in include_only_files:
        dbr_path = os.path.join(DBR_REPO, file)
        if os.path.exists(dbr_path):
            update_central_models_references(dbr_path)
            print(f"  ✅ Updated: {file}")
        else:
            print(f"  ❌ File not found: {file}")

def sync_description_only_files():
    """Sync files that only have description differences"""
    description_only_files = [
        "data_engineering/data_engineering.explore.lkml",
        "growth/cp_team_dashboard.explore.lkml",
        "growth/marketing_team_dashboard.explore.lkml",
        "growth/sales_team_dashboard.explore.lkml",
        "growth/xencapital_sales.explore.lkml"
    ]
    
    print(f"\n📋 SYNCING DESCRIPTION-ONLY FILES ({len(description_only_files)} files):")
    for file in description_only_files:
        dbr_path = os.path.join(DBR_REPO, file)
        if os.path.exists(dbr_path):
            update_central_models_references(dbr_path)
            print(f"  ✅ Updated: {file}")
        else:
            print(f"  ❌ File not found: {file}")

def sync_complex_files():
    """Handle files with complex differences that need manual copying"""
    complex_files = [
        "businesses/businesses.explore.lkml",
        "data_product/data_products.explore.lkml",
        "engineering/jira_issues.explore.lkml",
        "growth/growth_team_dashboard.explore.lkml",
        "growth/profit_calculation.explore.lkml",
        "growth/revenue_forecast_by_product.explore.lkml",
        "growth/sales_performance.explore.lkml",
        "growth/sales_target.explore.lkml",
        "growth/solution_dashboard.explore.lkml",
        "instamoney_performance/remittance_payouts_metrics.explore.lkml",
        "marketing/campaign_leads.explore.lkml",
        "remittance_money_out/e2e_rmo/e2e_rmo.explore.lkml",
        "remittance_money_out/remittance_money_out.explore.lkml",
        "revenue/revenue.explore.lkml",
        "sales/hubspot.explore.lkml",
        "transactions/digipay/digipay.explore.lkml",
        "transactions/transactions.explore.lkml",
        "transactions/cards/cards.explore.lkml",
        "transactions/report/report.explore.lkml",
        "virtual_account/virtual_account_payment.explore.lkml",
        "xenShield/xenshield.explore.lkml",
        "xenCapital/xenCapital.explore.lkml",
        "xensavings/xensavings.explore.lkml",
        "xensavings/xendit_app/xendit_app.explore.lkml"
    ]
    
    print(f"\n📋 SYNCING COMPLEX FILES ({len(complex_files)} files):")
    for file in complex_files:
        source_path = os.path.join(SOURCE_REPO, file)
        dbr_path = os.path.join(DBR_REPO, file)
        
        if os.path.exists(source_path):
            # Create backup
            backup_path = dbr_path + ".backup"
            if os.path.exists(dbr_path):
                shutil.copy2(dbr_path, backup_path)
            
            # Copy from source
            shutil.copy2(source_path, dbr_path)
            
            # Update references
            update_central_models_references(dbr_path)
            print(f"  ✅ Synced: {file}")
        else:
            print(f"  ❌ Source file not found: {file}")

def main():
    print("🚀 COMPREHENSIVE .EXPLORE.LKML SYNCHRONIZATION")
    print("=" * 60)
    
    # Step 1: Copy missing files
    copy_missing_files()
    
    # Step 2: Sync include-only files
    sync_include_only_files()
    
    # Step 3: Sync description-only files
    sync_description_only_files()
    
    # Step 4: Sync complex files
    sync_complex_files()
    
    print(f"\n✅ SYNCHRONIZATION COMPLETE!")
    print("📝 Note: Backup files (.backup) created for complex files")
    print("🧪 Ready for testing!")

if __name__ == "__main__":
    main()
