view: ag_ticket_time_metrics {
  sql_table_name: transform__experience_customer_success_cs_zendesk.ticket_time_metrics ;;
  drill_fields: [ticket.id]

  dimension: ticket_id{
    primary_key: yes
    type: number
    sql: ${TABLE}.ticket_id;;
  }

  dimension: requester_id{
    type: number
    sql: ${TABLE}.assignee_id;;
  }

  dimension: assignee_id{
    type: number
    sql: ${TABLE}.assignee_id;;
  }

  dimension_group: original_start_time_utc {
    label: "Timestamp Excluding Working Hours"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.original_start_time_utc ;;
  }

  dimension_group: adjusted_start_time_utc {
    label: "Timestamp within Working Hours"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.adjusted_start_time_utc ;;
  }

  dimension_group: end_time_utc {
    label: "L2 First Response"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.end_time_utc ;;
  }

  dimension_group: adjusted_end_time_utc {
    label: "Adjusted First Respond Timestamp"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.adjusted_end_time_utc ;;
  }

  dimension: l2_frt_minutes {
    label: "L2 FRT"
    type: number
    sql: ${TABLE}.l2_frt_minutes;;
    value_format: "0\" mins\""
  }

  dimension_group: email_frt_start_time {
    label: "Email FRT Start Time"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.start_time_gmt7 ;;
  }

  dimension_group: email_frt_end_time {
    label: "Email FRT End Time"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.end_time_gmt7 ;;
  }

  dimension: email_frt {
    label: "Email FRT"
    type: number
    sql: ${TABLE}.email_reply_time_in_minutes;;
    value_format: "0\" mins\""
  }
}
