view: start_stop_pause_events {
  derived_table: {
    datagroup_trigger: views_updated_based_on_time
    sql:
      SELECT
        *,
        (
          CASE
            WHEN se_label = 'account-activation-start' THEN (
              DATEDIFF(
                second,
                aae1.dvce_created_tstamp,
                (
                  SELECT
                    MIN(aae2.dvce_created_tstamp)
                  FROM
                    ${start_stop_pause_events_raw.SQL_TABLE_NAME} AS aae2
                  WHERE
                    aae2.start_stop_pause_idx = aae1.start_stop_pause_idx + 1
                    AND (
                      aae2.se_label = 'account-activation-stop'
                      OR aae2.se_label = 'account-activation-complete'
                    )
                    AND aae2.domain_sessionidx = aae1.domain_sessionidx
                    AND aae2.domain_sessionid = aae1.domain_sessionid
                    AND aae2.se_property = aae1.se_property
                )
              )
            )
            ELSE NULL
            END
        ) AS active_time,
        (
          SELECT
            MIN(aae2.dvce_created_tstamp)
          FROM
            ${start_stop_pause_events_raw.SQL_TABLE_NAME} AS aae2
          WHERE
            aae2.start_stop_pause_idx = aae1.start_stop_pause_idx + 1
            AND (
              aae2.se_label = 'account-activation-stop'
              OR aae2.se_label = 'account-activation-complete'
            )
            AND aae2.domain_sessionidx = aae1.domain_sessionidx
            AND aae2.domain_sessionid = aae1.domain_sessionid
            AND aae2.se_property = aae1.se_property
        ) AS event_end_tstamp
      FROM
        ${start_stop_pause_events_raw.SQL_TABLE_NAME} AS aae1
    ;;
  }

  dimension: app_id {
    type: string
    label: "App ID"
    description: "A name representing the Xendit app this data was obtained from."
  }

  dimension: event_id {
    type: string
    label: "Event ID"
    description: "A unique ID assigned to this event."
  }

  dimension_group: dvce_created_tstamp {
    group_label: "Timestamp"
    label: "Start"
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    description: "A timestamp indicating when this event was created on the user's device."
  }

  dimension_group: event_end_tstamp {
    group_label: "Timestamp"
    label: "End"
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    description: "A field indicating the timestamp which closes this event."
  }

  dimension: user_id {
    type: string
    label: "User ID"
    description: "The unique ID of the user who generated this event."
  }

  dimension: start_stop_pause_idx {
    type:  number
    label: "Start Stop Pause Activity Index"
    description: "An index number representing the order of the start, stop, or pause event relative to other similar events in a user's single session."
  }

  dimension: domain_sessionid {
    type:  string
    label: "User Session ID"
    description: "The unique ID of the user's session when this event was generated."
  }

  dimension: domain_sessionidx {
    type: number
    label: "User Session Index"
    description: "The unique ID of the user's session when this event was generated."
  }

  dimension: se_category {
    label: "Event Category"
    type: string
    suggestions: ["accountActivation"]
    description: "The category of the event, as documented in the event tracking master document. For account activations, this is always \"accountActivation\"."
    link: {
      label: "Event tracking master document"
      url: "https://docs.google.com/spreadsheets/d/1q_OsqsB0SDNW-ohK6pXFOlLaZrRGdOnfKbbNf5IFk7Q/edit#gid=*********"
      icon_url: "https://mpng.subpng.com/********/esv/kisspng-google-docs-google-sheets-g-suite-google-drive-com-how-to-use-google-sheets-to-keep-every-part-of-you-5ba3ed73c092c2.3029433015374698117888.jpg"
    }
  }

  dimension: se_label {
    label: "Event Code"
    type: string
    description: "The code assigned to an event type, as documented in the event tracking master document."
    suggestions: ["account-activation-start", "account-activation-stop", "account-activation-complete"]
    link: {
      label: "Event code documentation"
      url: "https://xendit.atlassian.net/wiki/spaces/OA/pages/**********/Merchant+Operations+Time+Tracker#Events"
      icon_url: "https://pbs.twimg.com/profile_images/1022908662392619008/5_z16TbH_400x400.jpg"
    }
  }

  dimension: se_property {
    label: "Account ID"
    type: string
    description: "The unique ID of the account being processed."
  }

  dimension: se_value {
    label: "Account Resubmission Count"
    type: number
    description: "The count of resubmission."
  }

  dimension: active_time {
    label: "Active Time (Seconds)"
    type: number
    description: "The amount of time a user was active for a \"start\" event (in seconds)."
    link: {
      label: "Active time calculation methodology"
      url: "https://xendit.atlassian.net/wiki/spaces/OA/pages/**********/Merchant+Operations+Time+Tracker#Total-active-time"
      icon_url: "https://pbs.twimg.com/profile_images/1022908662392619008/5_z16TbH_400x400.jpg"
    }
  }

  dimension: page_urlhost {
    label: "Page Hostname"
    type: string
    description: "The hostname of the page that the user was on when this event was created."
  }

  measure: total_active_time {
    label: "Total Active Time (Seconds)"
    type: sum
    sql: ${active_time};;
    description: "The total amount of time a user was active for when processing account activations."
    link: {
      label: "Active time calculation methodology"
      url: "https://xendit.atlassian.net/wiki/spaces/OA/pages/**********/Merchant+Operations+Time+Tracker#Total-active-time"
      icon_url: "https://pbs.twimg.com/profile_images/1022908662392619008/5_z16TbH_400x400.jpg"
    }
  }
}
