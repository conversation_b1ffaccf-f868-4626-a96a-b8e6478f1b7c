#!/usr/bin/env python3

import os
import re
from collections import defaultdict

def extract_dimensions_measures_sets(content):
    """Extract all dimensions, measures, and sets from LookML content"""
    elements = defaultdict(list)
    
    # Pattern to match dimension, measure, or set definitions
    pattern = r'^\s*(dimension|measure|set|dimension_group|filter|parameter)(?:_group)?\s*:\s*(\w+)\s*\{'
    
    lines = content.split('\n')
    current_element = None
    current_name = None
    current_type = None
    brace_count = 0
    element_lines = []
    
    for i, line in enumerate(lines):
        match = re.match(pattern, line)
        
        if match:
            # If we were tracking a previous element, save it
            if current_element is not None:
                elements[current_type].append({
                    'name': current_name,
                    'definition': '\n'.join(element_lines),
                    'start_line': current_element,
                    'end_line': i - 1
                })
            
            # Start tracking new element
            current_type = match.group(1)
            current_name = match.group(2)
            current_element = i
            element_lines = [line]
            brace_count = line.count('{') - line.count('}')
        
        elif current_element is not None:
            element_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            
            # If braces are balanced, we've reached the end of the element
            if brace_count == 0:
                elements[current_type].append({
                    'name': current_name,
                    'definition': '\n'.join(element_lines),
                    'start_line': current_element,
                    'end_line': i
                })
                current_element = None
                current_name = None
                current_type = None
                element_lines = []
    
    # Handle case where file ends while tracking an element
    if current_element is not None:
        elements[current_type].append({
            'name': current_name,
            'definition': '\n'.join(element_lines),
            'start_line': current_element,
            'end_line': len(lines) - 1
        })
    
    return elements

def remove_duplicates_from_file(file_path):
    """Remove duplicate dimensions/measures/sets from a file, keeping only the first occurrence"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        elements = extract_dimensions_measures_sets(content)
        
        # Find duplicates and collect lines to remove
        lines_to_remove = set()
        duplicates_removed = []
        
        for element_type, element_list in elements.items():
            name_counts = defaultdict(list)
            
            for element in element_list:
                name_counts[element['name']].append(element)
            
            for name, occurrences in name_counts.items():
                if len(occurrences) > 1:
                    # Keep the first occurrence, remove the rest
                    for i in range(1, len(occurrences)):
                        dup = occurrences[i]
                        # Mark lines for removal (including empty lines around the element)
                        start_line = dup['start_line']
                        end_line = dup['end_line']
                        
                        # Include empty lines before the element if they exist
                        while start_line > 0 and lines[start_line - 1].strip() == '':
                            start_line -= 1
                        
                        # Include empty lines after the element if they exist
                        while end_line < len(lines) - 1 and lines[end_line + 1].strip() == '':
                            end_line += 1
                        
                        for line_num in range(start_line, end_line + 1):
                            lines_to_remove.add(line_num)
                        
                        duplicates_removed.append(f"{element_type}: {name}")
        
        if lines_to_remove:
            # Create new content without the duplicate lines
            new_lines = [line for i, line in enumerate(lines) if i not in lines_to_remove]
            new_content = '\n'.join(new_lines)
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ Cleaned {os.path.basename(file_path)}")
            print(f"   Removed duplicates: {', '.join(duplicates_removed)}")
            return len(duplicates_removed)
        else:
            print(f"✅ No duplicates found in {os.path.basename(file_path)}")
            return 0
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return 0

def main():
    # Files that had duplicates
    files_with_duplicates = [
        "growth/solution_attribution.view.lkml",
        "transactions/disbursements/integrated_ag_disbursements.view.lkml",
        "zendesk/ndt_tickets.view.lkml"
    ]
    
    total_removed = 0
    
    print("Removing duplicate dimensions, measures, and sets...")
    print("=" * 60)
    
    for file_path in files_with_duplicates:
        if os.path.exists(file_path):
            removed = remove_duplicates_from_file(file_path)
            total_removed += removed
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 60)
    print(f"SUMMARY: Removed {total_removed} duplicate elements")

if __name__ == "__main__":
    main()
