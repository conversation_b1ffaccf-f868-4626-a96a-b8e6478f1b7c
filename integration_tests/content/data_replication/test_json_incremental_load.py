from xenpy.data_replication.etl.json_etl import JsonETL
import xendit_de_registra_manager.models as rm
from pyspark.sql import SparkSession
from xendit_de_registra_manager.models import PartitionKey, BackfillFilter


spark = SparkSession.builder.enableHiveSupport().getOrCreate()
source_name = "json_staging_test"  # bucket name
s3_bucket = "xendit-de-test"
s3_prefix = "json_test/"  # prefix for searching files in s3 (could also be source_dqc/deeper/paths)
db_name = "json_test"  # db name in HMS
tbl_name = "profile"  # table name in HMS

source_dqc_configs = rm.SourceDQCGeneralConfigs(
    include_last_x_days=100,
    dqc_key="start_date",
)

manager = JsonETL(
    spark_session=spark,
    source_name=source_name,
    s3_bucket=s3_bucket,
    s3_prefix=s3_prefix,
    db_name=db_name,
    tbl_name=tbl_name,
    path_glob_filter="*.json",
    source_dqc_configs=source_dqc_configs,
    etl_mode="INCREMENTAL",
    multiline=True,
    upsert_key=["id"],
    backfill_filters=[BackfillFilter(backfill_id="date")],
    z_order_by=["name"],
    s3_files_backfill_strategy=rm.S3FilesBackfillStrategy(
        partition_key=rm.FilterS3FilesByPartitionKey(
            datetime_format="%Y-%m-%d",
            s3_prefix="json_test/",
            backfill_days=1000,
            folder_partition_key="date",
        ),
    ),
    run_quality_check=False
)

manager.load()

manager1 = JsonETL(
    spark_session=spark,
    source_name=source_name,
    s3_bucket=s3_bucket,
    s3_prefix=s3_prefix,
    db_name=db_name,
    tbl_name=tbl_name,
    path_glob_filter="*.json",
    source_dqc_configs=source_dqc_configs,
    etl_mode="INCREMENTAL",
    multiline=True,
    upsert_key=["id"],
    backfill_filters=[BackfillFilter(backfill_id="date")],
    z_order_by=["name"],
    s3_files_backfill_strategy=rm.S3FilesBackfillStrategy(
        partition_key=rm.FilterS3FilesByPartitionKey(
            datetime_format="%Y-%m-%d",
            s3_prefix="json_test/",
            backfill_days=1000,
            folder_partition_key="date",
        ),
    ),
    run_quality_check=False
)

manager1.load()
