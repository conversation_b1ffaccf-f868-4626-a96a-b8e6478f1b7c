include: "ag_salesforce_leads.view"
include: "ag_salesforce_task.view"
include: "ag_campaign_influence.view"
include: "ag_campaigns.view"
include: "ag_campaigns_member.view"
include: "ag_tactic_campaigns.view"
include: "ag_salesforce_users.view"
include: "ag_transform_salesforce_account.view"
include: "ag_transform_salesforce_activity.view"
include: "ag_transform_salesforce_opportunity.view"
include: "ag_transform_salesforce_opportunity_ramp.view"
include: "ag_salesforce_opportunity_snapshot.view"
include: "ag_salesforce_accounts.view"
include: "ag_parent_opportunities.view"
include: "ag_salesforce_product_status.view"
include: "ag_salesforce_products.view"
include: "ndt_salesforce_account.view"
include: "ndt_hygiene_flag.view"
include: "/growth/sales_attribution.view"
include: "//central-models-dbr/businesses/businesses.view"
include: "//central-models-dbr/businesses/business_facts.view"
include: "/growth/solution_attribution.view"
include: "/growth/sf_monthly_transactions.view"
include: "/marketing/business_id_marketing_source.view.lkml"
include: "/marketing/account_id_marketing_source.view.lkml"
include: "/growth/solution_product_attribution.view"
include: "/growth/solution_revenue_attribution_temp.view"
include: "/growth/solution_attribution_trx.view"
include: "/growth/solution_attribution_estrev.view"
include: "ag_salesforce_contacts.view"

explore: salesforce {
  group_label: "Salesforce"
  from: ndt_salesforce_account #ag_transform_salesforce_account
  view_label: "Salesforce Record"
  description: "List of salesforce records (leads + accounts)(xendit databricks)"
  hidden: yes

  join: salesforce_opportunity {
    from:  ag_transform_salesforce_opportunity
    sql_on:  ${salesforce.account_id} = ${salesforce_opportunity.account_id} ;;
    relationship: one_to_many
    type: left_outer
  }

  join: salesforce_contacts {
    from:  ag_salesforce_contacts
    sql_on:  ${salesforce.contact_id} = ${salesforce_contacts.id} ;;
    relationship: one_to_many
    type: left_outer
  }

  join: businesses {
    sql_on: ${businesses.business_id} = ${salesforce.business_id} ;;
    relationship: many_to_one
    type: left_outer
  }

  join: business_facts {
    from: business_facts
    sql_on: ${businesses.business_id} = ${business_facts.business_id};;
    relationship: one_to_one
    type :  left_outer
  }

  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${salesforce.business_id} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: one_to_one
  }
}

explore: ag_salesforce_contacts {
  group_label: "Salesforce"
  from: ag_salesforce_contacts
  view_label: "Salesforce Contacts"
  description: "List of salesforce contacts (xendit databricks)"
  hidden: no
}


explore: salesforce_activity {
  group_label: "Salesforce"
  from: ag_transform_salesforce_activity
  description: "List of salesforce activity (xendit databricks)"
  view_label: "Salesforce Activity"
}



explore: salesforce_accounts {
  group_label: "Salesforce"
  from: ag_salesforce_accounts
  description: "List of salesforce accounts (xendit databricks)"
  view_label: "Salesforce Accounts"

  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${salesforce_accounts.id} = ${sales_attribution.sf_id};;
    type: left_outer
    relationship: many_to_one
  }
}



explore: salesforce_opportunity_snapshot {
  group_label: "Salesforce"
  from: salesforce_opportunity_snapshot
  view_label: "Salesforce Opportunity Snapshot"
  description: "A snapshot of salesforce opportunities details on weekly, monthly, and at current state (xendit databricks)"

  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${salesforce_opportunity_snapshot.accountid} = ${sales_attribution.sf_id};;
    type: left_outer
    relationship: one_to_one
  }

  join: ag_transform_salesforce_account {
    # from: ag_transform_salesforce_account
    view_label: "Salesforce Account"
    sql_on: ${salesforce_opportunity_snapshot.accountid} = ${ag_transform_salesforce_account.account_id};;
    type: left_outer
    relationship: one_to_one
  }

  # join: ndt_salesforce_account {
  #   # from: ag_transform_salesforce_account
  #   view_label: "Salesforce Records"
  #   sql_on: ${salesforce_opportunity_snapshot.accountid} = ${ndt_salesforce_account.account_id};;
  #   type: left_outer
  #   relationship: one_to_one
  # }

  join: solution_attribution {
    view_label: "Solution Attribution"
    sql_on: ${salesforce_opportunity_snapshot.parent_opp_id} = ${solution_attribution.parent_opportunity_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: businesses {
    sql_on: ${businesses.business_id} = ${salesforce_opportunity_snapshot.business_id__c} ;;
    relationship: many_to_one
    type: left_outer
  }
}



explore: salesforce_opportunity_ramp {
  group_label: "Salesforce"
  from: ag_transform_salesforce_opportunity
  view_label: "Salesforce Opportunity Ramp"
  description: "A breakdown of salesforce opportunity ramp values (xendit databricks)"

  join: sales_attribution {
    # from: sales_attribution
    view_label: "Sales Attribution Live"
    sql_on: ${salesforce_opportunity_ramp.account_id} = ${sales_attribution.sf_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: solution_attribution {
    # from: solution_attribution
    view_label: "Solution Attribution"
    sql_on: ${salesforce_opportunity_ramp.parent_opp_id} = ${solution_attribution.parent_opportunity_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: ndt_hygiene_flag {
    from: ndt_hygiene_flag
    view_label: "Hygiene Flag"
    sql_on: ${salesforce_opportunity_ramp.child_opp_id} = ${ndt_hygiene_flag.child_opp_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: ag_transform_salesforce_account {
    # from: ag_transform_salesforce_account
    view_label: "Salesforce Account"
    sql_on: ${salesforce_opportunity_ramp.account_id} = ${ag_transform_salesforce_account.account_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: salesforce_parent_opportunities{
    from: ag_parent_opportunities
    view_label: "Parent Opportunity"
    sql_on: ${salesforce_opportunity_ramp.parent_opp_id} = ${salesforce_parent_opportunities.id};;
    type: left_outer
    relationship: one_to_one
  }
}



explore: salesforce_product_status {
  group_label: "Salesforce"
  from: ag_salesforce_product_status
  view_label: "Salesforce Product Status"
  description: "salesforce product status (xendit databricks)"
  # description: "A snapshot of salesforce opportunities details on weekly, monthly, and at current state"

  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${salesforce_product_status.businessid} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: businesses {
    sql_on: ${salesforce_product_status.businessid} = ${businesses.business_id} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: ag_salesforce_products {
    sql_on: ${salesforce_product_status.sfdcid} = ${ag_salesforce_products.product_code_batch_data__c} ;;
    type: left_outer
    relationship: many_to_one
  }
}




explore: salesforce_parent_opportunities {
  group_label: "Salesforce"
  from: ag_parent_opportunities
  view_label: "SFDC Parent Opportunity"
  description: "List of salesforce opportunities (xendit databricks)"
  # description: "A snapshot of salesforce opportunities details on weekly, monthly, and at current state"

  join: sales_attribution {
    view_label: "Sales Attribution"
    sql_on: ${salesforce_parent_opportunities.accountid} = ${sales_attribution.sf_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: solution_attribution {
    view_label: "Solution Attribution"
    sql_on: ${salesforce_parent_opportunities.id} = ${solution_attribution.parent_opportunity_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: salesforce_opportunity_snapshot {
    sql_on:  ${salesforce_parent_opportunities.id} = ${salesforce_opportunity_snapshot.parent_opp_id} ;;
    # sql_where: ${salesforce_opportunity_snapshot.period}="Latest" ;;
    relationship: one_to_many
    type: left_outer
  }

  # join: solution_product_attribution{
  #   from: solution_product_attribution
  #   view_label: "Solution Product Attribution"
  #   sql_on: ${solution_attribution.business_id} = ${solution_product_attribution.business_id};;
  #   type: left_outer
  #   relationship: many_to_one
  # }

  join: solution_attribution_trx {
    # from: solution_attribution
    view_label: "Salesforce Transactions (Business Lv Attr)"
    sql_on: ${solution_attribution.business_id} = ${solution_attribution_trx.business_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: businesses {
    sql_on: ${salesforce_parent_opportunities.business_id_formula__c} = ${businesses.business_id} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: business_facts {
    from: business_facts
    sql_on: ${businesses.business_id} = ${business_facts.business_id};;
    relationship: one_to_one
    type :  left_outer
  }

  join: campaign_influence {
    from: ag_campaign_influence
    view_label: "Campaign Influence"
    sql_on: ${salesforce_parent_opportunities.id} = ${campaign_influence.opportunityid} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: campaigns {
    from: ag_campaigns
    view_label: "Campaign"
    sql_on: ${campaign_influence.campaignid} = ${campaigns.id} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Opportunity Owner"
    sql_on: ${salesforce_parent_opportunities.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: ag_transform_salesforce_account {
    # from: ag_transform_salesforce_account
    view_label: "Account"
    sql_on: ${salesforce_parent_opportunities.accountid} = ${ag_transform_salesforce_account.account_id};;
    type: left_outer
    relationship: many_to_one
  }

  join: business_id_marketing_source {
    from: business_id_marketing_source
    view_label: "Marketing Source by Business ID"
    sql_on: ${salesforce_parent_opportunities.business_id_formula__c} = ${business_id_marketing_source.business_id__c};;
    relationship: one_to_one
    type :  left_outer
  }

  join: account_id_marketing_source {
    from: account_id_marketing_source
    view_label: "Marketing Source by Account ID"
    sql_on: ${salesforce_parent_opportunities.accountid} = ${account_id_marketing_source.accountid};;
    relationship: one_to_one
    type :  left_outer
  }
}




explore: salesforce_leads {
  group_label: "Salesforce"
  from: ag_salesforce_leads
  description: "List of salesforce leads (xendit databricks)"
  view_label: "SFDC Lead"
  # description: "A snapshot of salesforce opportunities details on weekly, monthly, and at current state"

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Salesforce Users"
    sql_on: ${salesforce_leads.bd_owner__c} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }


  join: salesforce_accounts {
    view_label: "Account"
    from: ag_salesforce_accounts
    sql_on: ${salesforce_accounts.id} = ${salesforce_leads.convertedaccountid} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: salesforce_parent_opportunities{
    from: ag_parent_opportunities
    view_label: "Parent Opportunity"
    sql_on: ${salesforce_parent_opportunities.id} = ${salesforce_leads.convertedopportunityid};;
    type: left_outer
    relationship: one_to_one
  }

  join: sales_attribution {
    view_label: "Sales Attribution"
    sql_on: ${salesforce_leads.convertedaccountid} = ${sales_attribution.sf_id};;
    type: left_outer
    relationship: one_to_one
  }

  join: businesses {
    sql_on: ${businesses.business_id} =  ${sales_attribution.business_id};;
    relationship: many_to_one
    type: left_outer
  }

  join: business_facts {
    from: business_facts
    sql_on: ${businesses.business_id} = ${business_facts.business_id};;
    relationship: one_to_one
    type :  left_outer
  }


}



explore: ag_campaigns_member {
  group_label: "Salesforce"
  from: ag_campaigns_member
  label: "SFDC Campaign Member"
  view_label: "SFDC Campaign Member"
  description: "List of salesforce campaign members (xendit databricks)"
  # description: "campaign table with master, program and tactic level of hierarchy, contains estimated/actual revenue and costs"

  join: campaigns {
    from: ag_campaigns
    view_label: "SFDC Campaign"
    sql_on: ${ag_campaigns_member.campaignid} = ${campaigns.id} ;;
    type: left_outer
    relationship: many_to_one
  }

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Campaign Owner"
    sql_on: ${campaigns.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: salesforce_leads {
    from: ag_salesforce_leads
    view_label: "SFDC Leads"
    sql_on: ${ag_campaigns_member.leadid} = ${salesforce_leads.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: salesforce_records {
    from: ndt_salesforce_account
    view_label: "SFDC Records"
    sql_on: ${ag_campaigns_member.leadid} = ${salesforce_records.lead_id} ;;
    type: left_outer
    relationship: one_to_one
  }
}



explore: campaign_influence {
  group_label: "Salesforce"
  from: ag_campaign_influence
  view_label: "SFDC Campaign Influence"
  description: "List of salesforce campaign influence (xendit databricks)"
  # description: "A snapshot of salesforce opportunities details on weekly, monthly, and at current state"

  join: campaigns {
    from: ag_campaigns
    view_label: "Campaign"
    sql_on: ${campaign_influence.campaignid} = ${campaigns.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: salesforce_parent_opportunities {
    from: ag_parent_opportunities
    view_label: "Parent Opportunity"
    sql_on: ${campaign_influence.opportunityid} = ${salesforce_parent_opportunities.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Campaign Owner"
    sql_on: ${campaigns.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }

  join: opportunity_owner {
    from: ag_salesforce_users
    view_label: "Opportunity Owner"
    sql_on: ${salesforce_parent_opportunities.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }
}

explore: task {
  group_label: "Salesforce"
  from: ag_salesforce_task
  label: "SFDC Task"
  description: "List of salesforce task (xendit databricks)"

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Task Owner"
    sql_on: ${task.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }
}

explore: campaign {
  group_label: "Salesforce"
  from: ag_campaigns
  label: "SFDC Campaign"
  description: "List of salesforce campaigns (xendit databricks)"

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Campaign Owner"
    sql_on: ${campaign.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }
}



explore: tactic_campaign {
  group_label: "Salesforce"
  from: ag_tactic_campaigns
  description: "List of salesforce tactic campaigns (xendit databricks)"
  label: "SFDC Tactic Campaign"

  join: salesforce_users {
    from: ag_salesforce_users
    view_label: "Campaign Owner"
    sql_on: ${tactic_campaign.ownerid} = ${salesforce_users.id} ;;
    type: left_outer
    relationship: one_to_one
  }
}
