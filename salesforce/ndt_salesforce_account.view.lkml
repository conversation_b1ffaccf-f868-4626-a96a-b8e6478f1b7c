view: ndt_salesforce_account {
  sql_table_name: transform__growth.salesforce_lead_account ;;
  # derived_table: {
  #   datagroup_trigger: views_updated_based_on_time
  #   sql:
  #       -- https://xenditco.cloud.looker.com/sql/qk7dbnrszrcgjt?toggle=dat,sql
  #       with
  #         salesforce_id as (
  #           select
  #             t1.id as lead_id
  #             , t2.id as acc_id
  #             , coalesce(t1.brand__c,t1.company) as brand
  #             , t1.createddate
  #             , from_unixtime(t1.createddate/1000, 'UTC') as created
  #             , from_unixtime(t1.createddate/1000, 'UTC') as created_lead
  #             , coalesce(t2.business_id__c,t1.business_id__c) as bid
  #             , t1.cp_manager__c
  #             , t1.ownerid
  #             , t1.email as lead_email
  #             , t1.xendit_account_email__c as lead_xendit_email
  #             , t2.xendit_account_email__c as acc_xendit_email
  #             , t1.business_id__c as lead_bid
  #             , t2.business_id__c as acc_bid
  #             , t3.id as contact_id
  #             , t3.email as contact_email
  #             , concat(t1.lastname, ' | ', t1.brand__c) as record_name
  #             , lower(t1.referral_code__c) as referral_code
  #             , t4.cp_segmentation__c as cp_segmentation
  #             , t4.referral_type__c as referral_type
  #           from clean__salesforce.leads t1
  #           left join clean__salesforce.accounts t2 on t1.convertedaccountid=t2.id
  #           left join clean__salesforce.contacts t3 on t1.convertedcontactid=t3.id
  #           left join clean__salesforce.referral_code t4 on lower(t1.referral_code__c)=lower(t4.code__c)
  #           where t1.isconverted = false

  #           union all

  #           select
  #             t1.id as lead_id
  #             , t2.id as acc_id
  #             , t2.brand__c as brand
  #             , case
  #                 when t1.id is null then t2.createddate
  #                 else t1.createddate
  #               end as createddate
  #             , case
  #                 when t1.id is null then from_unixtime(t2.createddate/1000, 'UTC')
  #                 else from_unixtime(t1.createddate/1000, 'UTC')
  #               end as created
  #             , coalesce(from_unixtime(t3.createddate/1000, 'UTC'),from_unixtime(t2.createddate/1000, 'UTC')) as created_lead
  #             , coalesce(t2.business_id__c,t1.business_id__c) as bid
  #             , coalesce(t2.cp_manager__c,t1.cp_manager__c) as cp_manager__c
  #             , coalesce(t2.ownerid,t1.ownerid) as ownerid
  #             , t1.email as lead_email
  #             , t1.xendit_account_email__c as lead_xendit_email
  #             , t2.xendit_account_email__c as acc_xendit_email
  #             , t1.business_id__c as lead_bid
  #             , t2.business_id__c as acc_bid
  #             , t4.id as contact_id
  #             , t4.email as contact_email
  #             , t2.name as record_name
  #             , t2.referral_code__c as referral_code
  #             , t5.cp_segmentation__c as cp_segmentation
  #             , t5.referral_type__c as referral_type
  #           from clean__salesforce.accounts t2
  #           LEFT JOIN (
  #             SELECT t1.acc_id as acc_id, t1.createddate, min(t2.id) as lead_id
  #             FROM (
  #               SELECT convertedaccountid as acc_id, min(createddate) as createddate
  #               FROM clean__salesforce.leads
  #               GROUP BY 1) t1
  #             LEFT JOIN clean__salesforce.leads t2 ON t1.acc_id=t2.convertedaccountid AND t1.createddate=t2.createddate
  #             GROUP BY 1,2
  #           ) t3 ON t3.acc_id=t2.id
  #           left join clean__salesforce.leads t1 on t1.id=t3.lead_id
  #           left join clean__salesforce.contacts t4 on t1.convertedcontactid=t4.id
  #           left join clean__salesforce.referral_code t5 on lower(t2.referral_code__c)=lower(t5.code__c)
  #         )

  #         ,compile as (
  #           select distinct
  #               coalesce(t1.acc_id,t1.lead_id) as record_id
  #               , cast(t1.created as timestamp) as created
  #               , cast(t1.created_lead as timestamp) as created_lead
  #               , coalesce(cast(t1.created_lead as timestamp),cast(t1.created as timestamp)) as record_created
  #               , case
  #                   when t1.lead_id is not null and t1.acc_id is null then 'Lead'
  #                   else 'Account'
  #                 end as record_type
  #               , t1.record_name as record_name
  #               , t1.brand as record_brand
  #               , t1.bid
  #               , t1.acc_id as account_id_1
  #               , t1.lead_id
  #               , concat(t4a.firstname,' ',t4a.lastname) as cpm_name
  #               , t4a.email as cpm_email
  #               , concat(t4b.firstname,' ',t4b.lastname) as ownername
  #               , t4b.email as owneremail
  #               , t4b.division as ownerdivision
  #               , t3.*
  #               , t5.fifth_transaction
  #               , t6.latest_transaction
  #               , t1.lead_email
  #               , t1.lead_xendit_email
  #               , t1.acc_xendit_email
  #               , t1.lead_bid
  #               , t1.acc_bid
  #               , t1.contact_id
  #               , t1.contact_email
  #               , t1.referral_code as sf_referral_id
  #               , t1.cp_segmentation
  #               , t1.referral_type as sf_referral_type
  #           from salesforce_id t1
  #           -- left join clean__salesforce.leads t2 on t1.bid = t2.business_id__c
  #           left join transform__growth.salesforce_account t3 on t1.acc_id = t3.account_id
  #           left join clean__salesforce.users t4a on t1.cp_manager__c = t4a.id
  #           left join clean__salesforce.users t4b on t1.ownerid = t4b.id
  #           left join transform__business_intelligence.dim_businesses t5 on t1.bid=t5.business_id
  #           left join transform__business_intelligence.dim_business_facts t6 on t1.bid=t6.business_id
  #           left join clean__salesforce.referral_code t7 ON lower(t1.referral_code)=lower(t7.code__c)
  #           -- left join account_multiple_lead t8 on t1.record_id=t2.record_id
  #         )

  #         ,final as (
  #           SELECT
  #             t1.*
  #             ,t2.is_cp_merchant_v2 as is_claimed_by_id_cp
  #             ,t2.sf_cp_partner_category as sf_cp_partner_category
  #             ,t2.sf_cp_partner_name as sf_cp_partner_name
  #             ,t2.sf_cp_partner_id as sf_cp_partner_id
  #             ,t2.sf_cp_partner_subcategory as sf_cp_partner_subcategory
  #             ,case when t2.sf_rollup_owner_email in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>') then t2.sf_rollup_owner_name_v2 else t2.sf_rollup_cp_manager_name
  #             END as cp_owner_name
  #             ,case when t2.sf_rollup_owner_email in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>') then t2.sf_rollup_owner_email_v2 else t2.sf_rollup_cp_manager_email
  #             END as cp_owner_email
  #           FROM compile t1
  #           left join transform__growth.sales_attribution t2 on t1.account_id_1=t2.sf_id
  #           WHERE t1.record_type='Account'

  #           UNION ALL

  #           SELECT
  #             t1.*
  #             ,case
  #                 when t1.cpm_email in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>')
  #                   or t1.owneremail in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>')
  #                 then true
  #                 else false
  #             end as is_claimed_by_id_cp
  #             ,case
  #                 when t4.master_bid__c is not null then t5.sf_cp_partner_category
  #                 else (
  #                       CASE
  #                           WHEN t1.cp_segmentation='VC & Incubator' THEN 'VC Partners'
  #                           WHEN t1.cp_segmentation='Direct Selling' AND t1.sf_referral_type='Alliance' THEN 'Referral Others'
  #                           ELSE (
  #                                 CASE
  #                                     WHEN t1.sf_referral_type='Referral' THEN 'Referral Partners'
  #                                     WHEN t1.sf_referral_type='Alliance' THEN 'Alliance Partners'
  #                                     ELSE 'TPI'
  #                                     END
  #                           )
  #                           END
  #                 )
  #             end as sf_cp_partner_category
  #             ,case
  #                 when t4.master_bid__c is not null then t5.sf_cp_partner_name
  #                 else lower(t1.sf_referral_id)
  #             end as sf_cp_partner_name
  #             ,t5.sf_cp_partner_id
  #             ,CASE
  #                 WHEN t1.owneremail = '<EMAIL>' OR t1.owneremail IS NULL THEN 'Self Serve'
  #                 WHEN t1.owneremail NOT IN ('<EMAIL>','<EMAIL>') AND t1.ownerdivision IN ('ID AM - Dedicated','ID Crypto','ID Pool AM','Pool AM','PH Account Management (Enterprise)','PH AM','PH IB + Pool AM') THEN 'AM Managed Merchants'
  #                   ELSE (
  #                     CASE WHEN t1.sf_referral_id IS NOT NULL THEN 'Referral Indirect' ELSE NULL END
  #                   )
  #             END AS sf_cp_partner_subcategory
  #             ,case when t1.owneremail in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>') then t1.ownername else t1.cpm_name
  #             END as cp_owner_name
  #             ,case when t1.owneremail in ('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>') then t1.owneremail else t1.cpm_email
  #             END as cp_owner_email
  #           FROM compile t1
  #           left join clean__salesforce.leads t4 on t1.lead_id=t4.id
  #           left join transform__growth.sales_attribution t5 on t4.master_bid__c=t5.business_id
  #           LEFT JOIN clean__google_sheets_growth.segment t6 ON lower(t4.referral_code__c) = lower(t6.referral_partner_id)
  #           WHERE t1.record_type='Lead'
  #         )

  #         select distinct
  #           t2.*
  #           ,case
  #               when sf_cp_partner_name IS NULL THEN FALSE
  #               ELSE TRUE
  #           END AS is_cp_partner
  #           ,case
  #               when sf_cp_partner_category NOT IN ('Referral Others','VC Partners','Referral Partners','Alliance Partners') THEN NULL
  #               else cp_segmentation
  #           end as sf_referral_cp_segmentation
  #           from (select record_id, min(lead_id) as lead_id from final group by 1) t1
  #           left join final t2 on t1.record_id=t2.record_id and t1.lead_id=t2.lead_id;;
  # }

  dimension: contact_id {
    type: string
    sql: ${TABLE}.contact_id ;;
  }

  dimension: contact_email {
    type: string
    sql: ${TABLE}.contact_email ;;
  }

  dimension: lead_email {
    type: string
    sql: ${TABLE}.lead_email ;;
  }

  dimension: lead_xendit_email {
    type: string
    label: "Xendit Acc Email (on Lead)"
    sql: ${TABLE}.lead_xendit_email ;;
  }

  dimension: acc_xendit_email {
    type: string
    label: "Xendit Acc Email (on Account)"
    sql: ${TABLE}.acc_xendit_email ;;
  }

  dimension: lead_bid {
    type: string
    label: "Business ID (on Lead)"
    sql: ${TABLE}.lead_bid ;;
  }

  dimension: acc_bid {
    type: string
    label: "Business ID (on Account)"
    sql: ${TABLE}.acc_bid ;;
  }

  dimension: business_id {
    primary_key: yes
    description: "Business ID"
    type: number
    hidden: yes
    sql: ${TABLE}.bid ;;
  }

  dimension: record_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.record_id ;;
  }

  dimension: record_name {
    type: string
    sql: ${TABLE}.record_name ;;
  }

  dimension: record_brand {
    type: string
    sql: ${TABLE}.record_brand ;;
  }

  dimension: account_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.account_id_1 ;;
  }

  dimension: lead_id {
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.lead_id ;;
  }

  dimension: is_cp_merchant {
    type: yesno
    label: "Is Claimed by ID CP"
    sql: ${TABLE}.is_claimed_by_id_cp ;;
  }

  # dimension: sf_cp_partner_category {
  #   type: string
  #   sql: ${TABLE}.sf_cp_partner_category ;;
  # }

  dimension: sf_referral_id {
    type: string
    sql: ${TABLE}.sf_referral_id ;;
  }

  dimension: sf_referral_id_cp_segmentation {
    type: string
    sql: ${TABLE}.sf_referral_cp_segmentation ;;
  }

  dimension: sf_referral_name {
    label: "Sf CP Partner Name"
    type: string
    sql: ${TABLE}.sf_cp_partner_name ;;

  }

  dimension: sf_referral_parent_id {
    type: string
    case_sensitive: yes
    sql: ${TABLE}.sf_cp_partner_id ;;
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
  }

  # dimension: sf_cp_partner_direct_indirect_subcategory {
  #   type: string
  #   sql: ${TABLE}.sf_cp_partner_category ;;
  #   # sql: ${TABLE}.sf_cp_partner_direct_indirect_subcategory ;;
  # }

  # dimension: sf_cp_partner_direct_indirect_category {
  #   type: string
  #   sql: ${TABLE}.sf_cp_partner_category ;;
  # }

  dimension: cp_owner_name {
    type: string
    sql: ${TABLE}.cp_owner_name ;;
  }

  dimension: cp_owner_email {
    type: string
    sql: ${TABLE}.cp_owner_email ;;
  }

  #---

  dimension: account_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension: record_type {
    type: string
    sql: ${TABLE}.record_type ;;
  }

  dimension: account_type {
    type: string
    sql: ${TABLE}.account_type ;;
  }

  dimension: account_status {
    type: string
    sql: ${TABLE}.account_status ;;
  }

  measure: addressable_IY_revenue {
    type: sum
    sql: ${TABLE}.addressable_iy_revenue__c ;;
  }

  measure: addressable_IY_revenue_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_iy_revenue__c_usd ;;
  }

  measure: addressable_IY_TPV {
    type: sum
    sql: ${TABLE}.addressable_iy_tpv__c ;;
  }

  measure: addressable_IY_TPV_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_iy_tpv__c_usd ;;
  }

  measure: addressable_revenue_annual {
    type: sum
    sql: ${TABLE}.addressable_merchant_revenue_annual__c ;;
  }

  measure: addressable_revenue_annual_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_revenue_annual__c_usd ;;
  }

  measure: addressable_revenue_monthly {
    type: sum
    sql: ${TABLE}.addressable_merchant_revenue_monthly__c ;;
  }

  measure: addressable_revenue_monthly_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_revenue_monthly__c_usd ;;
  }

  measure: addressable_TPV_annual {
    type: sum
    sql: ${TABLE}.addressable_merchant_tpv_annual__c ;;
  }

  measure: addressable_TPV_annual_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_tpv_annual__c_usd ;;
  }

  measure: addressable_TPV_monthly {
    type: sum
    sql: ${TABLE}.addressable_merchant_tpv_monthly__c ;;
  }

  measure: addressable_TPV_monthly_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_tpv_monthly__c_usd ;;
  }

  measure: addressable_WIY_revenue {
    type: sum
    sql: ${TABLE}.addressable_wiy_revenue__c ;;
  }

  measure: addressable_WIY_revenue_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_wiy_revenue__c_usd ;;
  }

  measure: addressable_WIY_TPV {
    type: sum
    sql: ${TABLE}.addressable_wiy_tpv__c ;;
  }

  measure: addressable_WIY_TPV_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_wiy_tpv__c_usd ;;
  }

  #dimension: salesforce_business_entity_type {
  #  type: string
  #  sql: ${TABLE}.business_entity_type__c ;;
  #}

  #dimension: business_id {
  #  type: string
  #  hidden: yes
  #  sql: ${TABLE}.business_id ;;
  #}

  #dimension: salesforce_child_industry {
  #  type: string
  #  sql: ${TABLE}.child_industry_v2__c ;;
  #}

  #dimension: country_of_HQ {
  #  type: string
  #  sql: ${TABLE}.country_of_hq__c ;;
  #}

  # dimension: CP_manager_email {
  #   type: string
  #   sql: ${TABLE}.cp_manager_email ;;
  # }

  dimension: CP_manager_email {
    type: string
    sql: ${TABLE}.cpm_email ;;
  }

  #dimension: cp_manager_id {
  #  type: string
  # sql: ${TABLE}.cp_manager_id ;;
  #}

  # dimension: CP_manager_name {
  #   type: string
  #   sql: ${TABLE}.cp_manager_name ;;
  # }

  dimension: CP_manager_name {
    type: string
    sql: ${TABLE}.cpm_name ;;
  }

  dimension_group: created {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    #sql: from_unixtime(cast(json_extract_scalar(${TABLE}.created, '$.timestamp["$date"]') as BIGINT)/1000) ;;
    #sql: date_format(from_unixtime(${TABLE}.created),'%Y-%m-%d') ;;
    sql: CAST(${TABLE}.created AS DATE) ;;
  }

  dimension_group: lead_created_date {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    #sql: from_unixtime(cast(json_extract_scalar(${TABLE}.created, '$.timestamp["$date"]') as BIGINT)/1000) ;;
    #sql: date_format(from_unixtime(${TABLE}.created),'%Y-%m-%d') ;;
    sql: CAST(${TABLE}.created_lead AS DATE) ;;
  }

  dimension_group: record_created {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    #sql: from_unixtime(cast(json_extract_scalar(${TABLE}.created, '$.timestamp["$date"]') as BIGINT)/1000) ;;
    #sql: date_format(from_unixtime(${TABLE}.created),'%Y-%m-%d') ;;
    sql: CAST(${TABLE}.record_created AS DATE) ;;
  }

  dimension_group: record_fifth_transaction {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    #sql: from_unixtime(cast(json_extract_scalar(${TABLE}.created, '$.timestamp["$date"]') as BIGINT)/1000) ;;
    #sql: date_format(from_unixtime(${TABLE}.created),'%Y-%m-%d') ;;
    sql: CAST(${TABLE}.fifth_transaction AS DATE) ;;
  }

  dimension_group: record_latest_transaction {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    #sql: from_unixtime(cast(json_extract_scalar(${TABLE}.created, '$.timestamp["$date"]') as BIGINT)/1000) ;;
    #sql: date_format(from_unixtime(${TABLE}.created),'%Y-%m-%d') ;;
    sql: CAST(${TABLE}.latest_transaction AS DATE) ;;
  }

  #dimension: GTM_motion {
  #  type: string
  #  sql: ${TABLE}.gtm_motion__c ;;
  #}

  #dimension: industry_category {
  #  type: string
  #  sql: ${TABLE}.industry_category__c ;;
  #}

  #dimension: is_VC_backed_startup {
  #  type: yesno
  #  sql: ${TABLE}.is_vc_backed_startup__c ;;
  #}

  #dimension_group: last_activity {
  #  type: time
  # timeframes: [
  #    raw,
  #    time,
  #    date,
  #    week,
  #    month,
  #    quarter,
  #    year
  #  ]
  # sql: ${TABLE}.lastactivitydate ;;
  #}

  #dimension: master_business_id {
  #  type: string
  #  sql: ${TABLE}.master_acc_business ;;
  #}

  # dimension: owner_division {
  #   type: string
  #   sql: ${TABLE}.owner_division ;;
  # }

  dimension: owner_division {
    type: string
    sql: ${TABLE}.ownerdivision ;;
  }

  # dimension: owner_email {
  #   type: string
  #   sql: ${TABLE}.owner_email ;;
  # }

  dimension: owner_email {
    type: string
    sql: ${TABLE}.owneremail ;;
  }

  #dimension: owner_id {
  #  type: string
  # sql: ${TABLE}.owner_id ;;
  #}

  dimension: owner_name {
    type: string
    sql: ${TABLE}.ownername ;;
  }

  #dimension: phone {
  #  type: string
  #  sql: ${TABLE}.phone ;;
  #}

  #dimension: segment {
  #  type: string
  # sql: ${TABLE}.segment__c ;;
  #}

  dimension: ultimate_parent_name {
    type: string
    sql: ${TABLE}.ultimate_parent_name ;;
  }

  #dimension: website {
  #  type: string
  #  sql: ${TABLE}.website ;;
  #}

  #dimension: xendit_account_email {
  #  type: string
  #  sql: ${TABLE}.xendit_account_email__c ;;
  #}

  dimension: is_cp_partner {
    label: "Is CP Partner?"
    type: yesno
    sql: ${TABLE}.is_cp_partner;;
  }

  dimension: is_merchant_live {
    label: "Is Live Transacting?"
    type: yesno
    sql: CASE WHEN ${TABLE}.fifth_transaction IS NOT NULL THEN TRUE ELSE FALSE END;;
  }

  dimension: is_merchant_churn {
    label: "Is Churn?"
    type: yesno
    sql:
    CASE
      WHEN date_diff(day, CAST(${TABLE}.latest_transaction AS DATE), CAST(now() AS DATE))>60
        THEN TRUE
      ELSE FALSE
      END;;
  }

  measure: count {
    type: count
    drill_fields: [record_created_date, record_id, record_type ,record_name, ultimate_parent_name, CP_manager_email, owner_email, is_cp_partner, sf_referral_name, is_merchant_live, record_fifth_transaction_date, is_merchant_churn, record_latest_transaction_date]
  }

  measure: partner_count {
    type: count_distinct
    sql: ${TABLE}.sf_cp_partner_name ;;
    drill_fields: [sf_referral_name]
  }

  dimension: sf_cp_partner_direct_indirect_category {
    type: string
    label: "Sf CP Partner Subcategory"
    sql: ${TABLE}.sf_cp_partner_subcategory ;;
  }

  dimension: sf_cp_partner_direct_indirect_subcategory {
    type: string
    label: "Sf CP Partner Category"
    sql: ${TABLE}.sf_cp_partner_category ;;
  }
}
