view: ag_transform_salesforce_account {
  sql_table_name: transform__growth.salesforce_account ;;
  suggestions: yes

  dimension: account_id {
    primary_key: yes
    type: string
    link: {
      label: "Open in Salesforce"
      url: "https://xendit.lightning.force.com/{{ value }}"
    }
    sql: ${TABLE}.account_id ;;
  }

  dimension: account_name {
    type: string
    sql: ${TABLE}.account_name ;;
  }

  dimension: account_type {
    type: string
    sql: ${TABLE}.account_type ;;
  }

  dimension: account_status {
    type: string
    sql: ${TABLE}.account_status ;;
  }

  measure: addressable_IY_revenue {
    type: sum
    sql: ${TABLE}.addressable_iy_revenue__c ;;
  }

  measure: addressable_IY_revenue_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_iy_revenue__c_usd ;;
  }

  measure: addressable_IY_TPV {
    type: sum
    sql: ${TABLE}.addressable_iy_tpv__c ;;
  }

  measure: addressable_IY_TPV_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_iy_tpv__c_usd ;;
  }

  measure: addressable_revenue_annual {
    type: sum
    sql: ${TABLE}.addressable_merchant_revenue_annual__c ;;
  }

  measure: addressable_revenue_annual_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_revenue_annual__c_usd ;;
  }

  measure: addressable_revenue_monthly {
    type: sum
    sql: ${TABLE}.addressable_merchant_revenue_monthly__c ;;
  }

  measure: addressable_revenue_monthly_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_revenue_monthly__c_usd ;;
  }

  measure: addressable_TPV_annual {
    type: sum
    sql: ${TABLE}.addressable_merchant_tpv_annual__c ;;
  }

  measure: addressable_TPV_annual_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_tpv_annual__c_usd ;;
  }

  measure: addressable_TPV_monthly {
    type: sum
    sql: ${TABLE}.addressable_merchant_tpv_monthly__c ;;
  }

  measure: addressable_TPV_monthly_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_merchant_tpv_monthly__c_usd ;;
  }

  measure: addressable_WIY_revenue {
    type: sum
    sql: ${TABLE}.addressable_wiy_revenue__c ;;
  }

  measure: addressable_WIY_revenue_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_wiy_revenue__c_usd ;;
  }

  measure: addressable_WIY_TPV {
    type: sum
    sql: ${TABLE}.addressable_wiy_tpv__c ;;
  }

  measure: addressable_WIY_TPV_usd {
    type: sum
    value_format_name: "usd"
    sql: ${TABLE}.addressable_wiy_tpv__c_usd ;;
  }

  dimension: salesforce_brand_name {
    type: string
    sql: ${TABLE}.brand_name ;;
  }

  #dimension: salesforce_business_entity_type {
  #  type: string
  #  sql: ${TABLE}.business_entity_type__c ;;
  #}

  dimension: business_id {
    type: string
    hidden: yes
    link: {
      label: "Open in Admin Dashboard"
      url: "https://admin-dashboard.tidnex.com/customer-management/{{ value }}"
    }
    sql: ${TABLE}.business_id ;;
  }

  dimension: child_industry {
    type: string
    sql: ${TABLE}.child_industry_v2__c ;;
  }

  dimension: parent_child_industry {
    type: string
    sql: ${TABLE}.parent_child_industry_v2__c ;;
  }

  dimension: country_of_HQ {
    type: string
    sql: ${TABLE}.country_of_hq__c ;;
  }

  dimension: parent_country_of_HQ {
    type: string
    sql: ${TABLE}.parent_country_of_hq__c ;;
  }

  dimension: CP_manager_email {
    type: string
    sql: ${TABLE}.cp_manager_email ;;
  }

  #dimension: cp_manager_id {
  #  type: string
  # sql: ${TABLE}.cp_manager_id ;;
  #}

  dimension: CP_manager_name {
    type: string
    sql: ${TABLE}.cp_manager_name ;;
  }

  dimension_group: created {
    type: time
    convert_tz: no
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: CAST(${TABLE}.createddate AS DATE) ;;
  }

  #dimension: GTM_motion {
  #  type: string
  #  sql: ${TABLE}.gtm_motion__c ;;
  #}

  #dimension: industry_category {
  #  type: string
  #  sql: ${TABLE}.industry_category__c ;;
  #}

  #dimension: is_VC_backed_startup {
  #  type: yesno
  #  sql: ${TABLE}.is_vc_backed_startup__c ;;
  #}

  #dimension_group: last_activity {
  #  type: time
  # timeframes: [
  #    raw,
  #    time,
  #    date,
  #    week,
  #    month,
  #    quarter,
  #    year
  #  ]
  # sql: ${TABLE}.lastactivitydate ;;
  #}

  #dimension: master_business_id {
  #  type: string
  #  sql: ${TABLE}.master_acc_business ;;
  #}

  dimension: team_assignment {
    type: string
    sql: ${TABLE}.team_assignment__c ;;
  }

  dimension: parent_team_assignment {
    type: string
    sql: ${TABLE}.parent_team_assignment__c ;;
  }

  dimension: sales_team {
    type: string
    sql: ${TABLE}.sales_team ;;
  }

  dimension: owner_division {
    type: string
    sql: ${TABLE}.owner_division ;;
  }

  dimension: parent_owner_division {
    type: string
    sql: ${TABLE}.parent_owner_division ;;
  }

  dimension: owner_email {
    type: string
    sql: ${TABLE}.owner_email ;;
  }

  dimension: parent_owner_email {
    type: string
    sql: ${TABLE}.parent_owner_email ;;
  }

  #dimension: owner_id {
  #  type: string
  # sql: ${TABLE}.owner_id ;;
  #}

  dimension: owner_name {
    type: string
    sql: ${TABLE}.owner_name ;;
  }

  #dimension: phone {
  #  type: string
  #  sql: ${TABLE}.phone ;;
  #}

  #dimension: segment {
  #  type: string
  # sql: ${TABLE}.segment__c ;;
  #}

  dimension: ultimate_parent_name {
    type: string
    sql: ${TABLE}.ultimate_parent_name ;;
  }

  #dimension: website {
  #  type: string
  #  sql: ${TABLE}.website ;;
  #}

  #dimension: xendit_account_email {
  #  type: string
  #  sql: ${TABLE}.xendit_account_email__c ;;
  #}

  measure: count {
    type: count
    drill_fields: [account_id,
                  account_name,
                  salesforce_brand_name,
                  ultimate_parent_name,
                  addressable_TPV_monthly_usd,
                  addressable_TPV_annual_usd,
                  addressable_IY_TPV_usd,
                  addressable_WIY_TPV_usd,
                  addressable_revenue_monthly_usd,
                  addressable_revenue_annual_usd,
                  addressable_IY_revenue_usd,
                  addressable_WIY_revenue_usd,
                  CP_manager_name,
                  owner_name,
                  owner_division]
  }
}
