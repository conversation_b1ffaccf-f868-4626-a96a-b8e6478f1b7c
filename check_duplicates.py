#!/usr/bin/env python3

import os
import re
from collections import defaultdict

def extract_dimensions_measures_sets(content):
    """Extract all dimensions, measures, and sets from LookML content"""
    elements = defaultdict(list)
    
    # Pattern to match dimension, measure, or set definitions
    pattern = r'^\s*(dimension|measure|set|dimension_group|filter|parameter)(?:_group)?\s*:\s*(\w+)\s*\{'
    
    lines = content.split('\n')
    current_element = None
    current_name = None
    current_type = None
    brace_count = 0
    element_lines = []
    
    for i, line in enumerate(lines):
        match = re.match(pattern, line)
        
        if match:
            # If we were tracking a previous element, save it
            if current_element is not None:
                elements[current_type].append({
                    'name': current_name,
                    'definition': '\n'.join(element_lines),
                    'start_line': current_element,
                    'end_line': i - 1
                })
            
            # Start tracking new element
            current_type = match.group(1)
            current_name = match.group(2)
            current_element = i
            element_lines = [line]
            brace_count = line.count('{') - line.count('}')
        
        elif current_element is not None:
            element_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            
            # If braces are balanced, we've reached the end of the element
            if brace_count == 0:
                elements[current_type].append({
                    'name': current_name,
                    'definition': '\n'.join(element_lines),
                    'start_line': current_element,
                    'end_line': i
                })
                current_element = None
                current_name = None
                current_type = None
                element_lines = []
    
    # Handle case where file ends while tracking an element
    if current_element is not None:
        elements[current_type].append({
            'name': current_name,
            'definition': '\n'.join(element_lines),
            'start_line': current_element,
            'end_line': len(lines) - 1
        })
    
    return elements

def find_duplicates_in_file(file_path):
    """Find duplicate dimensions/measures/sets within a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        elements = extract_dimensions_measures_sets(content)
        duplicates = []
        
        for element_type, element_list in elements.items():
            name_counts = defaultdict(list)
            
            for element in element_list:
                name_counts[element['name']].append(element)
            
            for name, occurrences in name_counts.items():
                if len(occurrences) > 1:
                    duplicates.append({
                        'type': element_type,
                        'name': name,
                        'occurrences': occurrences
                    })
        
        return duplicates
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def main():
    # Files that were recently updated
    updated_files = [
        "marketing/business_id_marketing_source.view.lkml",
        "growth/solution_attribution.view.lkml",
        "growth/transaction_trend.view.lkml",
        "salesforce/ag_transform_salesforce_opportunity.view.lkml",
        "zendesk/ag_ticket_time_metrics.view.lkml",
        "salesforce/ag_campaign_influence.view.lkml",
        "transactions/disbursements/integrated_ag_disbursements.view.lkml",
        "zendesk/ndt_tickets.view.lkml",
        "salesforce/ag_parent_opportunities.view.lkml",
        "zendesk/ag_tickets.view.lkml",
        "growth/country_target.view.lkml",
        "xensavings/xendit_app/ag_xendit_app_transactions.view.lkml",
        "transactions/report/ag_unified_transaction.view.lkml",
        "remittance_money_out/disbursement_detail.view.lkml",
        "growth/cohort_analysis.view.lkml",
        "transactions/report/ag_id_transaction.view.lkml",
        "xensavings/xendit_app/ag_xendit_app_users.view.lkml",
        "salesforce/ag_salesforce_product_status.view.lkml",
        "growth/cps_dashboard_detail_products.view.lkml",
        "growth/sales_performance.view.lkml",
        "transactions/digipay/ewallet/ag_ewallet_captures.view.lkml",
        "xenCapital/accrual_revenue_v2.view.lkml",
        "xensavings/xendit_app/ag_card_bri_digisign.view.lkml",
        "salesforce/ag_salesforce_leads.view.lkml",
        "billing/promotion/ag_promo_reimbursement.view.lkml"
    ]
    
    total_duplicates = 0
    files_with_duplicates = []
    
    print("Checking for duplicate dimensions, measures, and sets...")
    print("=" * 80)
    
    for file_path in updated_files:
        if os.path.exists(file_path):
            duplicates = find_duplicates_in_file(file_path)
            
            if duplicates:
                total_duplicates += len(duplicates)
                files_with_duplicates.append(file_path)
                
                print(f"\n🔍 DUPLICATES FOUND in {file_path}:")
                print("-" * 60)
                
                for dup in duplicates:
                    print(f"  {dup['type'].upper()}: {dup['name']} ({len(dup['occurrences'])} occurrences)")
                    for i, occ in enumerate(dup['occurrences']):
                        print(f"    #{i+1}: Lines {occ['start_line']+1}-{occ['end_line']+1}")
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 80)
    print(f"SUMMARY:")
    print(f"Files checked: {len([f for f in updated_files if os.path.exists(f)])}")
    print(f"Files with duplicates: {len(files_with_duplicates)}")
    print(f"Total duplicates found: {total_duplicates}")
    
    if files_with_duplicates:
        print(f"\nFiles requiring cleanup:")
        for file_path in files_with_duplicates:
            print(f"  - {file_path}")
    else:
        print("\n✅ No duplicates found!")

if __name__ == "__main__":
    main()
