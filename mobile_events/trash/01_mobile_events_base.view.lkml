# view: mobile_events_base {
#   derived_table: {
#     sql: select
#         t1.*,
#         t2.system_userid as system_uid,
#         t2.xendit_userid as user_id
#       from
#         ${mobile_events.SQL_TABLE_NAME} t1 inner join
#         ${mobile_identifier.SQL_TABLE_NAME} t2 on t1.session_id = t2.session_id and t1.app_id = t2.app_id
#       ;;
#     datagroup_trigger: views_updated_based_on_time
#   }

#   suggestions: no

#   measure: count {
#     type: count
#     drill_fields: [detail*]
#   }

#   dimension: app_id {
#     type: string
#     sql: ${TABLE}.app_id ;;
#   }

#   dimension: platform {
#     type: string
#     sql: ${TABLE}.platform ;;
#   }

#   dimension_group: dvce_created_tstamp {
#     type: time
#     sql: ${TABLE}.dvce_created_tstamp ;;
#   }

#   dimension_group: collector_tstamp {
#     type: time
#     sql: ${TABLE}.collector_tstamp ;;
#   }

#   dimension: useragent {
#     type: string
#     sql: ${TABLE}.useragent ;;
#   }

#   dimension: event {
#     type: string
#     sql: ${TABLE}.event ;;
#   }

#   dimension: event_id {
#     type: string
#     sql: ${TABLE}.event_id ;;
#   }

#   dimension: system_userid {
#     type: string
#     sql: ${TABLE}.system_userid ;;
#   }

#   dimension: xendit_userid {
#     type: string
#     sql: ${TABLE}.xendit_userid ;;
#   }

#   dimension: se_category {
#     type: string
#     sql: ${TABLE}.se_category ;;
#   }

#   dimension: se_action {
#     type: string
#     sql: ${TABLE}.se_action ;;
#   }

#   dimension: se_label {
#     type: string
#     sql: ${TABLE}.se_label ;;
#   }

#   dimension: se_property {
#     type: string
#     sql: ${TABLE}.se_property ;;
#   }

#   dimension: se_value {
#     type: number
#     sql: ${TABLE}.se_value ;;
#   }

#   dimension: screen_idgrp {
#     type: string
#     sql: ${TABLE}.screen_idgrp ;;
#   }

#   dimension: screen_namegrp {
#     type: string
#     sql: ${TABLE}.screen_namegrp ;;
#   }

#   dimension: screen_name {
#     type: string
#     sql: ${TABLE}.screen_name ;;
#   }

#   dimension: screen_id {
#     type: string
#     sql: ${TABLE}.screen_id ;;
#   }

#   dimension: prev_screen_name {
#     type: string
#     sql: ${TABLE}.prev_screen_name ;;
#   }

#   dimension: prev_screen_id {
#     type: string
#     sql: ${TABLE}.prev_screen_id ;;
#   }

#   dimension: build {
#     type: string
#     sql: ${TABLE}.build ;;
#   }

#   dimension: version {
#     type: string
#     sql: ${TABLE}.version ;;
#   }

#   dimension: os_type {
#     type: string
#     sql: ${TABLE}.os_type ;;
#   }

#   dimension: os_version {
#     type: string
#     sql: ${TABLE}.os_version ;;
#   }

#   dimension: device_manufacturer {
#     type: string
#     sql: ${TABLE}.device_manufacturer ;;
#   }

#   dimension: device_model {
#     type: string
#     sql: ${TABLE}.device_model ;;
#   }

#   dimension: carrier {
#     type: string
#     sql: ${TABLE}.carrier ;;
#   }

#   dimension: apple_idfv {
#     type: string
#     sql: ${TABLE}.apple_idfv ;;
#   }

#   dimension: android_idfa {
#     type: string
#     sql: ${TABLE}.android_idfa ;;
#   }

#   dimension: session_id {
#     type: string
#     sql: ${TABLE}.session_id ;;
#   }

#   dimension: session_index {
#     type: number
#     sql: ${TABLE}.session_index ;;
#   }

#   dimension: storage_mechanism {
#     type: string
#     sql: ${TABLE}.storage_mechanism ;;
#   }

#   dimension: previous_session_id {
#     type: string
#     sql: ${TABLE}.previous_session_id ;;
#   }

#   dimension: foreground_index {
#     type: number
#     sql: ${TABLE}.foreground_index ;;
#   }

#   dimension: background_index {
#     type: number
#     sql: ${TABLE}.background_index ;;
#   }

#   dimension: app_install {
#     type: string
#     sql: ${TABLE}.app_install ;;
#   }

#   dimension: dt {
#     type: date
#     sql: ${TABLE}.dt ;;
#   }

#   dimension: system_uid {
#     type: string
#     sql: ${TABLE}.system_uid ;;
#   }

#   dimension: user_id {
#     type: string
#     sql: ${TABLE}.user_id ;;
#   }

#   set: detail {
#     fields: [
#       app_id,
#       platform,
#       dvce_created_tstamp_time,
#       collector_tstamp_time,
#       useragent,
#       event,
#       event_id,
#       system_userid,
#       xendit_userid,
#       se_category,
#       se_action,
#       se_label,
#       se_property,
#       se_value,
#       screen_idgrp,
#       screen_namegrp,
#       screen_name,
#       screen_id,
#       prev_screen_name,
#       prev_screen_id,
#       build,
#       version,
#       os_type,
#       os_version,
#       device_manufacturer,
#       device_model,
#       carrier,
#       apple_idfv,
#       android_idfa,
#       session_id,
#       session_index,
#       storage_mechanism,
#       previous_session_id,
#       foreground_index,
#       background_index,
#       app_install,
#       dt,
#       system_uid,
#       user_id
#     ]
#   }
# }
