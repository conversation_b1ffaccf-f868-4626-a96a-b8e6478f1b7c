#!/usr/bin/env python3
"""
Synchronize zendesk files from source to DBR repository
"""

import os
import shutil
import subprocess

SOURCE_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

# Files that need synchronization based on our analysis
FILES_TO_SYNC = [
    "ag_chats.view.lkml",
    "ag_ticket_time_metrics.view.lkml", 
    "ag_tickets.view.lkml",
    "chats.view.lkml",
    "ndt_tickets.view.lkml",
    "ticket_metrics_timespand.view.lkml",
    "tickets.view.lkml"
    # Note: ticket.explore.lkml excluded as DBR version has correct references
]

def read_file(file_path):
    """Read file content"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def write_file(file_path, content):
    """Write file content"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def update_for_databricks(content, filename):
    """Update content for Databricks compatibility"""
    
    # Keep Presto SQL functions as requested in memories
    # Don't convert get_json_object to json_extract_scalar
    # Don't convert other Presto functions
    
    # For chats.view.lkml - keep the Presto timestamp functions
    if filename == "chats.view.lkml":
        # Keep from_iso8601_timestamp instead of to_utc_timestamp
        pass
    
    # For ticket_metrics_timespand.view.lkml - keep Presto SQL syntax
    if filename == "ticket_metrics_timespand.view.lkml":
        # Keep cross join unnest syntax instead of lateral view explode
        pass
    
    return content

def sync_file(filename):
    """Sync a single file from source to DBR"""
    source_file = os.path.join(SOURCE_REPO, "zendesk", filename)
    dbr_file = os.path.join(DBR_REPO, "zendesk", filename)
    
    if not os.path.exists(source_file):
        print(f"  ❌ Source file not found: {filename}")
        return False
    
    # Read source content
    source_content = read_file(source_file)
    
    # Update for Databricks if needed
    updated_content = update_for_databricks(source_content, filename)
    
    # Create backup of DBR file
    backup_file = f"{dbr_file}.backup"
    if os.path.exists(dbr_file):
        shutil.copy2(dbr_file, backup_file)
        print(f"  💾 Created backup: {filename}.backup")
    
    # Write updated content to DBR file
    write_file(dbr_file, updated_content)
    print(f"  ✅ Synchronized: {filename}")
    
    return True

def main():
    print("🔄 SYNCHRONIZING ZENDESK FILES")
    print("=" * 50)
    
    print(f"📊 Files to synchronize: {len(FILES_TO_SYNC)}")
    
    successful_syncs = 0
    failed_syncs = 0
    
    for filename in FILES_TO_SYNC:
        print(f"\n📋 Synchronizing: {filename}")
        print("-" * 30)
        
        if sync_file(filename):
            successful_syncs += 1
        else:
            failed_syncs += 1
    
    print(f"\n📊 SYNCHRONIZATION SUMMARY:")
    print(f"  ✅ Successfully synchronized: {successful_syncs}")
    print(f"  ❌ Failed synchronizations: {failed_syncs}")
    print(f"  📁 Total files processed: {len(FILES_TO_SYNC)}")
    
    if successful_syncs > 0:
        print(f"\n🎉 SYNCHRONIZATION COMPLETE!")
        print(f"  💾 Backup files created for safety")
        print(f"  📝 Note: ticket.explore.lkml kept as-is (correct DBR references)")
    
    print(f"\n🔍 NEXT STEPS:")
    print(f"  1. Review synchronized files")
    print(f"  2. Test the changes")
    print(f"  3. Remove backup files if satisfied")

if __name__ == "__main__":
    main()
