view: mobile_mau {
  derived_table: {
    sql:

          -- use existing businesses in looker_pdt_v4.LR_SLRH71642546952366_businesses
          SELECT
              user_id,
              business_id,
              last_day_of_month(from_iso8601_timestamp(created_at)) as login_period,
              MIN(from_iso8601_timestamp(created_at)) AS first_login_time,
              MAX(from_iso8601_timestamp(created_at)) AS last_login_time
          FROM clean__mobile_store_gateway.session  AS mobile_app_login_sessions
          GROUP BY 1,2,3
       ;;
  }

  dimension_group: first_login_time {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      hour_of_day,
      day_of_month,
      day_of_week
    ]
    sql: ${TABLE}."first_login_time" ;;
  }

  dimension_group: last_login_time {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      hour_of_day,
      day_of_month,
      day_of_week
    ]
    sql: ${TABLE}."last_login_time" ;;
  }

  dimension_group: login_period {
    type: time
    timeframes: [
      month
    ]
    sql: cast(${TABLE}."login_period" as timestamp) ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  measure: business_count {
    type: count_distinct
    sql: ${business_id} ;;
    drill_fields: [detail*]
  }

  measure: user_count {
    type: count_distinct
    sql: ${user_id} ;;
    drill_fields: [detail*]
  }

  set: detail {
    fields: [login_period_month, user_id, business_id,first_login_time_date,last_login_time_date]
  }
}
