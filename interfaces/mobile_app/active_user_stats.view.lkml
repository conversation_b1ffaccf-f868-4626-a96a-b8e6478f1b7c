view: active_user_stats {
  derived_table: {
    sql: with dau as (
          select
              date(to_utc_timestamp(s.created_at,"UTC")) login_date, count(distinct s.user_id) dau
          from
              clean__mobile_store_gateway.session s
            left join clean__xendit_business_service.businesses b on s.business_id = s.business_id
            where s.business_id IN (select id from clean__xendit_business_service.businesses where go_live_status = 'LIVE')
          group by 1
          )

      select login_date, dau,

      (select count(distinct s.user_id)
      from clean__mobile_store_gateway.session s
      where s.business_id IN (select id from clean__xendit_business_service.businesses where go_live_status = 'LIVE')
        and date(to_utc_timestamp(s.created_at,"UTC")) BETWEEN dateadd(DAY, -7, dau.login_date) AND dau.login_date) as wau,

      (select count(distinct s.user_id)
      from clean__mobile_store_gateway.session s
      where s.business_id IN (select id from clean__xendit_business_service.businesses where go_live_status = 'LIVE')
        and date(to_utc_timestamp(s.created_at,"UTC")) BETWEEN dateadd(DAY, -29, dau.login_date) AND dau.login_date) as mau

      from dau
      ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: login_date {
    primary_key: yes
    type: date
    sql: ${TABLE}.login_date ;;
    convert_tz: no
  }

  dimension: dau {
    type: number
    sql: ${TABLE}.dau ;;
  }

  dimension: wau {
    type: number
    sql: ${TABLE}.wau ;;
  }

  dimension: mau {
    type: number
    sql: ${TABLE}.mau ;;
  }

  set: detail {
    fields: [login_date, dau, wau, mau]
  }
}
