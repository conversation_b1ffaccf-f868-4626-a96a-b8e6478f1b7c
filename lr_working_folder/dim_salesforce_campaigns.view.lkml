# The name of this view in Looker is "Dim Salesforce Campaigns"
view: dim_salesforce_campaigns {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__growth.dim_salesforce_campaigns ;;
  suggestions: no

  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Actual Cost USD" in Explore.

  dimension: actual_cost_usd {
    type: number
    sql: ${TABLE}.actual_cost_usd ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_actual_cost_usd {
    type: sum
    sql: ${actual_cost_usd} ;;  }
  measure: average_actual_cost_usd {
    type: average
    sql: ${actual_cost_usd} ;;  }

  dimension: attribution_window {
    type: number
    sql: ${TABLE}.attribution_window ;;
  }

  dimension: campaign_actual_roi {
    type: number
    sql: ${TABLE}.campaign_actual_roi ;;
  }

  dimension: campaign_name {
    type: string
    sql: ${TABLE}.campaign_name ;;
  }

  dimension: campaign_status {
    type: string
    sql: ${TABLE}.campaign_status ;;
  }

  dimension: campaign_target_roi {
    type: number
    sql: ${TABLE}.campaign_target_roi ;;
  }

  dimension: campaign_type {
    type: string
    sql: ${TABLE}.campaign_type ;;
  }

  dimension: campaignid {
    type: string
    sql: ${TABLE}.campaignid ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: create {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.create_date ;;
  }

  dimension_group: end {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.end_date ;;
  }

  dimension: is_test_campaign {
    type: yesno
    sql: ${TABLE}.is_test_campaign ;;
  }

  dimension: isactive {
    type: yesno
    sql: ${TABLE}.isactive ;;
  }

  dimension: master_campaign_id {
    type: string
    sql: ${TABLE}.master_campaign_id ;;
  }

  dimension: master_campaign_name {
    type: string
    sql: ${TABLE}.master_campaign_name ;;
  }

  dimension: master_objective {
    type: string
    sql: ${TABLE}.master_objective ;;
  }

  dimension: master_three_word_summary {
    type: string
    sql: ${TABLE}.master_three_word_summary ;;
  }

  dimension: master_vertical {
    type: string
    sql: ${TABLE}.master_vertical ;;
  }

  dimension: owner_email {
    type: string
    sql: ${TABLE}.owner_email ;;
  }

  dimension: owner_id {
    type: string
    sql: ${TABLE}.owner_id ;;
  }

  dimension: parent_campaign_id {
    type: string
    sql: ${TABLE}.parent_campaign_id ;;
  }

  dimension: parent_campaign_name {
    type: string
    sql: ${TABLE}.parent_campaign_name ;;
  }

  dimension: parent_campaign_type {
    type: string
    sql: ${TABLE}.parent_campaign_type ;;
  }

  dimension: planned_budget_usd {
    type: number
    sql: ${TABLE}.planned_budget_usd ;;
  }

  dimension: program_objective {
    type: string
    sql: ${TABLE}.program_objective ;;
  }

  dimension: program_offer_type {
    type: string
    sql: ${TABLE}.program_offer_type ;;
  }

  dimension: program_product {
    type: string
    sql: ${TABLE}.program_product ;;
  }

  dimension: program_sub_objective {
    type: string
    sql: ${TABLE}.program_sub_objective ;;
  }

  dimension: response_count_actual {
    type: number
    sql: ${TABLE}.response_count_actual ;;
  }

  dimension: response_count_goal {
    type: number
    sql: ${TABLE}.response_count_goal ;;
  }

  dimension: revenue_actual_usd {
    type: number
    sql: ${TABLE}.revenue_actual_usd ;;
  }

  dimension: revenue_goal_usd {
    type: number
    sql: ${TABLE}.revenue_goal_usd ;;
  }

  dimension_group: start {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.start_date ;;
  }

  dimension: tactic_landing_page {
    type: string
    sql: ${TABLE}.tactic_landing_page ;;
  }

  dimension: tactic_type {
    type: string
    sql: ${TABLE}.tactic_type ;;
  }

  dimension: three_word_summary {
    type: string
    sql: ${TABLE}.three_word_summary ;;
  }
  measure: count {
    type: count
    drill_fields: [campaign_name, parent_campaign_name, master_campaign_name]
  }
}
