# The name of this view in Looker is "Fct Gs Macro Multigrain Metrics Bi"
view: business_multigrain_metrics {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__business_intelligence_transaction_volumes.fct_gs_macro_multigrain_metrics_bi ;;
  suggestions: no
  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Aggregation Level" in Explore.

  dimension: aggregation_level {
    type: string
    sql: ${TABLE}.aggregation_level ;;
  }

  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: business_created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.business_created_at ;;
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: channel_name {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension_group: fifth_transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.fifth_transaction_timestamp ;;
  }

  dimension: first_18_months_count_txn {
    type: number
    sql: ${TABLE}.first_18_months_count_txn ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_first_18_months_count_txn {
    type: sum
    sql: ${first_18_months_count_txn} ;;
  }

  measure: average_first_18_months_count_txn {
    type: average
    sql: ${first_18_months_count_txn} ;;
  }

  dimension: first_18_months_sum_tpv {
    type: number
    sql: ${TABLE}.first_18_months_sum_tpv ;;
  }

  dimension: first_month_count_txn {
    type: number
    sql: ${TABLE}.first_month_count_txn ;;
  }

  dimension: first_month_sum_tpv {
    type: number
    sql: ${TABLE}.first_month_sum_tpv ;;
  }

  dimension_group: first_transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.first_transaction_timestamp ;;
  }

  dimension_group: go_live {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.go_live ;;
  }

  dimension: has_more_than_5_lifetime_transactions {
    type: yesno
    sql: ${TABLE}.has_more_than_5_lifetime_transactions ;;
  }

  dimension: is_active {
    type: yesno
    sql: ${TABLE}.is_active ;;
  }

  dimension: is_churned {
    type: yesno
    sql: ${TABLE}.is_churned ;;
  }

  dimension: is_dormant {
    type: yesno
    sql: ${TABLE}.is_dormant ;;
  }

  dimension: is_new_active {
    type: yesno
    sql: ${TABLE}.is_new_active ;;
  }

  dimension: is_reactivated {
    type: yesno
    sql: ${TABLE}.is_reactivated ;;
  }

  dimension: is_retained_m1 {
    type: yesno
    sql: ${TABLE}.is_retained_m1 ;;
  }

  dimension: is_retained_m3 {
    type: yesno
    sql: ${TABLE}.is_retained_m3 ;;
  }

  dimension: is_retained_m6 {
    type: yesno
    sql: ${TABLE}.is_retained_m6 ;;
  }

  dimension_group: last_transaction_timestamp {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.last_transaction_timestamp ;;
  }

  dimension: lifetime_tpv_usd {
    type: number
    sql: ${TABLE}.lifetime_tpv_usd ;;
  }

  dimension: lifetime_tpv_usd_rank {
    type: number
    sql: ${TABLE}.lifetime_tpv_usd_rank ;;
  }

  dimension: lifetime_transaction_count {
    type: number
    sql: ${TABLE}.lifetime_transaction_count ;;
  }

  dimension: lifetime_transaction_count_rank {
    type: number
    sql: ${TABLE}.lifetime_transaction_count_rank ;;
  }

  dimension: master_account_id {
    type: string
    sql: ${TABLE}.master_account_id ;;
  }

  dimension: num_days_first_and_latest_transaction {
    type: number
    sql: ${TABLE}.num_days_first_and_latest_transaction ;;
  }

  dimension: num_days_since_first_transaction {
    type: number
    sql: ${TABLE}.num_days_since_first_transaction ;;
  }

  dimension: num_days_since_latest_transaction {
    type: number
    sql: ${TABLE}.num_days_since_latest_transaction ;;
  }

  dimension: prior_period_transactions {
    type: number
    sql: ${TABLE}.prior_period_transactions ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: second_month_count_txn {
    type: number
    sql: ${TABLE}.second_month_count_txn ;;
  }

  dimension: second_month_sum_tpv {
    type: number
    sql: ${TABLE}.second_month_sum_tpv ;;
  }

  dimension: third_month_count_txn {
    type: number
    sql: ${TABLE}.third_month_count_txn ;;
  }

  dimension: third_month_sum_tpv {
    type: number
    sql: ${TABLE}.third_month_sum_tpv ;;
  }

  dimension: this_and_prior_period_transactions {
    type: number
    sql: ${TABLE}.this_and_prior_period_transactions ;;
  }

  dimension: this_period_transactions {
    type: number
    sql: ${TABLE}.this_period_transactions ;;
  }

  measure: count {
    type: count
    drill_fields: [channel_name]
  }
}
