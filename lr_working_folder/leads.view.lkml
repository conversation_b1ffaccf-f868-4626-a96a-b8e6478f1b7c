# The name of this view in Looker is "Leads"
view: leads {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__salesforce.leads ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Account Type C" in Explore.

  dimension: account_type__c {
    type: string
    sql: ${TABLE}.account_type__c ;;
  }

  dimension: accountid {
    type: string
    sql: ${TABLE}.accountid ;;
  }

  dimension: activitydate {
    type: string
    sql: ${TABLE}.activitydate ;;
  }

  dimension: activitydatetime {
    type: number
    sql: ${TABLE}.activitydatetime ;;
  }

  dimension: activitysubtype {
    type: string
    sql: ${TABLE}.activitysubtype ;;
  }

  dimension: activitytype {
    type: string
    sql: ${TABLE}.activitytype ;;
  }

  dimension: ad_hubspot_owner__c {
    type: string
    sql: ${TABLE}.ad_hubspot_owner__c ;;
  }

  dimension: addressable_opportunity_tpv_monthly__c {
    type: string
    sql: ${TABLE}.addressable_opportunity_tpv_monthly__c ;;
  }

  dimension: alternatedetailid {
    type: string
    sql: ${TABLE}.alternatedetailid ;;
  }

  dimension: assign_to_prospect__c {
    type: yesno
    sql: ${TABLE}.assign_to_prospect__c ;;
  }

  dimension: average_monthly_revenue__c {
    type: string
    sql: ${TABLE}.average_monthly_revenue__c ;;
  }

  dimension: average_transaction_size_lead_qual__c {
    type: string
    sql: ${TABLE}.average_transaction_size_lead_qual__c ;;
  }

  dimension: average_transaction_volume_per_month__c {
    type: string
    sql: ${TABLE}.average_transaction_volume_per_month__c ;;
  }

  dimension: bd_owner__c {
    type: string
    sql: ${TABLE}.bd_owner__c ;;
  }

  dimension: brand__c {
    type: string
    sql: ${TABLE}.brand__c ;;
  }

  dimension: business_entity_type__c {
    type: string
    sql: ${TABLE}.business_entity_type__c ;;
  }

  dimension: business_id__c {
    type: string
    sql: ${TABLE}.business_id__c ;;
  }

  dimension: calldisposition {
    type: string
    sql: ${TABLE}.calldisposition ;;
  }

  dimension: calldurationinseconds {
    type: string
    sql: ${TABLE}.calldurationinseconds ;;
  }

  dimension: callobject {
    type: string
    sql: ${TABLE}.callobject ;;
  }

  dimension: calltype {
    type: string
    sql: ${TABLE}.calltype ;;
  }

  dimension: channel__c {
    type: string
    sql: ${TABLE}.channel__c ;;
  }

  dimension: child_industry_v2__c {
    type: string
    sql: ${TABLE}.child_industry_v2__c ;;
  }

  dimension: city {
    type: string
    sql: ${TABLE}.city ;;
  }

  dimension: company {
    type: string
    sql: ${TABLE}.company ;;
  }

  dimension: company_decision_making_model__c {
    type: string
    sql: ${TABLE}.company_decision_making_model__c ;;
  }

  dimension: completeddatetime {
    type: number
    sql: ${TABLE}.completeddatetime ;;
  }

  dimension: contact_person_role_lead_qual__c {
    type: string
    sql: ${TABLE}.contact_person_role_lead_qual__c ;;
  }

  dimension: converted_date__c {
    type: string
    sql: ${TABLE}.converted_date__c ;;
  }

  dimension: convertedaccountid {
    type: string
    sql: ${TABLE}.convertedaccountid ;;
  }

  dimension: convertedcontactid {
    type: string
    sql: ${TABLE}.convertedcontactid ;;
  }

  dimension: converteddate {
    type: string
    sql: ${TABLE}.converteddate ;;
  }

  dimension: convertedopportunityid {
    type: string
    sql: ${TABLE}.convertedopportunityid ;;
  }

  dimension: count_of_activity__c {
    type: number
    sql: ${TABLE}.count_of_activity__c ;;
  }

  dimension: country {
    type: string
    map_layer_name: countries
    sql: ${TABLE}.country ;;
  }

  dimension: country_of_hq__c {
    type: string
    sql: ${TABLE}.country_of_hq__c ;;
  }

  dimension: countrycode {
    type: string
    sql: ${TABLE}.countrycode ;;
  }

  dimension: cp_manager__c {
    type: string
    sql: ${TABLE}.cp_manager__c ;;
  }

  dimension: createdbyid {
    type: string
    sql: ${TABLE}.createdbyid ;;
  }

  dimension: createddate {
    type: number
    sql: ${TABLE}.createddate ;;
  }

  dimension: currencyisocode {
    type: string
    sql: ${TABLE}.currencyisocode ;;
  }

  dimension: data_enrichment_source__c {
    type: string
    sql: ${TABLE}.data_enrichment_source__c ;;
  }

  dimension: db_activity_type__c {
    type: string
    sql: ${TABLE}.db_activity_type__c ;;
  }

  dimension: db_created_date_without_time__c {
    type: string
    sql: ${TABLE}.db_created_date_without_time__c ;;
  }

  dimension: db_lead_age__c {
    type: number
    sql: ${TABLE}.db_lead_age__c ;;
  }

  dimension: department__c {
    type: string
    sql: ${TABLE}.department__c ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: disqualified_date__c {
    type: string
    sql: ${TABLE}.disqualified_date__c ;;
  }

  dimension: durationinminutes {
    type: number
    sql: ${TABLE}.durationinminutes ;;
  }

  dimension: email {
    type: string
    sql: ${TABLE}.email ;;
  }

  dimension: emailbounceddate {
    type: number
    sql: ${TABLE}.emailbounceddate ;;
  }

  dimension: emailbouncedreason {
    type: string
    sql: ${TABLE}.emailbouncedreason ;;
  }

  dimension: enddatetime {
    type: number
    sql: ${TABLE}.enddatetime ;;
  }

  dimension: entity__c {
    type: string
    sql: ${TABLE}.entity__c ;;
  }

  dimension: estimated_1st_transaction_date__c {
    type: string
    sql: ${TABLE}.estimated_1st_transaction_date__c ;;
  }

  dimension: estimated_transactions_handle_each_month__c {
    type: string
    sql: ${TABLE}.estimated_transactions_handle_each_month__c ;;
  }

  dimension: event_name__c {
    type: string
    sql: ${TABLE}.event_name__c ;;
  }

  dimension: expected_timeline__c {
    type: string
    sql: ${TABLE}.expected_timeline__c ;;
  }

  dimension: expected_tpv__c {
    type: string
    sql: ${TABLE}.expected_tpv__c ;;
  }

  dimension: expected_transaction_volume__c {
    type: string
    sql: ${TABLE}.expected_transaction_volume__c ;;
  }

  dimension: firstname {
    type: string
    sql: ${TABLE}.firstname ;;
  }

  dimension: geocodeaccuracy {
    type: string
    sql: ${TABLE}.geocodeaccuracy ;;
  }

  dimension: glr_flag__c {
    type: yesno
    sql: ${TABLE}.glr_flag__c ;;
  }

  dimension: go_live_status__c {
    type: string
    sql: ${TABLE}.go_live_status__c ;;
  }

  dimension: gtm_motion__c {
    type: string
    sql: ${TABLE}.gtm_motion__c ;;
  }

  dimension: heeet__bing_ads_acquisition_costs__c {
    type: string
    sql: ${TABLE}.heeet__bing_ads_acquisition_costs__c ;;
  }

  dimension: heeet__bing_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__bing_ads_id__c ;;
  }

  dimension: heeet__bing_ads_interaction__c {
    type: number
    sql: ${TABLE}.heeet__bing_ads_interaction__c ;;
  }

  dimension: heeet__direct_interactions__c {
    type: number
    sql: ${TABLE}.heeet__direct_interactions__c ;;
  }

  dimension: heeet__facebook_ads_acquisition_costs__c {
    type: string
    sql: ${TABLE}.heeet__facebook_ads_acquisition_costs__c ;;
  }

  dimension: heeet__facebook_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__facebook_ads_id__c ;;
  }

  dimension: heeet__facebook_ads_interactions__c {
    type: number
    sql: ${TABLE}.heeet__facebook_ads_interactions__c ;;
  }

  dimension: heeet__first_click_landing_url__c {
    type: string
    sql: ${TABLE}.heeet__first_click_landing_url__c ;;
  }

  dimension: heeet__first_visit_to_form__c {
    type: string
    sql: ${TABLE}.heeet__first_visit_to_form__c ;;
  }

  dimension: heeet__google_ads_acquisition_costs__c {
    type: number
    sql: ${TABLE}.heeet__google_ads_acquisition_costs__c ;;
  }

  dimension: heeet__google_ads_interactions__c {
    type: number
    sql: ${TABLE}.heeet__google_ads_interactions__c ;;
  }

  dimension: heeet__heeet_adgroup__c {
    type: string
    sql: ${TABLE}.heeet__heeet_adgroup__c ;;
  }

  dimension: heeet__heeet_channel__c {
    type: string
    sql: ${TABLE}.heeet__heeet_channel__c ;;
  }

  dimension: heeet__heeet_first_adgroup__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_adgroup__c ;;
  }

  dimension: heeet__heeet_first_click_bing_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_bing_ads_id__c ;;
  }

  dimension: heeet__heeet_first_click_campaign_keyword__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_campaign_keyword__c ;;
  }

  dimension: heeet__heeet_first_click_campaign_medium__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_campaign_medium__c ;;
  }

  dimension: heeet__heeet_first_click_campaign_name__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_campaign_name__c ;;
  }

  dimension: heeet__heeet_first_click_campaign_source__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_campaign_source__c ;;
  }

  dimension: heeet__heeet_first_click_channel__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_channel__c ;;
  }

  dimension: heeet__heeet_first_click_datetime__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_datetime__c ;;
  }

  dimension: heeet__heeet_first_click_facebook_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_facebook_ads_id__c ;;
  }

  dimension: heeet__heeet_first_click_google_client_id__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_google_client_id__c ;;
  }

  dimension: heeet__heeet_first_click_google_gclid__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_google_gclid__c ;;
  }

  dimension: heeet__heeet_first_click_iso__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_iso__c ;;
  }

  dimension: heeet__heeet_first_click_linkedin_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_linkedin_ads_id__c ;;
  }

  dimension: heeet__heeet_first_click_referrer__c {
    type: string
    sql: ${TABLE}.heeet__heeet_first_click_referrer__c ;;
  }

  dimension: heeet__heeet_google_campaign_keyword__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_campaign_keyword__c ;;
  }

  dimension: heeet__heeet_google_campaign_medium__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_campaign_medium__c ;;
  }

  dimension: heeet__heeet_google_campaign_name__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_campaign_name__c ;;
  }

  dimension: heeet__heeet_google_campaign_source__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_campaign_source__c ;;
  }

  dimension: heeet__heeet_google_client_id__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_client_id__c ;;
  }

  dimension: heeet__heeet_google_gclid__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_gclid__c ;;
  }

  dimension: heeet__heeet_google_referrer__c {
    type: string
    sql: ${TABLE}.heeet__heeet_google_referrer__c ;;
  }

  dimension: heeet__heeet_last_click_datetime__c {
    type: string
    sql: ${TABLE}.heeet__heeet_last_click_datetime__c ;;
  }

  dimension: heeet__heeet_last_click_iso__c {
    type: string
    sql: ${TABLE}.heeet__heeet_last_click_iso__c ;;
  }

  dimension: heeet__last_click_landing_url__c {
    type: string
    sql: ${TABLE}.heeet__last_click_landing_url__c ;;
  }

  dimension: heeet__lead_acquisition_costs__c {
    type: string
    sql: ${TABLE}.heeet__lead_acquisition_costs__c ;;
  }

  dimension: heeet__linkedin_ads_acquisition_costs__c {
    type: string
    sql: ${TABLE}.heeet__linkedin_ads_acquisition_costs__c ;;
  }

  dimension: heeet__linkedin_ads_id__c {
    type: string
    sql: ${TABLE}.heeet__linkedin_ads_id__c ;;
  }

  dimension: heeet__linkedin_ads_interactions__c {
    type: number
    sql: ${TABLE}.heeet__linkedin_ads_interactions__c ;;
  }

  dimension: heeet__online_interactions__c {
    type: number
    sql: ${TABLE}.heeet__online_interactions__c ;;
  }

  dimension: heeet__other_interactions__c {
    type: number
    sql: ${TABLE}.heeet__other_interactions__c ;;
  }

  dimension: heeet__seo_interactions__c {
    type: number
    sql: ${TABLE}.heeet__seo_interactions__c ;;
  }

  dimension: how_did_you_hear_about_us__c {
    type: string
    sql: ${TABLE}.how_did_you_hear_about_us__c ;;
  }

  dimension: how_do_you_want_to_use_xendit__c {
    type: string
    sql: ${TABLE}.how_do_you_want_to_use_xendit__c ;;
  }

  dimension: how_do_you_want_to_use_xendit_other__c {
    type: string
    sql: ${TABLE}.how_do_you_want_to_use_xendit_other__c ;;
  }

  dimension: how_would_you_prefer_to_meet__c {
    type: string
    sql: ${TABLE}.how_would_you_prefer_to_meet__c ;;
  }

  dimension: hubspot_created_date__c {
    type: string
    sql: ${TABLE}.hubspot_created_date__c ;;
  }

  dimension: i_want_to_get_more_info_on_contact_us__c {
    type: string
    sql: ${TABLE}.i_want_to_get_more_info_on_contact_us__c ;;
  }

  dimension: industry {
    type: string
    sql: ${TABLE}.industry ;;
  }

  dimension: industry_category__c {
    type: string
    sql: ${TABLE}.industry_category__c ;;
  }

  dimension: insurance_content_syndication_qn5__c {
    type: string
    sql: ${TABLE}.insurance_content_syndication_qn5__c ;;
  }

  dimension: internal_name__c {
    type: string
    sql: ${TABLE}.internal_name__c ;;
  }

  dimension: is_future_interest__c {
    type: yesno
    sql: ${TABLE}.is_future_interest__c ;;
  }

  dimension: is_reseller__c {
    type: yesno
    sql: ${TABLE}.is_reseller__c ;;
  }

  dimension: is_vc_backed_startup__c {
    type: yesno
    sql: ${TABLE}.is_vc_backed_startup__c ;;
  }

  dimension: isalldayevent {
    type: yesno
    sql: ${TABLE}.isalldayevent ;;
  }

  dimension: isclosed {
    type: yesno
    sql: ${TABLE}.isclosed ;;
  }

  dimension: isconverted {
    type: yesno
    sql: ${TABLE}.isconverted ;;
  }

  dimension: isdeleted {
    type: yesno
    sql: ${TABLE}.isdeleted ;;
  }

  dimension: ishighpriority {
    type: yesno
    sql: ${TABLE}.ishighpriority ;;
  }

  dimension: isreminderset {
    type: yesno
    sql: ${TABLE}.isreminderset ;;
  }

  dimension: istask {
    type: yesno
    sql: ${TABLE}.istask ;;
  }

  dimension: isunreadbyowner {
    type: yesno
    sql: ${TABLE}.isunreadbyowner ;;
  }

  dimension: isvisibleinselfservice {
    type: yesno
    sql: ${TABLE}.isvisibleinselfservice ;;
  }

  dimension: jigsaw {
    type: string
    sql: ${TABLE}.jigsaw ;;
  }

  dimension: jigsawcontactid {
    type: string
    sql: ${TABLE}.jigsawcontactid ;;
  }

  dimension: language__c {
    type: string
    sql: ${TABLE}.language__c ;;
  }

  dimension: last_touch_lead_source__c {
    type: string
    sql: ${TABLE}.last_touch_lead_source__c ;;
  }

  dimension: lastactivitydate {
    type: string
    sql: ${TABLE}.lastactivitydate ;;
  }

  dimension: lastmodifiedbyid {
    type: string
    sql: ${TABLE}.lastmodifiedbyid ;;
  }

  dimension: lastmodifieddate {
    type: number
    sql: ${TABLE}.lastmodifieddate ;;
  }

  dimension: lastname {
    type: string
    sql: ${TABLE}.lastname ;;
  }

  dimension: lastreferenceddate {
    type: string
    sql: ${TABLE}.lastreferenceddate ;;
  }

  dimension: lastvieweddate {
    type: string
    sql: ${TABLE}.lastvieweddate ;;
  }

  dimension: latitude {
    type: string
    sql: ${TABLE}.latitude ;;
  }

  dimension: lead_score__c {
    type: string
    sql: ${TABLE}.lead_score__c ;;
  }

  dimension: lead_stage__c {
    type: string
    sql: ${TABLE}.lead_stage__c ;;
  }

  dimension: leadsource {
    type: string
    sql: ${TABLE}.leadsource ;;
  }

  dimension: location {
    type: string
    sql: ${TABLE}.location ;;
  }

  dimension: longitude {
    type: string
    sql: ${TABLE}.longitude ;;
  }

  dimension: master_bid__c {
    type: string
    sql: ${TABLE}.master_bid__c ;;
  }

  dimension: masterrecordid {
    type: string
    sql: ${TABLE}.masterrecordid ;;
  }

  dimension: merchant_strategic_priorities_needs_pa__c {
    type: string
    sql: ${TABLE}.merchant_strategic_priorities_needs_pa__c ;;
  }

  dimension: middlename {
    type: string
    sql: ${TABLE}.middlename ;;
  }

  dimension: mobilephone {
    type: string
    sql: ${TABLE}.mobilephone ;;
  }

  dimension: mql_date__c {
    type: string
    sql: ${TABLE}.mql_date__c ;;
  }

  dimension: needs_pain_points__c {
    type: string
    sql: ${TABLE}.needs_pain_points__c ;;
  }

  dimension: number_of_employees_lead_qual__c {
    type: string
    sql: ${TABLE}.number_of_employees_lead_qual__c ;;
  }

  dimension: numberofemployees {
    type: number
    sql: ${TABLE}.numberofemployees ;;
  }

  dimension: offer_type_first_touch__c {
    type: string
    sql: ${TABLE}.offer_type_first_touch__c ;;
  }

  dimension: offer_type_last_touch__c {
    type: string
    sql: ${TABLE}.offer_type_last_touch__c ;;
  }

  dimension: opportunity_name_remark__c {
    type: string
    sql: ${TABLE}.opportunity_name_remark__c ;;
  }

  dimension: other_needs_pain_points__c {
    type: string
    sql: ${TABLE}.other_needs_pain_points__c ;;
  }

  dimension: ownerid {
    type: string
    sql: ${TABLE}.ownerid ;;
  }

  dimension: pains_priorities_and_needs__c {
    type: string
    sql: ${TABLE}.pains_priorities_and_needs__c ;;
  }

  dimension: phone {
    type: string
    sql: ${TABLE}.phone ;;
  }

  dimension: photourl {
    type: string
    sql: ${TABLE}.photourl ;;
  }

  dimension: pi__campaign__c {
    type: string
    sql: ${TABLE}.pi__campaign__c ;;
  }

  dimension: pi__comments__c {
    type: string
    sql: ${TABLE}.pi__comments__c ;;
  }

  dimension: pi__conversion_date__c {
    type: number
    sql: ${TABLE}.pi__conversion_date__c ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_pi__conversion_date__c {
    type: sum
    sql: ${pi__conversion_date__c} ;;  }
  measure: average_pi__conversion_date__c {
    type: average
    sql: ${pi__conversion_date__c} ;;  }

  dimension: pi__conversion_object_name__c {
    type: string
    sql: ${TABLE}.pi__conversion_object_name__c ;;
  }

  dimension: pi__conversion_object_type__c {
    type: string
    sql: ${TABLE}.pi__conversion_object_type__c ;;
  }

  dimension: pi__created_date__c {
    type: number
    sql: ${TABLE}.pi__created_date__c ;;
  }

  dimension: pi__first_activity__c {
    type: number
    sql: ${TABLE}.pi__first_activity__c ;;
  }

  dimension: pi__first_search_term__c {
    type: string
    sql: ${TABLE}.pi__first_search_term__c ;;
  }

  dimension: pi__first_search_type__c {
    type: string
    sql: ${TABLE}.pi__first_search_type__c ;;
  }

  dimension: pi__first_touch_url__c {
    type: string
    sql: ${TABLE}.pi__first_touch_url__c ;;
  }

  dimension: pi__grade__c {
    type: string
    sql: ${TABLE}.pi__grade__c ;;
  }

  dimension: pi__last_activity__c {
    type: number
    sql: ${TABLE}.pi__last_activity__c ;;
  }

  dimension: pi__notes__c {
    type: string
    sql: ${TABLE}.pi__notes__c ;;
  }

  dimension: pi__pardot_hard_bounced__c {
    type: yesno
    sql: ${TABLE}.pi__pardot_hard_bounced__c ;;
  }

  dimension: pi__score__c {
    type: number
    sql: ${TABLE}.pi__score__c ;;
  }

  dimension: pi__url__c {
    type: string
    sql: ${TABLE}.pi__url__c ;;
  }

  dimension: pi__utm_campaign__c {
    type: string
    sql: ${TABLE}.pi__utm_campaign__c ;;
  }

  dimension: pi__utm_content__c {
    type: string
    sql: ${TABLE}.pi__utm_content__c ;;
  }

  dimension: pi__utm_medium__c {
    type: string
    sql: ${TABLE}.pi__utm_medium__c ;;
  }

  dimension: pi__utm_source__c {
    type: string
    sql: ${TABLE}.pi__utm_source__c ;;
  }

  dimension: pi__utm_term__c {
    type: string
    sql: ${TABLE}.pi__utm_term__c ;;
  }

  dimension: postalcode {
    type: string
    sql: ${TABLE}.postalcode ;;
  }

  dimension: primaryaccountid {
    type: string
    sql: ${TABLE}.primaryaccountid ;;
  }

  dimension: primarywhoid {
    type: string
    sql: ${TABLE}.primarywhoid ;;
  }

  dimension: priority {
    type: string
    sql: ${TABLE}.priority ;;
  }

  dimension: products_interested_in__c {
    type: string
    sql: ${TABLE}.products_interested_in__c ;;
  }

  dimension: rating {
    type: string
    sql: ${TABLE}.rating ;;
  }

  dimension: referral_code__c {
    type: string
    sql: ${TABLE}.referral_code__c ;;
  }

  dimension: reminderdatetime {
    type: number
    sql: ${TABLE}.reminderdatetime ;;
  }

  dimension: review_changing_online_payment_gateway__c {
    type: string
    sql: ${TABLE}.review_changing_online_payment_gateway__c ;;
  }

  dimension: role_in_the_decision_making_process__c {
    type: string
    sql: ${TABLE}.role_in_the_decision_making_process__c ;;
  }

  dimension: salutation {
    type: string
    sql: ${TABLE}.salutation ;;
  }

  dimension: segments__c {
    type: string
    sql: ${TABLE}.segments__c ;;
  }

  dimension: startdatetime {
    type: number
    sql: ${TABLE}.startdatetime ;;
  }

  dimension: startup_industry__c {
    type: string
    sql: ${TABLE}.startup_industry__c ;;
  }

  dimension: startup_industry_others__c {
    type: string
    sql: ${TABLE}.startup_industry_others__c ;;
  }

  dimension: state {
    type: string
    sql: ${TABLE}.state ;;
  }

  dimension: statecode {
    type: string
    sql: ${TABLE}.statecode ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: street {
    type: string
    sql: ${TABLE}.street ;;
  }

  dimension: sub_reason__c {
    type: string
    sql: ${TABLE}.sub_reason__c ;;
  }

  dimension: subject {
    type: string
    sql: ${TABLE}.subject ;;
  }

  dimension: suffix {
    type: string
    sql: ${TABLE}.suffix ;;
  }

  dimension: systemmodstamp {
    type: number
    sql: ${TABLE}.systemmodstamp ;;
  }

  dimension: title {
    type: string
    sql: ${TABLE}.title ;;
  }

  dimension: total_funding_raised__c {
    type: number
    sql: ${TABLE}.total_funding_raised__c ;;
  }

  dimension: total_merchant_addressable_revenue_lead__c {
    type: string
    sql: ${TABLE}.total_merchant_addressable_revenue_lead__c ;;
  }

  dimension: total_merchant_tpv_monthly_local__c {
    type: string
    sql: ${TABLE}.total_merchant_tpv_monthly_local__c ;;
  }

  dimension: total_merchant_volume_monthly__c {
    type: string
    sql: ${TABLE}.total_merchant_volume_monthly__c ;;
  }

  dimension: total_merchant_volume_monthly_local__c {
    type: string
    sql: ${TABLE}.total_merchant_volume_monthly_local__c ;;
  }

  dimension: unqualified_reasons__c {
    type: string
    sql: ${TABLE}.unqualified_reasons__c ;;
  }

  dimension: update_to_ad__c {
    type: yesno
    sql: ${TABLE}.update_to_ad__c ;;
  }

  dimension: user_id__c {
    type: string
    sql: ${TABLE}.user_id__c ;;
  }

  dimension: utm_campaign__c {
    type: string
    sql: ${TABLE}.utm_campaign__c ;;
  }

  dimension: utm_campaign_checked__c {
    type: string
    sql: ${TABLE}.utm_campaign_checked__c ;;
  }

  dimension: utm_campaign_first_touch__c {
    type: string
    sql: ${TABLE}.utm_campaign_first_touch__c ;;
  }

  dimension: utm_campaign_last_touch__c {
    type: string
    sql: ${TABLE}.utm_campaign_last_touch__c ;;
  }

  dimension: utm_child_campaign_id__c {
    type: string
    sql: ${TABLE}.utm_child_campaign_id__c ;;
  }

  dimension: utm_content_checked__c {
    type: string
    sql: ${TABLE}.utm_content_checked__c ;;
  }

  dimension: utm_content_first_touch__c {
    type: string
    sql: ${TABLE}.utm_content_first_touch__c ;;
  }

  dimension: utm_content_last_touch__c {
    type: string
    sql: ${TABLE}.utm_content_last_touch__c ;;
  }

  dimension: utm_medium_checked__c {
    type: string
    sql: ${TABLE}.utm_medium_checked__c ;;
  }

  dimension: utm_medium_first_touch__c {
    type: string
    sql: ${TABLE}.utm_medium_first_touch__c ;;
  }

  dimension: utm_medium_last_touch__c {
    type: string
    sql: ${TABLE}.utm_medium_last_touch__c ;;
  }

  dimension: utm_source_checked__c {
    type: string
    sql: ${TABLE}.utm_source_checked__c ;;
  }

  dimension: utm_source_first_touch__c {
    type: string
    sql: ${TABLE}.utm_source_first_touch__c ;;
  }

  dimension: utm_source_last_touch__c {
    type: string
    sql: ${TABLE}.utm_source_last_touch__c ;;
  }

  dimension: utm_term_checked__c {
    type: string
    sql: ${TABLE}.utm_term_checked__c ;;
  }

  dimension: utm_term_first_touch__c {
    type: string
    sql: ${TABLE}.utm_term_first_touch__c ;;
  }

  dimension: utm_term_last_touch__c {
    type: string
    sql: ${TABLE}.utm_term_last_touch__c ;;
  }

  dimension: vc_backed_status__c {
    type: string
    sql: ${TABLE}.vc_backed_status__c ;;
  }

  dimension: visitor_id__c {
    type: string
    sql: ${TABLE}.visitor_id__c ;;
  }

  dimension: want_to_talk_to_sales__c {
    type: string
    sql: ${TABLE}.want_to_talk_to_sales__c ;;
  }

  dimension: website {
    type: string
    sql: ${TABLE}.website ;;
  }

  dimension: weekly_update__c {
    type: string
    sql: ${TABLE}.weekly_update__c ;;
  }

  dimension: weekly_update_history__c {
    type: string
    sql: ${TABLE}.weekly_update_history__c ;;
  }

  dimension: weekly_update_status__c {
    type: string
    sql: ${TABLE}.weekly_update_status__c ;;
  }

  dimension: what_else_is_customer_interested_in__c {
    type: string
    sql: ${TABLE}.what_else_is_customer_interested_in__c ;;
  }

  dimension: what_else_is_the_customer_interested_in__c {
    type: string
    sql: ${TABLE}.what_else_is_the_customer_interested_in__c ;;
  }

  dimension: whatid {
    type: string
    sql: ${TABLE}.whatid ;;
  }

  dimension: where_are_you_selling_your_products_now__c {
    type: string
    sql: ${TABLE}.where_are_you_selling_your_products_now__c ;;
  }

  dimension: whoid {
    type: string
    sql: ${TABLE}.whoid ;;
  }

  dimension: working_date__c {
    type: string
    sql: ${TABLE}.working_date__c ;;
  }

  dimension: xendit_account_email__c {
    type: string
    sql: ${TABLE}.xendit_account_email__c ;;
  }

  dimension: xendit_entity__c {
    type: string
    sql: ${TABLE}.xendit_entity__c ;;
  }
  measure: count {
    type: count
    drill_fields: [id, firstname, lastname, middlename]
  }
}
