# The name of this view in Looker is "Int Gs Macro T2 Lifetime V2 Comparison"
view: transaction_backbone_v2_comparison {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: transform__business_intelligence_transaction_volumes.int_gs_macro_t2_lifetime_v2_comparison ;;
  suggestions: no
  # No primary key is defined for this view. In order to join this view in an Explore,
  # define primary_key: yes on a dimension that has no repeated values.

  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Business ID" in Explore.

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: dt_month {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: cast(dt_month as timestamp) ;;
  }


  dimension: grainularity {
    type: string
    sql: ${TABLE}.grainularity ;;
  }

  dimension: master_acc_sales_rep_name {
    type: string
    sql: ${TABLE}.master_acc_sales_rep_name ;;
  }

  dimension: master_account_id {
    type: string
    sql: ${TABLE}.master_account_id ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: reference_match {
    type: yesno
    sql: ${TABLE}.reference_match ;;
  }

  dimension: sales_rep_name {
    type: string
    sql: ${TABLE}.sales_rep_name ;;
  }

  dimension: tpv_match {
    type: yesno
    sql: ${TABLE}.tpv_match ;;
  }

  measure: v1_reference_count {
    type: sum
    sql: ${TABLE}.v1_reference_count ;;
  }


  measure: v2_reference_count {
    type: sum
    sql: ${TABLE}.v2_reference_count ;;
  }

  measure: v1_tpv_sum {
    type: sum
    sql: ${TABLE}.v1_tpv_sum ;;
  }

  measure: v2_tpv_sum {
    type: sum
    sql: ${TABLE}.v2_tpv_sum ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: count {
    type: count
    drill_fields: [master_acc_sales_rep_name, sales_rep_name]
  }
}
