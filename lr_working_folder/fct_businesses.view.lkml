view: fct_businesses {
  sql_table_name: transform__business_intelligence.fct_businesses ;;
  suggestions: no

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: count_fct_businesses {
    type: number
    sql: ${TABLE}."count" ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: failed_transactions {
    type: number
    sql: ${TABLE}.failed_transactions ;;
  }

  dimension: first_18_months_count {
    type: number
    sql: ${TABLE}.first_18_months_count ;;
  }

  dimension: first_18_months_tpv {
    type: number
    sql: ${TABLE}.first_18_months_tpv ;;
  }

  dimension: first_month_count {
    type: number
    sql: ${TABLE}.first_month_count ;;
  }

  dimension: first_month_tpv {
    type: number
    sql: ${TABLE}.first_month_tpv ;;
  }

  dimension_group: first_successful_test_transaction {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.first_successful_test_transaction ;;
  }

  dimension_group: first_test_transaction {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.first_test_transaction ;;
  }

  dimension_group: first_transaction {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.first_transaction ;;
  }

  dimension_group: go_live {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.go_live ;;
  }

  dimension: go_live_to_first_transaction {
    type: number
    sql: ${TABLE}.go_live_to_first_transaction ;;
  }

  dimension: has_more_than_5_lifetime_transactions {
    type: yesno
    sql: ${TABLE}.has_more_than_5_lifetime_transactions ;;
  }

  dimension: is_active {
    type: yesno
    sql: ${TABLE}.is_active ;;
  }

  dimension: is_churned {
    type: yesno
    sql: ${TABLE}.is_churned ;;
  }

  dimension: is_dormant {
    type: yesno
    sql: ${TABLE}.is_dormant ;;
  }

  dimension: is_new_active {
    type: yesno
    sql: ${TABLE}.is_new_active ;;
  }

  dimension: is_non_starting {
    type: yesno
    sql: ${TABLE}.is_non_starting ;;
  }

  dimension: is_reactivated {
    type: yesno
    sql: ${TABLE}.is_reactivated ;;
  }

  dimension: is_retained_m1 {
    type: yesno
    sql: ${TABLE}.is_retained_m1 ;;
  }

  dimension: is_retained_m3 {
    type: yesno
    sql: ${TABLE}.is_retained_m3 ;;
  }

  dimension: is_retained_m6 {
    type: yesno
    sql: ${TABLE}.is_retained_m6 ;;
  }

  dimension: is_testing {
    type: yesno
    sql: ${TABLE}.is_testing ;;
  }

  dimension_group: latest_transaction {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.latest_transaction ;;
  }

  dimension: lifetime_tpv_usd_rank {
    type: number
    sql: ${TABLE}.lifetime_tpv_usd_rank ;;
  }

  dimension: lifetime_transaction_count_rank {
    type: number
    sql: ${TABLE}.lifetime_transaction_count_rank ;;
  }

  dimension: prior_period_transactions {
    type: number
    sql: ${TABLE}.prior_period_transactions ;;
  }

  dimension: second_month_count {
    type: number
    sql: ${TABLE}.second_month_count ;;
  }

  dimension: second_month_tpv {
    type: number
    sql: ${TABLE}.second_month_tpv ;;
  }

  dimension: third_month_count {
    type: number
    sql: ${TABLE}.third_month_count ;;
  }

  dimension: third_month_tpv {
    type: number
    sql: ${TABLE}.third_month_tpv ;;
  }

  dimension: this_and_prior_period_transactions {
    type: number
    sql: ${TABLE}.this_and_prior_period_transactions ;;
  }

  dimension: this_period_transactions {
    type: number
    sql: ${TABLE}.this_period_transactions ;;
  }

  dimension: tpv_usd {
    type: number
    sql: ${TABLE}.tpv_usd ;;
  }

  dimension: unique_product_activated {
    type: number
    sql: ${TABLE}.unique_product_activated ;;
  }

  dimension: unique_product_activation_request {
    type: number
    sql: ${TABLE}.unique_product_activation_request ;;
  }

  dimension: unique_transaction_products_offered {
    type: number
    sql: ${TABLE}.unique_transaction_products_offered ;;
  }

  dimension: unique_transaction_products_used_count {
    type: number
    sql: ${TABLE}.unique_transaction_products_used_count ;;
  }

  dimension: usd_usd {
    type: number
    sql: ${TABLE}.usd_usd ;;
  }

  dimension: used_channel_name {
    type: string
    sql: ${TABLE}.used_channel_name ;;
  }

  dimension: used_product_subtype {
    type: string
    sql: ${TABLE}.used_product_subtype ;;
  }

  dimension: used_product_type {
    type: string
    sql: ${TABLE}.used_product_type ;;
  }

  measure: count {
    type: count
    drill_fields: [used_channel_name]
  }
}
