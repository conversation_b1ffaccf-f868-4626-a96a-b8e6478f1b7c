#!/usr/bin/env python3
"""
Check for missing view files in salesforce folder between source and DBR repositories
"""

import os
import subprocess

SOURCE_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def get_view_files(repo_path):
    """Get all .view.lkml files in salesforce folder"""
    result = subprocess.run(
        ["find", f"{repo_path}/salesforce", "-name", "*.view.lkml"],
        capture_output=True, text=True
    )
    files = result.stdout.strip().split('\n') if result.stdout.strip() else []
    # Extract just the filename for comparison
    return [os.path.basename(f) for f in files if f]

def main():
    print("🔍 CHECKING SALESFORCE VIEW FILES")
    print("=" * 50)
    
    # Get files from both repositories
    source_files = set(get_view_files(SOURCE_REPO))
    dbr_files = set(get_view_files(DBR_REPO))
    
    print(f"📊 SOURCE REPO: {len(source_files)} view files")
    print(f"📊 DBR REPO: {len(dbr_files)} view files")
    
    # Find missing files
    missing_in_dbr = source_files - dbr_files
    extra_in_dbr = dbr_files - source_files
    common_files = source_files & dbr_files
    
    print(f"\n📋 COMPARISON RESULTS:")
    print(f"  ✅ Common files: {len(common_files)}")
    print(f"  ❌ Missing in DBR: {len(missing_in_dbr)}")
    print(f"  ➕ Extra in DBR: {len(extra_in_dbr)}")
    
    if missing_in_dbr:
        print(f"\n❌ FILES MISSING IN DBR REPOSITORY:")
        for file in sorted(missing_in_dbr):
            print(f"  - {file}")
    
    if extra_in_dbr:
        print(f"\n➕ EXTRA FILES IN DBR REPOSITORY:")
        for file in sorted(extra_in_dbr):
            print(f"  - {file}")
    
    if not missing_in_dbr and not extra_in_dbr:
        print(f"\n🎉 PERFECT MATCH! All view files are synchronized.")
    
    # Show all common files for verification
    print(f"\n✅ COMMON FILES ({len(common_files)}):")
    for file in sorted(common_files):
        print(f"  ✓ {file}")

if __name__ == "__main__":
    main()
