- pipeline: "Static Application Security Testing"
  on: "EVENT"
  events:
  - type: "PUSH"
    refs:
    - "refs/pull/*"
  priority: "NORMAL"
  auto_clear_cache: true
  fetch_all_refs: true
  trigger_conditions:
  - trigger_condition: "ALWAYS"
  actions:
  - action: "Run SAST"
    type: "RUN_DOCKER_CONTAINER"
    region: "ap-southeast-1"
    docker_image_name: "xendit/sast"
    docker_image_tag: "latest"
    inline_commands: "run.sh"
    volume_mappings:
    - "/:/usr/deploy"
    shell: "SH"
    variables:
    - key: "OWNER"
      value: "data-engineering"
      type: "VAR"
    trigger_conditions:
    - trigger_variable_key: "BUDDY_EXECUTION_PULL_REQUEST_BASE_BRANCH"
      # trigger_variable_value: "master"
      # trigger_condition: "VAR_IS"
      zone_id: ""
      trigger_hours: []
      trigger_days: []
    integration_hash: "5e85761fc5f225000f97dd99"
  - action: "Send notification to channel"
    type: "SLACK"
    trigger_time: "ON_FAILURE"
    content: "$BUDDY_PIPELINE_NAME execution failed. Please check details in Sonarqube"
    blocks: "[{\"type\":\"section\",\"fields\":[{\"type\":\"mrkdwn\",\"text\":\"*Failed execution:* <$BUDDY_EXECUTION_URL|Execution #$BUDDY_EXECUTION_ID $BUDDY_EXECUTION_COMMENT>\"},{\"type\":\"mrkdwn\",\"text\":\"*Pipeline:* <$BUDDY_PIPELINE_URL|$BUDDY_PIPELINE_NAME>\"},{\"type\":\"mrkdwn\",\"text\":\"*Sonar Link:* <https://sonar.tidnex.com/project/issues?id=$BUDDY_PROJECT_NAME&resolved=false&types=VULNERABILITY&severities=BLOCKER|$BUDDY_PROJECT_NAME>\"},{\"type\":\"mrkdwn\",\"text\":\"*Project:* <$BUDDY_PROJECT_URL|$BUDDY_PROJECT_NAME>\"}]}]"
    channel: "CEHUP5S1J"
    channel_name: "data-engg-alerts"
    trigger_conditions:
    - trigger_condition: "ALWAYS"
    integration_hash: "5ea6524a42eba4000eb35fbb"
