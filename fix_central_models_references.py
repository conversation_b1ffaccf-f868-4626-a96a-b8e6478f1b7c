#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix all references from 'central-models' to 'central-models-dbr' in the looker-transaction-models-dbr repository
"""
import os
import re
import subprocess

def find_files_with_central_models():
    """Find all .lkml files that reference central-models"""
    cmd = 'find . -name "*.lkml" -exec grep -l "central-models[^-]" {} \\;'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    files = []
    for line in result.stdout.strip().split('\n'):
        if line:
            files.append(line)
    
    return files

def fix_central_models_references():
    """Fix all central-models references to central-models-dbr"""
    
    # Find all files that need fixing
    files_to_fix = find_files_with_central_models()
    
    print(f"🔍 Found {len(files_to_fix)} files with central-models references")
    
    total_files_updated = 0
    total_replacements = 0
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            continue
            
        print(f"\n🔄 Processing {file_path}...")
        
        # Read file content
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Count occurrences before replacement
        original_count = len(re.findall(r'//central-models(?!-dbr)', content))
        
        if original_count == 0:
            print(f"  ✅ No references to fix in {file_path}")
            continue
        
        # Replace central-models with central-models-dbr (but not if it's already central-models-dbr)
        updated_content = re.sub(r'//central-models(?!-dbr)', '//central-models-dbr', content)
        
        # Count occurrences after replacement
        remaining_count = len(re.findall(r'//central-models(?!-dbr)', updated_content))
        replacements_made = original_count - remaining_count
        
        if replacements_made > 0:
            # Write updated content back to file
            with open(file_path, 'w') as f:
                f.write(updated_content)
            
            print(f"  ✅ Updated {file_path}: {replacements_made} replacements")
            total_files_updated += 1
            total_replacements += replacements_made
        else:
            print(f"  ⚠️  No changes needed in {file_path}")
    
    print(f"\n🎯 Summary:")
    print(f"Files updated: {total_files_updated}")
    print(f"Total replacements: {total_replacements}")
    
    return total_files_updated, total_replacements

def verify_changes():
    """Verify that all references have been updated correctly"""
    print(f"\n🔍 Verifying changes...")
    
    # Check for any remaining central-models references (excluding central-models-dbr)
    cmd = 'find . -name "*.lkml" -exec grep -H "//central-models[^-]" {} \\;'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.stdout.strip():
        print(f"❌ Still found some central-models references:")
        print(result.stdout)
        return False
    else:
        print(f"✅ All central-models references have been updated to central-models-dbr")
        return True

if __name__ == "__main__":
    print("🚀 Starting central-models reference fix...")
    
    files_updated, total_replacements = fix_central_models_references()
    
    if files_updated > 0:
        verify_changes()
    else:
        print("✅ No files needed updating!")
