version: 2
sources:
- name: clean__fee_service
  freshness:
    warn_after:
      count: 24
      period: hour
    error_after:
      count: 48
      period: hour
  loader: Xendit batch_pipeline
  meta:
    source_group: prod_postgres
    owner: DE Team
  tables:
  - name: default_rate_settings
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique
  - name: product_rate_setting
     loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique:
          where: "dt >= __two_months_ago__"
  - name: vat_rate_setting
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique:
          where: "dt >= __two_months_ago__"
  - name: migrations
  - name: billable_transaction_detail
    loaded_at_field: updated
    columns:
    - name: id
      tests:
      - unique:
          where: "dt >= __two_months_ago__"
