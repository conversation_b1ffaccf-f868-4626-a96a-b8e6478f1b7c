view: sequential_numbers {
  derived_table: {
    sql: with L0   as (select 1 as c union ALL select 1),       -- 2 rows.
           L1   as (select 1 as c from L0 as A, L0 as B),       -- 4 rows.
           L2   as (select 1 as c from L1 as A, L1 as B),       -- 16 rows.
           L3   as (select 1 as c from L2 as A, L2 as B),       -- 256 rows
           L4   as (select 1 as c from L3 as A, L3 as B),       -- 65536 rows.
           L5   as (select 1 as c from L2 as A, L4 as B),       -- 2^20 (1m) rows.
           L6   as (select 1 as c from L2 as A, L5 as B),       -- 2^24 (16m) rows
           nums as (select row_number() over(order by c) as n from L6)
      select * from nums ;;
    persist_for: "9999 hours"
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: n {
    type: number
    sql: ${TABLE}.n ;;
  }

  set: detail {
    fields: [n]
  }
}
