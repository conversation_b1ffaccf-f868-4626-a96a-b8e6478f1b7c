
view: ach_adapter__ph_instapay_top_recipients_acwp_per_day_monthly {
  derived_table: {
    sql: WITH recent_data AS (
        SELECT
          id,
          json_extract_scalar(destination, '$.channel_code') AS channel_code,
          json_extract_scalar(details, '$.transactionStatus') AS ach_transaction_status,
          created,
          updated,
          DATE(created) AS created_date
        FROM
          clean__ach_adapter_live.phbananafintech__transactions
        WHERE
          json_extract_scalar(destination, '$.channel_code') IN (
              'GCASH',
              'PAYMAYA',
              'ONB',
              'BPI',
              'UBP',
              'LBP',
              'MET',
              'GOTYME',
              'ABP',
              'SHOPEE',
              'PNB',
              'GRABPAY',
              'SEA',
              'SEC',
              'RCBC'
          )
          AND created >= current_date - INTERVAL '30' DAY
          AND created < current_date + INTERVAL '1' DAY
          AND direction = 'DEBIT'
      )
      SELECT
        created_date,
        channel_code,
        ROUND(CAST(COUNT(CASE WHEN ach_transaction_status = 'ACWP' THEN 1 END) AS DOUBLE) / CAST(COUNT(id) AS DOUBLE) * 100, 2)  AS pending_percentage_per_day
      FROM
        recent_data
      GROUP BY
        created_date,
        channel_code
      ORDER BY
        created_date,
        channel_code ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: created_date {
    type: string
    sql: ${TABLE}.created_date ;;
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: pending_percentage_per_day {
    type: number
    sql: ${TABLE}.pending_percentage_per_day ;;
  }

  set: detail {
    fields: [
        created_date,
  channel_code,
  pending_percentage_per_day
    ]
  }
}
