# The name of this view in Looker is "Xpi Statement Entries"
view: bank__statement_entries_dev {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.

  derived_table: {
    sql:
      SELECT * FROM
      (
        (SELECT 'XPI' as brand_partner, * from clean__bank_dev.xpi__statement_entries)
      UNION
        (SELECT 'XRI' as brand_partner, * from clean__bank_dev.xri__statement_entries)
      UNION
        (SELECT 'BILLEASE' as brand_partner, * from clean__bank_dev.billease__statement_entries)
      );;
  }

  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
  # Here's what a typical dimension looks like in LookML.
  # A dimension is a groupable field that can be used to filter query results.
  # This dimension will be called "Account ID" in Explore.

  dimension: account_id {
    type: string
    sql: ${TABLE}.account_id ;;
  }

  dimension: brand_partner {
    type:  string
    sql:  ${TABLE}.brand_partner ;;
  }
  dimension: adjustment {
    type: string
    sql: ${TABLE}.adjustment ;;
  }

  dimension: amount {
    type: number
    sql: ${TABLE}.amount ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_amount {
    type: sum
    sql: ${amount} ;;  }
  measure: average_amount {
    type: average
    sql: ${amount} ;;  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  dimension: currency {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: deposit {
    type: string
    sql: ${TABLE}.deposit ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: details {
    type: string
    sql: ${TABLE}.details ;;
  }

  dimension: direction {
    type: string
    sql: ${TABLE}.direction ;;
  }

  dimension: failure_code {
    type: string
    sql: ${TABLE}.failure_code ;;
  }

  dimension: fee {
    type: string
    sql: ${TABLE}.fee ;;
  }

  dimension: hold_id {
    type: string
    sql: ${TABLE}.hold_id ;;
  }

  dimension: interest {
    type: string
    sql: ${TABLE}.interest ;;
  }

  dimension: metadata {
    type: string
    sql: ${TABLE}.metadata ;;
  }

  dimension: payment {
    type: string
    sql: ${TABLE}.payment ;;
  }

  dimension: purchase {
    type: string
    sql: ${TABLE}.purchase ;;
  }

  dimension: qr_payment {
    type: string
    sql: ${TABLE}.qr_payment ;;
  }

  dimension: qr_payment_id {
    type:  string
    sql:  json_value(${TABLE}.qr_payment, 'lax $.id' DEFAULT '' ON ERROR) ;;
  }

  dimension: qr_payment_standard {
    type:  string
    sql:  json_value(${TABLE}.qr_payment, 'lax $.standard' DEFAULT '' ON ERROR) ;;
  }

  dimension: qr_payment_merchant_name {
    type:  string
    sql:  json_value(${TABLE}.qr_payment, 'lax $.merchant_name' DEFAULT '' ON ERROR) ;;
  }

  dimension: qr_payment_local_instrument_code {
    type:  string
    sql:  json_value(${TABLE}.qr_payment, 'lax $.local_instrument_code' DEFAULT '' ON ERROR) ;;
  }

  dimension: reference_id {
    type: string
    sql: ${TABLE}.reference_id ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: tax {
    type: string
    sql: ${TABLE}.tax ;;
  }

  dimension: transfer {
    type: string
    sql: ${TABLE}.transfer ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }

  dimension: withdrawal {
    type: string
    sql: ${TABLE}.withdrawal ;;
  }
  measure: count {
    type: count
    drill_fields: [id]
  }
}
