
view: bank__ph_instapay_top_recipients_latency_monthly {
  derived_table: {
    sql: WITH combined_data AS (
          SELECT
              json_extract_scalar(transfer, '$.destination.channel_code') AS channel_code,
              created,
              updated,
              date_diff('second', created, updated) AS latency_seconds
          FROM
              clean__bank_live.xpi__statement_entries
          WHERE
              json_extract_scalar(transfer, '$.destination.channel_code') IN (
                  'GCASH',
                  'PAYMAYA',
                  'ONB',
                  'BPI',
                  'UBP',
                  'LBP',
                  'MET',
                  'GOTYME',
                  'ABP',
                  'SHOPEE',
                  'PNB',
                  'GRABPAY',
                  'SEA',
                  'SEC',
                  'RCBC'
              )
              AND created >= current_date - INTERVAL '30' DAY
              AND created < current_date + INTERVAL '1' DAY
          UNION ALL
          SELECT
              json_extract_scalar(transfer, '$.destination.channel_code') AS channel_code,
              created,
              updated,
              date_diff('second', created, updated) AS latency_seconds
          FROM
              clean__bank_live.xri__statement_entries
          WHERE
              json_extract_scalar(transfer, '$.destination.channel_code') IN (
                  'GCASH',
                  'PAYMAYA',
                  'ONB',
                  'BPI',
                  'UBP',
                  'LBP',
                  'MET',
                  'GOTYME',
                  'ABP',
                  'SHOPEE',
                  'PNB',
                  'GRABPAY',
                  'SEA',
                  'SEC',
                  'RCBC'
              )
              AND created >= current_date - INTERVAL '30' DAY
              AND created < current_date + INTERVAL '1' DAY
      ),
      percentile_data AS (
          SELECT
              channel_code,
              approx_percentile(latency_seconds, 0.99) AS p99_latency,
              approx_percentile(latency_seconds, 0.95) AS p95_latency,
              approx_percentile(latency_seconds, 0.90) AS p90_latency
          FROM
              combined_data
          GROUP BY
              channel_code
      )
      SELECT
          channel_code,
          p99_latency,
          p95_latency,
          p90_latency
      FROM
          percentile_data ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: p99_latency {
    type: number
    sql: ${TABLE}.p99_latency ;;
  }

  dimension: p95_latency {
    type: number
    sql: ${TABLE}.p95_latency ;;
  }

  dimension: p90_latency {
    type: number
    sql: ${TABLE}.p90_latency ;;
  }

  set: detail {
    fields: [
        channel_code,
  p99_latency,
  p95_latency,
  p90_latency
    ]
  }
}
