
view: ach_adapter__ph_instapay_top_recipients_overall_status_rates {
  derived_table: {
    sql: WITH recent_data AS (
        SELECT
          id,
          json_extract_scalar(destination, '$.channel_code') AS channel_code,
          json_extract_scalar(details, '$.transactionStatus') AS ach_transaction_status,
          created,
          updated
        FROM
          clean__ach_adapter_live.phbananafintech__transactions
        WHERE
          json_extract_scalar(destination, '$.channel_code') IN (
              'GCASH',
              'PAYMAYA',
              'ONB',
              'BPI',
              'UBP',
              'LBP',
              'MET',
              'GOTYME',
              'ABP',
              'SHOPEE',
              'PNB',
              'GRABPAY',
              'SEA',
              'SEC',
              'RCBC'
          )
          AND created >= current_date - INTERVAL '30' DAY
          AND created < current_date + INTERVAL '1' DAY
          AND direction = 'DEBIT'
      ),
      aggregated_data AS (
        SELECT
          channel_code,
          COUNT(id) AS total_count,
          COUNT(CASE WHEN ach_transaction_status = 'RJCT' THEN 1 END) AS rejected_count,
          COUNT(CASE WHEN ach_transaction_status = 'ACWP' THEN 1 END) AS pending_count,
          COUNT(CASE WHEN ach_transaction_status = 'ACTC' THEN 1 END) AS accepted_count
        FROM
          recent_data
        GROUP BY
          channel_code
      )
      SELECT
        channel_code,
        ROUND((CAST(accepted_count as DOUBLE) / CAST(total_count AS DOUBLE)) * 100, 2) AS success_rate,
        ROUND((CAST(rejected_count as DOUBLE) / CAST(total_count AS DOUBLE)) * 100, 2) AS fail_rate,
        ROUND((CAST(pending_count AS DOUBLE) / CAST(total_count AS DOUBLE)) * 100, 2) AS pending_rate
      FROM
        aggregated_data ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: success_rate {
    type: number
    sql: ${TABLE}.success_rate ;;
  }

  dimension: fail_rate {
    type: number
    sql: ${TABLE}.fail_rate ;;
  }

  dimension: pending_rate {
    type: number
    sql: ${TABLE}.pending_rate ;;
  }

  set: detail {
    fields: [
        channel_code,
	success_rate,
	fail_rate,
	pending_rate
    ]
  }
}
