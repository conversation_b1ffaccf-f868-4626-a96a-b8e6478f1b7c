
view: ach_adapter__ph_instapay_top_recipients_pending_average_resolution_time {
  derived_table: {
    sql: WITH recent_data AS (
        SELECT
          id,
          json_extract_scalar(destination, '$.channel_code') AS channel_code,
          json_extract_scalar(details, '$.transactionStatus') AS ach_transaction_status,
          created,
          updated
        FROM
          clean__ach_adapter_live.phbananafintech__transactions
        WHERE
         json_extract_scalar(destination, '$.channel_code') IN (
              'GCASH',
              'PAYMAYA',
              'ONB',
              'BPI',
              'UBP',
              'LBP',
              'MET',
              'GOTYME',
              'ABP',
              'SHOPEE',
              'PNB',
              'GRABPAY',
              'SEA',
              'SEC',
              'RCBC'
          )
          AND created >= current_date - INTERVAL '30' DAY
          AND created < current_date + INTERVAL '1' DAY
          AND direction = 'DEBIT'
          AND status != 'UNKNOWN'
      ),
      pending_to_accepted AS (
        SELECT
          *,
          DATE_DIFF('day', created, updated) AS resolution_time
        FROM
          recent_data
        WHERE
          ach_transaction_status = 'ACWP'
      )
      SELECT
        channel_code,
        ROUND(AVG(resolution_time), 2) AS avg_resolution_time
      FROM
        pending_to_accepted
      GROUP BY
        channel_code
      ORDER BY
        channel_code ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: channel_code {
    type: string
    sql: ${TABLE}.channel_code ;;
  }

  dimension: avg_resolution_time {
    type: number
    sql: ${TABLE}.avg_resolution_time ;;
  }

  set: detail {
    fields: [
      channel_code,
      avg_resolution_time
    ]
  }
}
