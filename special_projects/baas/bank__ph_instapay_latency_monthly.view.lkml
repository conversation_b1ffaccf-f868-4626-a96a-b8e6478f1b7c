
view: bank__ph_instapay_latency_monthly {
  derived_table: {
    sql: WITH combined_data AS (
          SELECT
              created,
              updated,
              date_diff('second', created, updated) AS latency_seconds
          FROM
              clean__bank_live.xpi__statement_entries
          WHERE
              created >= current_date - INTERVAL '30' DAY
              AND created < current_date + INTERVAL '1' DAY
              AND json_extract_scalar(transfer, '$.transfer_method') = 'PH_INSTAPAY'
              AND direction = 'DEBIT'
          UNION ALL
          SELECT
              created,
              updated,
              date_diff('second', created, updated) AS latency_seconds
          FROM
              clean__bank_live.xri__statement_entries
          WHERE
              created >= current_date - INTERVAL '30' DAY
              AND created < current_date + INTERVAL '1' DAY
              AND json_extract_scalar(transfer, '$.transfer_method') = 'PH_INSTAPAY'
              AND direction = 'DEBIT'
          UNION ALL
          SELECT
              created,
              updated,
              date_diff('second', created, updated) AS latency_seconds
          FROM
              clean__bank_live.billease__statement_entries
          WHERE
              created >= current_date - INTERVAL '30' DAY
              AND created < current_date + INTERVAL '1' DAY
              AND json_extract_scalar(qr_payment, '$.transfer_method') = 'PH_INSTAPAY'
              AND direction = 'DEBIT'
      ),
      percentile_data AS (
          SELECT
              approx_percentile(latency_seconds, 0.99) AS p99_latency,
              approx_percentile(latency_seconds, 0.95) AS p95_latency,
              approx_percentile(latency_seconds, 0.90) AS p90_latency
          FROM
              combined_data
      )
      SELECT
          p99_latency,
          p95_latency,
          p90_latency
      FROM
          percentile_data ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: p99_latency {
    type: number
    sql: ${TABLE}.p99_latency ;;
  }

  dimension: p95_latency {
    type: number
    sql: ${TABLE}.p95_latency ;;
  }

  dimension: p90_latency {
    type: number
    sql: ${TABLE}.p90_latency ;;
  }

  set: detail {
    fields: [
        p99_latency,
	p95_latency,
	p90_latency
    ]
  }
}
