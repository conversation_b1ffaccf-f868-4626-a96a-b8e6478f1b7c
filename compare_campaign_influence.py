#!/usr/bin/env python3
"""
Compare ag_campaign_influence.view.lkml files between source and DBR repositories
"""

import difflib

SOURCE_FILE = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models/salesforce/ag_campaign_influence.view.lkml"
DBR_FILE = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr/salesforce/ag_campaign_influence.view.lkml"

def read_file(file_path):
    """Read file content"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.readlines()

def main():
    print("🔍 COMPARING ag_campaign_influence.view.lkml FILES")
    print("=" * 60)
    
    # Read both files
    source_lines = read_file(SOURCE_FILE)
    dbr_lines = read_file(DBR_FILE)
    
    print(f"📊 SOURCE FILE: {len(source_lines)} lines")
    print(f"📊 DBR FILE: {len(dbr_lines)} lines")
    
    # Generate unified diff
    diff = list(difflib.unified_diff(
        dbr_lines, source_lines,
        fromfile='DBR Repository',
        tofile='Source Repository',
        lineterm=''
    ))
    
    if not diff:
        print("\n🎉 FILES ARE IDENTICAL!")
        return
    
    print(f"\n📋 DIFFERENCES FOUND:")
    print("-" * 40)
    
    # Key differences to highlight
    key_differences = []
    
    for line in diff:
        if line.startswith('@@'):
            print(f"\n📍 {line}")
        elif line.startswith('-'):
            print(f"❌ DBR:    {line[1:].rstrip()}")
            if 'date_format' in line or 'yyyy-MM-dd' in line:
                key_differences.append("Date format difference")
            elif 'accountid' in line and 'contactid' in line:
                key_differences.append("AccountID dimension SQL reference")
            elif 'LEFT JOIN' in line:
                key_differences.append("Missing JOIN clauses")
        elif line.startswith('+'):
            print(f"✅ SOURCE: {line[1:].rstrip()}")
    
    print(f"\n🎯 KEY DIFFERENCES IDENTIFIED:")
    for i, diff in enumerate(set(key_differences), 1):
        print(f"  {i}. {diff}")
    
    print(f"\n📝 SUMMARY:")
    print(f"  - Source file has more comprehensive SQL with additional JOINs")
    print(f"  - Source file has proper accountid dimension")
    print(f"  - Source file has different date format string")
    print(f"  - Source file has better dimension organization")

if __name__ == "__main__":
    main()
