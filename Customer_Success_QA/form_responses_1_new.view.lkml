view: form_responses_1_new {
  sql_table_name: clean__google_sheets_customer_success_qa_form.new_qa_l1_responses ;;
  suggestions: no

  dimension_group: timestamp {
    label: "Timestamp"
    type: time
    timeframes: [
      raw,
      time,
      hour_of_day,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.timestamp ;;
  }

  dimension: generalist_name {
    label: "Generalist Name"
    type: string
    sql: ${TABLE}.generalist_name ;;
  }

  dimension: country_of_operation {
    label: "Country of Operation"
    type: string
    sql: ${TABLE}.country_of_operation ;;
  }

  dimension: year__e_g__2021 {
    label: "Year"
    type: number
    sql: CAST(${TABLE}.year__e_g__2021 AS INTEGER) ;;
  }

  dimension: qa_month {
    label: "QA Month"
    type: number
    sql: CAST(${TABLE}.qa_month  AS INTEGER);;
  }

  dimension: raw_qa_timestamp {
    type: string
    sql: case WHEN ${qa_month}<10 THEN CONCAT(${year__e_g__2021},'-0',${qa_month},'-01')
              ELSE  CONCAT(${year__e_g__2021},'-',${qa_month},'-01') END;;
    }

  dimension_group: qa_timestamp{
    label: "QA Timestamp"
    type: time
    timeframes: [
      month,
      quarter,
      year
    ]
    sql: ${raw_qa_timestamp} ;;
  }

  dimension: channel {
    label: "Channel"
    type: string
    sql: ${TABLE}.channel ;;
  }

  dimension: issue_question____in_ticket_type_format__issue__withdrawal__autowithdrawal_failure {
    label: "Issue/Question"
    type: string
    sql: ${TABLE}.issue_question____in_ticket_type_format__issue__withdrawal__autowithdrawal_failure ;;
  }

  dimension: ticket_link {
    label: "Ticket Link"
    type: string
    sql: ${TABLE}.ticket_link ;;
  }

 dimension: product_knowledge__30 {
    label: "Product Knowledge (30%)"
    type: number
    sql: CAST(${TABLE}.product_knowledge__30 AS INTEGER) ;;
  }

  dimension: notes_for_pk {
    label: "Notes for PK"
    type: string
    sql: ${TABLE}.notes_for_pk ;;
  }

  dimension: issue_resolution__30 {
    label: "Issue Resolution (30%)"
    type: number
    sql: ${TABLE}.issue_resolution__30 ;;
  }

  dimension: notes_for_ir {
    label: "Notes for IR"
    type: string
    sql: ${TABLE}.notes_for_ir ;;
  }

  dimension: empathy__5 {
    label: "Empathy (5%)"
    type: number
    sql: CAST(${TABLE}.empathy__5 AS INTEGER) ;;
  }

  dimension: notes_for_em {
    label: "Notes for EM"
    type: string
    sql: ${TABLE}.notes_for_em ;;
  }

  dimension: responsiveness__5 {
    label: "Responsiveness (5%)"
    type: number
    sql: CAST(${TABLE}.responsiveness__5 AS INTEGER) ;;
  }

  dimension: notes_for_rs {
    label: "Notes for RS"
    type: string
    sql: ${TABLE}.notes_for_rs ;;
  }

  dimension: communication__20 {
    label: "Communication (20%)"
    type: number
    sql: CAST(${TABLE}.communication__20 AS INTEGER) ;;
  }

  dimension: notes_for_co {
    label: "Notes for CO"
    type: string
    sql: ${TABLE}.notes_for_co ;;
  }

  dimension: zendesk_fields__10 {
    label: "Zendesk Fields (10%)"
    type: number
    sql: CAST(${TABLE}.zendesk_fields__10 AS INTEGER) ;;
  }

  dimension: notes_for_zf {
    label: "Notes for ZF"
    type: string
    sql: ${TABLE}.notes_for_zf ;;
  }

  dimension: ztp__zero_tolerance_policy__ticket {
    label: "ZTP (Zero Tolerance Policy) Ticket?"
    type: string
    sql: ${TABLE}.ztp__zero_tolerance_policy__ticket ;;
  }

  dimension: notes_for_ztp {
    label: "Notes for ZTP"
    type: string
    sql: ${TABLE}.notes_for_ztp ;;
  }

  dimension: positive_points {
    label: "Positive Points?"
    type: string
    sql: ${TABLE}.positive_points ;;
  }

  dimension: product_knowledge {
    label: "Product Knowledge"
    type: number
    sql: CAST(${TABLE}.product_knowledge AS INTEGER);;
  }

  dimension: issue_resolution {
    label: "Issue Resolution"
    type: number
    sql: CAST(${TABLE}.issue_resolution AS INTEGER) ;;
  }

  dimension: empathy {
    label: "Empathy"
    type: number
    sql: CAST(${TABLE}.empathy AS INTEGER);;
  }

  dimension: responsiveness {
    label: "Responsiveness"
    type: number
    sql: CAST(${TABLE}.responsiveness AS INTEGER);;
  }

  dimension: communication {
    label: "Communication"
    type: number
    sql: CAST(${TABLE}.communication AS INTEGER);;
  }

  dimension: accuracy {
    label: "Accuracy"
    type: number
    sql: CAST(${TABLE}.accuracy AS INTEGER);;
  }

  dimension: total {
    label: "Total"
    type: number
    sql: CAST(${TABLE}.total AS INTEGER);;
  }

  dimension: ticket_accuracy__pass_not_pass {
    label: "Ticket Accuracy (Pass/Not Pass)"
    type: string
    sql: ${TABLE}.ticket_accuracy__pass_not_pass ;;
  }

  #dimension: airbyte_additional_properties {
    #label: "Airbyte Additional Properties"
    #type: string
    #sql: ${TABLE}.airbyte_additional_properties ;;
  #}

  measure: count {
    type: count
    drill_fields: [generalist_name]
  }
}
