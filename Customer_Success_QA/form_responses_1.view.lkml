view: form_responses_1 {
    sql_table_name: clean__google_sheets_customer_success_qa_form.form_responses_1 ;;
    suggestions: no

    dimension: channel {
      type: string
      sql: ${TABLE}.channel ;;
    }

    dimension: country_of_operation {
      type: string
      sql: ${TABLE}.country_of_operation ;;
    }

    dimension: em_1 {
      type: number
      sql: CAST(${TABLE}.em_1 AS INTEGER);;
    }

    dimension: empathy {
      type: number
      sql: CAST(${TABLE}.empathy AS INTEGER);;
    }

    dimension: generalist_name {
      type: string
      sql: ${TABLE}.generalist_name ;;
    }

    dimension: ir_1 {
      type: number
      sql: CAST(${TABLE}.ir_1 AS INTEGER);;
    }

    dimension: ir_2 {
      type: number
      sql: CAST(${TABLE}.ir_2 AS INTEGER);;
    }

    dimension: ir_3 {
      type: number
      sql: CAST(${TABLE}.ir_3 AS INTEGER);;
    }

    dimension: ticket_type {
      type: string
      sql: ${TABLE}.issue_or_question ;;
    }

    dimension: issue_resolution {
      type: number
      sql: CAST(${TABLE}.issue_resolution AS INTEGER);;
    }

    dimension: notes_for_em_1 {
      type: string
      sql: ${TABLE}.notes_for_em_1 ;;
    }

    dimension: notes_for_ir_1 {
      type: string
      sql: ${TABLE}.notes_for_ir_1 ;;
    }

    dimension: notes_for_ir_2 {
      type: string
      sql: ${TABLE}.notes_for_ir_2 ;;
    }

    dimension: notes_for_ir_3 {
      type: string
      sql: ${TABLE}.notes_for_ir_3 ;;
    }

    dimension: notes_for_pk_1 {
      type: string
      sql: ${TABLE}.notes_for_pk_1 ;;
    }

    dimension: notes_for_pk_2 {
      type: string
      sql: ${TABLE}.notes_for_pk_2 ;;
    }

    dimension: notes_for_rs_1 {
      type: string
      sql: ${TABLE}.notes_for_rs_1 ;;
    }

    dimension: notes_for_sd_1 {
      type: string
      sql: ${TABLE}.notes_for_sd_1 ;;
    }

    dimension: notes_for_sd_2 {
      type: string
      sql: ${TABLE}.notes_for_sd_2 ;;
    }

    dimension: notes_for_sd_3 {
      type: string
      sql: ${TABLE}.notes_for_sd_3 ;;
    }

    dimension: pk_1 {
      type: number
      sql: CAST(${TABLE}.pk_1 AS INTEGER);;
    }

    dimension: pk_2 {
      type: number
      sql: CAST(${TABLE}.pk_2 AS INTEGER);;
    }

    dimension: positive_points {
      type: string
      sql: ${TABLE}.positive_points ;;
    }

    dimension: product_knowledge {
      type: number
      sql: CAST(${TABLE}.product_knowledge AS INTEGER);;
    }

    dimension: qa_month {
      type: number
      sql: ${TABLE}.qa_month ;;
    }

    dimension: responsiveness {
      type: number
      sql: CAST(${TABLE}.responsiveness AS INTEGER);;
    }

    dimension: structure_and_documentation{
      type: number
      sql: CAST(${TABLE}.structure_and_documentation AS INTEGER);;
    }

    dimension: rs_1 {
      type: number
      sql: CAST(${TABLE}.rs_1 AS INTEGER);;
    }

    dimension: sd_1 {
      type: number
      sql: CAST(${TABLE}.sd_1 AS INTEGER);;
    }

    dimension: sd_2 {
      type: number
      sql: CAST(${TABLE}.sd_2 AS INTEGER);;
    }

    dimension: sd_3 {
      type: number
      sql: CAST(${TABLE}.sd_3 AS INTEGER);;
    }

    dimension: ticket_link {
      type: string
      sql: ${TABLE}.ticket_link ;;
    }

    dimension_group: timestamp {
      type: time
      timeframes: [
        raw,
        time,
        hour_of_day,
        date,
        week,
        month,
        quarter,
        year
      ]
      sql: ${TABLE}.timestamp ;;
    }

    dimension: total {
      type: number
      sql: CAST(${TABLE}.total AS INTEGER) ;;
    }

    dimension: year__e_g__2021 {
    label: "Year"
    type: number
    sql: CAST(${TABLE}.year__e_g__2021 AS INTEGER) ;;
    }

    dimension_group: qa_timestamp{
      label: "QA Timestamp"
      type: time
      timeframes: [
        month,
        quarter,
        year
      ]
      sql: CONCAT(${year__e_g__2021},'-',${qa_month},'-01');;
    }

    measure: count {
      type: count
      drill_fields: [generalist_name]
    }
  }
