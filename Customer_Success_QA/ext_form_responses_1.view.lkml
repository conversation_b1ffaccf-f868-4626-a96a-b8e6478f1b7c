view: ext_form_responses_1 {

    derived_table: {
      sql: select * from clean__google_sheets_customer_success_qa_form.new_qa_l1_responses
      inner join clean__google_sheets_customer_success_qa_form.form_responses_1
      ON new_qa_l1_responses.ticket_link = form_responses_1.ticket_link
;;
}
      dimension: qa_month {
        description: "Unique ID for each user that has ordered"
        type: number
        sql: ${TABLE} ;;
      }
    }
