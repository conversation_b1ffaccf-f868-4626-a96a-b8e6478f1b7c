---
- dashboard: cps_dashboard__revenue_details_databricks
  title: CPS Dashboard - Revenue Details (databricks)
  layout: newspaper
  preferred_viewer: dashboards-next
  load_configuration: wait
  description: ''
  preferred_slug: W7j9tr1pIor2UB4Gfaa67M
  elements:
  - name: Monthly % Ratio Revenue per Channel (v2)
    title: Monthly % Ratio Revenue per Channel (v2)
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [revenue.revenue_usd, revenue.created_local_month, revenue.product_type,
      revenue.channel_name]
    pivots: [revenue.product_type, revenue.channel_name]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      businesses.go_live_status: ''
      revenue.revenue_usd: ">0"
    sorts: [revenue.created_local_month desc, revenue.product_type]
    limit: 500
    column_limit: 50
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: percent
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Type: revenue.product_type
      Channel Name: revenue.channel_name
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 49
    col: 0
    width: 12
    height: 8
  - name: Revenue Cumulative per Day of Month (v21)
    title: Revenue Cumulative per Day of Month (v21)
    model: xendit-databricks
    explore: revenue
    type: looker_line
    fields: [revenue.revenue_usd, revenue.created_local_day_of_month, revenue.created_local_month]
    pivots: [revenue.created_local_month]
    filters:
      businesses.go_live_status: ''
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_local_date: 4 month
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      revenue.revenue_usd: ">0"
    sorts: [revenue.created_local_month, revenue.created_local_day_of_month]
    limit: 500
    column_limit: 50
    dynamic_fields:
    - category: table_calculation
      expression: if(${revenue.revenue_usd}>=0, running_total(${revenue.revenue_usd}),
        null)
      label: Running Total
      value_format:
      value_format_name: usd
      _kind_hint: measure
      table_calculation: running_total
      _type_hint: number
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    show_null_points: false
    interpolation: linear
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    hidden_fields: [revenue.revenue_usd]
    listen:
      Product Type: revenue.product_type
      Channel Name: revenue.channel_name
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 8
    col: 0
    width: 12
    height: 8
  - name: Monthly Revenue per BID (v2)
    title: Monthly Revenue per BID (v2)
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [businesses.business_name, revenue.created_local_month, revenue.revenue_usd]
    pivots: [businesses.business_name]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      businesses.go_live_status: ''
    sorts: [revenue.created_local_month desc, businesses.business_name]
    limit: 500
    column_limit: 50
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    y_axes: []
    listen:
      Product Type: revenue.product_type
      Channel Name: revenue.channel_name
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 31
    col: 12
    width: 12
    height: 8
  - name: Monthly Revenue per Channel (v2)
    title: Monthly Revenue per Channel (v2)
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [revenue.revenue_usd, revenue.created_local_month, revenue.channel_name]
    pivots: [revenue.channel_name]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      businesses.go_live_status: ''
      revenue.revenue_usd: ">0"
    sorts: [revenue.created_local_month desc, revenue.channel_name]
    limit: 500
    column_limit: 50
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Type: revenue.product_type
      Channel Name: revenue.channel_name
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 49
    col: 12
    width: 12
    height: 8
  - name: Monthly Revenue per Product (v2)
    title: Monthly Revenue per Product (v2)
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [revenue.revenue_usd, revenue.created_local_month, revenue.product_type]
    pivots: [revenue.product_type]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      businesses.go_live_status: ''
      revenue.revenue_usd: ">0"
    sorts: [revenue.product_type, revenue.created_local_month desc]
    limit: 500
    column_limit: 50
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Product Type: revenue.product_type
      Channel Name: revenue.channel_name
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 39
    col: 12
    width: 12
    height: 8
  - name: Monthly % Ratio Revenue per Product (v2)
    title: Monthly % Ratio Revenue per Product (v2)
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [revenue.revenue_usd, revenue.created_local_month, revenue.product_type]
    pivots: [revenue.product_type]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      businesses.go_live_status: ''
      revenue.revenue_usd: ">0"
    sorts: [revenue.created_local_month desc, revenue.product_type]
    limit: 500
    column_limit: 50
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: percent
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    listen:
      Channel Name: revenue.channel_name
      Product Type: revenue.product_type
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 31
    col: 0
    width: 12
    height: 8
  - name: Revenue under AM
    title: Revenue under AM
    model: xendit-databricks
    explore: revenue
    type: looker_column
    fields: [revenue.created_local_month, custom_ultimate_parent_name, revenue.revenue_usd]
    pivots: [custom_ultimate_parent_name]
    fill_fields: [revenue.created_local_month]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 15 months
      revenue.is_manually_billed: 'no'
    sorts: [custom_ultimate_parent_name, revenue.created_local_month desc]
    limit: 500
    column_limit: 200
    dynamic_fields:
    - category: dimension
      expression: if(${sales_attribution.sf_rollup_ultimate_parent_id}="0015j000012qKhS",${sales_attribution.sf_business_name},${sales_attribution.sf_rollup_ultimate_parent_brand})
      label: Custom Ultimate Parent Name
      value_format:
      value_format_name:
      dimension: custom_ultimate_parent_name
      _kind_hint: dimension
      _type_hint: string
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_series: [Data Product - 11 - revenue.revenue_usd, Forex - 13 - revenue.revenue_usd,
      Early Settlement - 03 - revenue.revenue_usd, XenCapital - 10 - revenue.revenue_usd]
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Channel Name: revenue.channel_name
      Product Type: revenue.product_type
      Created Local Date: revenue.created_local_date
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 0
    col: 0
    width: 24
    height: 8
  - name: ''
    type: text
    title_text: ''
    subtitle_text: ''
    body_text: '[{"type":"p","children":[{"text":"Note: For Revenue per BID it might
      be "},{"text":"inaccurate","bold":true},{"text":" if the account have >50 accounts
      (calculating Sub Account and Master Account)"}]}]'
    rich_content_json: '{"format":"slate"}'
    row: 16
    col: 0
    width: 24
    height: 2
  - name: " (Copy)"
    type: text
    title_text: " (Copy)"
    subtitle_text: ''
    body_text: '[{"type":"p","children":[{"text":"Note: For Revenue per Channels it
      might be "},{"text":"inaccurate","bold":true},{"text":" if the channels have
      >50 activated (usually happened on when customer use Disbursement product)"}]}]'
    rich_content_json: '{"format":"slate"}'
    row: 47
    col: 0
    width: 24
    height: 2
  - name: Cumulative Revenue Merchants per Month of Year
    title: Cumulative Revenue Merchants per Month of Year
    model: xendit-databricks
    explore: revenue
    type: looker_line
    fields: [revenue.revenue_usd, custom_ultimate_parent_name, revenue.created_local_day_of_month,
      revenue.created_local_month]
    pivots: [custom_ultimate_parent_name, revenue.created_local_month]
    filters:
      businesses.go_live_status: ''
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_local_date: 2 months
      revenue.created_utc_date: 2 years
      revenue.is_manually_billed: 'no'
      revenue.revenue_usd: ">0"
    sorts: [custom_ultimate_parent_name, revenue.created_local_day_of_month]
    limit: 500
    column_limit: 50
    dynamic_fields:
    - category: table_calculation
      expression: if(${revenue.revenue_usd}>=0, running_total(${revenue.revenue_usd}),
        null)
      label: Running Total
      value_format:
      value_format_name: usd
      _kind_hint: measure
      table_calculation: running_total
      _type_hint: number
      is_disabled: false
    - category: dimension
      expression: extract_months(${revenue.created_local_month})
      label: Month
      value_format:
      value_format_name:
      dimension: month
      _kind_hint: dimension
      _type_hint: number
    - category: dimension
      expression: if(${sales_attribution.sf_rollup_ultimate_parent_id}="0015j000012qKhS",${sales_attribution.sf_business_name},${sales_attribution.sf_rollup_ultimate_parent_brand})
      label: Custom Ultimate Parent Name
      value_format:
      value_format_name:
      dimension: custom_ultimate_parent_name
      _kind_hint: dimension
      _type_hint: string
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: right
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    show_null_points: false
    interpolation: linear
    y_axes: []
    x_axis_zoom: true
    y_axis_zoom: true
    hide_legend: false
    ordering: none
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    show_row_numbers: true
    truncate_column_names: false
    hide_totals: false
    hide_row_totals: false
    table_theme: editable
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    defaults_version: 1
    hidden_pivots: {}
    hidden_fields: [revenue.revenue_usd]
    listen:
      Channel Name: revenue.channel_name
      Product Type: revenue.product_type
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 8
    col: 12
    width: 12
    height: 8
  - name: Monthly
    type: text
    title_text: Monthly
    subtitle_text: ''
    body_text: ''
    row: 29
    col: 0
    width: 24
    height: 2
  - name: Cumulative Target vs Revenue
    title: Cumulative Target vs Revenue
    model: xendit-databricks
    explore: sales_performance
    type: looker_line
    fields: [sales_performance.actual, sales_performance.target, sales_performance.target_week]
    filters:
      sales_performance.metric: PG Revenue - Overall,Pipeline Revenue
      sales_performance.target_quarter: 1 quarters
      sales_performance.target: ">0"
      sales_performance.team: ''
      sales_performance.target_date: 1 quarters
      salesforce_users.country: ''
      salesforce_users.division: ''
      division: ''
    sorts: [sales_performance.target_week]
    limit: 500
    column_limit: 50
    total: true
    dynamic_fields:
    - category: table_calculation
      expression: "${running_total_actual_revenue}/${target_revenue_per_quarter}"
      label: Achievement %
      value_format:
      value_format_name: percent_2
      _kind_hint: measure
      table_calculation: achievement
      _type_hint: number
    - category: dimension
      expression: if(${salesforce_users.division}="ID IS - Inbound","ID SDR",${salesforce_users.division})
      label: Division
      value_format:
      value_format_name:
      dimension: division
      _kind_hint: dimension
      _type_hint: string
    - category: dimension
      expression: |-
        if(${sales_performance.metric}="PG Revenue - Overall", "Revenue",
          if(${sales_performance.metric}="PG Revenue - Acquisition", "Revenue Acquisition",
            if(${sales_performance.metric}="PG Revenue - Existing", "Revenue Existing",${sales_performance.metric})))
      label: Metric
      value_format:
      value_format_name:
      dimension: metric
      _kind_hint: dimension
      _type_hint: string
    - category: table_calculation
      expression: if(${sales_performance.actual}>=0, running_total(${sales_performance.actual}),
        null)
      label: Running Total - Actual Revenue
      value_format:
      value_format_name: usd_0
      _kind_hint: measure
      table_calculation: running_total_actual_revenue
      _type_hint: number
    - category: table_calculation
      expression: if(${sales_performance.target}>=0, running_total(${sales_performance.target}),
        null)
      label: Running Total - Target Revenue
      value_format:
      value_format_name: usd_0
      _kind_hint: measure
      table_calculation: running_total_target_revenue
      _type_hint: number
    - category: table_calculation
      expression: row()
      label: Row Flag
      value_format:
      value_format_name:
      _kind_hint: dimension
      table_calculation: row_flag
      _type_hint: number
      is_disabled: true
    - category: table_calculation
      expression: index(${running_total_target_revenue},max(row()))
      label: Target Revenue per Quarter
      value_format:
      value_format_name: usd_0
      _kind_hint: measure
      table_calculation: target_revenue_per_quarter
      _type_hint: number
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: ''
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    show_null_points: true
    interpolation: linear
    y_axes: [{label: '', orientation: left, series: [{axisId: running_total_actual_revenue,
            id: running_total_actual_revenue, name: Running Total - Actual Revenue},
          {axisId: running_total_target_revenue, id: running_total_target_revenue,
            name: Running Total - Target Revenue}], showLabels: true, showValues: true,
        unpinAxis: false, tickDensity: default, tickDensityCustom: 5, type: linear},
      {label: !!null '', orientation: right, series: [{axisId: achievement, id: achievement,
            name: Achievement %}], showLabels: true, showValues: true, maxValue: !!null '',
        minValue: !!null '', unpinAxis: false, tickDensity: default, tickDensityCustom: 5,
        type: linear}]
    x_axis_zoom: true
    y_axis_zoom: true
    reference_lines: []
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    enable_conditional_formatting: true
    header_text_alignment: left
    header_font_size: '12'
    rows_font_size: '12'
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_sql_query_menu_options: false
    show_totals: true
    show_row_totals: true
    truncate_header: false
    minimum_column_width: 75
    series_cell_visualizations:
      sales_performance.actual:
        is_active: true
    conditional_formatting: [{type: less than, value: 0.8, background_color: "#FB5607",
        font_color: !!null '', color_application: {collection_id: xendit, palette_id: xendit-diverging-0,
          options: {constraints: {min: {type: minimum}, mid: {type: number, value: 0},
              max: {type: maximum}}, mirror: true, reverse: false, stepped: false}},
        bold: false, italic: false, strikethrough: false, fields: [achievement]},
      {type: between, value: [0.8, 1], background_color: "#FFBE0B", font_color: !!null '',
        color_application: {collection_id: xendit, palette_id: xendit-diverging-0,
          options: {constraints: {min: {type: minimum}, mid: {type: number, value: 0},
              max: {type: maximum}}, mirror: true, reverse: false, stepped: false}},
        bold: false, italic: false, strikethrough: false, fields: !!null ''}, {type: greater
          than, value: 1, background_color: "#4573FF", font_color: !!null '', color_application: {
          collection_id: xendit, palette_id: xendit-diverging-0, options: {constraints: {
              min: {type: minimum}, mid: {type: number, value: 0}, max: {type: maximum}},
            mirror: true, reverse: false, stepped: false}}, bold: false, italic: false,
        strikethrough: false, fields: [achievement]}]
    hidden_pivots: {}
    defaults_version: 1
    hidden_fields: [sales_performance.actual, sales_performance.target, target_revenue_per_quarter]
    listen:
      Target Revenue Email: sales_performance.email
    row: 20
    col: 0
    width: 12
    height: 7
  - name: Target Based
    type: text
    title_text: Target Based
    subtitle_text: ''
    body_text: ''
    row: 18
    col: 0
    width: 24
    height: 2
  - name: " (Copy 2)"
    type: text
    title_text: " (Copy 2)"
    subtitle_text: ''
    body_text: '[{"type":"p","children":[{"text":"Note: For Target data, please use
      the "},{"text":"Target Revenue Email","code":true},{"text":" Filter above (it''s
      different source of data from the rest)"}]}]'
    rich_content_json: '{"format":"slate"}'
    row: 27
    col: 0
    width: 24
    height: 2
  - name: Revenue Table Breakdown
    title: Revenue Table Breakdown
    model: xendit-databricks
    explore: revenue
    type: looker_grid
    fields: [custom_ultimate_parent_name, sales_attribution.rollup_business_id, sales_attribution.sf_rollup_owner_email,
      revenue.revenue_usd, revenue.created_local_month]
    pivots: [revenue.created_local_month]
    fill_fields: [revenue.created_local_month]
    filters:
      businesses.is_internal: 'no'
      businesses.is_soft_deleted_account: 'no'
      revenue.created_utc_date: 15 months
      revenue.is_manually_billed: 'no'
    sorts: [revenue.created_local_month desc, revenue.revenue_usd desc]
    limit: 500
    column_limit: 200
    dynamic_fields:
    - category: dimension
      expression: if(${sales_attribution.sf_rollup_ultimate_parent_id}="0015j000012qKhS",${sales_attribution.sf_business_name},${sales_attribution.sf_rollup_ultimate_parent_brand})
      label: Custom Ultimate Parent Name
      value_format:
      value_format_name:
      dimension: custom_ultimate_parent_name
      _kind_hint: dimension
      _type_hint: string
    query_timezone: Asia/Jakarta
    show_view_names: false
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    limit_displayed_rows: false
    enable_conditional_formatting: false
    header_text_alignment: left
    header_font_size: '12'
    rows_font_size: '12'
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_sql_query_menu_options: false
    show_totals: true
    show_row_totals: true
    truncate_header: false
    minimum_column_width: 75
    series_cell_visualizations:
      revenue.revenue_usd:
        is_active: false
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    legend_position: center
    point_style: none
    show_value_labels: true
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_series: [Data Product - 11 - revenue.revenue_usd, Forex - 13 - revenue.revenue_usd,
      Early Settlement - 03 - revenue.revenue_usd, XenCapital - 10 - revenue.revenue_usd]
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Created Local Date: revenue.created_local_date
      Channel Name: revenue.channel_name
      Product Type: revenue.product_type
      Created Local Day of Month: revenue.created_local_day_of_month
      Rollup Business ID: sales_attribution.rollup_business_id
      Business ID: businesses.business_id
      'Business ID: xenPlatform Master': businesses.master_account_id
      Company Account ID: accounts_company_relationships.account_id
      Sf Rollup Owner Email: sales_attribution.sf_rollup_owner_email
      Sf Rollup Ultimate Parent ID: sales_attribution.sf_rollup_ultimate_parent_id
      Sf Rollup Cps Email: sales_attribution.sf_rollup_cps_email
      Internal Name: businesses.internal_name
      Parent Industry Sector: businesses.parent_industry_sector
      Country of Operation: businesses.country_of_operation
    row: 57
    col: 0
    width: 24
    height: 6
  filters:
  - name: Rollup Business ID
    title: Rollup Business ID
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: sales_attribution.rollup_business_id
  - name: Business ID
    title: Business ID
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: businesses.business_id
  - name: 'Business ID: xenPlatform Master'
    title: 'Business ID: xenPlatform Master'
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: businesses.master_account_id
  - name: Company Account ID
    title: Company Account ID
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
      options: []
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: accounts_company_relationships.account_id
  - name: Sf Rollup Owner Email
    title: Sf Rollup Owner Email
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: sales_attribution.sf_rollup_owner_email
  - name: Sf Rollup Ultimate Parent ID
    title: Sf Rollup Ultimate Parent ID
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: sales_attribution.sf_rollup_ultimate_parent_id
  - name: Target Revenue Email
    title: Target Revenue Email
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: sales_performance
    listens_to_filters: []
    field: sales_performance.email
  - name: Sf Rollup Cps Email
    title: Sf Rollup Cps Email
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: sales_attribution.sf_rollup_cps_email
  - name: Internal Name
    title: Internal Name
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: businesses.internal_name
  - name: Created Local Date
    title: Created Local Date
    type: field_filter
    default_value: 12 month
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
      options: []
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: revenue.created_local_date
  - name: Product Type
    title: Product Type
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: revenue.product_type
  - name: Channel Name
    title: Channel Name
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
      options: []
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: revenue.channel_name
  - name: Parent Industry Sector
    title: Parent Industry Sector
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: businesses.parent_industry_sector
  - name: Created Local Day of Month
    title: Created Local Day of Month
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
      options:
        min: 1
        max: 31
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: revenue.created_local_day_of_month
  - name: Country of Operation
    title: Country of Operation
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: revenue
    listens_to_filters: []
    field: businesses.country_of_operation
