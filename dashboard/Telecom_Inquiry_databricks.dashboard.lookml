---
- dashboard: telkom_inquiry_databricks
  title: Telkom Inquiry (databricks)
  layout: newspaper
  preferred_viewer: dashboards-next
  description: ''
  preferred_slug: HcAQaFKjsG3pZqFrJYwNR2
  elements:
  - title: Annual Money In TPV
    name: Annual Money In TPV
    model: xendit-databricks
    explore: transaction_backbone
    type: looker_column
    fields: [transaction_backbone.product_type, transaction_backbone.tpv, transaction_backbone.created_year]
    pivots: [transaction_backbone.product_type]
    fill_fields: [transaction_backbone.created_year, transaction_backbone.product_type]
    filters: {}
    sorts: [transaction_backbone.product_type, transaction_backbone.created_year,
      transaction_backbone.tpv desc 0]
    limit: 1000
    column_limit: 100
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    enable_conditional_formatting: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Created (UTC) Date: transaction_backbone.dt_date
      Is Soft Deleted Account (Yes / No): businesses.is_soft_deleted_account
      Is Internal (Yes / No): businesses.is_internal
      Status Summary: transaction_backbone.status
      Money Flow: transaction_backbone.money_flow
      Product Type: transaction_backbone.product_type
      Currency: transaction_backbone.currency
    row: 7
    col: 0
    width: 19
    height: 7
  - title: VA Created Count
    name: VA Created Count
    model: xendit-databricks
    explore: virtual_account
    type: single_value
    fields: [virtual_account.count]
    filters:
      virtual_account.count: NOT NULL
      virtual_account.created_year: after 2016/01/01
      virtual_account.status: ACTIVE,CREATING,EXPIRED,INACTIVE,PAID,PENDING,UPDATING
      virtual_account.currency: IDR
    limit: 500
    column_limit: 50
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    show_null_points: true
    interpolation: linear
    defaults_version: 1
    hidden_pivots: {}
    listen: {}
    row: 0
    col: 19
    width: 5
    height: 7
  - title: Annual VA Created Count
    name: Annual VA Created Count
    model: xendit-databricks
    explore: virtual_account
    type: looker_column
    fields: [virtual_account.count, virtual_account.created_year, virtual_account.status]
    pivots: [virtual_account.status]
    filters:
      virtual_account.count: NOT NULL
      virtual_account.created_year: after 2016/01/01
      virtual_account.status: ACTIVE,CREATING,EXPIRED,INACTIVE,PAID,PENDING,UPDATING
      virtual_account.currency: IDR
    sorts: [virtual_account.status, virtual_account.created_year desc]
    limit: 500
    column_limit: 50
    row_total: left
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    show_null_points: true
    interpolation: linear
    defaults_version: 1
    hidden_pivots: {}
    listen: {}
    row: 0
    col: 0
    width: 19
    height: 7
  - title: Paid Count
    name: Paid Count
    model: xendit-databricks
    explore: transaction_backbone
    type: single_value
    fields: [transaction_backbone.count]
    filters: {}
    limit: 1000
    column_limit: 100
    query_timezone: Asia/Jakarta
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    limit_displayed_rows: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Created (UTC) Date: transaction_backbone.dt_date
      Is Soft Deleted Account (Yes / No): businesses.is_soft_deleted_account
      Is Internal (Yes / No): businesses.is_internal
      Status Summary: transaction_backbone.status
      Money Flow: transaction_backbone.money_flow
      Product Type: transaction_backbone.product_type
      Currency: transaction_backbone.currency
    row: 7
    col: 19
    width: 5
    height: 7
  - title: TPV
    name: TPV
    model: xendit-databricks
    explore: transaction_backbone
    type: single_value
    fields: [transaction_backbone.tpv]
    filters: {}
    limit: 1000
    column_limit: 100
    query_timezone: Asia/Jakarta
    custom_color_enabled: true
    show_single_value_title: true
    show_comparison: false
    comparison_type: value
    comparison_reverse_colors: false
    show_comparison_label: true
    enable_conditional_formatting: false
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    show_view_names: false
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    limit_displayed_rows: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Created (UTC) Date: transaction_backbone.dt_date
      Is Soft Deleted Account (Yes / No): businesses.is_soft_deleted_account
      Is Internal (Yes / No): businesses.is_internal
      Status Summary: transaction_backbone.status
      Money Flow: transaction_backbone.money_flow
      Product Type: transaction_backbone.product_type
      Currency: transaction_backbone.currency
    row: 14
    col: 19
    width: 5
    height: 8
  - title: Annual Money Count
    name: Annual Money Count
    model: xendit-databricks
    explore: transaction_backbone
    type: looker_column
    fields: [transaction_backbone.product_type, transaction_backbone.created_year,
      transaction_backbone.count]
    pivots: [transaction_backbone.product_type]
    fill_fields: [transaction_backbone.created_year, transaction_backbone.product_type]
    filters: {}
    sorts: [transaction_backbone.product_type, transaction_backbone.created_year]
    limit: 1000
    column_limit: 100
    query_timezone: Asia/Jakarta
    x_axis_gridlines: false
    y_axis_gridlines: true
    show_view_names: false
    show_y_axis_labels: true
    show_y_axis_ticks: true
    y_axis_tick_density: default
    y_axis_tick_density_custom: 5
    show_x_axis_label: true
    show_x_axis_ticks: true
    y_axis_scale_mode: linear
    x_axis_reversed: false
    y_axis_reversed: false
    plot_size_by_field: false
    trellis: ''
    stacking: normal
    limit_displayed_rows: false
    legend_position: center
    point_style: none
    show_value_labels: false
    label_density: 25
    x_axis_scale: auto
    y_axis_combined: true
    ordering: desc
    show_null_labels: false
    show_totals_labels: true
    show_silhouette: false
    totals_color: "#808080"
    show_row_numbers: true
    transpose: false
    truncate_text: true
    hide_totals: false
    hide_row_totals: false
    size_to_fit: true
    table_theme: white
    enable_conditional_formatting: false
    header_text_alignment: left
    header_font_size: 12
    rows_font_size: 12
    conditional_formatting_include_totals: false
    conditional_formatting_include_nulls: false
    x_axis_zoom: true
    y_axis_zoom: true
    hidden_pivots: {}
    defaults_version: 1
    listen:
      Created (UTC) Date: transaction_backbone.dt_date
      Is Soft Deleted Account (Yes / No): businesses.is_soft_deleted_account
      Is Internal (Yes / No): businesses.is_internal
      Status Summary: transaction_backbone.status
      Money Flow: transaction_backbone.money_flow
      Product Type: transaction_backbone.product_type
      Currency: transaction_backbone.currency
    row: 14
    col: 0
    width: 19
    height: 8
  filters:
  - name: Status Summary
    title: Status Summary
    type: field_filter
    default_value: COMPLETED
    allow_multiple_values: true
    required: false
    ui_config:
      type: advanced
      display: popover
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: transaction_backbone.status
  - name: Created (UTC) Date
    title: Created (UTC) Date
    type: field_filter
    default_value: ''
    allow_multiple_values: true
    required: false
    ui_config:
      type: relative_timeframes
      display: inline
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: transaction_backbone.dt_date
  - name: Is Internal (Yes / No)
    title: Is Internal (Yes / No)
    type: field_filter
    default_value: 'no'
    allow_multiple_values: true
    required: false
    ui_config:
      type: button_toggles
      display: inline
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: businesses.is_internal
  - name: Is Soft Deleted Account (Yes / No)
    title: Is Soft Deleted Account (Yes / No)
    type: field_filter
    default_value: 'no'
    allow_multiple_values: true
    required: false
    ui_config:
      type: button_toggles
      display: inline
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: businesses.is_soft_deleted_account
  - name: Product Type
    title: Product Type
    type: field_filter
    default_value: PayLater,QR Code,Virtual Account,Retail Outlet,eWallet,Direct Debit,Credit
      Card
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: transaction_backbone.product_type
  - name: Money Flow
    title: Money Flow
    type: field_filter
    default_value: Money In
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: transaction_backbone.money_flow
  - name: Currency
    title: Currency
    type: field_filter
    default_value: IDR
    allow_multiple_values: true
    required: false
    ui_config:
      type: checkboxes
      display: popover
    model: xendit-databricks
    explore: transaction_backbone
    listens_to_filters: []
    field: transaction_backbone.currency
