view: brand_consolidation_migrations {
  derived_table: {
    sql:
          SELECT
            *,
            todo[1] as migration_checklist_1,
            todo[1] as migration_checklist_2,
            todo[1] as migration_checklist_3,
            todo[1] as migration_checklist_4,
            todo[1] as migration_checklist_5
          FROM
            clean__xendit_user_service.brandconsolidationmigrations
        ;;
  }

  dimension: id {
    label: "ID"
    type: string
  }

  dimension: business_id {
    label: "Business ID"
    type: string
  }

  dimension_group: created {
    label: "Created"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
  }

  dimension_group: updated {
    label: "Updated"
    type: time
    timeframes: [raw, time, date, week, month, quarter, year, day_of_month]
  }

  dimension: requested {
    label: "Requested"
    type: yesno
  }

  dimension: migration_checklist_1 {
    label: "Migration Checklist 1"
    type: yesno
  }

  dimension: migration_checklist_2 {
    label: "Migration Checklist 2"
    type: yesno
  }
  dimension: migration_checklist_3 {
    label: "Migration Checklist 3"
    type: yesno
  }
  dimension: migration_checklist_4 {
    label: "Migration Checklist 4"
    type: yesno
  }
  dimension: migration_checklist_5 {
    label: "Migration Checklist 5"
    type: yesno
  }
}
