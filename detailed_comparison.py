#!/usr/bin/env python3
"""
<PERSON>ript to create detailed comparison and generate update patches for LookML files
"""
import os
import re
import sys
from pathlib import Path

def extract_full_definitions(file_path):
    """Extract full dimension and measure definitions from a LookML file"""
    definitions = {}

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Split content into lines for better parsing
        lines = content.split('\n')
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Check if this line starts a dimension or measure
            match = re.match(r'(dimension(?:_group)?|measure)\s*:\s*(\w+)\s*{', line)
            if match:
                type_name = match.group(1)
                element_name = match.group(2)

                # Find the complete block
                block_lines = [lines[i]]
                brace_count = 1
                i += 1

                while i < len(lines) and brace_count > 0:
                    current_line = lines[i]
                    block_lines.append(current_line)

                    # Count braces
                    brace_count += current_line.count('{') - current_line.count('}')
                    i += 1

                definitions[element_name] = {
                    'type': type_name,
                    'definition': '\n'.join(block_lines)
                }
            else:
                i += 1

    except Exception as e:
        print(f"Error reading {file_path}: {e}")

    return definitions

def generate_update_script(source_repo, target_repo, relative_path):
    """Generate update instructions for a specific file"""
    source_file = os.path.join(source_repo, relative_path)
    target_file = os.path.join(target_repo, relative_path)
    
    if not os.path.exists(source_file) or not os.path.exists(target_file):
        return None
    
    source_defs = extract_full_definitions(source_file)
    target_defs = extract_full_definitions(target_file)
    
    missing_elements = []
    for name, definition in source_defs.items():
        if name not in target_defs:
            missing_elements.append(definition)
    
    return missing_elements

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 detailed_comparison.py <relative_file_path>")
        sys.exit(1)
    
    relative_path = sys.argv[1]
    source_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
    target_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"
    
    missing_elements = generate_update_script(source_repo, target_repo, relative_path)
    
    if missing_elements:
        print(f"Missing elements in {relative_path}:")
        print("=" * 80)
        for i, element in enumerate(missing_elements):
            print(f"{i+1}. {element['type']} definition:")
            print(element['definition'])
            print("-" * 40)
    else:
        print(f"No missing elements found in {relative_path}")

if __name__ == "__main__":
    main()
