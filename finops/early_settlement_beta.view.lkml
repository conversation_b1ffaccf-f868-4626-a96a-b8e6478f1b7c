view: early_settlement_beta {
  derived_table: {
    sql: SELECT business_id,
                type,
                amount,
                payment_date,
                settled_at,
                settlement_date as estimated_settlement_date,
                is_switcher_payment,
                channel_name,
                dt,
                currency,
                is_credit,
                payment_id,
                reference,
                status,
                account_identifier

        FROM clean__tcdb.transaction
        -- note: this only contains customers that migrate from t3 to t4
        ;;
    }

  dimension: business_id  {
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: amount  {
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: channel_name  {
    type: string
    sql: ${TABLE}.channel_name ;;
  }

  dimension: currency  {
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: is_credit  {
    type: yesno
    sql: ${TABLE}.is_credit ;;
  }

  dimension: type  {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dt ;;
  }

  dimension_group: payment_date {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.payment_date ;;
  }

  dimension_group: settled_at {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.settled_at ;;
  }

    dimension_group: settlement_date {
      type: time
      timeframes: [
        raw,
        time,
        date,
        week,
        month,
        quarter,
        year
      ]
      sql: ${TABLE}.settlement_date ;;
    }

  dimension: is_early_settled {
    type: yesno
    sql:  date_diff(hour, ${TABLE}.payment_date, ${TABLE}.settled_at) < 1;;
    description: "if less than an hour, consider instant"
  }

  dimension: is_repaid_estimated  {
    type: yesno
    sql: date_diff(day,${TABLE}.payment_date, now()) >= 2 ;;
    description: "if 2 days passed payment date, then considered repaid"
  }

  dimension: is_switcher_payment  {
    type: yesno
    sql: ${TABLE}.is_switcher_payment ;;
  }

  dimension: type_and_channel_name {
    type: string
    sql: ${TABLE}.type || '-' || ${TABLE}.channel_name ;;
  }

  measure: sum_amount  {
    type: sum
    sql: ${TABLE}.amount ;;
    drill_fields: [business_id, amount, type, channel_name, payment_date_time, settled_at_time, dt_date]
  }

  parameter: business_id_search {
    type: string
    suggest_explore: xenplatform_relationships
    suggest_dimension: xenplatform_relationships.master_acc_business
    suggest_persist_for: "24 hours"
  }

  dimension: filtered_business_id {
    hidden: yes
    type: yesno
    sql:  ${business_id} IN (
            SELECT sub_acc_business
            FROM  clean__xendit_platform_service_live.xenplatformrelationships
            WHERE master_acc_business = {{ business_id_search._parameter_value }}) ;;
  }
  }