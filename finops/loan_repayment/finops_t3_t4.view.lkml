view: finops_t3_t4 {
  derived_table: {
    sql:  select * from transform__finops_automation.manual_transaction;;
  }

  dimension: id {
    label: "Transaction Id"
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: amount {
    label: "Amount"
    type: number
    sql: ${TABLE}.amount ;;
  }

  dimension: business_id {
    label: "Business ID"
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: payment_id {
    label: "Payment Id"
    type: string
    sql: ${TABLE}.business_id ;;
  }

  dimension: type {
    label: "Transaction Type"
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: currency {
    label: "Currency"
    type: string
    sql: ${TABLE}.currency ;;
  }

  dimension: reference_type {
    label: "Reference Type"
    type: string
    sql: ${TABLE}.reference_type ;;
  }

  dimension: status {
    label: "Status"
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: version {
    label: "Version"
    type: string
    sql: ${TABLE}.version ;;
  }

  dimension: business_name {
    label: "Business Name"
    type: string
    sql: ${TABLE}.business_name ;;
  }

  dimension_group: created {
    label: "Created"
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }
}
