view: ag_tickets_ops {
  sql_table_name: clean__zendesk.tickets ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: affected_transaction {
    type: string
    sql: ${TABLE}.affected_transaction ;;
  }

  dimension: allow_attachments {
    type: yesno
    sql: ${TABLE}.allow_attachments ;;
  }

  dimension: allow_channelback {
    type: yesno
    sql: ${TABLE}.allow_channelback ;;
  }

  dimension: assignee_id {
    type: number
    sql: ${TABLE}.assignee_id ;;
  }

  dimension: brand_id {
    type: number
    sql: ${TABLE}.brand_id ;;
  }

  dimension: collaborator_ids {
    type: string
    sql: ${TABLE}.collaborator_ids ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension: custom_fields {
    type: string
    sql: ${TABLE}.custom_fields ;;
  }

  dimension: deleted_ticket_form_id {
    type: number
    sql: ${TABLE}.deleted_ticket_form_id ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: due_at {
    type: string
    sql: ${TABLE}.due_at ;;
  }

  dimension: email_cc_ids {
    type: string
    sql: ${TABLE}.email_cc_ids ;;
  }

  dimension: escalation {
    type: string
    sql: ${TABLE}.escalation ;;
  }

  dimension: external_id {
    type: string
    sql: ${TABLE}.external_id ;;
  }

  dimension: fields {
    type: string
    sql: ${TABLE}.fields ;;
  }

  dimension: follower_ids {
    type: string
    sql: ${TABLE}.follower_ids ;;
  }

  dimension: followup_ids {
    type: string
    sql: ${TABLE}.followup_ids ;;
  }

  dimension: forum_topic_id {
    type: string
    sql: ${TABLE}.forum_topic_id ;;
  }

  dimension: group_id {
    type: number
    sql: ${TABLE}.group_id ;;
  }

  dimension: has_incidents {
    type: yesno
    sql: ${TABLE}.has_incidents ;;
  }

  dimension: is_public {
    type: yesno
    sql: ${TABLE}.is_public ;;
  }

  dimension_group: loaded {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.loaded_at ;;
  }

  dimension: organization_id {
    type: number
    sql: ${TABLE}.organization_id ;;
  }

  dimension: priority {
    type: string
    sql: ${TABLE}.priority ;;
  }

  dimension: problem_id {
    type: string
    sql: ${TABLE}.problem_id ;;
  }

  dimension: raw_subject {
    type: string
    sql: ${TABLE}.raw_subject ;;
  }

  dimension: recipient {
    type: string
    sql: ${TABLE}.recipient ;;
  }

  dimension: requester_email {
    type: string
    sql: ${TABLE}.requester_email ;;
  }

  dimension: requester_id {
    type: number
    sql: ${TABLE}.requester_id ;;
  }

  dimension: requester_name {
    type: string
    sql: ${TABLE}.requester_name ;;
  }

  dimension: requester_organization_id {
    type: number
    sql: ${TABLE}.requester_organization_id ;;
  }

  dimension: requester_phone {
    type: string
    sql: ${TABLE}.requester_phone ;;
  }

  dimension: requester_role {
    type: string
    sql: ${TABLE}.requester_role ;;
  }

  dimension: satisfaction_rating {
    type: string
    sql: ${TABLE}.satisfaction_rating ;;
  }

  dimension: sharing_agreement_ids {
    type: string
    sql: ${TABLE}.sharing_agreement_ids ;;
  }

  dimension: solution {
    type: string
    sql: ${TABLE}.solution ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: subject {
    type: string
    sql: ${TABLE}.subject ;;
  }

  dimension: submitter_id {
    type: number
    sql: ${TABLE}.submitter_id ;;
  }

  dimension: tags {
    type: string
    sql: ${TABLE}.tags ;;
  }

  dimension: ticket_form_id {
    type: number
    sql: ${TABLE}.ticket_form_id ;;
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated_at ;;
  }

  dimension: url {
    type: string
    sql: ${TABLE}.url ;;
  }

  dimension: via {
    type: string
    sql: ${TABLE}.via ;;
  }

  measure: count {
    type: count
    drill_fields: [id, requester_name, tickets_audits.count]
  }
}
