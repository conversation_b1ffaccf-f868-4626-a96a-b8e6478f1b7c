view: ag_user_segments {
  sql_table_name: clean__zendesk.user_segments ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: added_user_ids {
    type: string
    sql: ${TABLE}.added_user_ids ;;
  }

  dimension: built_in {
    type: yesno
    sql: ${TABLE}.built_in ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension: group_ids {
    type: string
    sql: ${TABLE}.group_ids ;;
  }

  dimension_group: loaded {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.loaded_at ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: or_tags {
    type: string
    sql: ${TABLE}.or_tags ;;
  }

  dimension: organization_ids {
    type: string
    sql: ${TABLE}.organization_ids ;;
  }

  dimension: tags {
    type: string
    sql: ${TABLE}.tags ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated_at ;;
  }

  dimension: user_type {
    type: string
    sql: ${TABLE}.user_type ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name, sections.count]
  }
}
