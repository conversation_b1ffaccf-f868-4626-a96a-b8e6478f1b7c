view: ag_sections {
  sql_table_name: clean__zendesk.sections ;;
  drill_fields: [parent_section_id]
  suggestions: no

  dimension: parent_section_id {
    primary_key: yes
    type: string
    sql: ${TABLE}.parent_section_id ;;
  }

  dimension: category_id {
    type: number
    sql: ${TABLE}.category_id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension: description {
    type: string
    sql: ${TABLE}.description ;;
  }

  dimension: html_url {
    type: string
    sql: ${TABLE}.html_url ;;
  }

  dimension: id {
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension_group: loaded {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.loaded_at ;;
  }

  dimension: locale {
    type: string
    sql: ${TABLE}.locale ;;
  }

  dimension: manageable_by {
    type: string
    sql: ${TABLE}.manageable_by ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  dimension: outdated {
    type: yesno
    sql: ${TABLE}.outdated ;;
  }

  dimension: position {
    type: number
    sql: ${TABLE}.position ;;
  }

  dimension: sorting {
    type: string
    sql: ${TABLE}.sorting ;;
  }

  dimension: source_locale {
    type: string
    sql: ${TABLE}.source_locale ;;
  }

  dimension: theme_template {
    type: string
    sql: ${TABLE}.theme_template ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated_at ;;
  }

  dimension: url {
    type: string
    sql: ${TABLE}.url ;;
  }

  dimension: user_segment_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.user_segment_id ;;
  }

  measure: count {
    type: count
    drill_fields: [parent_section_id, name, user_segments.id, user_segments.name]
  }
}
