include: "/zendesk_ops_guide/*.view.lkml"

explore: ticket_audit_events {
  group_label: "Sales / Marketing / CS"
  label: "Zendesk Audit"
  from: ext_ticket_audit_events

  join: tickets_audits {
    from: ag_tickets_audits
    view_label: "Ticket events"
    sql_on: ${tickets_audits.id}=${ticket_audit_events.id};;
    relationship: many_to_one
    type:  left_outer
    }

  join: articles {
    from: ag_articles
    view_label: "Articles"
    sql_on: ${ticket_audit_events.article_id} = ${articles.id};;
    relationship: many_to_one
    type:  left_outer
  }

  join: tickets_ops {
    from: ag_tickets_ops
    view_label: "Ticket"
    sql_on: ${tickets_audits.ticket_id} = ${tickets_ops.id};;
    relationship: many_to_one
    type:  left_outer
  }
}
