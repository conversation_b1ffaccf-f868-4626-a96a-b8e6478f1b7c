view: ext_ticket_audit_events
{

  # Or, you could make this view a derived table, like this:
  derived_table: {
    sql: select ticket_audits.id,ticket_audits.ticket_id, body, type
from clean__zendesk.tickets_audits as ticket_audits
lateral view explode(events) as param
      ;;
  }

  # Define your dimensions and measures here, like this:
  dimension: id {
    description: "Unique ID for each user that has ordered"
    type: number
    sql: ${TABLE}.id ;;
  }

  dimension: body {
    description: "The total number of orders for each user"
    type: string
    sql: ${TABLE}.body;;
  }

  dimension: article_id {
    description: "The total number of orders for each user"
    type: number
    sql: get_json_object(body,'$.id');;
  }

  dimension: type {
    description: "the type of the events"
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: ticket_id{
    description: "ticket ids"
    type: number
    sql: ${TABLE}.ticket_id ;;
  }
}
