view: t4_invoice_txn_events {
  derived_table: {
    sql_trigger_value: select max(dt) from clean__tcdb.transaction ;;
    sql: select
      transaction_id
      , array_agg(get_json_object(payment_snapshot, '$.payment_method')) as pmt_methods
    from clean__tcdb.transaction_event e
    where transaction_id in (
        select id from clean__tcdb.transaction t
        where
          status = 'COMPLETED'
          and type = 'INVOICE'
          and channel_name is null
    )
    group by 1
      ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension: pmt_methods {
    type: string
    sql: ${TABLE}.pmt_methods ;;
  }

  set: detail {
    fields: [transaction_id, pmt_methods]
  }
}
