include: "./t4_invoice_txn_events.view.lkml"
include: "./t4_cc_txn_events.view.lkml"

view: t4_settled_daily {
  derived_table: {
    sql:
    with bd_items as (
    SELECT
        b.id as bd_id
    FROM
        clean__xendit_withdrawal_service.batchdisbursements as b
        left join clean__xendit_withdrawal_service.batchdisbursementitems as bdi on b.id = bdi.batch_disbursement_id
    )

    select
        settled_date
        , case
            when type = 'DIRECT_DEBIT_PAYMENT' then 'DIRECT_DEBIT'
            when type = 'CREDIT_CARD_PAYMENT' then 'CREDIT_CARD'
            when type = 'RO_PAYMENT' then'RETAIL_OUTLET'
            when type = 'PAYLATER_PAYMENT' then 'PAYLATER'
            when type = 'VIRTUAL_ACCOUNT' then 'VA'
            when type = 'EWALLET_PAYMENT' then 'EWALLET'
            when type = 'QR_CODE_PAYMENT' then 'QR_CODE'
          else type
          end as `type`
        , sum(cnt) as row_count
      from (

      select
      cast(settled_at as date) as settled_date
      , case
      when type in ('DISBURSEMENT','REMITTANC<PERSON>','REMITTANCE_PAYOUT') then 'DISBURSEMENT'
      when type in ('BATCH_DISBURSEMENT') then 'BATCH_DISBURSEMENT'
      when type in ('XP_TRANSFER_IN', 'PLATFORM_FEE_IN') then 'IN-HOUSE TRANSACTION'
      when type in ('VA_PAYMENT', 'REMITTANCE_VA_PAYMENT') then
      case when is_switcher_payment then 'VIRTUAL_ACCOUNT_SWITCHER' else 'VIRTUAL_ACCOUNT' end
      when type = 'INVOICE' and channel_name in ('ALFAMART', 'INDOMARET') then 'RO_PAYMENT'
      when type = 'INVOICE' and channel_name in ('BRI','BNI','MANDIRI','PERMATA','BCA') then
      case when is_switcher_payment then 'VIRTUAL_ACCOUNT_SWITCHER' else 'VIRTUAL_ACCOUNT' end
      when type = 'INVOICE' and channel_name is null then (
      case when array_contains(pmt_methods, 'RETAIL_OUTLET') then 'RETAIL_OUTLET'
      else
      case when is_switcher_payment then 'VIRTUAL_ACCOUNT_SWITCHER' else 'VIRTUAL_ACCOUNT' end
      end
      )
      else type end as type
      , count(*) as cnt
      from clean__tcdb.transaction t
      left join ${t4_invoice_txn_events.SQL_TABLE_NAME} invoice_txn_events on t.id = invoice_txn_events.transaction_id
      left join ${t4_cc_txn_events.SQL_TABLE_NAME} cc_txn_events on t.id = cc_txn_events.transaction_id
      left join bd_items bdi on t.payment_id = bdi.bd_id
      where status = 'COMPLETED'
      and type not in ('XP_TRANSFER_OUT', 'PLATFORM_FEE_OUT')
      and (not array_contains(client_types, 'OFF_RAILS') or client_types is null)
      group by 1,2


      union all


      select
      cast(settled_at as date) as settled_date,
      case
      when type in ('DISBURSEMENT','REMITTANCE','REMITTANCE_PAYOUT') then 'DISBURSEMENT'
      when type in ('BATCH_DISBURSEMENT') then 'BATCH_DISBURSEMENT'
      when type in ('XP_TRANSFER_IN', 'PLATFORM_FEE_IN') then 'IN-HOUSE TRANSACTION'
      when type in ('DIRECT_DEBIT_PAYMENT') then 'DIRECT_DEBIT_PAYMENT'
      else type end as type
      , count(*)
      from clean__transaction_service_v4.transaction
      where status = 'COMPLETED'
      and type not in ('XP_TRANSFER_OUT', 'PLATFORM_FEE_OUT')
      group by 1,2

      )
      group by 1,2
      ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: settled_date {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.settled_date ;;
    description: "Settled Date of transaction in UTC time"
  }


  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  dimension: row_count {
    type: number
    sql: ${TABLE}.row_count ;;
  }

  measure: t4_total_rows {
    type: sum
    sql: ${TABLE}.row_count ;;
  }

  set: detail {
    fields: [settled_date_raw, type, t4_total_rows]
  }
}
