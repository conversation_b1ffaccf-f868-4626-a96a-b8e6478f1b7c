view: txn_backbone_settled_daily {
  derived_table: {
    sql: select
        case
          when bb.product_subtype = 'BATCH_DISBURSEMENT' then 'BATCH_DISBURSEMENT'
          when bb.product_subtype = 'ESCROW_DISBURSEMENT' then 'ESCROW_DISBURSEMENT'
          when bb.product_subtype in ('VA_FIXED', 'VA_NONFIXED', 'VA_REMITTANCE') then
            case
              when va.is_switching = true then 'Switcher VA' -- need to add in
              else 'VA'
            end
          when bb.product_subtype in ('VA_ESCROW') then 'ESCROW_VA'
          else bb.product_type end as product_type
        , cast(settled as date) as settled_date
        , count(*) as rows
      from
        transform__transaction_volumes.transaction_backbone bb
        left join ${virtual_accounts.SQL_TABLE_NAME} va on bb.reference = va.id
      where
        upper(bb.status) in ('COMPLETED', 'SETTLED', 'CAPTURED', 'SETTLEMENT', 'CLAIMED', 'SUCCEEDED', 'SUCCESS', 'SUCCESS_COMPLETED', 'FORCE_PAYMENT', 'SUCCESSFUL')
        and bb.dt >= current_date - interval '90' day
      group by 1, 2
 ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension_group: settled_date {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.settled_date ;;
    description: "Settled Date of transaction in UTC time"
  }

  dimension: rows {
    type: number
    sql: ${TABLE}."rows" ;;
  }

  measure: txn_backbone_rows {
    type: sum
    sql: ${TABLE}."rows" ;;
  }

  set: detail {
    fields: [product_type, settled_date_raw, rows]
  }
}
