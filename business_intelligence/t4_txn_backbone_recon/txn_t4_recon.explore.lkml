include: "txn_backbone_created_daily.view"
include: "txn_backbone_settled_daily.view"
include: "t4_settled_daily.view"

explore: t4_txn_backbone_recon {
  from: txn_backbone_created_daily
  description: "Recon for Transaction Backbone and T4 transactions"
  hidden: yes

  join: t4_settled_daily {
    sql_on: ${t4_settled_daily.settled_date_date} = ${t4_txn_backbone_recon.created_date_date}  and ${t4_settled_daily.type} = ${t4_txn_backbone_recon.product_type};;
    relationship: one_to_one
    type: full_outer
  }

  join: txn_backbone_settled_daily {
    sql_on: ${txn_backbone_settled_daily.settled_date_date} = ${t4_txn_backbone_recon.created_date_date}  and ${txn_backbone_settled_daily.product_type} = ${t4_txn_backbone_recon.product_type};;
    relationship: one_to_one
    type: full_outer
  }
}
