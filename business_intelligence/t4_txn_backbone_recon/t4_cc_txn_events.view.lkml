view: t4_cc_txn_events {
  derived_table: {
    sql_trigger_value: select max(dt) from clean__tcdb.transaction ;;
    sql: select
      e.transaction_id
      , array_agg(get_json_object(payment_snapshot, '$.client_type')) as client_types
    from clean__tcdb.transaction_event e
    inner join clean__tcdb.transaction t on t.id = e.transaction_id
    inner join clean__credit_card_production.creditcardcharges ccca on ccca.id = t.payment_id
    where ccca.client_type = 'OFF_RAILS'
    group by 1
 ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: transaction_id {
    type: string
    sql: ${TABLE}.transaction_id ;;
  }

  dimension: client_types {
    type: string
    sql: ${TABLE}.client_types ;;
  }

  set: detail {
    fields: [transaction_id, client_types]
  }
}
