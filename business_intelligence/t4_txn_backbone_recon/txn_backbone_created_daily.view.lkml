view: txn_backbone_created_daily {
    derived_table: {
      sql: select
        case
          when bb.product_subtype = 'BATCH_DISBURSEMENT' then 'BATCH_DISBURSEMENT'
          when bb.product_subtype = 'ESCROW_DISBURSEMENT' then 'ESCROW_DISBURSEMENT'
          when bb.product_subtype in ('VA_FIXED', 'VA_NONFIXED', 'VA_REMITTANCE') then
            case
              when va.is_switching = true then 'Switcher VA' -- need to add in
              else 'VA'
            end
          when bb.product_subtype in ('VA_ESCROW') then 'ESCROW_VA'
          else bb.product_type end as product_type
        , cast(bb.created as date) as created_date
        , count(*) as rows
        , sum(case when settled is not null then 1 else 0 end) settled_rows
      from
        transform__transaction_volumes.transaction_backbone bb
        left join ${virtual_accounts.SQL_TABLE_NAME} va on bb.reference = va.id
      where
        upper(bb.status) in ('COMPLETED', 'SETTLED', 'CAPTURED', 'SETTLEMENT', 'CLAIMED', 'SUCCEEDED', 'SUCCESS', 'SUCCESS_COMPLETED', 'FORCE_PAYMENT', 'SUCCESSFUL')
        and bb.dt >= current_date - interval '90' day
      group by 1, 2
 ;;
    }

    suggestions: no

    measure: count {
      type: count
      drill_fields: [detail*]
    }

    dimension: product_type {
      type: string
      sql: ${TABLE}.product_type ;;
    }

  dimension_group: created_date {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.created_date ;;
    description: "Created Date of transaction in UTC time. Partitioned column, highly recommended to use as filter"
  }

    dimension: rows {
      type: number
      sql: ${TABLE}."rows" ;;
    }

    measure: txn_backbone_rows {
      type: sum
      sql: ${TABLE}."rows" ;;
    }

    measure: settled_rows {
      type: sum
      sql: ${TABLE}.settled_rows ;;
    }

    set: detail {
      fields: [product_type, created_date_raw, rows, settled_rows]
    }
  }
