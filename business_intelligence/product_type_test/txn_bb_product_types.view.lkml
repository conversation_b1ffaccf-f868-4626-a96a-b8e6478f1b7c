view: txn_bb_product_types {
  derived_table: {
    sql: -- TODO: IN_HOUSE_TRANSACTION / PLATFORM_FEE diff in capped vs uncapped and IN vs OUT respectively
      -- TODO: Stuff to add to Transaction Backbone: DIRECT_BANK_TRANSFER
      with transaction_backbone_mapper as (
      select distinct
        product_type
        , product_subtype
        , case
            when product_type = 'CREDIT_CARD' then 'CREDIT_CARD_PAYMENT'
            when product_subtype = 'ACH' then 'ACH_PAYMENT'
            when product_subtype = 'BATCH_DISBURSEMENT' then 'BATCH_DISBURSEMENT'
            when product_subtype = 'DIRECT_DEBIT' then 'DIRECT_DEBIT_PAYMENT'
            when product_type = 'EWALLET' then 'EWALLET_PAYMENT'
            when product_type = 'IN-HOUSE TRANSACTION' and product_subtype = 'XENPLATFORM_TRANSFER' then 'IN-HOUSE TRANSACTION'
            when product_type = 'PAYLATER' then 'PAYLATER_PAYMENT'
            when product_type = 'IN-HOUSE TRANSACTION' and product_subtype = 'XENPLATFORM_SPLIT' then 'PLATFORM_SPLIT'
            when product_type = 'QR_CODE' then 'QR_CODE_PAYMENT'
            when product_subtype in ('REMITTANCE', 'REMITTANCE_PAYOUT') then product_subtype
            when product_subtype = 'VA_REMITTANCE' then 'IM_REMITTANCE_VA_PAYMENT'
            when product_subtype = 'VA_ESCROW' then 'ESCROW_MONEY_IN_EVENT'
            when product_subtype = 'ESCROW_DISBURSEMENT' then 'ESCROW_MONEY_OUT_EVENT'
            when product_type = 'RETAIL_OUTLET' then 'RO_PAYMENT'
            when product_type = 'VIRTUAL_ACCOUNT' then 'VA_PAYMENT'
            when product_type = 'DISBURSEMENT' then 'DISBURSEMENT'
          end as txn_type
        , case
            when product_type = 'CREDIT_CARD' and channel_name = 'BRI' then
              case
                when product_subtype = 'CC_INSTALLMENT_03_MO' then 'XENDIT_BRI_ECOM_INS_3MO'
                when product_subtype = 'CC_INSTALLMENT_06_MO' then 'XENDIT_BRI_ECON_INS_6MO'
                when product_subtype = 'CC_INSTALLMENT_12_MO' then 'XENDIT_BRI_ECON_INS_12MO'
              end
            when product_type = 'CREDIT_CARD' then 'DEFAULT'
            when product_subtype = 'ACH' then 'DEFAULT'
            when product_subtype = 'BATCH_DISBURSEMENT' then 'DEFAULT'
            when product_subtype = 'DIRECT_DEBIT' then channel_name
            when product_type = 'EWALLET' then channel_name
            when product_type = 'IN-HOUSE TRANSACTION' and product_subtype = 'XENPLATFORM_TRANSFER' then 'DEFAULT'
            when product_type = 'PAYLATER' then channel_name
            when product_type = 'IN-HOUSE TRANSACTION' and product_subtype = 'XENPLATFORM_SPLIT' then 'DEFAULT'
            when product_type = 'QR_CODE' then 'QRIS'
            when product_subtype in ('REMITTANCE', 'REMITTANCE_PAYOUT')  then channel_name
            when product_subtype = 'VA_REMITTANCE' then channel_name
            when product_subtype = 'VA_ESCROW' then channel_name
            when product_subtype = 'ESCROW_DISBURSEMENT' then channel_name
            when product_type = 'RETAIL_OUTLET' then channel_name
            when product_type = 'VIRTUAL_ACCOUNT' and commercial_model = 'SWITCHER' then channel_name || '_' || 'SWITCHING'
            when product_type = 'VIRTUAL_ACCOUNT' then channel_name
            when product_type = 'DISBURSEMENT' then channel_name

      end as txn_channel
      from
      transform__transaction_volumes.transaction_backbone
      where
      dt >= date '2022-01-01'
      )
      -- Add default entries to the table above
      , transaction_backbone_mapper_w_default as (

      select distinct
      product_type,
      product_subtype,
      txn_type,
      txn_channel
      from
      (
      select
      product_type,
      product_subtype,
      txn_type,
      txn_channel
      from
      transaction_backbone_mapper

      union all

      select distinct
      product_type,
      product_subtype,
      txn_type,
      'DEFAULT' as txn_channel
      from
      transaction_backbone_mapper
      )

      )


      -- Adds country prefixes to the name of the channel. Helpful for linking EWallet channels
      , ewallet_channel_naming as (
      select
      *
      , concat('ID_', txn_channel) as id_channel
      , concat('PH_', txn_channel) as ph_channel
      , concat('TH_', txn_channel) as th_channel
      , concat('MY_', txn_channel) as my_channel
      , concat('VN_', txn_channel) as vn_channel
      , concat('SG_', txn_channel) as sg_channel
      from
      transaction_backbone_mapper_w_default
      )

      , refund_enabled_products as (
      select
      name
      , concat(name, '_REFUND') as refund_transaction_types
      from
      clean__transaction_service_v4.transactions_type
      where
      is_refundable = true
      )

      , add_refunds_to_product_types as (
      select
      *
      from
      ewallet_channel_naming

      union all

      select
      t1.product_type
      , t1.product_subtype
      , t2.refund_transaction_types
      , t1.txn_channel
      , t1.id_channel
      , t1.ph_channel
      , t1.th_channel
      , t1.my_channel
      , t1.vn_channel
      , t1.sg_channel
      from
      ewallet_channel_naming t1
      left join refund_enabled_products t2 on t1.txn_type = t2.name
      )

      select * from add_refunds_to_product_types
      ;;
    sql_trigger_value: select current_date() ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
  }

  dimension: product_subtype {
    type: string
    sql: ${TABLE}.product_subtype ;;
  }

  dimension: txn_type {
    type: string
    sql: ${TABLE}.txn_type ;;
  }

  dimension: txn_channel {
    type: string
    sql: ${TABLE}.txn_channel ;;
  }

  dimension: id_channel {
    type: string
    sql: ${TABLE}.id_channel ;;
  }

  dimension: ph_channel {
    type: string
    sql: ${TABLE}.ph_channel ;;
  }

  dimension: th_channel {
    type: string
    sql: ${TABLE}.th_channel ;;
  }

  dimension: my_channel {
    type: string
    sql: ${TABLE}.my_channel ;;
  }

  dimension: vn_channel {
    type: string
    sql: ${TABLE}.vn_channel ;;
  }

  dimension: sg_channel {
    type: string
    sql: ${TABLE}.sg_channel ;;
  }

  set: detail {
    fields: [
      product_type,
      product_subtype,
      txn_type,
      txn_channel,
      id_channel,
      ph_channel,
      th_channel,
      my_channel,
      vn_channel,
      sg_channel
    ]
  }
}
