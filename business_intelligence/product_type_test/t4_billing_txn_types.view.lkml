view: t4_billing_txn_types {
  derived_table: {
    sql: with t4_crossjoin_tables as (
      select
        name as t4_transaction_type
        , t2.transaction_type as billing_transaction_type
        , t2.transaction_channel as billing_transaction_channel
        , t2.max_effective_date as prs_effective_date_latest
        , t3.rowcount as immutable_table_rowcount
        , t3.datecount as immutable_table_unique_dates
      from
        clean__transaction_service_v4.transactions_type t1
        full outer join (
          select
              case
                when t1.transaction_type in ('REMITTANCE_VA_PAYMENT') then 'IM_REMITTANCE_VA_PAYMENT'
                when t1.transaction_type in ('CARDLESS_CREDIT_PAYMENT') then 'PAYLATER_PAYMENT'
                when t1.transaction_type in ('QRCODE_PAYMENT') then 'QR_CODE_PAYMENT'
                when t1.transaction_type in ('VIRTUAL_ACCOUNT_PAYMENT', 'VA_PAYMENTi') then 'VA_PAYMENT'
                else t1.transaction_type
              end as transaction_type,
              t1.transaction_channel,
              max(t2.max_effective_date) as max_effective_date
          from clean__fee_service.product_rate_setting t1
            inner join (
              select
                transaction_type,
                transaction_channel,
                business_id,
                max(effective_date) as max_effective_date
              from
                clean__fee_service.product_rate_setting t2
              group by 1, 2, 3
            ) t2 on
              t1.transaction_type = t2.transaction_type
              and t1.transaction_channel = t2.transaction_channel
              and t1.business_id = t2.business_id
              and t1.effective_date = t2.max_effective_date
          where
            t1.transaction_type not like '%_REFUND'
            and t1.transaction_type not like '%_SETTLEMENT'
          group by 1, 2
      ) t2
      on t1.name = t2.transaction_type
      inner join (
        select
          transaction_type,
          transaction_channel,
          count(distinct id) as rowcount,
          count(distinct dt) as datecount
        from
          clean__fee_service.billable_transaction_detail
        group by 1, 2
        ) t3
      on t2.transaction_type = t3.transaction_type and t2.transaction_channel = t3.transaction_channel
      inner join (
        select transaction_type, transaction_channel
        from clean__fee_service.default_rate_settings
        group by 1, 2
      ) t4 on t2.transaction_type = t4.transaction_type and t2.transaction_channel = t4.transaction_channel
      )


      select
      *
      , case when billing_transaction_type in (select
      distinct txn_type
      from
      ${txn_bb_product_types.SQL_TABLE_NAME}) then true else false end as is_in_txn_backbone
      , case when billing_transaction_type in (select
      distinct transaction_type
      from
      ${revenue_product_types.SQL_TABLE_NAME}) then true else false end as is_in_revenue_backbone
      from t4_crossjoin_tables
      ;;
    sql_trigger_value: select current_date() ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: t4_transaction_type {
    type: string
    sql: ${TABLE}.t4_transaction_type ;;
  }

  dimension: billing_transaction_type {
    type: string
    sql: ${TABLE}.billing_transaction_type ;;
  }

  dimension: billing_transaction_channel {
    type: string
    sql: ${TABLE}.billing_transaction_channel ;;
  }

  dimension_group: prs_effective_date_latest {
    type: time
    sql: ${TABLE}.prs_effective_date_latest ;;
  }

  dimension: is_refund_txn {
    type: yesno
    sql: case when ${billing_transaction_type} like '%_REFUND' then true
      else false end ;;
  }

  dimension: is_in_txn_backbone {
    type: yesno
    sql: ${TABLE}.is_in_txn_backbone ;;
  }

  dimension: is_in_revenue_backbone {
    type: yesno
    sql: ${TABLE}.is_in_revenue_backbone ;;
  }

  measure: immutable_table_rowcount {
    type: sum
    sql: ${TABLE}.immutable_table_rowcount ;;
  }

  measure: immutable_table_unique_dates {
    type: sum
    sql: ${TABLE}.immutable_table_unique_dates ;;
  }

  set: detail {
    fields: [t4_transaction_type, billing_transaction_type, billing_transaction_channel, prs_effective_date_latest_time]
  }
}
