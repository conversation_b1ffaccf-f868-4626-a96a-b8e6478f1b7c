include: "//central-models-dbr/business_intelligence/bi_metrics/databricks/databricks.explore"
include: "/hr/employees/ag_data_operating_model_roles.view"
include: "/hr/employees/ag_persons.view"
include: "/hr/employees/ag_teams.view"
include: "/hr/employees/ag_person_teams.view"

explore: +audit_logs {
  join: persons {
    from: ag_persons
    sql_on: ${audit_logs.user_email} = ${persons.email} ;;
    relationship: one_to_one
    type: left_outer
  }

  join: data_operating_model_roles {
    fields: [role]
    relationship: many_to_one
    sql_on: ${persons.email} = ${data_operating_model_roles.email} ;;
    type: left_outer
  }

  join: person_teams {
    from: ag_person_teams
    relationship: one_to_many
    sql_on: ${persons.id} = ${person_teams.person_id} ;;
    fields: []
  }

  join: xendit_teams {
    from: ag_teams
    relationship: many_to_one
    sql_on: ${person_teams.team_id} = ${xendit_teams.id} ;;
  }
}
