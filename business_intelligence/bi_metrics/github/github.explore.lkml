include: "//central-models-dbr/business_intelligence/bi_metrics/github/github.explore"
include: "/hr/employees/ag_data_operating_model_roles.view"
include: "/hr/employees/ag_persons.view"
include: "/hr/employees/ag_teams.view"
include: "/hr/employees/ag_person_teams.view"

explore: +repositories {
  join: persons {
    from:  ag_persons
    type: left_outer
    relationship: one_to_one
    sql_on: ${github_users.login} = ${persons.git_login_username} ;;
  }

  join: person_teams {
    from: ag_person_teams
    relationship: one_to_many
    sql_on: ${persons.id} = ${person_teams.person_id} ;;
    fields: []
  }

  join: xendit_teams {
    from: ag_teams
    relationship: many_to_one
    sql_on: ${person_teams.team_id} = ${xendit_teams.id} ;;
  }

  join: data_operating_model_roles {
    type: left_outer
    relationship: many_to_one
    sql_on: ${persons.email} = ${data_operating_model_roles.email} ;;
    fields: [role]
  }
}
