"""add pk and other fields

Revision ID: 97818f511d30
Revises: 123d91a952f9
Create Date: 2022-12-08 12:40:57.806349

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "97818f511d30"
down_revision = "123d91a952f9"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("source", sa.Column("cron_schedule", sa.String(), nullable=False))
    op.add_column(
        "source",
        sa.Column("databricks_init_scripts", sa.ARRAY(sa.String()), nullable=True),
    )
    op.add_column("source", sa.Column("is_active", sa.<PERSON>(), nullable=False))
    op.add_column("source", sa.Column("source_group_id", sa.Integer(), nullable=False))
    op.add_column(
        "source",
        sa.Column(
            "source_type",
            sa.Enum(
                "POSTGRES",
                "AZURE",
                "MONG<PERSON>",
                "SPLUNK",
                "ZENDESK",
                "S3_JSON",
                "S3_CSV",
                "S3_PARQUET",
                name="sourcetype",
            ),
            nullable=False,
        ),
    )
    op.create_index(
        op.f("ix_source_source_group_id"), "source", ["source_group_id"], unique=False
    )
    op.create_index(
        op.f("ix_source_source_type"), "source", ["source_type"], unique=False
    )
    op.create_foreign_key(
        None,
        "source",
        "source_group",
        ["source_group_id"],
        ["id"],
        onupdate="CASCADE",
        ondelete="RESTRICT",
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "source", type_="foreignkey")
    op.drop_index(op.f("ix_source_source_type"), table_name="source")
    op.drop_index(op.f("ix_source_source_group_id"), table_name="source")
    op.drop_column("source", "source_type")
    op.drop_column("source", "source_group_id")
    op.drop_column("source", "is_active")
    op.drop_column("source", "databricks_init_scripts")
    op.drop_column("source", "cron_schedule")
    # ### end Alembic commands ###
