# The name of this view in Looker is "Load"
view: moneygram_connector_load {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__moneygram_connector.load ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Accountnumber" in Explore.

  dimension: accountnumber {
    type: string
    sql: ${TABLE}.accountnumber ;;
  }

  dimension: channelcode {
    type: string
    sql: ${TABLE}.channelcode ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.dt ;;
  }

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: mgireferencenumber {
    type: string
    sql: ${TABLE}.mgireferencenumber ;;
  }

  dimension: mgitransactionid {
    type: string
    sql: ${TABLE}.mgitransactionid ;;
  }

  dimension: purposecode {
    type: string
    sql: ${TABLE}.purposecode ;;
  }

  dimension: receiveamountcurrencycode {
    type: string
    sql: ${TABLE}.receiveamountcurrencycode ;;
  }

  dimension: receiveamountvalue {
    type: number
    sql: ${TABLE}.receiveamountvalue ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_receiveamountvalue {
    type: sum
    sql: ${receiveamountvalue} ;;  }
  measure: average_receiveamountvalue {
    type: average
    sql: ${receiveamountvalue} ;;  }

  dimension: receivecountrycode {
    type: string
    sql: ${TABLE}.receivecountrycode ;;
  }

  dimension: recipientaddresscity {
    type: string
    sql: ${TABLE}.recipientaddresscity ;;
  }

  dimension: recipientaddresscountrycode {
    type: string
    sql: ${TABLE}.recipientaddresscountrycode ;;
  }

  dimension: recipientaddressline1 {
    type: string
    sql: ${TABLE}.recipientaddressline1 ;;
  }

  dimension: recipientaddressline2 {
    type: string
    sql: ${TABLE}.recipientaddressline2 ;;
  }

  dimension: recipientcustomerid {
    type: string
    sql: ${TABLE}.recipientcustomerid ;;
  }

  dimension: recipientgivenname {
    type: string
    sql: ${TABLE}.recipientgivenname ;;
  }

  dimension: recipientphonecountrycode {
    type: string
    sql: ${TABLE}.recipientphonecountrycode ;;
  }

  dimension: recipientphonenumber {
    type: string
    sql: ${TABLE}.recipientphonenumber ;;
  }

  dimension: recipientsurname {
    type: string
    sql: ${TABLE}.recipientsurname ;;
  }

  dimension: remittancefailurecode {
    type: string
    sql: ${TABLE}.remittancefailurecode ;;
  }

  dimension: remittanceid {
    type: string
    # hidden: yes
    sql: ${TABLE}.remittanceid ;;
  }

  dimension: remittancestatus {
    type: string
    sql: ${TABLE}.remittancestatus ;;
  }

  dimension: sendamountcurrencycode {
    type: string
    sql: ${TABLE}.sendamountcurrencycode ;;
  }

  dimension: sendamountvalue {
    type: number
    sql: ${TABLE}.sendamountvalue ;;
  }

  dimension: sendcountrycode {
    type: string
    sql: ${TABLE}.sendcountrycode ;;
  }

  dimension: senderaddresscity {
    type: string
    sql: ${TABLE}.senderaddresscity ;;
  }

  dimension: senderaddresscountrycode {
    type: string
    sql: ${TABLE}.senderaddresscountrycode ;;
  }

  dimension: senderaddressline1 {
    type: string
    sql: ${TABLE}.senderaddressline1 ;;
  }

  dimension: senderaddressline2 {
    type: string
    sql: ${TABLE}.senderaddressline2 ;;
  }

  dimension: sendercustomerid {
    type: string
    sql: ${TABLE}.sendercustomerid ;;
  }

  dimension: sendergivenname {
    type: string
    sql: ${TABLE}.sendergivenname ;;
  }

  dimension: senderphonecountrycode {
    type: string
    sql: ${TABLE}.senderphonecountrycode ;;
  }

  dimension: senderphonenumber {
    type: string
    sql: ${TABLE}.senderphonenumber ;;
  }

  dimension: sendersurname {
    type: string
    sql: ${TABLE}.sendersurname ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
  id,
  sendergivenname,
  sendersurname,
  recipientgivenname,
  recipientsurname,
  remittance.remittanceid,
  remittance.count
  ]
  }

}
