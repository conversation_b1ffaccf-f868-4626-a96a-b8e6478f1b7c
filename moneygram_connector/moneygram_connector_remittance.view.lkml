# The name of this view in Looker is "Remittance"
view: moneygram_connector_remittance {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__moneygram_connector.remittance ;;
  drill_fields: [remittanceid]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: remittanceid {
    primary_key: yes
    type: string
    sql: ${TABLE}.remittanceid ;;
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Environment" in Explore.

  dimension: environment {
    type: string
    sql: ${TABLE}.environment ;;
  }

  dimension: externalid {
    type: string
    sql: ${TABLE}.externalid ;;
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: loadid {
    type: string
    # hidden: yes
    sql: ${TABLE}.loadid ;;
  }

  dimension: remittancefailurecode {
    type: string
    sql: ${TABLE}.remittancefailurecode ;;
  }

  dimension: remittancestatus {
    type: string
    sql: ${TABLE}.remittancestatus ;;
  }

  dimension: retriedtimes {
    type: number
    sql: ${TABLE}.retriedtimes ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_retriedtimes {
    type: sum
    sql: ${retriedtimes} ;;  }
  measure: average_retriedtimes {
    type: average
    sql: ${retriedtimes} ;;  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  # ----- Sets of fields for drilling ------
  set: detail {
    fields: [
  remittanceid,
  load.id,
  load.sendergivenname,
  load.sendersurname,
  load.recipientgivenname,
  load.recipientsurname,
  load.count
  ]
  }

}
