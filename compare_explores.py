#!/usr/bin/env python3
"""
Script to compare explores between source and target repositories and identify missing explores
"""
import subprocess
import re
from collections import defaultdict

def get_explores_from_repo(repo_path):
    """Get all explores from a repository"""
    cmd = f'find {repo_path} -name "*.explore.lkml" -exec grep -H "^explore:" {{}} \\;'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    explores = {}
    for line in result.stdout.strip().split('\n'):
        if line:
            # Parse: /path/to/file.explore.lkml:explore: explore_name {
            match = re.match(r'([^:]+):explore:\s*([^{]+)', line)
            if match:
                file_path = match.group(1)
                explore_name = match.group(2).strip()
                
                # Get relative path
                if repo_path in file_path:
                    relative_path = file_path.replace(repo_path + '/', '')
                else:
                    relative_path = file_path
                
                if relative_path not in explores:
                    explores[relative_path] = []
                explores[relative_path].append(explore_name)
    
    return explores

def compare_explores():
    """Compare explores between source and target repositories"""
    source_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
    target_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"
    
    print("🔍 Getting explores from source repository...")
    source_explores = get_explores_from_repo(source_repo)
    
    print("🔍 Getting explores from target repository...")
    target_explores = get_explores_from_repo(target_repo)
    
    print(f"\nSource repository has {len(source_explores)} explore files")
    print(f"Target repository has {len(target_explores)} explore files")
    
    # Find missing explore files
    missing_files = []
    for file_path in source_explores:
        if file_path not in target_explores:
            missing_files.append(file_path)
    
    # Find files with missing explores
    missing_explores_in_files = defaultdict(list)
    for file_path in source_explores:
        if file_path in target_explores:
            source_file_explores = set(source_explores[file_path])
            target_file_explores = set(target_explores[file_path])
            missing_explores = source_file_explores - target_file_explores
            if missing_explores:
                missing_explores_in_files[file_path] = list(missing_explores)
    
    print(f"\n📊 Analysis Results:")
    print(f"Missing explore files: {len(missing_files)}")
    print(f"Files with missing explores: {len(missing_explores_in_files)}")
    
    if missing_files:
        print(f"\n❌ Missing Explore Files ({len(missing_files)}):")
        for file_path in sorted(missing_files):
            explores_list = ', '.join(source_explores[file_path])
            print(f"  - {file_path} (explores: {explores_list})")
    
    if missing_explores_in_files:
        print(f"\n⚠️  Files with Missing Explores ({len(missing_explores_in_files)}):")
        for file_path, missing_explores in sorted(missing_explores_in_files.items()):
            explores_list = ', '.join(missing_explores)
            print(f"  - {file_path}: {explores_list}")
    
    return missing_files, missing_explores_in_files

def copy_missing_explores(missing_files, missing_explores_in_files):
    """Copy missing explores to target repository"""
    source_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
    target_repo = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"
    
    total_copied = 0
    total_failed = 0
    
    # Copy entire missing files
    if missing_files:
        print(f"\n🔄 Copying {len(missing_files)} missing explore files...")
        for file_path in missing_files:
            source_file = f"{source_repo}/{file_path}"
            target_file = f"{target_repo}/{file_path}"
            
            try:
                # Create target directory if needed
                import os
                target_dir = os.path.dirname(target_file)
                os.makedirs(target_dir, exist_ok=True)
                
                # Copy file
                import shutil
                shutil.copy2(source_file, target_file)
                print(f"✅ Copied file: {file_path}")
                total_copied += 1
            except Exception as e:
                print(f"❌ Failed to copy {file_path}: {e}")
                total_failed += 1
    
    # Handle files with missing explores (would need manual intervention)
    if missing_explores_in_files:
        print(f"\n⚠️  Files with missing explores need manual review:")
        for file_path, missing_explores in missing_explores_in_files.items():
            print(f"  - {file_path}: {', '.join(missing_explores)}")
            print(f"    → These explores exist in source but not in target")
    
    print(f"\n🎯 Summary:")
    print(f"Files copied: {total_copied}")
    print(f"Files failed: {total_failed}")
    if missing_explores_in_files:
        print(f"Files needing manual review: {len(missing_explores_in_files)}")

if __name__ == "__main__":
    missing_files, missing_explores_in_files = compare_explores()
    
    if missing_files or missing_explores_in_files:
        print(f"\n🚀 Proceeding with copying missing explores...")
        copy_missing_explores(missing_files, missing_explores_in_files)
    else:
        print(f"\n✅ All explores are already synchronized!")
