view: insufficient_balance_errors {
  derived_table: {
    sql: WITH transactions_with_currency AS (
      SELECT
        t.id,
        t.type,
        t.created_at,
        d.disbursement_routing_id,
        d.connector_code,
        from_json(t.data, 'business_id string').business_id as business_id,
        CASE
          -- For internal_transfer, use parent transaction's type to determine category
          WHEN t.type = 'internal_transfer' THEN
            CASE
              WHEN tp.type = 'payout' OR tp.type = 'disbursement' THEN 'PAYOUT'
              WHEN tp.type = 'conversion' THEN 'CONVERSION'
              WHEN tp.type = 'withdrawal' THEN 'WITHDRAWAL'
              WHEN tp.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
              WHEN tp.type = 'remittance' THEN 'REMITTANCE'
              ELSE 'INTERNAL_TRANSFER' -- fallback if no parent or unknown parent type
            END
          -- For non-internal_transfer transactions, use their own type
          WHEN t.type = 'payout' OR t.type = 'disbursement' THEN 'PAYOUT'
          WHEN t.type = 'conversion' THEN 'CONVERSION'
          WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          WHEN t.type = 'remittance' THEN 'REMITTANCE'
          ELSE NULL
        END AS product_type,
        CASE
          WHEN t.type = 'payment_conversion' THEN NULL
          WHEN t.type IN ('internal_transfer', 'conversion', 'withdrawal', 'payout', 'disbursement', 'remittance')
            THEN from_json(t.data, 'destination_currency string').destination_currency
          WHEN t.type = 'disbursement'
            THEN from_json(t.data, 'currency string').currency
          ELSE NULL
        END AS destination_currency
      FROM main.clean__regional_orchestrator_live.transactions t
      LEFT JOIN main.clean__regional_orchestrator_live.transactions tp
        ON t.parent_transaction_id = tp.id
      INNER JOIN main.clean__disbursement_service.disbursement d
        ON t.id = d.reference_id
    ),
    insufficient_balance_records AS (
      -- IDR
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_idr.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'idr'
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- MYR
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_myr.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'myr'
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- PHP
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_php.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'php'
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- THB
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_thb.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'thb'
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- VND
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_vnd.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'vnd'
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- SGD (non-gl)
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_sgd.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE LOWER(t.destination_currency) = 'sgd'
        AND NOT (t.connector_code ILIKE 'gl_%')
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- Global (including SGD with gl_)
      SELECT
        t.id,
        t.created_at,
        t.business_id,
        t.product_type,
        1 as insufficient_balance_flag
      FROM transactions_with_currency t
      INNER JOIN main.clean__disb_routing_service_global.disbursement_route_history drh
        ON t.disbursement_routing_id = drh.disbursement_route_id
      WHERE (
          LOWER(t.destination_currency) NOT IN ('idr', 'myr', 'php', 'thb', 'vnd', 'sgd')
          OR (LOWER(t.destination_currency) = 'sgd' AND t.connector_code ILIKE 'gl_%')
        )
        AND get_json_object(drh.disbursement_route_snapshot, '$.status') = 'FAILED'
        AND get_json_object(drh.disbursement_route_snapshot, '$.failureCode') = 'INSUFFICIENT_BALANCE'

      UNION ALL

      -- Transaction Events (for conversions and other transactions with insufficient balance errors)
      SELECT
        t.id,
        t.created_at,
        c.business_id,
        'CONVERSION' as product_type,
        1 as insufficient_balance_flag
      FROM main.clean__regional_orchestrator_live.transactions t
      INNER JOIN (
        SELECT *,
               ROW_NUMBER() OVER (PARTITION BY transaction_id ORDER BY created_at DESC) as rn
        FROM (
          SELECT *,
                 from_json(metadata, 'MAP<STRING, STRING>') as metadata_json
          FROM main.clean__regional_orchestrator_live.transaction_events
        ) parsed_events
        WHERE metadata_json IS NOT NULL
          AND (
              metadata_json['error'] ILIKE '%insufficient_balance%'
              OR metadata_json['error'] ILIKE '%insufficient balance%'
          )
      ) lte ON lte.transaction_id = t.id AND lte.rn = 1
      LEFT JOIN main.clean__regional_orchestrator_live.conversions c ON t.id = c.transaction_id
      LEFT JOIN main.clean__forex_conversion_service.bookings b ON c.quote_id = b.reference_id
      WHERE c.business_id IS NOT NULL
    )
    SELECT
      -- Extract base transaction ID (removing -retry-N suffix)
      REGEXP_REPLACE(id, '-retry-[0-9]+$', '') AS transaction_id,
      MIN(created_at) AS created_at,
      business_id,
      product_type,
      SUM(insufficient_balance_count) AS insufficient_balance_count
    FROM (
      SELECT
        id,
        created_at,
        business_id,
        product_type,
        COUNT(*) AS insufficient_balance_count
      FROM insufficient_balance_records
      GROUP BY
        id,
        created_at,
        business_id,
        product_type
    ) grouped_records
    GROUP BY
      REGEXP_REPLACE(id, '-retry-[0-9]+$', ''),
      business_id,
      product_type
    ORDER BY
      created_at DESC,
      transaction_id ;;
  }

  # Dimensions
  dimension: transaction_id {
    type: string
    primary_key: yes
    sql: ${TABLE}.transaction_id ;;
    description: "Base transaction ID with retry suffixes removed"
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created_at ;;
    description: "Timestamp of the first transaction attempt"
  }

  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
    description: "Business identifier"
    link: {
      label: "View Business Details"
      url: "/dashboards/business_details?business_id={{ value }}"
    }
  }

  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
    description: "Product type (PAYOUT, CONVERSION, WITHDRAWAL, etc.)"

    html:
      {% if value == 'PAYOUT' %}
        <span style="color: #1f77b4;">{{ value }}</span>
      {% elsif value == 'CONVERSION' %}
        <span style="color: #ff7f0e;">{{ value }}</span>
      {% elsif value == 'WITHDRAWAL' %}
        <span style="color: #2ca02c;">{{ value }}</span>
      {% elsif value == 'PAYMENT_CONVERSION' %}
        <span style="color: #d62728;">{{ value }}</span>
      {% elsif value == 'REMITTANCE' %}
        <span style="color: #9467bd;">{{ value }}</span>
      {% else %}
        {{ value }}
      {% endif %} ;;
  }

  # Measures
  measure: total_insufficient_balance_count {
    type: sum
    sql: ${TABLE}.insufficient_balance_count ;;
    description: "Total count of insufficient balance failures including retries"
    drill_fields: [transaction_detail*]
  }

  measure: transaction_count {
    type: count_distinct
    sql: ${transaction_id} ;;
    description: "Count of unique transactions"
    drill_fields: [transaction_detail*]
  }

  measure: affected_businesses {
    type: count_distinct
    sql: ${business_id} ;;
    description: "Count of unique businesses affected"
    drill_fields: [business_id, transaction_count, total_insufficient_balance_count]
  }

  measure: average_failures_per_transaction {
    type: number
    sql: 1.0 * ${total_insufficient_balance_count} / NULLIF(${transaction_count}, 0) ;;
    value_format_name: decimal_2
    description: "Average number of insufficient balance failures per transaction"
  }

  # Drill sets
  set: transaction_detail {
    fields: [
      transaction_id,
      created_date,
      business_id,
      product_type,
      total_insufficient_balance_count
    ]
  }
}
