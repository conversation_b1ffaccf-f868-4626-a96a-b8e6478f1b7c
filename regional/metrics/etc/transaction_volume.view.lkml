view: transaction_volume {
  sql_table_name: main.clean__regional_orchestrator_live.transactions ;;

  # Primary key
  dimension: id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.id ;;
  }

  # Transaction type - hidden from users
  dimension: type {
    type: string
    hidden: yes
    sql: ${TABLE}.type ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week,
      hour_of_day
    ]
    sql: ${TABLE}.created_at ;;
  }

  # Business ID extracted from JSON
  dimension: business_id {
    type: string
    sql: from_json(${TABLE}.data, 'business_id string').business_id ;;
  }

  # Origin currency - different JSON structure per transaction type
  dimension: origin_currency {
    type: string
    sql: CASE
          WHEN ${type} = 'payment_conversion' THEN from_json(${TABLE}.data, 'origin_currency string, destination_currency string').origin_currency
          WHEN ${type} = 'conversion' THEN from_json(${TABLE}.data, 'source_currency string').source_currency
          WHEN ${type} = 'withdrawal' THEN from_json(${TABLE}.data, 'source_currency string').source_currency
          WHEN ${type} = 'payout' THEN from_json(${TABLE}.data, 'source_currency string').source_currency
          WHEN ${type} = 'disbursement' THEN from_json(${TABLE}.data, 'currency string').currency
          ELSE NULL
        END ;;
  }

  # Origin amount - different JSON structure per transaction type
  dimension: origin_amount {
    type: number
    sql: CASE
          WHEN ${type} = 'payment_conversion' THEN from_json(${TABLE}.data, 'origin_amount string').origin_amount
          WHEN ${type} = 'conversion' THEN from_json(${TABLE}.data, 'source_amount string').source_amount
          WHEN ${type} = 'withdrawal' THEN from_json(${TABLE}.data, 'source_amount string').source_amount
          WHEN ${type} = 'payout' THEN from_json(${TABLE}.data, 'source_amount string').source_amount
          WHEN ${type} = 'disbursement' THEN from_json(${TABLE}.data, 'amount string').amount
          ELSE NULL
        END ;;
    value_format: "#,##0.00"
  }

  # Destination currency - different JSON structure per transaction type
  dimension: destination_currency {
    type: string
    sql: CASE
          WHEN ${type} = 'payment_conversion' THEN from_json(${TABLE}.data, 'origin_currency string, destination_currency string').destination_currency
          WHEN ${type} = 'conversion' THEN from_json(${TABLE}.data, 'destination_currency string').destination_currency
          WHEN ${type} = 'withdrawal' THEN from_json(${TABLE}.data, 'destination_currency string').destination_currency
          WHEN ${type} = 'payout' THEN from_json(${TABLE}.data, 'destination_currency string').destination_currency
          WHEN ${type} = 'disbursement' THEN from_json(${TABLE}.data, 'currency string').currency
          ELSE NULL
        END ;;
  }

  # Destination amount - different JSON structure per transaction type
  dimension: destination_amount {
    type: number
    sql: CASE
          WHEN ${type} = 'payment_conversion' THEN NULL
          WHEN ${type} = 'conversion' THEN from_json(${TABLE}.data, 'destination_amount string').destination_amount
          WHEN ${type} = 'withdrawal' THEN from_json(${TABLE}.data, 'destination_amount string').destination_amount
          WHEN ${type} = 'payout' THEN from_json(${TABLE}.data, 'destination_amount string').destination_amount
          WHEN ${type} = 'disbursement' THEN from_json(${TABLE}.data, 'amount string').amount
          ELSE NULL
        END ;;
    value_format: "#,##0.00"
  }

  # Product type (renamed from txn_category)
  dimension: product_type {
    type: string
    sql: CASE
          WHEN ${type} IN ('payout', 'disbursement') THEN 'PAYOUT'
          WHEN ${type} = 'conversion' THEN 'CONVERSION'
          WHEN ${type} = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN ${type} = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          ELSE NULL
        END ;;
  }

  # Measures
  measure: count {
    type: count
    drill_fields: [created_date, business_id, product_type, type, count]
  }

  measure: total_origin_amount {
    type: sum
    sql: ${origin_amount} ;;
    value_format: "#,##0.00"
  }

  measure: total_destination_amount {
    type: sum
    sql: ${destination_amount} ;;
    value_format: "#,##0.00"
  }

  measure: unique_business_count {
    type: count_distinct
    sql: ${business_id} ;;
  }
}
