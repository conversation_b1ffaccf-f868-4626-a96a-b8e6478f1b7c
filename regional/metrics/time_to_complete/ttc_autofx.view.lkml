view: ttc_autofx_transactions {
  sql_table_name: main.clean__regional_orchestrator_live.transactions ;;

  # Primary key
  dimension: id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.id ;;
  }

  # Transaction type (for filtering)
  dimension: type {
    type: string
    hidden: yes
    sql: ${TABLE}.type ;;
  }

  # State dimension
  dimension: state {
    type: string
    sql: ${TABLE}.state ;;
  }

  # State category for clarity
  dimension: state_category {
    type: string
    sql: CASE
          WHEN ${state} = 'PAYMENT_CONVERSION_SETTLED' THEN 'Success'
          WHEN ${state} = 'PAYMENT_CONVERSION_MERCHANT_TRADE_FAILED' THEN 'Failed'
          ELSE 'Other'
        END ;;
  }

  # Extract business_id from JSON data
  dimension: business_id {
    type: string
    sql: get_json_object(${TABLE}.data, '$.business_id') ;;
  }

  # Extract currencies from JSON data
  dimension: source_currency {
    type: string
    sql: get_json_object(${TABLE}.data, '$.origin_currency') ;;
  }

  dimension: destination_currency {
    type: string
    sql: get_json_object(${TABLE}.data, '$.destination_currency') ;;
  }

  # Currency pair
  dimension: currency_pair {
    type: string
    sql: ${source_currency} || ' → ' || ${destination_currency} ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week,
      hour_of_day
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date]
    sql: ${TABLE}.updated_at ;;
  }

  # Time to complete calculations
  dimension: ttc_seconds {
    type: number
    sql: unix_timestamp(${TABLE}.updated_at) - unix_timestamp(${TABLE}.created_at) ;;
    hidden: yes
  }

  dimension: ttc_minutes {
    type: number
    sql: ${ttc_seconds} / 60.0 ;;
    value_format: "0.00"
    hidden: yes
  }

  dimension: ttc_hours {
    type: number
    sql: ${ttc_seconds} / 3600.0 ;;
    value_format: "0.00"
    hidden: yes
  }

  # Time buckets for distribution analysis
  dimension: ttc_bucket {
    type: tier
    tiers: [0, 60, 300, 900, 1800, 3600, 7200]
    style: interval
    sql: ${ttc_seconds} ;;
    description: "Time buckets: <1min, 1-5min, 5-15min, 15-30min, 30-60min, 1-2hrs, >2hrs"
  }

  # Measures
  measure: count {
    type: count
    drill_fields: [created_date, business_id, state, currency_pair, avg_ttc_minutes]
  }

  measure: avg_ttc_seconds {
    type: average
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
    drill_fields: [created_date, business_id, currency_pair, state, avg_ttc_seconds]
  }

  measure: avg_ttc_minutes {
    type: average
    sql: ${ttc_minutes} ;;
    value_format: "0.00"
    drill_fields: [created_date, business_id, currency_pair, state, avg_ttc_minutes]
  }

  measure: avg_ttc_hours {
    type: average
    sql: ${ttc_hours} ;;
    value_format: "0.00"
    drill_fields: [created_date, business_id, currency_pair, state, avg_ttc_hours]
  }

  measure: median_ttc_seconds {
    type: median
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: median_ttc_minutes {
    type: median
    sql: ${ttc_minutes} ;;
    value_format: "0.00"
  }

  measure: p90_ttc_seconds {
    type: percentile
    percentile: 90
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: p95_ttc_seconds {
    type: percentile
    percentile: 95
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: min_ttc_seconds {
    type: min
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: max_ttc_seconds {
    type: max
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  # Separate measures by state category
  measure: avg_ttc_seconds_success {
    type: average
    sql: ${ttc_seconds} ;;
    filters: [state: "PAYMENT_CONVERSION_SETTLED"]
    value_format: "0.00"
    label: "Avg TTC Seconds (Success)"
  }

  measure: avg_ttc_seconds_failed {
    type: average
    sql: ${ttc_seconds} ;;
    filters: [state: "PAYMENT_CONVERSION_MERCHANT_TRADE_FAILED"]
    value_format: "0.00"
    label: "Avg TTC Seconds (Failed)"
  }

  measure: count_success {
    type: count
    filters: [state: "PAYMENT_CONVERSION_SETTLED"]
    label: "Count Success"
  }

  measure: count_failed {
    type: count
    filters: [state: "PAYMENT_CONVERSION_MERCHANT_TRADE_FAILED"]
    label: "Count Failed"
  }
}
