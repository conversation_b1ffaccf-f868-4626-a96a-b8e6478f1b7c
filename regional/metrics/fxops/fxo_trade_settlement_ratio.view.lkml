view: fxo_trade_settlement_ratio {
  derived_table: {
    sql:
      -- API trades from internal_trades
      SELECT
        it.id as trade_id,
        'API' as trade_type,
        it.created_at,
        it.business_id,
        it.status as trade_status,
        CASE
          WHEN it.status = 'INTERNAL_TRADE_SUCCEEDED' THEN 'Settled'
          WHEN it.status = 'INTERNAL_TRADE_FAILED' THEN 'Failed'
          ELSE 'Requested'
        END as settlement_status,
        replace(it.parent_transaction_id, 'cnv-', '') as conversion_id,
        c.transaction_id,
        c.quote_id,
        COALESCE(b.origin_currency, 'JPM_API') as origin_currency,
        COALESCE(b.destination_currency, 'JPM_API') as destination_currency,
        'JPM_API' as origin_account,
        'JPM_API' as destination_account,
        t.state as transaction_state,
        t.type as transaction_type,
        CASE
          WHEN t.type IN ('payout', 'disbursement') THEN 'PAYOUT'
          WHEN t.type = 'conversion' THEN 'CONVERSION'
          WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          ELSE NULL
        END as product_type
      FROM main.clean__regional_orchestrator_live.internal_trades it
      LEFT JOIN main.clean__regional_orchestrator_live.conversions c
        ON replace(it.parent_transaction_id, 'cnv-', '') = c.id
      LEFT JOIN main.clean__forex_conversion_service.bookings b
        ON c.quote_id = b.reference_id
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
        ON c.transaction_id = t.id

      UNION ALL

      -- BATCH trades from trade_instructions
      SELECT
      ti.id as trade_id,
      'BATCH' as trade_type,
      ti.created_at,
      ti.business_id,
      ti.status as trade_status,
      CASE
      WHEN ti.status = 'SUCCEEDED' THEN 'Settled'
      WHEN ti.status = 'FAILED' THEN 'Failed'
      ELSE 'Requested'
      END as settlement_status,
      NULL as conversion_id,
      ti.parent_transaction_id as transaction_id,
      NULL as quote_id,
      ti.origin_currency,
      ti.destination_currency,
      ti.origin_account,
      ti.destination_account,
      t.state as transaction_state,
      t.type as transaction_type,
      CASE
      WHEN t.type = 'payout' THEN 'PAYOUT'
      WHEN t.type = 'disbursement' THEN 'DISBURSEMENT'
      WHEN t.type = 'conversion' THEN 'CONVERSION'
      WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
      WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
      ELSE NULL
      END as product_type
      FROM main.clean__regional_orchestrator_live.trade_instructions ti
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
      ON ti.parent_transaction_id = t.id
      ;;

    sql_trigger_value: SELECT CURRENT_DATE() ;;
  }

  # Primary key
  dimension: trade_id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.trade_id ;;
  }

  # Business ID dimension
  dimension: business_id {
    type: string
    sql: ${TABLE}.business_id ;;
    description: "Business ID associated with the trade"
  }

  # Trade type dimension (API or BATCH)
  dimension: trade_type {
    type: string
    sql: ${TABLE}.trade_type ;;
    description: "API (automated) or BATCH (manual) trade"
  }

  # Trade status (raw status from the tables)
  dimension: trade_status {
    type: string
    sql: ${TABLE}.trade_status ;;
    hidden: yes
  }

  # Settlement status (Settled/Failed/Requested)
  dimension: settlement_status {
    type: string
    sql: ${TABLE}.settlement_status ;;
    description: "Settlement status: Settled, Failed, or Requested (pending)"
  }

  # Transaction category
  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
    description: "Transaction category: PAYOUT, DISBURSEMENT, CONVERSION, WITHDRAWAL, PAYMENT_CONVERSION"
  }

  # Currency dimensions
  dimension: origin_currency {
    type: string
    sql: ${TABLE}.origin_currency ;;
  }

  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }

  dimension: currency_pair {
    type: string
    sql: ${origin_currency} || ' → ' || ${destination_currency} ;;
  }

  # Account dimensions
  dimension: origin_account {
    type: string
    sql: ${TABLE}.origin_account ;;
  }

  dimension: destination_account {
    type: string
    sql: ${TABLE}.destination_account ;;
  }

  dimension: account_pair {
    type: string
    sql: ${origin_account} || ' → ' || ${destination_account} ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week,
      hour_of_day
    ]
    sql: ${TABLE}.created_at ;;
  }

  # ==== SETTLEMENT RATIO MEASURES ====

  measure: total_trades {
    type: count
    label: "Total Trades"
    drill_fields: [created_date, business_id, product_type, trade_type, currency_pair, total_trades]
  }

  measure: count_settled {
    type: count
    filters: [settlement_status: "Settled"]
    label: "Count Settled"
    drill_fields: [created_date, business_id, product_type, trade_type, currency_pair, count_settled]
  }

  measure: count_requested {
    type: count
    filters: [settlement_status: "Requested"]
    label: "Count Requested (Pending)"
    drill_fields: [created_date, business_id, product_type, trade_type, currency_pair, count_requested]
  }

  measure: count_failed {
    type: count
    filters: [settlement_status: "Failed"]
    label: "Count Failed"
    drill_fields: [created_date, business_id, product_type, trade_type, currency_pair, count_failed]
  }

  measure: settlement_ratio {
    type: number
    sql: CASE
          WHEN (${count_settled} + ${count_requested}) = 0 THEN NULL
          ELSE 100.0 * ${count_settled} / (${count_settled} + ${count_requested})
        END ;;
    value_format: "0.00\"%\""
    description: "Percentage of trades that have settled (excluding failed)"
    drill_fields: [product_type, trade_type, count_settled, count_requested, settlement_ratio]
  }

  # Separate measures by trade type
  measure: count_settled_api {
    type: count
    filters: [
      settlement_status: "Settled",
      trade_type: "API"
    ]
    label: "Count Settled (API)"
  }

  measure: count_requested_api {
    type: count
    filters: [
      settlement_status: "Requested",
      trade_type: "API"
    ]
    label: "Count Requested (API)"
  }

  measure: settlement_ratio_api {
    type: number
    sql: CASE
          WHEN (${count_settled_api} + ${count_requested_api}) = 0 THEN NULL
          ELSE 100.0 * ${count_settled_api} / (${count_settled_api} + ${count_requested_api})
        END ;;
    value_format: "0.00\"%\""
    label: "Settlement Ratio % (API)"
  }

  measure: count_settled_batch {
    type: count
    filters: [
      settlement_status: "Settled",
      trade_type: "BATCH"
    ]
    label: "Count Settled (BATCH)"
  }

  measure: count_requested_batch {
    type: count
    filters: [
      settlement_status: "Requested",
      trade_type: "BATCH"
    ]
    label: "Count Requested (BATCH)"
  }

  measure: settlement_ratio_batch {
    type: number
    sql: CASE
          WHEN (${count_settled_batch} + ${count_requested_batch}) = 0 THEN NULL
          ELSE 100.0 * ${count_settled_batch} / (${count_settled_batch} + ${count_requested_batch})
        END ;;
    value_format: "0.00\"%\""
    label: "Settlement Ratio % (BATCH)"
  }

  # Combined success/settlement metrics
  measure: overall_completion_rate {
    type: number
    sql: CASE
          WHEN ${total_trades} = 0 THEN NULL
          ELSE 100.0 * ${count_settled} / ${total_trades}
        END ;;
    value_format: "0.00\"%\""
    description: "Percentage of all trades that have successfully settled"
  }
}
