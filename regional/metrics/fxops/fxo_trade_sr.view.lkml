view: fxo_trade_sr {
  derived_table: {
    sql:
      -- API trades from internal_trades
      SELECT
        it.id as trade_id,
        'API' as trade_type,
        it.created_at,
        it.status as trade_status,
        CASE
          WHEN it.status = 'INTERNAL_TRADE_SUCCEEDED' THEN 'Success'
          WHEN it.status = 'INTERNAL_TRADE_FAILED' THEN 'Failed'
          ELSE 'Other'
        END as trade_result,
        replace(it.parent_transaction_id, 'cnv-', '') as conversion_id,
        c.transaction_id,
        c.quote_id,
        COALESCE(b.origin_currency, 'JPM_API') as origin_currency,
        COALESCE(b.destination_currency, 'JPM_API') as destination_currency,
        'JPM_API' as origin_account,
        'JPM_API' as destination_account,
        t.state as transaction_state,
        t.type as transaction_type,
        CASE
          WHEN t.type IN ('payout', 'disbursement') THEN 'PAYOUT'
          WHEN t.type = 'conversion' THEN 'CONVERSION'
          WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          ELSE NULL
        END as product_type
      FROM main.clean__regional_orchestrator_live.internal_trades it
      LEFT JOIN main.clean__regional_orchestrator_live.conversions c
        ON replace(it.parent_transaction_id, 'cnv-', '') = c.id
      LEFT JOIN main.clean__forex_conversion_service.bookings b
        ON c.quote_id = b.reference_id
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
        ON c.transaction_id = t.id
      WHERE it.status IN ('INTERNAL_TRADE_SUCCEEDED', 'INTERNAL_TRADE_FAILED')

      UNION ALL

      -- BATCH trades from trade_instructions
      SELECT
      ti.id as trade_id,
      'BATCH' as trade_type,
      ti.created_at,
      ti.status as trade_status,
      CASE
      WHEN ti.status = 'SUCCEEDED' THEN 'Success'
      WHEN ti.status = 'FAILED' THEN 'Failed'
      ELSE 'Other'
      END as trade_result,
      NULL as conversion_id,
      ti.parent_transaction_id as transaction_id,
      NULL as quote_id,
      ti.origin_currency,
      ti.destination_currency,
      ti.origin_account,
      ti.destination_account,
      t.state as transaction_state,
      t.type as transaction_type,
      CASE
      WHEN t.type = 'payout' THEN 'PAYOUT'
      WHEN t.type = 'disbursement' THEN 'DISBURSEMENT'
      WHEN t.type = 'conversion' THEN 'CONVERSION'
      WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
      WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
      ELSE NULL
      END as product_type
      FROM main.clean__regional_orchestrator_live.trade_instructions ti
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
      ON ti.parent_transaction_id = t.id
      WHERE ti.status IN ('SUCCEEDED', 'FAILED')
      ;;

    sql_trigger_value: SELECT CURRENT_DATE() ;;
  }

  # Primary key
  dimension: trade_id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.trade_id ;;
  }

  # Trade type dimension (API or BATCH)
  dimension: trade_type {
    type: string
    sql: ${TABLE}.trade_type ;;
    description: "API (automated) or BATCH (manual) trade"
  }

  # Trade status (raw status from the tables)
  dimension: trade_status {
    type: string
    sql: ${TABLE}.trade_status ;;
    hidden: yes
  }

  # Trade result (Success/Failed)
  dimension: trade_result {
    type: string
    sql: ${TABLE}.trade_result ;;
    description: "Trade result: Success or Failed"
  }

  # Product type (renamed from txn_category)
  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
    description: "Product type: PAYOUT, DISBURSEMENT, CONVERSION, WITHDRAWAL, PAYMENT_CONVERSION"
  }

  # Currency dimensions
  dimension: origin_currency {
    type: string
    sql: ${TABLE}.origin_currency ;;
  }

  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }

  dimension: currency_pair {
    type: string
    sql: ${origin_currency} || ' → ' || ${destination_currency} ;;
  }

  # Account dimensions
  dimension: origin_account {
    type: string
    sql: ${TABLE}.origin_account ;;
  }

  dimension: destination_account {
    type: string
    sql: ${TABLE}.destination_account ;;
  }

  dimension: account_pair {
    type: string
    sql: ${origin_account} || ' → ' || ${destination_account} ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week,
      hour_of_day
    ]
    sql: ${TABLE}.created_at ;;
  }

  # ==== SUCCESS RATE MEASURES ====

  measure: total_trades {
    type: count
    label: "Total Trades"
    drill_fields: [created_date, product_type, trade_type, currency_pair, total_trades]
  }

  measure: count_success {
    type: count
    filters: [trade_result: "Success"]
    label: "Count Success"
    drill_fields: [created_date, product_type, trade_type, currency_pair, count_success]
  }

  measure: count_failed {
    type: count
    filters: [trade_result: "Failed"]
    label: "Count Failed"
    drill_fields: [created_date, product_type, trade_type, currency_pair, count_failed]
  }

  measure: success_rate {
    type: number
    sql: CASE
          WHEN (${count_success} + ${count_failed}) = 0 THEN NULL
          ELSE 100.0 * ${count_success} / (${count_success} + ${count_failed})
        END ;;
    value_format: "0.00\"%\""
    description: "Percentage of trades that succeeded"
    drill_fields: [product_type, trade_type, count_success, count_failed, success_rate]
  }

  # Separate measures by trade type
  measure: count_success_api {
    type: count
    filters: [
      trade_result: "Success",
      trade_type: "API"
    ]
    label: "Count Success (API)"
  }

  measure: count_failed_api {
    type: count
    filters: [
      trade_result: "Failed",
      trade_type: "API"
    ]
    label: "Count Failed (API)"
  }

  measure: success_rate_api {
    type: number
    sql: CASE
          WHEN (${count_success_api} + ${count_failed_api}) = 0 THEN NULL
          ELSE 100.0 * ${count_success_api} / (${count_success_api} + ${count_failed_api})
        END ;;
    value_format: "0.00\"%\""
    label: "Success Rate % (API)"
  }

  measure: count_success_batch {
    type: count
    filters: [
      trade_result: "Success",
      trade_type: "BATCH"
    ]
    label: "Count Success (BATCH)"
  }

  measure: count_failed_batch {
    type: count
    filters: [
      trade_result: "Failed",
      trade_type: "BATCH"
    ]
    label: "Count Failed (BATCH)"
  }

  measure: success_rate_batch {
    type: number
    sql: CASE
          WHEN (${count_success_batch} + ${count_failed_batch}) = 0 THEN NULL
          ELSE 100.0 * ${count_success_batch} / (${count_success_batch} + ${count_failed_batch})
        END ;;
    value_format: "0.00\"%\""
    label: "Success Rate % (BATCH)"
  }
}
