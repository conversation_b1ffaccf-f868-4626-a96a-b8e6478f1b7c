# This view is exactly the same as fxo_all_trades but with a different name for clarity
# You could actually use the same view for both explores if you prefer
view: fxo_trade_ttc {
  derived_table: {
    sql:
      -- API/Automated trades from internal_trades
      SELECT
        it.id as trade_id,
        'API' as trade_type,
        it.created_at,
        it.updated_at,
        unix_timestamp(it.updated_at) - unix_timestamp(it.created_at) as ttc_seconds,
        replace(it.parent_transaction_id, 'cnv-', '') as conversion_id,
        c.transaction_id,
        c.quote_id,
        COALESCE(b.origin_currency, 'JPM_API') as origin_currency,
        COALESCE(b.destination_currency, 'JPM_API') as destination_currency,
        'JPM_API' as origin_account,
        'JPM_API' as destination_account,
        t.state as transaction_state,
        t.type as transaction_type,
        CASE
          WHEN t.type IN ('payout', 'disbursement') THEN 'PAYOUT'
          WHEN t.type = 'conversion' THEN 'CONVERSION'
          WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          ELSE NULL
        END as product_type
      FROM main.clean__regional_orchestrator_live.internal_trades it
      LEFT JOIN main.clean__regional_orchestrator_live.conversions c
        ON replace(it.parent_transaction_id, 'cnv-', '') = c.id
      LEFT JOIN main.clean__forex_conversion_service.bookings b
        ON c.quote_id = b.reference_id
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
        ON c.transaction_id = t.id
      WHERE it.status IN ('INTERNAL_TRADE_SUCCEEDED', 'INTERNAL_TRADE_FAILED')

      UNION ALL

      -- Manual trades from trade_instructions
      SELECT
      ti.id as trade_id,
      'Manual' as trade_type,
      ti.created_at,
      ti.updated_at,
      unix_timestamp(ti.updated_at) - unix_timestamp(ti.created_at) as ttc_seconds,
      NULL as conversion_id,
      ti.parent_transaction_id as transaction_id,
      NULL as quote_id,
      ti.origin_currency,
      ti.destination_currency,
      ti.origin_account,
      ti.destination_account,
      t.state as transaction_state,
      t.type as transaction_type,
      CASE
      WHEN t.type = 'payout' THEN 'PAYOUT'
      WHEN t.type = 'disbursement' THEN 'DISBURSEMENT'
      WHEN t.type = 'conversion' THEN 'CONVERSION'
      WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
      WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
      ELSE NULL
      END as product_type
      FROM main.clean__regional_orchestrator_live.trade_instructions ti
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
      ON ti.parent_transaction_id = t.id
      WHERE ti.status IN ('SUCCEEDED', 'FAILED')
      ;;

    sql_trigger_value: SELECT CURRENT_DATE() ;;
  }

  # Primary key
  dimension: trade_id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.trade_id ;;
  }

  # Trade type dimension - this is the key filter
  dimension: trade_type {
    type: string
    sql: ${TABLE}.trade_type ;;
    description: "API (automated) or Manual trade"
  }

  # Product type (renamed from txn_category)
  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
    description: "Product type: PAYOUT, DISBURSEMENT, CONVERSION, WITHDRAWAL, PAYMENT_CONVERSION"
  }

  # Currency dimensions
  dimension: origin_currency {
    type: string
    sql: ${TABLE}.origin_currency ;;
  }

  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }

  dimension: currency_pair {
    type: string
    sql: ${origin_currency} || ' → ' || ${destination_currency} ;;
  }

  # Account dimensions
  dimension: origin_account {
    type: string
    sql: ${TABLE}.origin_account ;;
  }

  dimension: destination_account {
    type: string
    sql: ${TABLE}.destination_account ;;
  }

  dimension: account_pair {
    type: string
    sql: ${origin_account} || ' → ' || ${destination_account} ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week,
      hour_of_day
    ]
    sql: ${TABLE}.created_at ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date]
    sql: ${TABLE}.updated_at ;;
  }

  # Time to complete dimensions
  dimension: ttc_seconds {
    type: number
    sql: ${TABLE}.ttc_seconds ;;
    hidden: yes
  }

  dimension: ttc_minutes {
    type: number
    sql: ${ttc_seconds} / 60.0 ;;
    value_format: "0.00"
    hidden: yes
  }

  dimension: ttc_hours {
    type: number
    sql: ${ttc_seconds} / 3600.0 ;;
    value_format: "0.00"
    hidden: yes
  }

  dimension: ttc_bucket {
    type: tier
    tiers: [0, 60, 300, 900, 1800, 3600, 7200]
    style: interval
    sql: ${ttc_seconds} ;;
    description: "Time buckets: <1min, 1-5min, 5-15min, 15-30min, 30-60min, 1-2hrs, >2hrs"
  }

  # Measures
  measure: count {
    type: count
    drill_fields: [created_date, product_type, trade_type, currency_pair, count]
  }

  measure: avg_ttc_seconds {
    type: average
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
    drill_fields: [created_date, product_type, trade_type, currency_pair, avg_ttc_seconds]
  }

  measure: avg_ttc_minutes {
    type: average
    sql: ${ttc_minutes} ;;
    value_format: "0.00"
  }

  measure: avg_ttc_hours {
    type: average
    sql: ${ttc_hours} ;;
    value_format: "0.00"
  }

  # Separate measures by trade type
  measure: avg_ttc_seconds_api {
    type: average
    sql: ${ttc_seconds} ;;
    filters: [trade_type: "API"]
    value_format: "0.00"
    label: "Avg TTC Seconds (API)"
  }

  measure: avg_ttc_seconds_manual {
    type: average
    sql: ${ttc_seconds} ;;
    filters: [trade_type: "Manual"]
    value_format: "0.00"
    label: "Avg TTC Seconds (Manual)"
  }

  measure: median_ttc_seconds {
    type: median
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: median_ttc_seconds_api {
    type: median
    sql: ${ttc_seconds} ;;
    filters: [trade_type: "API"]
    value_format: "0.00"
    label: "Median TTC Seconds (API)"
  }

  measure: median_ttc_seconds_manual {
    type: median
    sql: ${ttc_seconds} ;;
    filters: [trade_type: "Manual"]
    value_format: "0.00"
    label: "Median TTC Seconds (Manual)"
  }

  measure: p90_ttc_seconds {
    type: percentile
    percentile: 90
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: p95_ttc_seconds {
    type: percentile
    percentile: 95
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: min_ttc_seconds {
    type: min
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }

  measure: max_ttc_seconds {
    type: max
    sql: ${ttc_seconds} ;;
    value_format: "0.00"
  }
}
