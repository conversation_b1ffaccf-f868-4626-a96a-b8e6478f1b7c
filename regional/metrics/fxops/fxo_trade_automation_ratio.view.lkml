# Combined trades view focused on automation ratio metrics
view: fxo_all_trades {
  derived_table: {
    sql:
      -- API/Automated trades from internal_trades
      SELECT
        it.id as trade_id,
        'API' as trade_type,
        it.created_at,
        replace(it.parent_transaction_id, 'cnv-', '') as conversion_id,
        c.transaction_id,
        c.quote_id,
        COALESCE(b.origin_currency, 'JPM_API') as origin_currency,
        COALESCE(b.destination_currency, 'JPM_API') as destination_currency,
        'JPM_API' as origin_account,
        'JPM_API' as destination_account,
        t.state as transaction_state,
        t.type as transaction_type,
        CASE
          WHEN t.type IN ('payout', 'disbursement') THEN 'PAYOUT'
          WHEN t.type = 'conversion' THEN 'CONVERSION'
          WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
          WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
          ELSE NULL
        END as product_type
      FROM main.clean__regional_orchestrator_live.internal_trades it
      LEFT JOIN main.clean__regional_orchestrator_live.conversions c
        ON replace(it.parent_transaction_id, 'cnv-', '') = c.id
      LEFT JOIN main.clean__forex_conversion_service.bookings b
        ON c.quote_id = b.reference_id
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
        ON c.transaction_id = t.id
      WHERE it.status IN ('INTERNAL_TRADE_SUCCEEDED', 'INTERNAL_TRADE_FAILED')

      UNION ALL

      -- Manual trades from trade_instructions
      SELECT
      ti.id as trade_id,
      'Manual' as trade_type,
      ti.created_at,
      NULL as conversion_id,
      ti.parent_transaction_id as transaction_id,
      NULL as quote_id,
      ti.origin_currency,
      ti.destination_currency,
      ti.origin_account,
      ti.destination_account,
      t.state as transaction_state,
      t.type as transaction_type,
      CASE
      WHEN t.type = 'payout' THEN 'PAYOUT'
      WHEN t.type = 'disbursement' THEN 'DISBURSEMENT'
      WHEN t.type = 'conversion' THEN 'CONVERSION'
      WHEN t.type = 'withdrawal' THEN 'WITHDRAWAL'
      WHEN t.type = 'payment_conversion' THEN 'PAYMENT_CONVERSION'
      ELSE NULL
      END as product_type
      FROM main.clean__regional_orchestrator_live.trade_instructions ti
      LEFT JOIN main.clean__regional_orchestrator_live.transactions t
      ON ti.parent_transaction_id = t.id
      WHERE ti.status IN ('SUCCEEDED', 'FAILED')
      ;;

    sql_trigger_value: SELECT CURRENT_DATE() ;;
  }

  # Primary key
  dimension: trade_id {
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.trade_id ;;
  }

  # Trade type dimension - this is the key filter
  dimension: trade_type {
    type: string
    sql: ${TABLE}.trade_type ;;
    description: "API (automated) or Manual trade"
  }

  # Product type (renamed from txn_category)
  dimension: product_type {
    type: string
    sql: ${TABLE}.product_type ;;
    description: "Product type: PAYOUT, DISBURSEMENT, CONVERSION, WITHDRAWAL, PAYMENT_CONVERSION"
  }

  # Currency dimensions
  dimension: origin_currency {
    type: string
    sql: ${TABLE}.origin_currency ;;
  }

  dimension: destination_currency {
    type: string
    sql: ${TABLE}.destination_currency ;;
  }

  # Account dimensions
  dimension: origin_account {
    type: string
    sql: ${TABLE}.origin_account ;;
  }

  dimension: destination_account {
    type: string
    sql: ${TABLE}.destination_account ;;
  }

  # Time dimensions
  dimension_group: created {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year,
      week_of_year,
      day_of_week
    ]
    sql: ${TABLE}.created_at ;;
  }

  # ==== AUTOMATION RATIO MEASURES ONLY ====

  measure: total_trades {
    type: count
    label: "Total Trades"
    drill_fields: [created_date, product_type, trade_type, total_trades]
  }

  measure: total_api_trades {
    type: count
    filters: [trade_type: "API"]
    label: "Total API Trades"
    drill_fields: [created_date, product_type, total_api_trades]
  }

  measure: total_manual_trades {
    type: count
    filters: [trade_type: "Manual"]
    label: "Total Manual Trades"
    drill_fields: [created_date, product_type, total_manual_trades]
  }

  measure: automation_ratio {
    type: number
    sql: CASE
          WHEN ${total_trades} = 0 THEN NULL
          ELSE 100.0 * ${total_api_trades} / ${total_trades}
        END ;;
    value_format: "0.00\"%\""
    description: "Percentage of trades that are automated (API)"
    drill_fields: [product_type, total_api_trades, total_manual_trades, automation_ratio]
  }
}
