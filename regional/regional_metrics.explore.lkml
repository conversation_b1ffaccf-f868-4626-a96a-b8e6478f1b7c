include: "/regional/metrics/success_rate/*.view.lkml"
include: "/regional/metrics/time_to_complete/*.view.lkml"
include: "/regional/metrics/fxops/*.view.lkml"
include: "/regional/metrics/finops/*.view.lkml"
include: "/regional/metrics/etc/*.view.lkml"

# AutoFX Success Rate
explore: sr_autofx {
  from: sr_autofx_transactions
  view_name: sr_autofx_transactions
  group_label: "Regional"
  label: "Regional AutoFX Success Rate"

  sql_always_where:
    ${sr_autofx_transactions.type} = 'payment_conversion'
    AND ${sr_autofx_transactions.state} IN (
      'PAYMENT_CONVERSION_SETTLED',
      'PAYMENT_CONVERSION_MERCHANT_TRADE_FAILED'
    ) ;;
}

# Conversion Success Rate
explore: sr_conversion {
  from: sr_conversion_conversions
  view_name: sr_conversion_conversions
  group_label: "Regional"
  label: "Regional Conversion Success Rate"

  join: sr_conversion_quotes {
    type: left_outer
    sql_on: ${sr_conversion_conversions.quote_id} = ${sr_conversion_quotes.id} ;;
    relationship: many_to_one
  }

  sql_always_where:
    ${sr_conversion_conversions.state} IN ('CONVERSION_SETTLED', 'CONVERSION_FAILED') ;;
}

# Withdrawal Success Rate
explore: sr_withdrawal {
  from: sr_withdrawal_transactions
  view_name: sr_withdrawal_transactions
  group_label: "Regional"
  label: "Regional Withdrawal Success Rate"

  sql_always_where:
    ${sr_withdrawal_transactions.type} = 'withdrawal'
    AND ${sr_withdrawal_transactions.state} IN (
      'WITHDRAWAL_SETTLED',
      'WITHDRAWAL_INTERNAL_TRANSFER_SUCCEEDED',
      'WITHDRAWAL_INTERNAL_TRANSFER_SKIPPED',
      'WITHDRAWAL_HOLD_FAILED',
      'WITHDRAWAL_BOOKING_FAILED',
      'WITHDRAWAL_PAYOUT_FAILED',
      'WITHDRAWAL_SETTLEMENT_FAILED'
    ) ;;
}

# Conversion Time to Complete
explore: ttc_conversion {
  from: ttc_conversion_conversions
  view_name: ttc_conversion_conversions
  group_label: "Regional"
  label: "Regional Conversion Time to Complete"

  join: ttc_quotes {
    type: left_outer
    sql_on: ${ttc_conversion_conversions.quote_id} = ${ttc_quotes.id} ;;
    relationship: many_to_one
  }

  sql_always_where:
    ${ttc_conversion_conversions.state} IN ('CONVERSION_SETTLED', 'CONVERSION_FAILED') ;;
}

# Withdrawal Time to Complete
explore: ttc_withdrawal {
  from: ttc_withdrawal_transactions
  view_name: ttc_withdrawal_transactions
  group_label: "Regional"
  label: "Regional Withdrawal Time to Complete"

  sql_always_where:
    ${ttc_withdrawal_transactions.type} = 'withdrawal'
    AND ${ttc_withdrawal_transactions.state} IN (
      'WITHDRAWAL_SETTLED',
      'WITHDRAWAL_INTERNAL_TRANSFER_SUCCEEDED',
      'WITHDRAWAL_INTERNAL_TRANSFER_SKIPPED',
      'WITHDRAWAL_HOLD_FAILED',
      'WITHDRAWAL_BOOKING_FAILED',
      'WITHDRAWAL_PAYOUT_FAILED',
      'WITHDRAWAL_SETTLEMENT_FAILED'
    ) ;;
}

# FXOps Trade Automation Ratio
# This explore uses a single unified view that contains both API and Manual trades
explore: fxo_trade_automation_ratio {
  from: fxo_all_trades
  view_name: fxo_all_trades
  group_label: "Regional"
  label: "Regional FXOps Trade Automation Ratio"

  # Filter out NULL transaction categories
  sql_always_where: ${fxo_all_trades.product_type} IS NOT NULL ;;

  # No joins needed - everything is in the unified view
  # Users can filter by trade_type (API or Manual) to analyze different trade types
}

# FXOps Trade Time to Complete
# This explore uses a single unified view that contains both API and Manual trades
explore: fxo_trade_ttc {
  from: fxo_trade_ttc
  view_name: fxo_trade_ttc
  group_label: "Regional"
  label: "Regional FXOps Trade Time to Complete"

  # Filter out NULL transaction categories
  sql_always_where: ${fxo_trade_ttc.product_type} IS NOT NULL ;;

  # No joins needed - everything is in the unified view
  # Users can filter by trade_type (API or Manual) to analyze different trade types
}

# Payment Conversion Time to Complete
explore: ttc_autofx {
  from: ttc_autofx_transactions
  view_name: ttc_autofx_transactions
  group_label: "Regional"
  label: "Regional Payment Conversion Time to Complete"

  sql_always_where:
    ${ttc_autofx_transactions.type} = 'payment_conversion'
    AND ${ttc_autofx_transactions.state} IN (
      'PAYMENT_CONVERSION_SETTLED',
      'PAYMENT_CONVERSION_MERCHANT_TRADE_FAILED'
    ) ;;
}

# FXOps Trade Success Rate
explore: fxo_trade_sr {
  from: fxo_trade_sr
  view_name: fxo_trade_sr
  group_label: "Regional"
  label: "Regional FXOps Trade Success Rate"

  # Filter out NULL transaction categories
  sql_always_where: ${fxo_trade_sr.product_type} IS NOT NULL ;;
}

# FXOps Trade Settlement Ratio
explore: fxo_trade_settlement_ratio {
  from: fxo_trade_settlement_ratio
  view_name: fxo_trade_settlement_ratio
  group_label: "Regional"
  label: "Regional FXOps Trade Settlement Ratio"

  # Filter out NULL transaction categories
  sql_always_where: ${fxo_trade_settlement_ratio.product_type} IS NOT NULL ;;
}

# Insufficient Balance Errors
explore: insufficient_balance_errors {
  group_label: "Regional"
  label: "Regional FinOps Insufficient Balance Errors"

  # always_filter: {
  #   filters: [created_date: "30 days"]
  # }
}

explore: transaction_volume {
  from: transaction_volume
  view_name: transaction_volume
  group_label: "Regional"
  label: "Regional Transaction Volume"

  # Filter to only the relevant transaction types
  sql_always_where:
    ${transaction_volume.type} IN ('payment_conversion', 'conversion', 'withdrawal', 'payout', 'disbursement') ;;
}
