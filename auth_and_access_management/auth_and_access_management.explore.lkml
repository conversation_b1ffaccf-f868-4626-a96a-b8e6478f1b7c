include: "/auth_and_access_management/*.view.lkml"
include: "/businesses/ag_businesses.view.lkml"

explore: auth_platform_business {
  group_label: "Auth and Access Management"
  from: ag_business
  view_name: auth_business
}

explore: business_service_business {
  group_label: "Auth and Access Management"
  from: ag_businesses
  view_name: business_businesses
}

explore: all_business {
  group_label: "Auth and Access Management"
  from: ag_businesses
  view_name: all_business

  join: auth_business {
    from: ag_business
    sql_on: ${all_business.id} = ${auth_business.id} ;;
    type: full_outer
    relationship: one_to_one
  }
}
