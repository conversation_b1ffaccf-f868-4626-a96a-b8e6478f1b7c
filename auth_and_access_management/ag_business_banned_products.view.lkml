view: ag_business_banned_products {
  sql_table_name: clean__auth_platform_db.business_banned_products ;;
  suggestions: no

  dimension: business_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.business_id ;;
  }

  dimension: product_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.product_id ;;
  }

  dimension: reasons {
    type: string
    sql: ${TABLE}.reasons ;;
  }

  measure: count {
    type: count
    drill_fields: [business.id, product.id, product.name]
  }
}
