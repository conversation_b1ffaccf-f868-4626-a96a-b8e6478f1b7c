view: ag_product_auth_platform {
  sql_table_name: clean__auth_platform_db.product ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }

  measure: count {
    type: count
    drill_fields: [id, name, business_banned_products.count, business_banned_products_dev.count]
  }
}
