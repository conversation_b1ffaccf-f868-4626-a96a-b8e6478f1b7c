view: compilation_v1_v2_lms_raw {
  sql_table_name: transform__xencapital.compilation_v1_v2_lms_raw ;;
  drill_fields: [id]
  suggestions: no

  dimension: unique_id {
    primary_key: yes
    type: string
    sql: ${TABLE}.unique_id ;;
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }
  dimension: acquisition_channel {
    type: string
    sql: ${TABLE}.acquisition_channel ;;
  }
  dimension: am_details {
    type: string
    sql: ${TABLE}.am_details ;;
  }
  dimension: borrower_id {
    type: string
    sql: ${TABLE}.borrower_id ;;
  }
  dimension: code {
    type: string
    sql: ${TABLE}.code ;;
  }
  dimension: drawdown_code {
    type: string
    sql: ${TABLE}.drawdown_code ;;
  }
  dimension_group: drawdown {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.drawdown_date ;;
  }
  dimension: drawdown_transaction_id {
    type: string
    sql: ${TABLE}.drawdown_transaction_id ;;
  }
  dimension_group: end {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.end_date ;;
  }
  dimension: expected_interest_payment {
    type: number
    sql: ${TABLE}.expected_interest_payment ;;
  }
  dimension: expected_principal_payment {
    type: number
    sql: ${TABLE}.expected_principal_payment ;;
  }
  dimension_group: facility_expiry {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.facility_expiry_date ;;
  }
  dimension: industry {
    type: string
    sql: ${TABLE}.industry ;;
  }
  dimension: interest_rate {
    type: number
    sql: ${TABLE}.interest_rate ;;
  }
  dimension: interest_repayment {
    type: number
    sql: ${TABLE}.interest_repayment ;;
  }
  dimension: late_fee_payment {
    type: number
    sql: ${TABLE}.late_fee_payment ;;
  }
  dimension: lender_company_id {
    type: string
    sql: ${TABLE}.lender_company_id ;;
  }
  dimension_group: loan_agreement {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.loan_agreement_date ;;
  }
  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }
  dimension: loan_currency {
    type: string
    sql: ${TABLE}.loan_currency ;;
  }
  dimension: loan_id {
    type: string
    sql: ${TABLE}.loan_id ;;
  }
  dimension: loan_limit {
    type: number
    sql: ${TABLE}.loan_limit ;;
  }
  dimension: loan_purpose {
    type: string
    sql: ${TABLE}.loan_purpose ;;
  }
  dimension: loan_type {
    type: string
    sql: ${TABLE}.loan_type ;;
  }
  dimension: name {
    type: string
    sql: ${TABLE}.name ;;
  }
  dimension: parent_loan_id {
    type: string
    sql: ${TABLE}.parent_loan_id ;;
  }
  dimension_group: payment {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.payment_date ;;
  }
  dimension: principal_repayment {
    type: number
    sql: ${TABLE}.principal_repayment ;;
  }
  dimension_group: start {
    type: time
    timeframes: [raw, date, week, month, quarter, year]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.start_date ;;
  }
  dimension: third_party_id {
    type: string
    sql: ${TABLE}.third_party_id ;;
  }

  dimension: dpd {
    type: number
    sql: ${TABLE}.dpd ;;
  }

  measure: dpd_max {
    type: max
    sql: ${dpd} ;;
  }

  measure: drawdown_amount {
    type: sum_distinct
    sql:  CASE WHEN ${status} = 'Closed' THEN 0 ELSE ${expected_principal_payment} END;;
    sql_distinct_key: ${id} ;;
    value_format: "0.000,,\" M\""
    group_label: "Drawdown"
  }

  measure: principal_repayment_amount {
    type: sum
    sql:  CASE WHEN ${status} = 'Closed' THEN 0 ELSE ${principal_repayment} END;;
    value_format: "0.000,,\" M\""
    group_label: "Repayment"
  }

  measure: revenue_repayment_amount {
    type: sum
    sql:  CASE WHEN ${status} = 'Closed' THEN 0 ELSE ${interest_repayment} + ${late_fee_payment} END;;
    value_format: "0.000,,\" M\""
    group_label: "Repayment"
  }

  measure: OS {
    type: number
    sql: ${drawdown_amount} - ${principal_repayment_amount} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Dec_2022 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2022-12-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Dec_2022 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2022-12-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Dec_2022 {
    type: number
    sql: ${drawdown_amount_Dec_2022} - ${repayment_principal_Dec_2022} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Dec_2023 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2023-12-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Dec_2023 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2023-12-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Dec_2023 {
    type: number
    sql: ${drawdown_amount_Dec_2023} - ${repayment_principal_Dec_2023} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Dec_2024 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2024-12-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Dec_2024 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2024-12-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Dec_2024 {
    type: number
    sql: ${drawdown_amount_Dec_2024} - ${repayment_principal_Dec_2024} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Jan_2025 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2025-01-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Jan_2025 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2025-01-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Jan_2025 {
    type: number
    sql: ${drawdown_amount_Jan_2025} - ${repayment_principal_Jan_2025} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Feb_2025 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2025-02-28' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Feb_2025 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2025-02-28' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Feb_2025 {
    type: number
    sql: ${drawdown_amount_Feb_2025} - ${repayment_principal_Feb_2025} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Mar_2025 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2025-03-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Mar_2025 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2025-03-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Mar_2025 {
    type: number
    sql: ${drawdown_amount_Mar_2025} - ${repayment_principal_Mar_2025} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_Apr_2025 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2025-04-30' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_Apr_2025 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2025-04-30' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_Apr_2025 {
    type: number
    sql: ${drawdown_amount_Apr_2025} - ${repayment_principal_Apr_2025} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }

  measure: drawdown_amount_May_2025 {
    type: sum_distinct
    sql: CASE WHEN (cast(${drawdown_date} as date) <= cast('2025-05-31' as date) AND status in ('Actual','Open')) THEN ${expected_principal_payment} ELSE 0 END;;
    sql_distinct_key: ${id} ;;
    group_label: "Drawdown"
  }

  measure: repayment_principal_May_2025 {
    type: sum
    sql: CASE WHEN (cast(${payment_date} as date) <= cast('2025-05-31' as date) AND status in ('Actual','Open') ) THEN ${principal_repayment} ELSE 0 END;;
    group_label: "Repayment"
  }

  measure: OS_May_2025 {
    type: number
    sql: ${drawdown_amount_May_2025} - ${repayment_principal_May_2025} ;;
    value_format: "0.000,,\" M\""
    group_label: "Outstanding"
  }


}
