view: xencap_summary_by_month {
  derived_table: {
    sql: with drawdown_month as (select loan_id, CAST(drawdown_date as timestamp) as month, amount as drawdown_amount, 0 as repayment_amount, 0 as expected_payment, 0 as revenue, 0 as actual_principal, NULL as amortization_id, 0 as expected_principal, 0 as expected_interest, 0 as actual_interest
          from clean__xendit_loan_service.drawdown_transaction dt
          left join clean__xendit_loan_service.loan l on l.id = dt.loan_id
          where dt.deleted_at is null and l.loan_type = 'Revolving Line of Credit'
          ),

      repayment_month as (select loan_id, CAST(payment_date as timestamp) as month, 0 as drawwdown_amount, amount as repayment_amount, 0 as expected_payment, 0 as revenue, 0 as actual_principal, NULL as amortization_id, 0 as expected_principal, 0 as expected_interest, 0 as actual_interest
      from clean__xendit_loan_service.repayment_transaction
      where deleted_at is null
      ),

      amortization_month as (select loan_id, CAST(end_date as timestamp) as month, 0 as drawdown_amount, 0 as repayment_amount, expected_principal_payment+expected_interest_payment as expected_payment, 0 as revenue, 0 as actual_principal, id as amortization_id, expected_principal_payment as expected_principal, expected_interest_payment as expected_interest, 0 as actual_interest
      from clean__xendit_loan_service.repayment_amortization
      where deleted_at is null and status = 'Open'
      ),

      revenue_month as (select rt.loan_id, CAST(b.payment_date as timestamp) as month, 0 as drawdown_amount, 0 as repayment_amount, 0 as expected_payment, b.actual_interest_payment+b.late_fee as revenue, b.actual_principal_payment as actual_principal, NULL as amortization_id, 0 as expected_principal, 0 as expected_interest, b.actual_interest_payment as actual_interest
      from clean__xendit_loan_service.repayment_transaction rt
      left join clean__xendit_loan_service.repayment_breakdown b on b.repayment_transaction_id = rt.id
      where rt.deleted_at is null
      ),

      TL_drawdown_month as (select id, coalesce(CAST(expected_drawdown_date as timestamp),cast(created_at as timestamp)) as month, loan_limit as drawdown_amount, 0 as repayment_amount, 0 as expected_payment, 0 as revenue, 0 as actual_principal, NULL as amortization_id, 0 as expected_principal, 0 as expected_interest, 0 as actual_interest
      from clean__xendit_loan_service.loan
      where deleted_at is null and is_old_gmf = false and loan_type = 'Term Loan'
      ),

      RLOC_ND as (select id, coalesce(CAST(expected_drawdown_date as timestamp),cast(created_at as timestamp)) as month, 0 as drawdown_amount, 0 as repayment_amount, 0 as expected_payment, 0 as revenue, 0 as actual_principal, NULL as amortization_id, 0 as expected_principal, 0 as expected_interest, 0 as actual_interest
      from clean__xendit_loan_service.loan
      where deleted_at is null and is_old_gmf = false and loan_type = 'Revolving Line of Credit' and id not in (Select loan_id from drawdown_month)
      )


            select loan_id, month, drawdown_amount, repayment_amount, expected_payment, revenue, actual_principal, amortization_id, expected_principal, expected_interest, actual_interest
      from
      (select * from drawdown_month
      union all
      select * from repayment_month
      union all
      select * from amortization_month
      union all
      select * from revenue_month
      union all
      select * from TL_drawdown_month
      union all
      select * from RLOC_ND)
      order by 2
      ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: loan_id {
    type: string
    primary_key: yes
    sql: ${TABLE}.loan_id ;;
  }

  dimension_group: date {
  type: time
  datatype: timestamp
  timeframes: [raw, date, week, month, quarter, year]
  sql: dateadd(hour,8, cast(${TABLE}.month as timestamp));;
}

  dimension: drawdown_amount {
    type: number
    sql: ${TABLE}.drawdown_amount ;;
  }

  dimension: repayment_amount {
    type: number
    sql: ${TABLE}.repayment_amount ;;
  }

  dimension: expected_payment {
    type: number
    sql: ${TABLE}.expected_payment ;;
  }

  dimension: expected_principal {
    type: number
    sql: ${TABLE}.expected_principal ;;
  }

  dimension: expected_interest {
    type: number
    sql: ${TABLE}.expected_interest ;;
  }

  dimension: revenue {
    type: number
    sql: ${TABLE}.revenue ;;
  }

  dimension: actual_interest {
    type: number
    sql: ${TABLE}.actual_interest ;;
  }

  dimension: actual_principal{
    type: number
    sql: ${TABLE}.actual_principal ;;
  }

  dimension: amortization_id {
    type: string
    sql: ${TABLE}.amortization_id ;;
  }

  measure: drawdown_amount_sum {
    type: sum
    sql: ${TABLE}.drawdown_amount ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: repayment_amount_sum {
    type: sum
    sql: ${TABLE}.repayment_amount ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: expected_payment_sum {
    type: sum
    sql: ${TABLE}.expected_payment ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: expected_principal_sum {
    type: sum
    sql: ${TABLE}.expected_principal ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: expected_interest_sum {
    type: sum
    sql: ${TABLE}.expected_interest ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: revenue_sum {
    type: sum
    sql: ${TABLE}.revenue ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: actual_principal_sum {
    type: sum
    sql: ${TABLE}.actual_principal ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  measure: actual_interest_sum {
    type: sum
    sql: ${TABLE}.actual_interest ;;
    value_format: "0.000,,\" M\""
    drill_fields: [detail*]
  }

  set: detail {
    fields: [
      loan_id,
      date_date,
      drawdown_amount,
      repayment_amount,
      expected_payment,
      revenue
    ]
  }
}
