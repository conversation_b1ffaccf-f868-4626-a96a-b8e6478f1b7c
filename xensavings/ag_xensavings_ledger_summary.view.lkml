view: xensavings_ledger_summary {
  derived_table: {
    sql:
          WITH prep AS(
          SELECT *, row_number() over(partition by account_id, u_from order by created desc) as rn
          FROM clean__bpr_mvp_prod_live.ledger_summary)

          SELECT * FROM prep
          WHERE rn = 1
        ;;
  }
  drill_fields: [id]
  suggestions: yes

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension_group: from {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.u_from ;;
  }

  dimension_group: to {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.u_to ;;
  }

  dimension: account_id {
    type: string
    # hidden: yes
    sql: ${TABLE}.account_id ;;
  }

  dimension: closing_balance {
    type: number
    sql: ${TABLE}.closing_balance ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.dt ;;
  }

  dimension: entries {
    type: string
    sql: ${TABLE}.entries ;;
  }

  dimension: entry_count {
    type: number
    sql: ${TABLE}.entry_count ;;
  }

  dimension: highest_balance {
    type: number
    sql: ${TABLE}.highest_balance ;;
  }

  dimension: lowest_balance {
    type: number
    sql: ${TABLE}.lowest_balance ;;
  }

  dimension: opening_balance {
    type: number
    sql: ${TABLE}.opening_balance ;;
  }

  dimension: pending_transaction_count {
    type: number
    sql: ${TABLE}.pending_transaction_count ;;
  }

  dimension: pending_transactions {
    type: string
    sql: ${TABLE}.pending_transactions ;;
  }

  measure: sum_of_opening_balance {
    type: sum
    sql: ${opening_balance} ;;
  }

  measure: average_of_opening_balance {
    type: average
    sql: ${opening_balance} ;;
  }

  measure: median_of_opening_balance {
    type: median
    sql: ${opening_balance} ;;
  }

  measure: sum_of_closing_balance {
    type: sum
    sql: ${closing_balance} ;;
  }

  measure: average_of_closing_balance {
    type: average
    sql: ${closing_balance} ;;
  }

  measure: median_of_closing_balance {
    type: median
    sql: ${closing_balance} ;;
  }
}
