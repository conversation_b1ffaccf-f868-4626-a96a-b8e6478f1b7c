view: ag_xensavings_active_users {
  derived_table: {
    sql: SELECT
          xensavings_ledger.dt as `xensavings_ledger.dt`,
          xensavings_account_holders.id  AS `xensavings_transactions.account_holders.id`,
          CASE WHEN MAX(date_diff(day, DATE_TRUNC('DAY', CAST(xensavings_ledger.dt  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(xensavings_transactions.created AS TIMESTAMP)))) >= -1 THEN 1
            ELSE 0
            END AS DAU,
          CASE WHEN MAX(date_diff(day, DATE_TRUNC('DAY', CAST(xensavings_ledger.dt  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(xensavings_transactions.created AS TIMESTAMP)))) >= -7 THEN 1
            ELSE 0
            END AS WAU,
          CASE WHEN MAX(date_diff(day, DATE_TRUNC('DAY', CAST(xensavings_ledger.dt  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(xensavings_transactions.created AS TIMESTAMP)))) >= -30 THEN 1
            ELSE 0
            END AS MAU
      FROM clean__bpr_mvp_prod_live.ledger_summary AS xensavings_ledger
      LEFT JOIN clean__bpr_mvp_prod_live.transaction  AS xensavings_transactions ON xensavings_transactions.account_id = xensavings_ledger.account_id
      LEFT JOIN clean__bpr_mvp_prod_live.account  AS xensavings_accounts ON xensavings_ledger.account_id = xensavings_accounts.id
      LEFT JOIN clean__bpr_mvp_prod_live.account_account_holders_account_holder  AS xensavings_account_account_holders_account_holders ON xensavings_accounts.id = xensavings_account_account_holders_account_holders.accountid
      LEFT JOIN clean__bpr_mvp_prod_live.account_holder  AS xensavings_account_holders ON xensavings_account_account_holders_account_holders.accountholderid = xensavings_account_holders.id
      WHERE  (xensavings_transactions.category ) IN ('DEPOSIT', 'TRANSFER')
      AND DATE_TRUNC('DAY', CAST(xensavings_transactions.created AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(xensavings_ledger.dt  AS TIMESTAMP))
      AND xensavings_account_holders.id is not null
      AND xensavings_accounts.status = 'ACTIVE'
      AND xensavings_transactions.status = 'COMPLETED'
      GROUP BY 1,2
      ORDER BY 1 DESC
       ;;
  }

  suggestions: yes

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dt {
    type: time
    timeframes: [
      raw,
      date,
      week,
      month,
      quarter,
      year
    ]
    convert_tz: no
    datatype: date
    sql: ${TABLE}.`xensavings_ledger.dt` ;;
  }

  dimension: account_holders_id {
    type: string
    sql: ${TABLE}.`xensavings_transactions.account_holders.id` ;;
  }

  dimension: DAU {
    type: number
    sql: ${TABLE}.DAU ;;
  }

  dimension: WAU_rolling7D {
    type: number
    sql: ${TABLE}.WAU ;;
  }

  dimension: MAU_rolling30D {
    type: number
    sql: ${TABLE}.MAU ;;
  }

  set: detail {
    fields: [account_holders_id, DAU, WAU_rolling7D, MAU_rolling30D]
  }

  measure: Sum_DAU {
    type: sum
    sql: ${TABLE}.DAU ;;
    drill_fields: [account_holders_id]
  }

  measure: Sum_WAU_rolling7D {
    type: sum
    sql: ${TABLE}.WAU ;;
    drill_fields: [account_holders_id]
  }

  measure: Sum_MAU_rolling30D {
    type: sum
    sql: ${TABLE}.MAU ;;
    drill_fields: [account_holders_id]
  }

}
