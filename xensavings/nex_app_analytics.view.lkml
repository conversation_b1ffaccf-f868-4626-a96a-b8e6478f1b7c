
view: nex_app_analytics {
  derived_table: {
    sql: WITH kyc AS
              (SELECT u.id AS user_id, u.phone_number AS phone_number,
                      bc.banking_api_provider AS banking_api_provider,
                      min(bc.created_at) AS kyc_approved_date
               FROM clean__nex_app.users u
               LEFT JOIN clean__nex_app.banking_clients bc ON bc.user_id = u.id
               WHERE bc.account_status = 'ACTIVE'
               GROUP BY u.id, u.phone_number,
                        bc.banking_api_provider),
                 deposit AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_deposit_date,
                      max(t.created_at) AS last_deposit_date
               FROM clean__nex_app.transactions t
               WHERE t.type = '7'
                 AND t.status = '2'
               GROUP BY t.user_id),
                 bpr_transfer AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_bank_transfer_date,
                      max(t.created_at) AS last_bank_transfer_date
               FROM clean__nex_app.transactions t
               LEFT JOIN clean__nex_app.ts_savings_to_bank_account_transactions tstbat ON tstbat.transaction_id = t.id
               LEFT JOIN clean__nex_app.banking_clients bc ON bc.user_id = t.user_id
               WHERE t.type = '6'
                 AND t.status = '2'
                 AND tstbat.account_bank_code != 'BPRAC'
                 AND bc.banking_account_code = 'XENSAVINGS'
                 AND bc.banking_api_provider = 'BPR_API'
               GROUP BY t.user_id),
                 bpr_send_to_friend AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_send_friend_date,
                      max(t.created_at) AS last_send_friend_date
               FROM clean__nex_app.transactions t
               LEFT JOIN clean__nex_app.ts_savings_to_bank_account_transactions tstbat ON tstbat.transaction_id = t.id
               LEFT JOIN clean__nex_app.banking_clients bc ON bc.user_id = t.user_id
               WHERE t.type = '6'
                 AND t.status = '2'
                 AND tstbat.account_bank_code = 'BPRAC'
                 AND bc.banking_account_code = 'XENSAVINGS'
                 AND bc.banking_api_provider = 'BPR_API'
               GROUP BY t.user_id),
                 bss_transfer AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_bank_transfer_date,
                      max(t.created_at) AS last_bank_transfer_date
               FROM clean__nex_app.transactions t
               LEFT JOIN clean__nex_app.ts_savings_to_bank_account_transactions tstbat ON tstbat.transaction_id = t.id
               LEFT JOIN clean__nex_app.banking_clients bc ON bc.user_id = t.user_id
               WHERE t.type = '6'
                 AND t.status = '2'
                 AND tstbat.account_bank_code != 'SAHABAT_SAMPOERNA'
                 AND bc.banking_account_code = 'XENSAVINGS'
                 AND bc.banking_api_provider = 'BAAS'
               GROUP BY t.user_id),
                 bss_send_to_friend AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_send_friend_date,
                      max(t.created_at) AS last_send_friend_date
               FROM clean__nex_app.transactions t
               LEFT JOIN clean__nex_app.ts_savings_to_bank_account_transactions tstbat ON tstbat.transaction_id = t.id
               LEFT JOIN clean__nex_app.banking_clients bc ON bc.user_id = t.user_id
               WHERE t.type = '6'
                 AND t.status = '2'
                 AND tstbat.account_bank_code = 'SAHABAT_SAMPOERNA'
                 AND bc.banking_account_code = 'XENSAVINGS'
                 AND bc.banking_api_provider = 'BAAS'
               GROUP BY t.user_id),
                 past_7day_money_in AS
              (SELECT t.user_id,
                      sum(t.amount) AS past_7_day_money_in_sum
               FROM clean__nex_app.transactions t
               WHERE t.type = '7'
                 AND t.status = '2'
                 AND t.created_at >= CURRENT_TIMESTAMP - INTERVAL '168' HOUR
               GROUP BY t.user_id) ,
                 past_7day_money_out AS
              (SELECT t.user_id,
                      sum(t.amount) AS past_7_day_money_out_sum
               FROM clean__nex_app.transactions t
               WHERE t.type IN ('6',
                                '9')
                 AND t.status = '2'
                 AND t.created_at >= CURRENT_TIMESTAMP - INTERVAL '168' HOUR
               GROUP BY t.user_id) ,
                 past_7day_bills AS
              (SELECT t.user_id,
                      sum(t.amount) AS past_7_day_bills_sum
               FROM clean__nex_app.transactions t
               WHERE t.type = '11'
                 AND t.status = '2'
                 AND t.created_at >= CURRENT_TIMESTAMP - INTERVAL '168' HOUR
               GROUP BY t.user_id),
                 past_7day_qr AS
              (SELECT t.user_id,
                      sum(t.amount) AS past_7_day_qr_sum
               FROM clean__nex_app.transactions t
               WHERE t.type = '13'
                 AND t.status = '2'
                 AND t.created_at >= CURRENT_TIMESTAMP - INTERVAL '168' HOUR
               GROUP BY t.user_id) ,
                 ewallet AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_ewallet_topup_date,
                      max(t.created_at) AS last_ewallet_topup_date
               FROM clean__nex_app.transactions t
               WHERE t.type = '9'
                 AND t.status = '2'
               GROUP BY t.user_id),
                 bills AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_bills_date,
                      max(t.created_at) AS last_bills_date
               FROM clean__nex_app.transactions t
               WHERE t.type = '11'
                 AND t.status = '2'
               GROUP BY t.user_id),
                 qris AS
              (SELECT t.user_id,
                      min(t.created_at) AS first_qris_date,
                      max(t.created_at) AS last_qris_date
               FROM clean__nex_app.transactions t
               WHERE t.type = '13'
                 AND t.status = '2'
               GROUP BY t.user_id),
                 aum AS
              (SELECT u.id AS user_id,
                      SUM(t.amount) * 365 / CASE
                                                WHEN bc.banking_api_provider = 'BAAS' AND min(t.created_at) >= DATE '2024-11-01' THEN 0.035
                                                WHEN bc.banking_api_provider = 'BAAS' THEN 0.0425
                                                WHEN bc.banking_api_provider = 'BPR_API' THEN 0.05
                                            END AS estimated_balance
               FROM clean__nex_app.banking_clients bc
               LEFT JOIN clean__nex_app.ts_banking_interest_transactions tbit ON tbit.account_id = bc.account_id
               LEFT JOIN clean__nex_app.transactions t ON t.id = tbit.transaction_id
               LEFT JOIN clean__nex_app.users u ON t.user_id = u.id
               WHERE bc.banking_api_provider IN ('BAAS',
                                                 'BPR_API')
                 AND t.type = '8'
                 AND t.created_at >= CURRENT_TIMESTAMP - INTERVAL '24' HOUR
               GROUP BY u.id,
                        bc.banking_api_provider),
                 aum_d30ago AS (WITH latest_transactions AS
                                  ( SELECT u.id AS user_id,
                                           tbit.account_id,
                                           t.amount,
                                           t.created_at,
                                           bc.banking_api_provider,
                                           ROW_NUMBER() OVER (PARTITION BY u.id, tbit.account_id
                                                              ORDER BY t.created_at DESC) AS rn
                                   FROM clean__nex_app.banking_clients bc
                                   LEFT JOIN clean__nex_app.ts_banking_interest_transactions tbit ON tbit.account_id = bc.account_id
                                   LEFT JOIN clean__nex_app.transactions t ON t.id = tbit.transaction_id
                                   LEFT JOIN clean__nex_app.users u ON t.user_id = u.id
                                   WHERE bc.banking_api_provider IN ('BAAS',
                                                                     'BPR_API')
                                     AND t.type = '8'
                                     AND DATE(t.created_at) = DATE(CURRENT_DATE - INTERVAL '30' DAY) )
                                SELECT user_id,
                                       SUM(amount) * 365 / CASE
                                                              WHEN banking_api_provider = 'BAAS' AND max(created_at) >= DATE '2024-11-01' THEN 0.035
                                                              WHEN banking_api_provider = 'BAAS' THEN 0.0425
                                                               WHEN banking_api_provider = 'BPR_API' THEN 0.05
                                                           END AS estimated_balance
                                FROM latest_transactions
                                WHERE rn = 1
                                GROUP BY user_id,
                                         banking_api_provider),
                 aum_d7ago AS (WITH latest_transactions AS
                                 ( SELECT u.id AS user_id,
                                          tbit.account_id,
                                          t.amount,
                                          t.created_at,
                                          bc.banking_api_provider,
                                          ROW_NUMBER() OVER (PARTITION BY u.id, tbit.account_id
                                                             ORDER BY t.created_at DESC) AS rn
                                  FROM clean__nex_app.banking_clients bc
                                  LEFT JOIN clean__nex_app.ts_banking_interest_transactions tbit ON tbit.account_id = bc.account_id
                                  LEFT JOIN clean__nex_app.transactions t ON t.id = tbit.transaction_id
                                  LEFT JOIN clean__nex_app.users u ON t.user_id = u.id
                                  WHERE bc.banking_api_provider IN ('BAAS',
                                                                    'BPR_API')
                                    AND t.type = '8'
                                    AND DATE(t.created_at) = DATE(CURRENT_DATE - INTERVAL '7' DAY) )
                               SELECT user_id,
                                      SUM(amount) * 365 / CASE
                                                              WHEN banking_api_provider = 'BAAS' AND max(created_at) >= DATE '2024-11-01' THEN 0.035
                                                              WHEN banking_api_provider = 'BAAS' THEN 0.0425
                                                              WHEN banking_api_provider = 'BPR_API' THEN 0.05
                                                          END AS estimated_balance
                               FROM latest_transactions
                               WHERE rn = 1
                               GROUP BY user_id,
                                        banking_api_provider),
                 referral AS
              (SELECT r.user_id AS user_id,
                      COUNT(DISTINCT CASE WHEN rubr.referral_product_type = 'NEX_BANK_ACCOUNT' THEN rubr.id END) AS nex_account_referral_count,
                      COUNT(DISTINCT CASE WHEN rubr.referral_product_type = 'NEX_CARD' THEN rubr.id END) AS nex_card_referral_count
               FROM clean__nex_app.referrals r
               LEFT JOIN clean__nex_app.registered_users_by_referrals rubr ON r.id = rubr.referral_id
               WHERE rubr.referral_product_type IN ('NEX_BANK_ACCOUNT',
                                                    'NEX_CARD')
               GROUP BY r.user_id),
                 envelopes AS
              (SELECT bc.user_id AS user_id,
                      COUNT(DISTINCT(bc.id)) AS envelopes_opened_count,
                      COUNT(DISTINCT CASE WHEN bc.account_status = 'ACTIVE' THEN bc.id END) AS envelopes_active_count
               FROM clean__nex_app.banking_clients bc
               WHERE bc.banking_account_code != 'XENSAVINGS'
               GROUP BY bc.user_id)
            SELECT kyc.user_id, kyc.phone_number,
                   banking_api_provider,
                   kyc.kyc_approved_date,
                   deposit.first_deposit_date,
                   deposit.last_deposit_date,
                   date_diff(MINUTE, kyc.kyc_approved_date, deposit.first_deposit_date) AS time_to_first_deposit,
                   CASE
                       WHEN kyc.banking_api_provider = 'BPR_API' THEN bpr_transfer.first_bank_transfer_date
                       WHEN kyc.banking_api_provider = 'BAAS' THEN bss_transfer.first_bank_transfer_date
                   END AS first_bank_transfer,
                   CASE
                       WHEN kyc.banking_api_provider = 'BPR_API' THEN bpr_send_to_friend.first_send_friend_date
                       WHEN kyc.banking_api_provider = 'BAAS' THEN bss_send_to_friend.first_send_friend_date
                   END AS first_send_to_friend,
                   ewallet.first_ewallet_topup_date,
                   bills.first_bills_date,
                   qris.first_qris_date,
                   CASE
                       WHEN kyc.banking_api_provider = 'BPR_API' THEN bpr_transfer.last_bank_transfer_date
                       WHEN kyc.banking_api_provider = 'BAAS' THEN bss_transfer.last_bank_transfer_date
                   END AS last_bank_transfer,
                   CASE
                       WHEN kyc.banking_api_provider = 'BPR_API' THEN bpr_send_to_friend.last_send_friend_date
                       WHEN kyc.banking_api_provider = 'BAAS' THEN bss_send_to_friend.last_send_friend_date
                   END AS last_send_to_friend,
                   ewallet.last_ewallet_topup_date,
                   bills.last_bills_date,
                   qris.last_qris_date,
                   aum.estimated_balance,
                   aum_d30ago.estimated_balance AS estimated_balance_d30ago,
                   aum_d7ago.estimated_balance AS estimated_balance_d7ago,
                   CASE
                       WHEN aum.estimated_balance IS NULL THEN 'Went to zero'
                       WHEN aum_d30ago.estimated_balance IS NULL THEN 'Up from zero'
                       ELSE CONCAT(CAST(ROUND(((aum.estimated_balance - aum_d30ago.estimated_balance) / aum_d30ago.estimated_balance) * 100, 2) AS STRING), '%')
                   END AS d30_aum_change,
                   CASE
                       WHEN aum.estimated_balance IS NULL THEN 'Went to zero'
                       WHEN aum_d7ago.estimated_balance IS NULL THEN 'Up from zero'
                       ELSE CONCAT(CAST(ROUND(((aum.estimated_balance - aum_d7ago.estimated_balance) / aum_d7ago.estimated_balance) * 100, 2) AS STRING), '%')
                   END AS d7_aum_change,
                   past_7day_money_in.past_7_day_money_in_sum,
                   past_7day_money_out.past_7_day_money_out_sum,
                   past_7day_bills.past_7_day_bills_sum,
                   past_7day_qr.past_7_day_qr_sum,
                   CASE
                       WHEN deposit.last_deposit_date IS NULL THEN 'INACTIVE'
                       WHEN (GREATEST(COALESCE(deposit.last_deposit_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(ewallet.last_ewallet_topup_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bills.last_bills_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(qris.last_qris_date, CAST('0001-12-31' AS TIMESTAMP))) >= CURRENT_TIMESTAMP - INTERVAL '30' DAY) THEN 'ACTIVE'
                       WHEN (GREATEST(COALESCE(deposit.last_deposit_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(ewallet.last_ewallet_topup_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bills.last_bills_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(qris.last_qris_date, CAST('0001-12-31' AS TIMESTAMP))) >= CURRENT_TIMESTAMP - INTERVAL '90' DAY) THEN 'SOFT CHURN'
                       WHEN (GREATEST(COALESCE(deposit.last_deposit_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(ewallet.last_ewallet_topup_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bills.last_bills_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(qris.last_qris_date, CAST('0001-12-31' AS TIMESTAMP))) < CURRENT_TIMESTAMP - INTERVAL '90' DAY) THEN 'HARD CHURN'
                   END AS transaction_status,
                   CASE
                       WHEN deposit.last_deposit_date IS NULL THEN 9999
                       ELSE CAST(DATE_DIFF(day, GREATEST(COALESCE(deposit.last_deposit_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_transfer.last_bank_transfer_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bpr_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bss_send_to_friend.last_send_friend_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(ewallet.last_ewallet_topup_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(bills.last_bills_date, CAST('0001-12-31' AS TIMESTAMP)), COALESCE(qris.last_qris_date, CAST('0001-12-31' AS TIMESTAMP))), CURRENT_DATE) AS INTEGER)
                   END AS days_since_transaction,
                   referral.nex_account_referral_count AS nex_account_referral_count,
                   referral.nex_card_referral_count AS nex_card_referral_count,
                   envelopes.envelopes_opened_count AS envelopes_opened_count,
                   envelopes.envelopes_active_count AS envelopes_active_count
            FROM kyc
            LEFT JOIN deposit ON deposit.user_id = kyc.user_id
            LEFT JOIN bpr_transfer ON bpr_transfer.user_id = kyc.user_id
            LEFT JOIN bpr_send_to_friend ON bpr_send_to_friend.user_id = kyc.user_id
            LEFT JOIN bss_transfer ON bss_transfer.user_id = kyc.user_id
            LEFT JOIN bss_send_to_friend ON bss_send_to_friend.user_id = kyc.user_id
            LEFT JOIN past_7day_money_in ON past_7day_money_in.user_id = kyc.user_id
            LEFT JOIN past_7day_money_out ON past_7day_money_out.user_id = kyc.user_id
            LEFT JOIN past_7day_bills ON past_7day_bills.user_id = kyc.user_id
            LEFT JOIN past_7day_qr ON past_7day_qr.user_id = kyc.user_id
            LEFT JOIN ewallet ON ewallet.user_id = kyc.user_id
            LEFT JOIN bills ON bills.user_id = kyc.user_id
            LEFT JOIN qris ON qris.user_id = kyc.user_id
            LEFT JOIN aum ON aum.user_id = kyc.user_id
            LEFT JOIN aum_d30ago ON aum_d30ago.user_id = kyc.user_id
            LEFT JOIN aum_d7ago ON aum_d7ago.user_id = kyc.user_id
            LEFT JOIN referral ON referral.user_id = kyc.user_id
            LEFT JOIN envelopes ON envelopes.user_id = kyc.user_id ;;
  }

  suggestions: no
  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: phone_number {
    type: string
    sql: ${TABLE}.phone_number ;;
  }

  dimension: banking_api_provider {
    type: string
    sql: ${TABLE}.banking_api_provider ;;
  }

  dimension_group: kyc_approved_date {
    type: time
    sql: ${TABLE}.kyc_approved_date ;;
  }

  dimension_group: first_deposit_date {
    type: time
    sql: ${TABLE}.first_deposit_date ;;
  }

  dimension_group: last_deposit_date {
    type: time
    sql: ${TABLE}.last_deposit_date ;;
  }

  dimension: time_to_first_deposit {
    type: number
    sql: ${TABLE}.time_to_first_deposit ;;
  }

  dimension_group: first_bank_transfer {
    type: time
    sql: ${TABLE}.first_bank_transfer ;;
  }

  dimension_group: first_send_to_friend {
    type: time
    sql: ${TABLE}.first_send_to_friend ;;
  }

  dimension_group: first_ewallet_topup_date {
    type: time
    sql: ${TABLE}.first_ewallet_topup_date ;;
  }

  dimension_group: first_bills_date {
    type: time
    sql: ${TABLE}.first_bills_date ;;
  }

  dimension_group: first_qris_date {
    type: time
    sql: ${TABLE}.first_qris_date ;;
  }

  dimension_group: last_bank_transfer {
    type: time
    sql: ${TABLE}.last_bank_transfer ;;
  }

  dimension_group: last_send_to_friend {
    type: time
    sql: ${TABLE}.last_send_to_friend ;;
  }

  dimension_group: last_ewallet_topup_date {
    type: time
    sql: ${TABLE}.last_ewallet_topup_date ;;
  }

  dimension_group: last_bills_date {
    type: time
    sql: ${TABLE}.last_bills_date ;;
  }

  dimension_group: last_qris_date {
    type: time
    sql: ${TABLE}.last_qris_date ;;
  }

  dimension: estimated_balance {
    type: number
    sql: ${TABLE}.estimated_balance ;;
  }

  dimension: estimated_balance_d30ago {
    type: number
    sql: ${TABLE}.estimated_balance_d30ago ;;
  }

  dimension: estimated_balance_d7ago {
    type: number
    sql: ${TABLE}.estimated_balance_d7ago ;;
  }

  dimension: d30_aum_change {
    type: string
    sql: ${TABLE}.d30_aum_change ;;
  }

  dimension: d7_aum_change {
    type: string
    sql: ${TABLE}.d7_aum_change ;;
  }

  dimension: past_7_day_money_in_sum {
    type: number
    sql: ${TABLE}.past_7_day_money_in_sum ;;
  }

  dimension: past_7_day_money_out_sum {
    type: number
    sql: ${TABLE}.past_7_day_money_out_sum ;;
  }

  dimension: past_7_day_bills_sum {
    type: number
    sql: ${TABLE}.past_7_day_bills_sum ;;
  }

  dimension: past_7_day_qr_sum {
    type: number
    sql: ${TABLE}.past_7_day_qr_sum ;;
  }

  dimension: transaction_status {
    type: string
    sql: ${TABLE}.transaction_status ;;
  }

  dimension: days_since_transaction {
    type: number
    sql: ${TABLE}.days_since_transaction ;;
  }

  dimension: nex_account_referral_count {
    type: number
    sql: ${TABLE}.nex_account_referral_count ;;
  }

  dimension: nex_card_referral_count {
    type: number
    sql: ${TABLE}.nex_card_referral_count ;;
  }

  dimension: envelopes_opened_count {
    type: number
    sql: ${TABLE}.envelopes_opened_count ;;
  }

  dimension: envelopes_active_count {
    type: number
    sql: ${TABLE}.envelopes_active_count ;;
  }

  set: detail {
    fields: [
      user_id,
      phone_number,
      banking_api_provider,
      kyc_approved_date_time,
      first_deposit_date_time,
      last_deposit_date_time,
      time_to_first_deposit,
      first_bank_transfer_time,
      first_send_to_friend_time,
      first_ewallet_topup_date_time,
      first_bills_date_time,
      first_qris_date_time,
      last_bank_transfer_time,
      last_send_to_friend_time,
      last_ewallet_topup_date_time,
      last_bills_date_time,
      last_qris_date_time,
      estimated_balance,
      estimated_balance_d30ago,
      estimated_balance_d7ago,
      d30_aum_change,
      d7_aum_change,
      past_7_day_money_in_sum,
      past_7_day_money_out_sum,
      past_7_day_bills_sum,
      past_7_day_qr_sum,
      transaction_status,
      days_since_transaction,
      nex_account_referral_count,
      nex_card_referral_count,
      envelopes_opened_count,
      envelopes_active_count
    ]
  }
}
