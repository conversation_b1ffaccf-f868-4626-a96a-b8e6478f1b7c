view: ag_xendit_app_user_transaction_deepdive {
  derived_table: {
    sql: SELECT
      main_transactions.id as txn_id,
      kyc_user_table.id as txn_user_id,
      kyc_user_table.banking_api_provider as banking_api_provider,
      main_transactions.created_at as txn_date,
      main_transactions.type as txn_type,
      kyc_user_table.kyc_date as kyc_date,
      kyc_user_table.app_signup_date as app_signup_date,
      first_txn_table.first_txn_date as first_txn_date,
      date_diff(day, DATE_TRUNC('DAY', CAST(main_transactions.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP))) as days_since_txn
      FROM (
        SELECT banking_clients.user_id AS id , banking_clients.banking_api_provider, CAST(banking_clients.updated_at AS TIMESTAMP) as kyc_date, app_signup_date
        FROM clean__nex_app.banking_clients as banking_clients
        LEFT JOIN
          (SELECT id, CAST(created_at AS TIMESTAMP) as app_signup_date FROM clean__nex_app.users GROUP BY 1,2
          ) as user_table ON user_table.id = banking_clients.user_id
        WHERE banking_clients.account_status = 'ACTIVE' AND banking_clients.banking_account_code IN ('XENSAVINGS','ID_BSS')
        GROUP BY 1,2,3,4
        ) AS kyc_user_table
      LEFT JOIN (SELECT * FROM clean__nex_app.transactions WHERE status = '2' AND "type" NOT IN ('8')) AS main_transactions ON kyc_user_table.id = main_transactions.user_id
      LEFT JOIN (SELECT user_id, type, CAST(MIN(created_at) as TIMESTAMP) AS first_txn_date FROM clean__nex_app.transactions GROUP BY 1,2) as first_txn_table ON first_txn_table.user_id = kyc_user_table.id AND first_txn_table.type = main_transactions.type
      -- Includes ALL completed transaction types except for interest
      -- AND kyc_user_table.id = '5eab2419-b324-4e84-a7e0-934fd1b2951a'
      GROUP BY 1,2,3,4,5,6,7,8
      ORDER BY 1 desc, 3 desc
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: txn_id {
    description: "unique transaction ID"
    primary_key: yes
    type: string
    sql: ${TABLE}.txn_id ;;
  }

  dimension: txn_user_id {
    type: string
    sql: ${TABLE}.txn_user_id ;;
  }

  dimension_group: txn_date {
    type: time
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.txn_date ;;
  }

  dimension: txn_type {
    description: "type of transaction from Nex App"
    case: {
      when: {
        sql: ${TABLE}.txn_type = '0' ;;
        label: "Top up"
      }
      when: {
        sql: ${TABLE}.txn_type = '1' ;;
        label: "Debit"
      }
      when: {
        sql: ${TABLE}.txn_type = '2' ;;
        label: "Direct debit"
      }
      when: {
        sql: ${TABLE}.txn_type = '3' ;;
        label: "Withdraw"
      }
      when: {
        sql: ${TABLE}.txn_type = '4' ;;
        label: "Wallet"
      }
      when: {
        sql: ${TABLE}.txn_type = '5' ;;
        label: "Pulsa"
      }
      when: {
        sql: ${TABLE}.txn_type = '6' ;;
        label: "Banking account transfer"
      }
      when: {
        sql: ${TABLE}.txn_type = '7' ;;
        label: "Banking deposit"
      }
      when: {
        sql: ${TABLE}.txn_type = '8' ;;
        label: "Banking interest (gross)"
      }
      when: {
        sql: ${TABLE}.txn_type = '9' ;;
        label: "Banking eWallet transaction"
      }
      when: {
        sql: ${TABLE}.txn_type = '10' ;;
        label: "Banking move funds"
      }
      when: {
        sql: ${TABLE}.txn_type = '11' ;;
        label: "Bill payment"
      }
      when: {
        sql: ${TABLE}.txn_type = '12' ;;
        label: "Crypto Charge"
      }
      when: {
        sql: ${TABLE}.txn_type = '13' ;;
        label: "QR Payment"
      }
    }
    sql: ${TABLE}.txn_type ;;
  }

  dimension_group: kyc_date {
    type: time
    description: "date user has KYC approved"
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.kyc_date ;;
  }

  dimension_group: app_signup_date {
    type: time
    description: "Date user created Nex app account"
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.app_signup_date ;;
  }


  dimension_group: first_txn_date {
    type: time
    description: "Date user first made particular transaction type"
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.first_txn_date ;;
  }

  dimension: days_since_txn {
    type: number
    sql: ${TABLE}.days_since_txn ;;
  }

  set: detail {
    fields: [
      txn_id,
      txn_user_id,
      txn_type,
      days_since_txn
    ]
  }
}