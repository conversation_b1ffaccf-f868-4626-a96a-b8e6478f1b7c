view: ag_card_transaction_user_status {
  derived_table: {
    sql: SELECT dates,
      cardholder_id,
      card_country,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 1 THEN 1
        ELSE 0
        END AS DTU,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 7 THEN 1
        ELSE 0
        END AS WTU,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 30 THEN 1
        ELSE 0
        END AS MTU,
      CASE WHEN (MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 90
        AND MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) > 30) THEN 1
        ELSE 0
        END AS SOFT_CHURN,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) > 90 THEN 1
        ELSE 0
        END AS HARD_CHURN,
      CASE WHEN (MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) is null
      AND (MIN(date_diff(day,DATE_TRUNC('DAY',CAST(updated_at as TIMESTAMP)),DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP))) >= 0))) THEN 1
        ELSE 0
        END AS INACTIVE,
      MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) as "days_since_transaction"
      FROM (
        SELECT user_dates_table.created_at as dates,
        user_dates_table.id as cardholder_id,
        user_dates_table.updated_date as updated_at,
        user_dates_table.card_country as card_country,
        card_transactions.created_at as txn_date
        FROM
        ((SELECT cardholder.id as id, card_info.card_country, CAST(cardholder.updated_at as DATE) as updated_date FROM clean__nex_app.cardholder LEFT JOIN clean__nex_app.card_info on cardholder.id = card_info.cardholder_id GROUP BY 1,2,3)
        CROSS JOIN  (
        SELECT CAST(CAST(created_at AS DATE) as TIMESTAMP) as created_at from clean__nex_app.transactions
        WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 90 GROUP BY 1
        )) AS user_dates_table
        LEFT JOIN
        (SELECT cardholder.id as cardholder_id , card_info.card_country, card_transactions.*
        FROM clean__nex_app.card_transactions
        LEFT JOIN clean__nex_app.card_info on card_info.id = card_transactions.card_id
        LEFT JOIN clean__nex_app.cardholder on cardholder.id = card_info.cardholder_id
        ) AS card_transactions ON user_dates_table.id = card_transactions.cardholder_id
        WHERE (DATE_TRUNC('DAY', CAST(card_transactions.created_at AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(user_dates_table.created_at  AS TIMESTAMP)) OR card_transactions.created_at is null)
        AND card_transactions.transaction_type = 'PAYMENT'
        GROUP BY 1,2,3,4,5)
      GROUP BY 1,2,3
      ORDER BY 1 desc
      -- NOTE: This query will only work for XenIssuing (PH), as we dont store transaction for BRI (ID)
 ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dates {
    type: time
    sql: ${TABLE}.dates ;;
  }

  dimension: cardholder_id {
    type: string
    sql: ${TABLE}.cardholder_id ;;
  }

  dimension: card_country {
    type: string
    sql: ${TABLE}.card_country ;;
  }

  dimension: DTU {
    type: number
    description: "Users who made transactions in the past day"
    sql: ${TABLE}.DTU ;;
  }

  dimension: WTU_rolling7D {
    type: number
    description: "Users who made transactions in the past 7 days"
    sql: ${TABLE}.WTU ;;
  }

  dimension: MTU_rolling30D {
    type: number
    description: "Users who made transactions in the past 30 days"
    sql: ${TABLE}.MTU ;;
  }

  dimension: soft_churn {
    type: number
    description: "Users who has not made a transaction in the last 30 days but within 90 days"
    sql: ${TABLE}.SOFT_CHURN ;;
  }

  dimension: hard_churn {
    type: number
    description: "Users who has not made a transaction in the last 90 days"
    sql: ${TABLE}.HARD_CHURN ;;
  }

  dimension: inactive {
    type: number
    description: "Users who has not made a transaction since the dawn of time"
    sql: ${TABLE}.INACTIVE ;;
  }

  dimension: days_since_transaction {
    type: number
    sql: ${TABLE}.days_since_transaction ;;
  }

  set: detail {
    fields: [
      dates_time,
      cardholder_id,
      card_country,
      DTU,
      WTU_rolling7D,
      MTU_rolling30D,
      soft_churn,
      hard_churn,
      inactive,
      days_since_transaction
    ]
  }

  measure: Sum_DTU {
    type: sum
    sql: ${TABLE}.DTU ;;
    drill_fields: [cardholder_id]
  }

  measure: Sum_WTU_rolling7D {
    type: sum
    sql: ${TABLE}.WTU ;;
    drill_fields: [cardholder_id]
  }

  measure: Sum_MTU_rolling30D {
    type: sum
    sql: ${TABLE}.MTU ;;
    drill_fields: [cardholder_id]
  }

  measure: Sum_soft_churn {
    type: sum
    sql: ${TABLE}.soft_churn ;;
    drill_fields: [cardholder_id]
  }

  measure: Sum_hard_churn {
    type: sum
    sql: ${TABLE}.hard_churn ;;
    drill_fields: [cardholder_id]
  }

  measure: Sum_inactive {
    type: sum
    sql: ${TABLE}.inactive ;;
    drill_fields: [cardholder_id]
  }

}
