view: ag_xendit_app_recurring_historical_status {
  derived_table: {
    sql: SELECT user_dates_table.dates_created_at, recurring.user_id, recurring.id as recurring_id,
      CASE
        WHEN CAST(recurring.deleted_at AS DATE) < CAST(user_dates_table.dates_created_at AS DATE) THEN 'DISABLED'
        WHEN CAST (recurring.end_date AS DATE) < CAST(user_dates_table.dates_created_at AS DATE) THEN 'EXPIRED'
        WHEN
        CAST(recurring.created_at AS DATE) <= CAST(user_dates_table.dates_created_at AS DATE)
        AND (CAST (recurring.end_date AS DATE) >= CAST(user_dates_table.dates_created_at AS DATE) OR recurring.end_date is null)
        AND (CAST(recurring.deleted_at AS DATE) >= CAST(user_dates_table.dates_created_at AS DATE) OR recurring.deleted_at is null)
        THEN 'ACTIVE'
        ELSE 'UNKNOWN'
        END AS recurring_status
      FROM
        ((SELECT user_id AS id FROM clean__nex_app.banking_clients WHERE account_status = 'ACTIVE' GROUP BY 1)
        CROSS JOIN  (
        SELECT CAST(CAST(created_at AS DATE) as TIMESTAMP) as dates_created_at from clean__nex_app.transactions
        WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 90 GROUP BY 1
        )) AS user_dates_table
      LEFT JOIN clean__nex_app.recurring_plans AS recurring ON user_dates_table.id = recurring.user_id
      WHERE recurring.id is not null
      AND DATE_TRUNC('DAY', CAST(recurring.created_at AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(user_dates_table.dates_created_at  AS TIMESTAMP))
      -- AND recurring.id = 'dea1da61-525c-4bef-9072-31c8aac5ad99'
;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dates_created_at {
    type: time
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dates_created_at ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: recurring_id {
    type: string
    sql: ${TABLE}.recurring_id ;;
  }

  dimension: recurring_status {
    type: string
    sql: ${TABLE}.recurring_status ;;
  }

  set: detail {
    fields: [dates_created_at_time, user_id, recurring_id, recurring_status]
  }
}
