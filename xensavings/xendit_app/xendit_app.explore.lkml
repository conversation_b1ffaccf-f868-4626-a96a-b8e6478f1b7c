include: "/xensavings/xendit_app/*.view.lkml"

explore: xenditapp_transactions_v2 {
  view_label: "Transactions"
  from: ag_xendit_app_transactions
  group_label: "xenSavings"
  label: "Nex Transactions"
  description: "Transactions for Nex"

  join: xensavings_users {
    from: xendit_app_users_joint
    view_label: "Users"
    relationship: many_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.user_id} = ${xensavings_users.id} ;;
  }

  join: banking_deposit_transactions {
    from: ag_ts_banking_deposit_transactions
    view_label: "Banking Deposit Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${banking_deposit_transactions.transaction_id};;
  }

  join: banking_interest_transactions {
    from: ag_ts_banking_interest_transactions
    view_label: "Banking Interest Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${banking_interest_transactions.transaction_id};;
  }

  # join: banking_interest_tax_transactions {
  #   from: ag_ts_banking_interest_tax_transactions
  #   view_label: "Banking Interest Tax Transaction Details"
  #   relationship: one_to_one
  #   type: left_outer
  #   sql_on: ${banking_interest_transactions.banking_interest_id} = ${banking_interest_tax_transactions.id} ;;
  # }

  join: savings_bank_account_transactions {
    from: ag_ts_savings_to_bank_account_transactions
    view_label: "Savings to Bank Account Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${savings_bank_account_transactions.transaction_id};;
  }

  join: savings_ewallet_transactions {
    from: ag_ts_savings_to_ewallet_transactions
    view_label: "Savings to E-Wallet Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${savings_ewallet_transactions.transaction_id};;
  }

  join: bill_payments_transactions {
    from: ag_ts_bill_payment_transactions
    view_label: "Bill Payment Transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${bill_payments_transactions.transaction_id};;
  }

  join: savings_qr_transactions {
    from: ag_ts_savings_to_qr_payment_transactions
    view_label: "QR Payment Transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${savings_qr_transactions.transaction_id};;
  }

  join: banking_client {
    from: ag_banking_clients
    view_label: "Banking provider"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.user_id} = ${banking_client.user_id} ;;
  }

  join: request {
    from: ag_request_funds
    view_label: "Requests"
    relationship: many_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.user_id} = ${request.request_from_user_id} ;;
  }

  join: request_transactions {
    from: ag_request_funds_transactions
    view_label: "Request transactions"
    relationship: one_to_many
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${request_transactions.transaction_id} and ${request_transactions.request_fund_id} = ${request.id};;
  }

  join: recurring_plan {
    from: ag_recurring_plans
    view_label: "Recurring plan"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.user_id} = ${recurring_plan.user_id} ;;
  }

  join: recurring_transactions {
    from: ag_recurring_plans_transactions
    view_label: "Recurring transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.id} = ${recurring_transactions.transaction_id};;
  }

  join: envelopes {
    from: ag_envelopes
    view_label: "Envelopes"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xenditapp_transactions_v2.user_id} = ${envelopes.user_id} and ${envelopes.banking_client_id} = ${banking_client.id};;
  }
}

explore: xendit_app_user_status {
  from:  xendit_app_users_joint
  group_label: "xenSavings"
  label: "Nex User Statuses"
  description: "Transaction and session status for Nex users"

  join: xendit_app_user_transaction_status {
    from:  ag_xendit_app_user_transaction_status
    view_label: "Nex User Transaction Status"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_user_status.id} = ${xendit_app_user_transaction_status.txn_user_id} ;;
  }

  join: xendit_app_user_active_status {
    from:  ag_xendit_app_user_app_status
    view_label: "Nex User Active Status"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_user_status.id} = ${xendit_app_user_active_status.user_id} ;;
  }

  join: xendit_transaction_type_status {
    from:  ag_xendit_app_transaction_type_status
    view_label: "Nex User Transaction Type Status (for MTU3)"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_user_status.id} = ${xendit_transaction_type_status.txn_user_id} ;;
  }

  join: xendit_app_user_segmentation {
    from:  ag_xendit_app_user_segmentation
    view_label: "Transaction count and types for segmenting users"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_user_status.id} = ${xendit_app_user_segmentation.user_id} ;;
  }

  join: ag_cardholder {
    from:  ag_cardholder
    view_label: "Cardholder - used for joins"
    relationship: one_to_one
    type:  left_outer
    sql_on:  ${xendit_app_user_status.id} = ${ag_cardholder.id} ;;
  }

  join: ag_card_transaction_user_status_PH {
    from:  ag_card_transaction_user_status
    view_label: "Transaction status for Nex Cards PH"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${ag_cardholder.id} = ${ag_card_transaction_user_status_PH.cardholder_id} ;;
  }

  join: ag_points_ledger {
    from: ag_points_ledger
    view_label: "Nex Points Ledger"
    relationship: one_to_many
    type: left_outer
    sql_on:${xendit_app_user_status.id} = ${ag_points_ledger.user_id};;
  }

  join: ag_baas_customers {
    from: ag_baas_customers
    view_label: "Nex Account BSS Customer Mapping"
    relationship: one_to_many
    type: left_outer
    sql_on: ${xendit_app_user_status.id} = ${ag_baas_customers.user_id};;
  }

  join: ag_banking_kyc_baas {
    from: ag_banking_kyc_baas
    view_label: "Nex Account KYC Information"
    relationship: one_to_many
    type: left_outer
    sql_on: ${ag_baas_customers.id} = ${ag_banking_kyc_baas.customer_id};;
  }

  join: ag_card_kyc_bri {
    from: ag_card_kyc_bri
    view_label: "Nex Card KYC Details(BRI)"
    relationship: one_to_many
    type: left_outer
    sql_on:${xendit_app_user_status.id} = ${ag_card_kyc_bri.user_id};;
  }

  join: banking_client {
    from: ag_banking_clients
    view_label: "Banking Provider"
    relationship:  one_to_many
    type: left_outer
    sql_on: ${xendit_app_user_status.id} = ${banking_client.user_id}  ;;
  }

  join: bss_balance_snapshot {
    from: ag_bss_balance_snapshot
    view_label: "BSS Balance Snapshot"
    relationship:  one_to_many
    type: left_outer
    sql_on: ${banking_client.account_number} = ${bss_balance_snapshot.account_number}  ;;
  }

  join: session_log {
    from: ag_session_log
    view_label: "Nex Session Log"
    relationship:  one_to_many
    type: left_outer
    sql_on: ${xendit_app_user_status.id} = ${session_log.user_id}  ;;
  }
}

explore: xendit_app_feature_status {
  from:  xendit_app_users_joint
  group_label: "xenSavings"
  label: "Nex Feature Statuses"
  description: "Feature statuses for envelopes and recurring"

  join: banking_provider {
    from: ag_banking_clients
    view_label: "Banking Provider"
    relationship:  one_to_many
    type: left_outer
    sql_on: ${xendit_app_feature_status.id} = ${banking_provider.user_id}  ;;
  }

  join: xendit_app_envelopes_historical_status {
    from: ag_xendit_app_envelopes_history
    view_label: "Nex Envelopes Status"
    relationship:  one_to_many
    type: left_outer
    sql_on: ${xendit_app_feature_status.id} = ${xendit_app_envelopes_historical_status.user_id} ;;
  }

  join: xenditapp_envelopes_explore {
    from: ag_envelopes
    view_label: "Envelopes"
    relationship: many_to_one
    type: left_outer
    sql_on: ${xendit_app_envelopes_historical_status.id} = ${xenditapp_envelopes_explore.id} AND ${xenditapp_envelopes_explore.banking_client_id} = ${banking_provider.id};;
  }

  join: xendit_app_recurring_historical_status {
    from:  ag_xendit_app_recurring_historical_status
    view_label: "History of recurring statuses for users"
    relationship:  one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_recurring_historical_status.user_id} = ${xendit_app_feature_status.id} ;;
  }

  join: xenditapp_recurring_explore {
    from: ag_recurring_plans
    view_label: "Recurring plans"
    relationship: many_to_one
    type: left_outer
    sql_on: ${xendit_app_recurring_historical_status.recurring_id} = ${xenditapp_recurring_explore.id} AND ${xenditapp_recurring_explore.user_banking_client_id} = ${banking_provider.id} ;;
  }

  join: xenditapp_recurring_transactions {
    from: ag_recurring_plans_transactions
    view_label: "Recurring transactions"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xendit_app_recurring_historical_status.recurring_id} = ${xenditapp_recurring_transactions.recurring_plan_id} ;;
  }

  join: xendit_app_user_segmentation {
    from:  ag_xendit_app_user_segmentation
    view_label: "Transaction count and types for segmenting users"
    relationship: one_to_many
    type:  left_outer
    sql_on:  ${xendit_app_feature_status.id} = ${xendit_app_user_segmentation.user_id} ;;
  }
}

explore: xendit_app_user_transactions_deepdive {
  from:  ag_xendit_app_user_transaction_deepdive
  group_label: "xenSavings"
  label: "Nex Transactions Deepdive"
  description: "Transactions and users analysis and deepdive"

  join: banking_deposit_transactions {
    from: ag_ts_banking_deposit_transactions
    view_label: "Banking Deposit Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${banking_deposit_transactions.transaction_id} ;;
  }

  join: banking_interest_transactions {
    from: ag_ts_banking_interest_transactions
    view_label: "Banking Interest Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${banking_interest_transactions.transaction_id} ;;
  }

  join: savings_bank_account_transactions {
    from: ag_ts_savings_to_bank_account_transactions
    view_label: "Savings to Bank Account Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${savings_bank_account_transactions.transaction_id};;
  }

  join: savings_ewallet_transactions {
    from: ag_ts_savings_to_ewallet_transactions
    view_label: "Savings to E-Wallet Transaction Details"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${savings_ewallet_transactions.transaction_id} ;;
  }

  join: bill_payments_transactions {
    from: ag_ts_bill_payment_transactions
    view_label: "Bill Payment Transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${bill_payments_transactions.transaction_id};;
  }

  join: banking_client {
    from: ag_banking_clients
    view_label: "Banking provider"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_user_id} = ${banking_client.user_id} ;;
  }

  join: request_transactions {
    from: ag_request_funds_transactions
    view_label: "Request transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${request_transactions.transaction_id} ;;
  }

  join: recurring_plan {
    from: ag_recurring_plans
    view_label: "Recurring plan"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_user_id} = ${recurring_plan.user_id} and ${recurring_plan.user_banking_client_id} on ${banking_client.id};;
  }

  join: recurring_transactions {
    from: ag_recurring_plans_transactions
    view_label: "Recurring transactions"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${recurring_transactions.transaction_id} ;;
  }

  join: transactions {
    from: ag_xendit_app_transactions
    view_label: "Full transactions list"
    relationship: one_to_one
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_id} = ${transactions.id} ;;
  }

  join: envelopes {
    from: ag_envelopes
    view_label: "Envelopes"
    relationship: many_to_many
    type: left_outer
    sql_on: ${xendit_app_user_transactions_deepdive.txn_user_id} = ${envelopes.user_id} and ${envelopes.banking_client_id} on ${banking_client.id} ;;
  }
}

explore: xendit_app_waitlist {
  from:  ag_xendit_app_waitlist
  group_label: "xenSavings"
  label: "Nex Waitlisted Users"
  description: "Users on Nex Waitlist"
}

explore: xendit_app_discovery {
  from:  xendit_app_users_joint
  group_label: "xenSavings"
  label: "Xendit App Discovery"
  description: "Discovery of Xendit app"

  join: ag_user_inboxes {
    from: ag_user_inboxes
    view_label: "User inbox"
    relationship: one_to_many
    type: left_outer
    sql_on: ${xendit_app_discovery.id} = ${ag_user_inboxes.user_id};;
  }

  join: ag_inbox_messages {
    from: ag_inbox_messages
    view_label: "Inbox messages"
    relationship: many_to_one
    type: left_outer
    sql_on: ${ag_user_inboxes.message_id} = ${ag_inbox_messages.id};;
  }
}

explore: nex_card_transactions {
  from:  ag_card_transactions
  group_label: "xenSavings"
  label: "Nex Card Transactions PH"
  description: "Nex Card Transactions"

  join: ag_card_merchant_data {
    from: ag_card_merchant_data
    view_label: "Nex Card Merchant Data"
    relationship: one_to_one
    type: left_outer
    sql_on: ${nex_card_transactions.id} = ${ag_card_merchant_data.card_transaction_id};;
  }

  join: ag_card_info {
    from: ag_card_info
    view_label: "Nex Card Details"
    relationship: many_to_one
    type: left_outer
    sql_on: ${nex_card_transactions.card_id} = ${ag_card_info.id};;
  }

  join: ag_credit_account {
    from: ag_credit_account
    view_label: "Nex Card Credit Account Details"
    relationship: many_to_one
    type: left_outer
    sql_on: ${ag_card_info.credit_account_id} = ${ag_credit_account.id};;
  }

  join: ag_cardholder {
    from: ag_cardholder
    view_label: "Nex Cardholder Details"
    relationship: many_to_one
    type: left_outer
    sql_on: ${ag_card_info.cardholder_id} = ${ag_cardholder.id} AND ${ag_credit_account.cardholder_id} = ${ag_cardholder.id};;
  }

  join: ag_xendit_app_users {
    from: ag_xendit_app_users
    view_label: "Nex Cardholder Details"
    relationship: many_to_one
    type: left_outer
    sql_on: ${ag_cardholder.user_id} = ${ag_xendit_app_users.id};;
  }
}

explore: nex_card_billing {
  from:  ag_credit_account_billing_statement
  group_label: "xenSavings"
  label: "Nex Card Billing PH"
  description: "Billing Statements for Nex Cards"

  join: ag_credit_account_PH {
    from: ag_credit_account
    view_label: "Nex Card Credit Account Details PH"
    relationship: many_to_one
    type: left_outer
    sql_on: ${nex_card_billing.credit_account_id} = ${ag_credit_account_PH.id};;
  }

  join: ag_cardholder {
    from: ag_cardholder
    view_label: "Nex Cardholder Details PH"
    relationship: many_to_one
    type: left_outer
    sql_on:${ag_credit_account_PH.cardholder_id} = ${ag_cardholder.id};;
  }
}

explore: nex_card_ID_users {
  from:  xendit_app_users_joint
  group_label: "xenSavings"
  label: "Nex Card Users (ID)"
  description: "Nex Card Users (ID)"

  join: ag_cardholder {
    from: ag_cardholder
    view_label: "Nex Card Cardholders"
    relationship: one_to_one
    type: left_outer
    sql_on: ${nex_card_ID_users.id} = ${ag_cardholder.user_id};;
  }

  join: ag_credit_account {
    from: ag_credit_account
    view_label: "Nex Card Credit Account"
    relationship: one_to_one
    type: left_outer
    sql_on: ${ag_cardholder.id} = ${ag_credit_account.cardholder_id};;
  }

  join: ag_card_info {
    from: ag_card_info
    view_label: "Nex Card Card Info"
    relationship: one_to_one
    type: left_outer
    sql_on: ${ag_credit_account.id} = ${ag_card_info.credit_account_id};;
  }

  join: ag_card_kyc_bri {
    from: ag_card_kyc_bri
    view_label: "Nex Card KYC Details(BRI)"
    relationship: one_to_one
    type: left_outer
    sql_on:${nex_card_ID_users.id} = ${ag_card_kyc_bri.user_id};;
  }

  join: ag_points_ledger {
    from: ag_points_ledger
    view_label: "Nex Points Ledger"
    relationship: one_to_many
    type: left_outer
    sql_on:${nex_card_ID_users.id} = ${ag_points_ledger.user_id};;
  }

  join: ag_card_bri_digisign {
    from: ag_card_bri_digisign
    view_label: "Nex Card KYC Links (BRI)"
    relationship: one_to_many
    type: left_outer
    sql_on: ${ag_cardholder.id} = ${ag_card_bri_digisign.cardholder_id};;
  }

  join: ag_card_kyc_bri_internal_feedback {
    from: ag_card_kyc_bri_internal_feedback
    view_label: "Nex Card KYC Internal Ops Feedback"
    relationship: one_to_many
    type: left_outer
    sql_on: ${ag_card_kyc_bri.id} = ${ag_card_kyc_bri_internal_feedback.card_kyc_bri_id};;
  }

  join: ag_monthly_user_card_transaction {
    from: ag_monthly_user_card_transaction
    view_label: "Nex Card ID Monthly Transaction Volume and Count"
    relationship: one_to_many
    type: left_outer
    sql_on: ${nex_card_ID_users.id} = ${ag_monthly_user_card_transaction.user_id};;
  }
}

explore: ag_combined_card_metrics{
  from:  ag_combined_card_metrics
  group_label: "xenSavings"
  label: "Nex Card Users (combined)"
  description: "Nex Card Users (combined)"
}
