view: ag_xendit_app_user_segmentation {
  derived_table: {
    sql: SELECT transaction_table.user_id, user_table.banking_api_provider, transaction_table.type,
        COUNT(CASE WHEN (date_diff(day, DATE_TRUNC('DAY', CAST(transaction_table.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 31 THEN 1 END) as "30DTXN",
        COUNT(CASE WHEN ((date_diff(day, DATE_TRUNC('DAY', CAST(transaction_table.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 61
          AND (date_diff(day, DATE_TRUNC('DAY', CAST(transaction_table.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) > 31)
          THEN 1 END) as "60DTXN",
        COUNT(CASE WHEN ((date_diff(day, DATE_TRUNC('DAY', CAST(transaction_table.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 91
          AND (date_diff(day, DATE_TRUNC('DAY', CAST(transaction_table.created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) > 61)
          THEN 1 END) as "90DTXN"
      FROM (SELECT user_id AS id, banking_api_provider FROM clean__nex_app.banking_clients WHERE account_status = 'ACTIVE' GROUP BY 1,2) as user_table
      LEFT JOIN
      (SELECT user_id, type, created_at FROM clean__nex_app.transactions
      WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 91
      AND status = '2'
      AND type NOT IN ('8')
      ) as transaction_table
      ON user_table.id = transaction_table.user_id
      -- WHERE transaction_table.user_id = '98800c17-c6e6-4546-9183-2dd2b7aab23a'
      GROUP BY 1,2,3
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: banking_api_provider {
    type: string
    sql: ${TABLE}.banking_api_provider ;;
  }


  dimension: transaction_type {
    description: "type of transaction from Nex App"
    case: {
      when: {
        sql: ${TABLE}.type = '0' ;;
        label: "Top up"
      }
      when: {
        sql: ${TABLE}.type = '1' ;;
        label: "Debit"
      }
      when: {
        sql: ${TABLE}.type = '2' ;;
        label: "Direct debit"
      }
      when: {
        sql: ${TABLE}.type = '3' ;;
        label: "Withdraw"
      }
      when: {
        sql: ${TABLE}.type = '4' ;;
        label: "Wallet"
      }
      when: {
        sql: ${TABLE}.type = '5' ;;
        label: "Pulsa"
      }
      when: {
        sql: ${TABLE}.type = '6' ;;
        label: "Banking account transfer"
      }
      when: {
        sql: ${TABLE}.type = '7' ;;
        label: "Banking deposit"
      }
      when: {
        sql: ${TABLE}.type = '8' ;;
        label: "Banking interest (gross)"
      }
      when: {
        sql: ${TABLE}.type = '9' ;;
        label: "Banking eWallet transaction"
      }
      when: {
        sql: ${TABLE}.type = '10' ;;
        label: "Banking move funds"
      }
      when: {
        sql: ${TABLE}.type = '11' ;;
        label: "Bill payment"
      }
      when: {
        sql: ${TABLE}.type = '12' ;;
        label: "Crypto Charge"
      }
      when: {
        sql: ${TABLE}.type = '13' ;;
        label: "QR Payment"
      }
    }
  }
  dimension: 30_dtxn {
    type: number
    sql: ${TABLE}."30DTXN" ;;
  }

  dimension: 60_dtxn {
    type: number
    sql: ${TABLE}."60DTXN" ;;
  }

  dimension: 90_dtxn {
    type: number
    sql: ${TABLE}."90DTXN" ;;
  }

  set: detail {
    fields: [user_id, transaction_type, 30_dtxn, 60_dtxn, 90_dtxn]
  }
}