view: ag_xendit_app_user_transaction_status {
  derived_table: {
    sql: SELECT dates,
      txn_user_id,
      banking_api_provider,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 1 THEN 1
        ELSE 0
        END AS DTU,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 7 THEN 1
        ELSE 0
        END AS WTU,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 30 THEN 1
        ELSE 0
        END AS MTU,
      CASE WHEN (MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) <= 90
        AND MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) > 30) THEN 1
        ELSE 0
        END AS SOFT_CHURN,
      CASE WHEN MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) > 90 THEN 1
        ELSE 0
        END AS HARD_CHURN,
      CASE WHEN (MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) is null
      AND (MIN(date_diff(day,DATE_TRUNC('DAY',CAST(updated_at as TIMESTAMP)),DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP))) >= 0))) THEN 1
        ELSE 0
        END AS INACTIVE,
      MIN(date_diff(day, DATE_TRUNC('DAY', CAST(txn_date  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(dates AS TIMESTAMP)))) as days_since_transaction
      FROM (
        SELECT user_dates_table.created_at as dates,
        user_dates_table.id as txn_user_id,
        user_dates_table.updated_at as updated_at,
        user_dates_table.banking_api_provider as banking_api_provider,
        main_transactions.created_at as txn_date,
        main_transactions.type as txn_type
        FROM
        ((SELECT user_id AS id, banking_api_provider, CAST(updated_at as DATE) as updated_at FROM clean__nex_app.banking_clients WHERE account_status = 'ACTIVE' GROUP BY 1,2,3)
        CROSS JOIN  (
        SELECT CAST(CAST(created_at AS DATE) as TIMESTAMP) as created_at from clean__nex_app.transactions
        WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 90 GROUP BY 1
        )) AS user_dates_table
        LEFT JOIN clean__nex_app.transactions AS main_transactions ON user_dates_table.id = main_transactions.user_id
        WHERE (main_transactions.type NOT IN ('8') OR main_transactions.type is null)
        AND (main_transactions.status = '2'OR main_transactions.status is null)
        -- Includes ALL completed transaction types except for interest OR user does not have any transactions
        AND (DATE_TRUNC('DAY', CAST(main_transactions.created_at AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(user_dates_table.created_at  AS TIMESTAMP)) OR main_transactions.created_at is null)
        -- AND user_dates_table.id = '64faaf41-58e9-4987-b65c-56bd569596ef'
        GROUP BY 1,2,3,4,5,6)
      GROUP BY 1,2,3
      ORDER BY 1 desc
       ;;
  }

  suggestions: no

################################################
# Primary keys {
################################################
  dimension: pk1_user_date_partner {
    hidden: yes
    primary_key: yes
    sql: concat(${pk3_user_id}, cast(${pk3_dates} as varchar), ${pk3_banking_api_provider} ) ;;
  }
  dimension: pk3_user_id {
    hidden: yes
    sql: ${txn_user_id} ;;
  }
  dimension: pk3_dates {
    hidden: yes
    sql: ${dates_raw} ;;
  }
  dimension: pk3_banking_api_provider {
    hidden: yes
    sql: ${banking_api_provider} ;;
  }
################################################
# Primary keys }
################################################

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dates {
    type: time
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dates ;;
  }

  dimension: txn_user_id {
    type: string
    sql: ${TABLE}.txn_user_id ;;
  }

  dimension: banking_api_provider {
    type: string
    sql: ${TABLE}.banking_api_provider ;;
  }

  dimension: DTU {
    type: number
    description: "Users who made transactions in the past day"
    sql: ${TABLE}.DTU ;;
  }

  dimension: WTU_rolling7D {
    type: number
    description: "Users who made transactions in the past 7 days"
    sql: ${TABLE}.WTU ;;
  }

  dimension: MTU_rolling30D {
    type: number
    description: "Users who made transactions in the past 30 days"
    sql: ${TABLE}.MTU ;;
  }

  dimension: soft_churn {
    type: number
    description: "Users who has not made a transaction in the last 30 days but within 90 days"
    sql: ${TABLE}.SOFT_CHURN ;;
  }

  dimension: hard_churn {
    type: number
    description: "Users who has not made a transaction in the last 90 days"
    sql: ${TABLE}.HARD_CHURN ;;
  }

  dimension: inactive {
    type: number
    description: "Users who has not made a transaction since the dawn of time"
    sql: ${TABLE}.INACTIVE ;;
  }

  dimension: days_since_transaction {
    type: number
    sql: ${TABLE}.days_since_transaction ;;
  }

  set: detail {
    fields: [
      dates_time,
      txn_user_id,
      DTU,
      WTU_rolling7D,
      MTU_rolling30D,
      days_since_transaction
    ]
  }

  measure: Sum_DTU {
    type: sum
    sql: ${TABLE}.DTU ;;
    drill_fields: [txn_user_id]
  }

  measure: Sum_WTU_rolling7D {
    type: sum
    sql: ${TABLE}.WTU ;;
    drill_fields: [txn_user_id]
  }

  measure: Sum_MTU_rolling30D {
    type: sum
    sql: ${TABLE}.MTU ;;
    drill_fields: [txn_user_id]
  }

  measure: Sum_soft_churn {
    type: sum
    sql: ${TABLE}.soft_churn ;;
    drill_fields: [txn_user_id]
  }

  measure: Sum_hard_churn {
    type: sum
    sql: ${TABLE}.hard_churn ;;
    drill_fields: [txn_user_id]
  }

  measure: Sum_inactive {
    type: sum
    sql: ${TABLE}.inactive ;;
    drill_fields: [txn_user_id]
  }
}
