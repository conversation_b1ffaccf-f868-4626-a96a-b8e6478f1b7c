view: ag_xendit_app_envelopes_history {
  derived_table: {
    sql: SELECT user_dates_table.dates_created_at, envelopes.id, envelopes.user_id,
      CASE
        WHEN CAST(envelopes.deleted_at AS DATE) < CAST(user_dates_table.dates_created_at AS DATE) THEN 'INACTIVE'
        WHEN CAST (envelopes.created_at AS DATE) <= CAST(user_dates_table.dates_created_at AS DATE)
          AND (CAST(envelopes.deleted_at AS DATE) >= CAST(user_dates_table.dates_created_at AS DATE) OR envelopes.deleted_at is null)
        THEN 'ACTIVE'
      ELSE 'UNKNOWN'
      END AS envelopes_status
      FROM
      ((SELECT user_id AS id FROM clean__nex_app.banking_clients WHERE account_status = 'ACTIVE' GROUP BY 1)
      CROSS JOIN  (
      SELECT CAST(CAST(created_at AS DATE) as TIMESTAMP) as dates_created_at from clean__nex_app.transactions
      WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 90 GROUP BY 1
      )) AS user_dates_table
      LEFT JOIN clean__nex_app.envelopes AS envelopes ON user_dates_table.id = envelopes.user_id
      WHERE envelopes.id is not null
      AND DATE_TRUNC('DAY', CAST(envelopes.created_at AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(user_dates_table.dates_created_at  AS TIMESTAMP))
      -- AND envelopes.id = 'c37da550-8f6f-4747-af6c-bf06103eeaa2'
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dates_created_at {
    type: time
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dates_created_at ;;
  }

  dimension: id {
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: envelopes_status {
    type: string
    sql: ${TABLE}.envelopes_status ;;
  }

  set: detail {
    fields: [dates_created_at_time, id, user_id, envelopes_status]
  }
}
