view: ag_xendit_app_transaction_type_status {
  derived_table: {
    sql: SELECT dates,
      txn_user_id,
      banking_api_provider,
      COUNT (DISTINCT txn_type) AS txn_type_count
        FROM (
          SELECT user_dates_table.created_at as dates,
          user_dates_table.id as txn_user_id,
          user_dates_table.banking_api_provider as banking_api_provider,
          main_transactions.created_at as txn_date,
          main_transactions.type as txn_type
          FROM
          ((SELECT user_id AS id, banking_api_provider FROM clean__nex_app.banking_clients WHERE account_status = 'ACTIVE' GROUP BY 1,2)
          CROSS JOIN  (
          SELECT CAST(CAST(created_at AS DATE) as TIMESTAMP) as created_at from clean__nex_app.transactions
          WHERE (date_diff(day, DATE_TRUNC('DAY', CAST(created_at  AS TIMESTAMP)), DATE_TRUNC('DAY', CAST(NOW() AS TIMESTAMP)))) <= 90 GROUP BY 1
          )) AS user_dates_table
          LEFT JOIN clean__nex_app.transactions AS main_transactions ON user_dates_table.id = main_transactions.user_id
          WHERE (main_transactions.type NOT IN ('8') OR main_transactions.type is null)
          AND (main_transactions.status = '2'OR main_transactions.status is null)
          -- Includes ALL completed transaction types except for interest OR user does not have any transactions
          AND ((DATE_TRUNC('DAY', CAST(main_transactions.created_at AS TIMESTAMP)) <= DATE_TRUNC('DAY', CAST(user_dates_table.created_at  AS TIMESTAMP))
          AND date_diff(day,CAST(main_transactions.created_at AS TIMESTAMP), DATE_TRUNC('DAY', CAST(user_dates_table.created_at  AS TIMESTAMP))) <= 30)
          OR main_transactions.created_at is null)
          -- Looking at transaction dates where it is 0-30 days before the date of comparison
          -- AND user_dates_table.id = '1758d870-ad69-4a2d-bb5a-a867a094ecd1'
          GROUP BY 1,2,3,4,5)
      GROUP BY 1,2,3
      ORDER BY 1 DESC
       ;;
  }

  suggestions: no

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  dimension_group: dates {
    type: time
    timeframes: [
      raw,
      time,
      day_of_month,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.dates ;;
  }

  dimension: txn_user_id {
    type: string
    sql: ${TABLE}.txn_user_id ;;
  }

  dimension: banking_api_provider {
    type: string
    sql: ${TABLE}.banking_api_provider ;;
  }

  dimension: txn_type_count {
    type: number
    sql: ${TABLE}.txn_type_count ;;
  }

  set: detail {
    fields: [dates_time, txn_user_id, banking_api_provider, txn_type_count]
  }
}