# The name of this view in Looker is "Monthly User Card Transaction"
view: ag_monthly_user_card_transaction {
  # The sql_table_name parameter indicates the underlying database table
  # to be used for all fields in this view.
  sql_table_name: clean__nex_app.monthly_user_card_transaction ;;
  drill_fields: [id]
  suggestions: no

  # This primary key is the unique key for this table in the underlying database.
  # You need to define a primary key in a view in order to join to other views.

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }
    # Here's what a typical dimension looks like in LookML.
    # A dimension is a groupable field that can be used to filter query results.
    # This dimension will be called "Collectibility Status" in Explore.

  dimension: collectibility_status {
    case: {
      when: {
        sql: ${TABLE}.collectibility_status = 'GOOD' ;;
        label: "Pays on time"
      }
      when: {
        sql: ${TABLE}.collectibility_status = 'SPECIAL_ATTENTION' ;;
        label: "SPECIAL_ATTENTION - Late payment 1-90 days"
      }
      when: {
        sql: ${TABLE}.collectibility_status = 'SUBSTANDARD' ;;
        label: "SUBSTANDARD - Late payment 91 - 120 days"
      }
      when: {
        sql: ${TABLE}.collectibility_status = 'DOUBTFUL' ;;
        label: "DOUBTFUL - Late payment 121 - 180"
      }
      when: {
        sql: ${TABLE}.collectibility_status = 'BAD' ;;
        label: "BAD - Late payment more than 180 days"
      }
      when: {
        sql: ${TABLE}.collectibility_status = 'UNKNOWN' ;;
        label: "Unknown - Could not get statement"
      }
    }
  }
  # Dates and timestamps can be represented in Looker using a dimension group of type: time.
  # Looker converts dates and timestamps to the specified timeframes within the dimension group.

  dimension_group: created {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.created_at ;;
  }

  dimension: month {
    type: number
    sql: ${TABLE}.`month` ;;
  }

  dimension: total_transaction_amount {
    type: number
    sql: ${TABLE}.total_transaction_amount ;;
  }

  dimension: total_transaction_amount_with_loans_and_fees {
    type: number
    sql: ${TABLE}.total_transaction_amount_with_loans_and_fees ;;
  }

  dimension: statement_credit_limit {
    type: number
    sql: ${TABLE}.statement_credit_limit ;;
  }

  dimension: statement_balance {
    type: number
    sql: ${TABLE}.statement_balance ;;
  }

  # A measure is a field that uses a SQL aggregate function. Here are defined sum and average
  # measures for this dimension, but you can also add measures of many different aggregates.
  # Click on the type parameter to see all the options in the Quick Help panel on the right.

  measure: total_total_transaction_amount {
    type: sum
    sql: ${total_transaction_amount} ;;  }

  measure: average_total_transaction_amount {
    type: average
    sql: ${total_transaction_amount} ;;  }

  dimension: transaction_count {
    type: number
    sql: ${TABLE}.transaction_count ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [raw, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated_at ;;
  }

  dimension: user_id {
    type: string
    sql: ${TABLE}.user_id ;;
  }

  dimension: year {
    type: number
    sql: ${TABLE}.year ;;
  }

  dimension_group: statement {
    type: time
    sql: CAST(CONCAT(${year}, '-', ${month},'-05') AS TIMESTAMP) ;;
    timeframes: [raw, month, quarter, year]
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}
