#!/usr/bin/env python3
"""
Compare all zendesk files between source and DBR repositories
"""

import os
import subprocess
import difflib

SOURCE_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models"
DBR_REPO = "/Users/<USER>/Documents/Xendit_Codebase/looker-transaction-models-dbr"

def get_zendesk_files():
    """Get all .lkml files in zendesk folder"""
    result = subprocess.run(
        ["find", f"{SOURCE_REPO}/zendesk", "-name", "*.lkml"],
        capture_output=True, text=True
    )
    files = result.stdout.strip().split('\n') if result.stdout.strip() else []
    return [os.path.basename(f) for f in files if f]

def read_file(file_path):
    """Read file content"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.readlines()
    except Exception as e:
        return [f"Error reading file: {str(e)}\n"]

def compare_files(source_file, dbr_file, filename):
    """Compare two files and return differences"""
    source_lines = read_file(source_file)
    dbr_lines = read_file(dbr_file)
    
    # Generate unified diff
    diff = list(difflib.unified_diff(
        dbr_lines, source_lines,
        fromfile=f'DBR: {filename}',
        tofile=f'SOURCE: {filename}',
        lineterm=''
    ))
    
    return diff, len(source_lines), len(dbr_lines)

def main():
    print("🔍 COMPARING ZENDESK FILES BETWEEN REPOSITORIES")
    print("=" * 60)
    
    # Get all zendesk files
    zendesk_files = get_zendesk_files()
    print(f"📊 Found {len(zendesk_files)} zendesk files to compare")
    
    files_with_differences = []
    identical_files = []
    
    for filename in sorted(zendesk_files):
        source_file = os.path.join(SOURCE_REPO, "zendesk", filename)
        dbr_file = os.path.join(DBR_REPO, "zendesk", filename)
        
        print(f"\n📋 Comparing: {filename}")
        print("-" * 40)
        
        if not os.path.exists(source_file):
            print(f"  ❌ Source file missing: {filename}")
            continue
        
        if not os.path.exists(dbr_file):
            print(f"  ❌ DBR file missing: {filename}")
            continue
        
        diff, source_lines, dbr_lines = compare_files(source_file, dbr_file, filename)
        
        if not diff:
            print(f"  ✅ IDENTICAL ({source_lines} lines)")
            identical_files.append(filename)
        else:
            print(f"  ❌ DIFFERENCES FOUND (Source: {source_lines}, DBR: {dbr_lines} lines)")
            files_with_differences.append(filename)
            
            # Show key differences
            key_changes = []
            for line in diff[:20]:  # Show first 20 diff lines
                if line.startswith('@@'):
                    print(f"    📍 {line}")
                elif line.startswith('-') and not line.startswith('---'):
                    change = line[1:].strip()
                    if change and len(change) > 10:
                        print(f"    ❌ DBR:    {change[:80]}...")
                        if 'central-models' in change:
                            key_changes.append("Include references")
                        elif 'presto' in change.lower() or 'databricks' in change.lower():
                            key_changes.append("Description terminology")
                elif line.startswith('+') and not line.startswith('+++'):
                    change = line[1:].strip()
                    if change and len(change) > 10:
                        print(f"    ✅ SOURCE: {change[:80]}...")
            
            if key_changes:
                print(f"    🎯 Key changes: {', '.join(set(key_changes))}")
    
    print(f"\n📊 COMPARISON SUMMARY:")
    print(f"  📁 Total files compared: {len(zendesk_files)}")
    print(f"  ✅ Identical files: {len(identical_files)}")
    print(f"  ❌ Files with differences: {len(files_with_differences)}")
    
    if files_with_differences:
        print(f"\n❌ FILES NEEDING SYNCHRONIZATION:")
        for i, filename in enumerate(files_with_differences, 1):
            print(f"  {i}. {filename}")
    else:
        print(f"\n🎉 ALL FILES ARE IDENTICAL!")

if __name__ == "__main__":
    main()
