# @TODO check reason for xendit disb not being in this dataset

include: "/data_product/ag_bankaccountvalidations.view.lkml"

view: bank_name_validator {
  sql_table_name: transform__data_product.bankaccountvalidationbackbone ;;

  # DIMENSIONS #

  dimension: business_id {
    hidden: yes
    type: string
    sql: ${TABLE}.user_id ;;
  }
  dimension: id {
    primary_key: yes
    description: "Request ID"
    type: string
    sql: ${TABLE}.id ;;
    suggestable: no
  }
  dimension: dt {
    description: "UTC date. Data is partitioned by this so use it for coarse date filtering"
    label: "UTC Date"
    type: date
    sql: ${TABLE}.dt ;;
    convert_tz: no
  }
  dimension_group: created {
    description: "The time we received the API request at the Bank NV service"
    type: time
    timeframes: [raw,hour, minute, time, date, week, month, quarter, year]
    sql: ${TABLE}.created ;;
  }
  dimension_group: updated {
    description: "The last updated time of the request. Unless manually updated, this will be at end of processing by the Bank NV service, prior to callback being sent"
    type: time
    timeframes: [raw, hour, minute, time, date, week, month, quarter, year]
    sql: ${TABLE}.updated ;;
  }
  dimension: duration {
    group_label: "Duration"
    group_item_label: "Milliseconds"
    type: number
    sql: ${TABLE}.request_duration ;;
  }
  dimension_group: duration_group {
    group_label: "Duration"
    description: "Bank NV service processing time (ms)"
    type: duration
    intervals: [hour,minute,second]
    sql_start: ${created_raw}  ;;
    sql_end: ${updated_raw} ;;
  }
  dimension: duration_tier {
    description: "Bank NV service processing time (ms)"
    type: tier
    tiers: [1000,2000,3000,4000,5000,10000,15000,30000]
    sql: ${TABLE}.request_duration ;;
  }
  dimension: status {
    label: "Status (deprecated)"
    description: "Combined request processing and data validation status. Use request status and is found (yes/no) instead"
    type: string
    sql: ${TABLE}.status ;;
    suggest_persist_for: "168 hours"
  }
  dimension: request_status{
    description: "The stage of the request processing that this request is in (at time of data load)"
    type: string
    sql: ${TABLE}.request_status ;;
    suggest_persist_for: "168 hours"
  }
  dimension: failure_reason {
    description: "The failure reason for the request, if known"
    type: string
    sql: ${TABLE}.failure_reason ;;
    suggest_persist_for: "168 hours"
  }
  dimension: is_billable {
    description: "Whether we charge the client for the request. We will not charge for our or our partners' technical errors in Iluma APIs"
    type: string
    sql: ${TABLE}.is_billable ;;
  }
  dimension: is_found {
    group_label: "Validation result"
    description: "Whether the target account number was found"
    type: yesno
    sql: ${status} = 'SUCCESS' ;;
  }
  dimension: type {
    description: "Type of Bank NV Request. USER, BATCH_DISBURSEMENT, ..."
    type: string
    sql: ${TABLE}.type ;;
    suggestable: yes
    suggest_persist_for: "168 hours"
  }
  dimension: is_cached {
    description: "Yes if this Bank NV request grabs from our internal cache. No when a call is made to the bank connectors"
    type: yesno
    sql: ${TABLE}.is_cached ;;
  }
  dimension: is_duplicate {
    description: "Yes if this request was a duplicate of a in-progress request from the same client. See https://xendit.atlassian.net/wiki/spaces/DATA/pages/**********/Bank+Name+Validation for more detail"
    type: yesno
    sql: instr(${events},'WAITING_ON_BANK') > 0 ;;
  }
  dimension: is_normal_account {
    group_label: "Validation result"
    description: "Yes if the account appears to be normal. No if it is likely to be a VA"
    type: yesno
    sql: ${TABLE}.is_normal_account ;;
  }
  dimension: endpoint {
    description: "An inference using the DB table structure to get to the endpoint the request was made on"
    type: string
    sql: ${TABLE}.endpoint ;;
  }
  dimension: bank_code {
    description: "The institution holding the bank account, as supplied by the client in the API request and defined in https://docs.iluma.ai/bank_codes.html"
    type: string
    sql: ${TABLE}.bank_code ;;
    suggestable: yes
    suggest_persist_for: "168 hours"
  }
  dimension: bank_account_number {
    description: "The bank account number as supplied by the client in the API request"
    type: string
    sql: ${TABLE}.bank_account_number ;;
    suggestable: no
  }
  dimension: active_bank_channel {
    description: "The last channel which was used to look up the name. Is null for cached results"
    group_label: "Connector routing details"
    type: string
    sql: ${TABLE}.active_bank_channel ;;
    suggestable: yes
    suggest_persist_for: "168 hours"
  }
  dimension: failed_bank_channels {
    group_label: "Connector routing details"
    hidden: yes
    sql: ${TABLE}.failed_bank_channels ;;
  }
  dimension: failed_bank_channels_string {
    description: "An array of channels which were used to look up the name but did not find the account"
    label: "Failed Bank Channels"
    group_label: "Connector routing details"
    type: string
    sql: array_join(${failed_bank_channels}, ',') ;;
  }
  dimension: number_of_connector_calls {
    description: "The number of calls to the connectors that were used in processing the individual request. Does not include retries within the connector itself"
    group_label: "Connector routing details"
    type: number
    sql: case
      when ${is_cached} OR ${is_duplicate} then 0
      when cardinality(${failed_bank_channels}) = 0 and ${active_bank_channel} is not null then 1
      when ${status} = 'FAILURE' then cardinality(${failed_bank_channels})
      else cardinality(${failed_bank_channels}) + 1
    end;;
  }
  dimension: source {
    description: "The database source for the request"
    type: string
    sql: ${TABLE}.source ;;
    suggestions: ["iluma","xendit"]
  }
  dimension: events { # Note this is a json string (as at 2022-01-28. Can json_parse if required to get to named content
    description: "An array of update events for this request, including raw responses from the banks"
    type: string
    sql: ${TABLE}.events ;;
    required_access_grants: [global__can_view_end_user_pii]
  }
  dimension: name_match_result {
    group_label: "Validation result"
    type: string
    sql: ${TABLE}.name_matching_result;;
    suggest_persist_for: "168 hours"
  }
  dimension: bank_account_holder_name {
    group_label: "Validation result"
    description: "The name on the bank account returned from the cache or bank networks"
    type: string
    sql: ${TABLE}.bank_account_holder_name ;;
    required_access_grants: [global__can_view_end_user_pii]
    suggestable: no
  }
  dimension: given_name {
    description: "The given names supplied by the client in the API request"
    type: string
    sql: ${TABLE}.given_name ;;
    required_access_grants: [global__can_view_end_user_pii]
    suggestable: no
  }
  dimension: surname {
    description: "The surname supplied by the client in the API request"
    type: string
    sql: ${TABLE}.surname ;;
    required_access_grants: [global__can_view_end_user_pii]
    suggestable: no
  }
  dimension: reference_id {
    description: "Client's supplied ID that can be used to reconsile request to their system"
    type: string
    sql: ${TABLE}.reference_id ;;
    suggestable: no
  }
  dimension: api_version  {
    description: "Iluma api endpoint version, if available"
    type: string
    sql: coalesce(${bankaccountvalidations.version},'Unavailable') ;;
  }

  # MEASURES #

  measure: count {
    description: "Count of all bank name validation requests"
    type: count
    drill_fields: [business_id, status, count]
  }
  measure: count_success {
    type: count
    filters: [
      status: "SUCCESS"
    ]
  }
  measure: success_rate {
    type: number
    value_format_name: percent_1
    sql: cast(${count_success} as double) / ${count} ;;
  }
  measure: count_distinct {
    description: "Distinct count of bank code + account number"
    type: count_distinct
    sql:  ${bank_code} || ${bank_account_number};;
    drill_fields: [business_id, status, count_distinct]
  }
  measure: count_bank_connector_calls {
    description: "Total number of calls made across all bank connectors. Does not include any retries within the connectors themselves. NOT to be used with Active or Failed Bank Channels dimensions to get total counts per bank"
    type: sum
    sql: ${number_of_connector_calls} ;;
  }

  measure: duration_p99 {
    group_label: "Request Durations"
    type: percentile
    percentile: 99
    sql: ${duration} ;;
    drill_fields: [type,is_cached,active_bank_channel,durations_pxx*,count]
  }
  measure: duration_p95 {
    group_label: "Request Durations"
    type: percentile
    percentile: 95
    sql: ${duration} ;;
    drill_fields: [type,is_cached,active_bank_channel,durations_pxx*,count]
  }
  measure: duration_p90 {
    group_label: "Request Durations"
    type: percentile
    percentile: 90
    sql: ${duration} ;;
    drill_fields: [type,is_cached,active_bank_channel,durations_pxx*,count]
  }
  measure: duration_p50 {
    group_label: "Request Durations"
    type: percentile
    percentile: 50
    sql: ${duration} ;;
    drill_fields: [type,is_cached,active_bank_channel,durations_pxx*,count]
  }
  set: durations_pxx {
    fields: [duration_p50,duration_p90,duration_p95,duration_p99]
  }
}
