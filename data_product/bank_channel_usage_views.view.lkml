view: bank_nv_inquiry {
  derived_table: {
    sql_trigger_value: select count(*) from clean__name_validator_prod_live.bankaccountvalidations ;;
    sql:
    with iluma_nv as (
      select
        case
          when bank_code = 'BNI_SYR' then 'BNI'
          when bank_code = 'SINARMAS_UUS' then 'SINARMAS'
          when bank_code = 'CIMB_UUS' then 'CIMB'
          when bank_code = 'PERMATA_UUS' then 'PERMATA'
          else bank_code end
        AS target_bank
        , status
        , active_bank_channel AS channel
        , failed_bank_channels
        , length(bank_account_number) as bank_account_number_length
        , dt AS day
        , "type"
      from clean__name_validator_prod_live.bankaccountvalidations
      where dt > CURRENT_DATE - interval
        -- if dev -- '30' day
        -- if prod -- '180' day
    ), xendit_nv AS (
      select
        case
          when bank_code = 'BNI_SYR' then 'BNI'
          when bank_code = 'SINARMAS_UUS' then 'SINARMAS'
          when bank_code = 'CIMB_UUS' then 'CIMB'
          when bank_code = 'PERMATA_UUS' then 'PERMATA'
          else bank_code end
        AS target_bank
        , status
        , case when cardinality(events) > 0 then active_bank_channel else null end as channel
        , failed_bank_channels
        , length(bank_account_number) as bank_account_number_length
        , dt AS day
        , "type"
      from clean__xendit_disbursement_service.bankaccountdatarequests
      where dt > CURRENT_DATE - interval
        -- if dev -- '30' day
        -- if prod -- '180' day
    )

    select
      target_bank
      , status
      , channel
      , bank_account_number_length
      , day
      , count(*) as cnt
      , sum(case when "type" = 'USER' then 1 else 0 end) as cnt_bnv
      , sum(case when "type" = 'EWALLET_ACCOUNT_VALIDATION' then 1 else 0 end) as cnt_env
      , sum(case when "type" = 'BATCH_DISBURSEMENT' then 1 else 0 end) as cnt_bd
      , sum(case when "type" not in ('USER', 'BATCH_DISBURSEMENT', 'EWALLET_ACCOUNT_VALIDATION') then 1 else 0 end) as cnt_other
    from iluma_nv where status = 'SUCCESS' group by 1,2,3,4,5
    union all
    select
      target_bank
      , 'FAILURE'
      , failed_channel
      , bank_account_number_length
      , day
      , count(*) as cnt
      , sum(case when "type" = 'USER' then 1 else 0 end) as cnt_bnv
      , sum(case when "type" = 'EWALLET_ACCOUNT_VALIDATION' then 1 else 0 end) as cnt_env
      , sum(case when "type" = 'BATCH_DISBURSEMENT' then 1 else 0 end) as cnt_bd
      , sum(case when "type" not in ('USER', 'BATCH_DISBURSEMENT', 'EWALLET_ACCOUNT_VALIDATION') then 1 else 0 end) as cnt_other
    from iluma_nv
      lateral view explode(failed_bank_channels)t as failed_channel
    where cardinality(failed_bank_channels) != 0
    group by 1,2,3,4,5
    union all
    select
      target_bank
      , status
      , channel
      , bank_account_number_length
      , day
      , count(*) as cnt
      , sum(case when "type" = 'USER' then 1 else 0 end) as cnt_bnv
      , sum(case when "type" = 'EWALLET_ACCOUNT_VALIDATION' then 1 else 0 end) as cnt_env
      , sum(case when "type" = 'BATCH_DISBURSEMENT' then 1 else 0 end) as cnt_bd
      , sum(case when "type" not in ('USER', 'BATCH_DISBURSEMENT', 'EWALLET_ACCOUNT_VALIDATION') then 1 else 0 end) as cnt_other
    from xendit_nv where status = 'SUCCESS' group by 1,2,3,4,5
    union all
    select
      target_bank
      , 'FAILURE'
      , failed_channel
      , bank_account_number_length
      , day
      , count(*) as cnt
      , sum(case when "type" = 'USER' then 1 else 0 end) as cnt_bnv
      , sum(case when "type" = 'EWALLET_ACCOUNT_VALIDATION' then 1 else 0 end) as cnt_env
      , sum(case when "type" = 'BATCH_DISBURSEMENT' then 1 else 0 end) as cnt_bd
      , sum(case when "type" not in ('USER', 'BATCH_DISBURSEMENT', 'EWALLET_ACCOUNT_VALIDATION') then 1 else 0 end) as cnt_other
    from xendit_nv
      lateral view explode(failed_bank_channels)t as failed_channel --   cross join unnest(CASE
      --  when cardinality(failed_bank_channels)=0 then ARRAY['']
      --  else failed_bank_channels
      --  end) AS t (failed_channel)
    where cardinality(failed_bank_channels) != 0
    group by 1,2,3,4,5
    ;;
  }
  suggestions: no

  dimension: target_bank {
    type: string
    sql: ${TABLE}.target_bank ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }

  dimension: bank_account_number_length {
    type: number
    sql: ${TABLE}.bank_account_number_length ;;
  }

  dimension_group: inquiry {
    convert_tz: no
    type: time
    sql: ${TABLE}.day ;;
    timeframes: [date,month,quarter,year]
  }

  dimension: type {
    type: string
    sql: ${TABLE}.type ;;
  }

  measure: inquiry_count {
    type: sum
    sql: ${TABLE}.cnt ;;
  }
  measure: cnt_bnv {
    label: "Bank name validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_bnv ;;
  }
  measure: cnt_env {
    label: "eWallet name validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_env ;;
  }
  measure: cnt_bd {
    label: "Batch disbursement validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_bd ;;
  }
  measure: cnt_other {
    label: "Other validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_other ;;
  }
}

view: bank_nv_disbursement {
  derived_table: {
    sql_trigger_value: select count(*) from clean__xendit_disbursement_service.disbursements ;;
    sql:
    with disb as (
      select
        case
          when d.bank_code = 'BNI_SYR' then 'BNI'
          when d.bank_code = 'SINARMAS_UUS' then 'SINARMAS'
          when d.bank_code = 'CIMB_UUS' then 'CIMB'
          when d.bank_code = 'PERMATA_UUS' then 'PERMATA'
          else d.bank_code
        end as target_bank
        , case
          when d.status = 'COMPLETED' then 'SUCCESS'
          when cardinality(h.failed_bank_channels) != 0 then 'FAILURE'
          else null
        end as status
        , case
            when h.active_channel_code like '%BCA%' then 'BCA'
            when h.active_channel_code like '%CIMB%' then 'CIMB'
            when h.active_channel_code like '%BNI%' then 'BNI'
            when h.active_channel_code like '%BRI%' then 'BRI'
            when h.active_channel_code like '%DBS%' then 'DBS'
            when h.active_channel_code like '%MANDIRI%' then 'MANDIRI'
            when h.active_channel_code like '%PERMATA%' then 'PERMATA'
            when h.active_channel_code like '%SAHABAT_SAMPOERNA%' then 'SAHABAT_SAMPOERNA'
            else h.active_channel_code
        end AS channel
        , failed_bank_channels
        , length(d.bank_account_number) as bank_account_number_length
        , d.dt AS day
      from clean__xendit_disbursement_service.disbursements d
      join clean__xendit_disbursement_service.routinghistories h ON d.id = h.disbursement_id
      where (d.status = 'COMPLETED' or cardinality(h.failed_bank_channels) != 0)
        and d.dt > CURRENT_DATE - interval
          -- if dev -- '30' day
          -- if prod -- '180' day
    )

    select target_bank, status, channel, bank_account_number_length, day, count(*) as cnt from disb where status = 'SUCCESS' group by 1,2,3,4,5
    union all
    select
      target_bank
      , status
      , case
        when failed_channel like '%BCA%' then 'BCA'
        when failed_channel like '%CIMB%' then 'CIMB'
        when failed_channel like '%BNI%' then 'BNI'
        when failed_channel like '%BRI%' then 'BRI'
        when failed_channel like '%DBS%' then 'DBS'
        when failed_channel like '%MANDIRI%' then 'MANDIRI'
        when failed_channel like '%PERMATA%' then 'PERMATA'
        when failed_channel like '%SAHABAT_SAMPOERNA%' then 'SAHABAT_SAMPOERNA'
        else failed_channel
      end as channel
      , bank_account_number_length
      , day
      , count(*)
    from disb
    lateral view explode(failed_bank_channels)t as failed_channel
    where status = 'FAILURE'
    group by 1,2,3,4,5
    ;;
  }
  suggestions: no

  dimension: target_bank {
    type: string
    sql: ${TABLE}.target_bank ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }

  dimension: bank_account_number_length {
    type: number
    sql: ${TABLE}.bank_account_number_length ;;
  }

  dimension_group: disbursement {
    convert_tz: no
    type: time
    timeframes: [date, month, quarter, year]
    sql: ${TABLE}.day ;;
  }

  dimension: day {
    hidden: yes
    sql: ${TABLE}.day ;;
  }

  measure: disbursement_count {
    type: sum
    sql: ${TABLE}.cnt ;;
  }
}

view: bank_channel_usage {
  derived_table: {
    sql:
      select
        coalesce(i.target_bank, d.target_bank) as target_bank
        , coalesce(i.status, d.status) as status
        , coalesce(i.channel, d.channel) as channel
        , coalesce(i.bank_account_number_length, d.bank_account_number_length) as bank_account_number_length
        , coalesce(i.day, d.day) as day
        , sum(d.cnt) as cnt_disb
        , sum(i.cnt) as cnt_inq
        , sum(i.cnt_bnv) as cnt_bnv
        , sum(i.cnt_env) as cnt_env
        , sum(i.cnt_bd) as cnt_bd
        , sum(i.cnt_other) as cnt_other
      from ${bank_nv_inquiry.SQL_TABLE_NAME} i
      full outer join ${bank_nv_disbursement.SQL_TABLE_NAME} d
      on i.target_bank = d.target_bank and i.status = d.status and i.channel = d.channel and i.bank_account_number_length = d.bank_account_number_length and i.day = d.day
      group by 1,2,3,4,5
    ;;
  }
  suggestions: no

  dimension: target_bank {
    type: string
    sql: ${TABLE}.target_bank ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: channel {
    type: string
    sql: ${TABLE}.channel ;;
  }

  dimension: bank_account_number_length {
    type: number
    sql: ${TABLE}.bank_account_number_length ;;
  }

  dimension_group: activity {
    convert_tz: no
    type: time
    timeframes: [date, month, quarter, year]
    sql: ${TABLE}.day ;;
  }

  measure: disbursement_count {
    type: sum
    sql: ${TABLE}.cnt_disb ;;
  }

  measure: inquiry_count {
    type: sum
    sql: ${TABLE}.cnt_inq ;;
    drill_fields: [bank_pairs*,inq_counts*]
    link: {
      label: "Bank NV: Channel success rates"
      url: "/looks/5
?f[bank_channel_usage.channel]={{ _filters['bank_channel_usage.channel'] | url_encode }}
&f[bank_channel_usage.target_bank]={{ _filters['bank_channel_usage.target_bank'] | url_encode }}
&f[bank_channel_usage.activity_date]={{ _filters['bank_channel_usage.activity_date'] | url_encode }}
&{% if bank_channel_usage.channel._is_selected %}f[bank_channel_usage.channel]={{bank_channel_usage.channel._value}}{% endif %}
&{% if bank_channel_usage.target_bank._is_selected %}f[bank_channel_usage.target_bank]={{bank_channel_usage.target_bank._value}}{% endif %}
"
    }
    # Note: Need to
    # (1) have specific filters from data panel (the "is selected" content) come after the filter section as the later query params override earlier ones
    # (2) use is_selected as Looker will insert a filtered field into the query group by statement which will explode rows when filter setting = any value
    # (3) have all filters exist on destination for these to pass through. "X no longer exists and will be ignored..." errors will occur otherwise
  }

  measure: count_success {
    hidden: yes
    type: sum
    sql: coalesce(${TABLE}.cnt_inq,0) + coalesce(${TABLE}.cnt_disb,0) ;;
    filters: [status: "SUCCESS"]
  }

  measure: success_ratio {
    value_format_name: percent_1
    type: number
    sql: cast(${count_success} as double) / (${inquiry_count} + ${disbursement_count});;
  }

  measure: cnt_bnv {
    label: "Bank name validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_bnv ;;
  }

  measure: cnt_env {
    label: "eWallet name validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_env ;;
  }

  measure: cnt_bd {
    label: "Batch disbursement validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_bd ;;
  }

  measure: cnt_other {
    label: "Other validation count"
    group_label: "Counts by inquiry type"
    type: sum
    sql: ${TABLE}.cnt_other ;;
  }

  measure: inquiry_ratio {
    type: number
    value_format_name: percent_1
    sql: cast(${inquiry_count} as double) / ${disbursement_count} ;;
    drill_fields: [bank_pairs*, disbursement_count, inquiry_count]
  }

  set: inq_counts {
    fields: [cnt_bnv,cnt_env,cnt_bd,cnt_other]
  }

  set: bank_pairs {
    fields: [channel,target_bank]
  }
}
