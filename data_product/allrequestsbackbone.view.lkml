view: allrequestsbackbone {
  sql_table_name: transform__data_product.allrequestsbackbone ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    description: "The request reference id"
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
    suggestable: no
  }
  dimension: user_id {
    hidden: yes
    type: string
    sql: ${TABLE}.user_id ;;
  }
  dimension: product {
    hidden: yes
    description: "Naming used in data model before 202207"
    type: string
    sql: ${TABLE}.product ;;
  }
  dimension: billing_product {
    hidden: yes
    type: string
    sql: ${TABLE}.billing_product ;;
  }
  dimension: country_of_service {
    type: string
    sql: ${TABLE}.country_of_service ;;
  }
  dimension: database_location {
    type: string
    sql: ${TABLE}.database_location ;;
    suggestions: ["iluma","xendit"]
  }
  dimension: is_cached {
    type: yesno
    sql: ${TABLE}.is_cached ;;
  }
  dimension: status {
    type: string
    sql: CASE
          WHEN UPPER(${TABLE}.status) in ('COMPLETED', 'COMPLETE', 'MANUAL_PROCESSED', 'MANUAL_COMPLETE', 'MANUALLY_COMPLETED_20180909', 'SUCCESS', 'ACTIVE', 'SUCCESS', 'FOUND') then 'COMPLETED'
          WHEN UPPER(${TABLE}.status) in ('FAILURE', 'FAILED', 'NOT_FOUND') then 'FAILED'
          WHEN UPPER(${TABLE}.status) in ('VALIDATING', 'WAITING_ON_BANK', 'PENDING', 'WAITING_ON_SOURCE') then 'PENDING'
          ELSE UPPER(${TABLE}.status) END ;;
    suggest_persist_for: "168 hours"
  }
  dimension: dt {
    description: "UTC date. Data is partitioned by this so use it for coarse date filtering"
    label: "UTC Date"
    type: date
    sql: CAST(${TABLE}.dt AS DATE) ;;
    convert_tz: no
  }
  dimension_group: created {
    description: "The created timestamp in our databases. Approximately equal to when the API request was received"
    type: time
    timeframes: [raw,year,month,week,day_of_month,date,time,hour]
    sql: ${TABLE}.created ;;
  }
  dimension_group: updated {
    description: "The updated timestamp in our databases. Approximately equal to when the API response was sent back"
    type: time
    timeframes: [raw,year,month,week,day_of_month,date,time,hour]
    sql: ${TABLE}.updated ;;
  }
  dimension: month_to_date_agnostic {
    description: "True if the created date is between the first day of the month up to the current day of month. Does NOT manage the various number of days in months (28, 29, 30, 31)"
    type: yesno
    sql: ${created_day_of_month} < day(current_date) ;;
  }
  dimension: account {
    description: "Main information queried by the request"
    type: string
    sql: ${TABLE}.account ;;
  }
  dimension_group: create_to_completion {
    description: "Time taken from request receipt to completion or callback delivery if used"
    type: duration
    intervals: [second,minute]
    sql_start: ${created_raw} ;;
    sql_end: coalesce(
      ${iluma_callbacks.status_updated_raw}
      , ${updated_raw}
    ) ;;
  }
  dimension: duration {
    group_label: "Duration Create to Completion"
    group_item_label: "Milliseconds"
    type: number
    sql: ${TABLE}.request_duration ;;
  }
  dimension: is_billable {
    description: "Whether we charge the client for the request. We will not charge for our or our partners' technical errors in Iluma APIs. This metric will be null for some products where billing is yet to be finalised"
    type: string
    sql: ${TABLE}.is_billable ;;
  }
  measure: request_count {
    type: count
    drill_fields: [product, user_id, request_count, distinct_request_count]
  }
  measure: distinct_request_count {
    type: count_distinct
    sql: ${TABLE}.account ;;
    drill_fields: [product, user_id, request_count, distinct_request_count]
  }
  measure: active_clients {
    description: "Number of active businesses making at least one request within the selected timeframe"
    type: count_distinct
    sql: ${user_id} ;;
  }
  measure: most_recent_request {
    description: "An indication of the latest API request / the last updated date for the ETL"
    type: date_time
    sql: max(${created_raw}) ;;
  }
  measure: duration_p99 {
    group_label: "Request Durations"
    type: percentile
    percentile: 99
    sql: ${duration} ;;
    drill_fields: [product, request_count, durations_pxx*]
  }
  measure: duration_p95 {
    group_label: "Request Durations"
    type: percentile
    percentile: 95
    sql: ${duration} ;;
    drill_fields: [product, request_count, durations_pxx*]
  }
  measure: duration_p90 {
    group_label: "Request Durations"
    type: percentile
    percentile: 90
    sql: ${duration} ;;
    drill_fields: [product, request_count, durations_pxx*]
  }
  measure: duration_p50 {
    group_label: "Request Durations"
    type: percentile
    percentile: 50
    sql: ${duration} ;;
    drill_fields: [product, request_count, durations_pxx*]
  }
  set: durations_pxx {
    fields: [duration_p50,duration_p90,duration_p95,duration_p99]
  }
}
