include: "./allrequestsbackbone.view.lkml"
include: "./iluma_businesses.view.lkml"

view: req_subfact1 {
  derived_table: {
    sql_trigger_value: select count(*) from ${allrequestsbackbone.SQL_TABLE_NAME} ;;
    sql:
      select
        user_id as business_id
        , count(*) as request_cnt
        , min(created) as first_request_time
        , max(created) as latest_request_time
      FROM ${allrequestsbackbone.SQL_TABLE_NAME}
      GROUP BY ALL
    ;;
  }
  dimension: business_id {
    primary_key: yes
  }
}

view: req_subfact2 {
  derived_table: {
    sql_trigger_value: select count(*) from ${req_subfact1.SQL_TABLE_NAME} ;;
    sql: SELECT a.* FROM (WITH monthly_cnt as (
      SELECT
        user_id as business_id
        , date_format(created, 'yyyy-MM') as mth
        , count(*) as monthly_cnt
        , rank() over(partition by user_id order by date_format(created, 'yyyy-MM')) as monthly_order
      FROM ${allrequestsbackbone.SQL_TABLE_NAME}
      GROUP BY 1,2
      )
    SELECT
      business_id
      , max(case when monthly_order = 1 then monthly_cnt else 0 end) as first_month_cnt
      , max(case when monthly_order = 2 then monthly_cnt else 0 end) as second_month_cnt
      , max(case when monthly_order = 3 then monthly_cnt else 0 end) as third_month_cnt
      , sum(case when monthly_order <= 18 then monthly_cnt else 0 end) as eighteen_month_cnt_total
    FROM monthly_cnt
    WHERE monthly_order <= 18
    GROUP by 1) as a
    ;;
  }
  dimension: business_id {
    primary_key: yes
  }
}

view: iluma_business_facts {
  derived_table: {
    sql_trigger_value: select count(*) from ${req_subfact2.SQL_TABLE_NAME} ;;
    sql:
    SELECT
      b.pk as business_id
      , case when b.created > sf1.first_request_time then sf1.first_request_time else b.created end as first_sign_up_date
      , sf1.request_cnt
      , sf1.first_request_time
      , sf1.latest_request_time
      , sf2.first_month_cnt
      , sf2.second_month_cnt
      , sf2.third_month_cnt
    FROM ${iluma_businesses.SQL_TABLE_NAME} b
    LEFT JOIN ${req_subfact1.SQL_TABLE_NAME} sf1 ON b.pk=sf1.business_id
    LEFT JOIN ${req_subfact2.SQL_TABLE_NAME} sf2 ON b.pk=sf2.business_id
    ;;
  }

  # DIMENSIONS #

  dimension: business_id {
    type: string
    primary_key: yes
    hidden: yes
    sql: ${TABLE}.business_id ;;
  }

  dimension_group: first_sign_up {
    description: "When business first sign up"
    type: time
    timeframes: [raw,date,week,month,year]
    sql: ${TABLE}.first_sign_up_date ;;
  }

  dimension_group: first_request {
    description: "When business first makes an API request"
    type: time
    timeframes: [raw,date,week,month,year]
    sql: ${TABLE}.first_request_time ;;
  }

  dimension_group: create_to_first_request {
    description: "time taken between business creation to first request"
    type: duration
    sql_start: ${first_sign_up_raw} ;;
    sql_end: ${first_request_raw} ;;
    intervals: [hour, day, week, month]
  }

  dimension_group: lifetime {
    description: "time elapsed from business creation to date"
    type: duration
    sql_start: ${first_sign_up_raw} ;;
    sql_end: CURRENT_TIMESTAMP ;;
    intervals: [day, week, month, year]
  }

  dimension: lifetime_request_count {
    description: "request count across all products"
    type: number
    sql: ${TABLE}.request_cnt ;;
  }

  dimension: first_month_count {
    type: number
    sql: ${TABLE}.first_month_cnt ;;
    group_label: "First 3 months request count"
  }

  dimension: second_month_count {
    type: number
    sql: ${TABLE}.second_month_cnt ;;
    group_label: "First 3 months request count"
  }

  dimension: third_month_count {
    type: number
    sql: ${TABLE}.third_month_cnt ;;
    group_label: "First 3 months request count"
  }
}
