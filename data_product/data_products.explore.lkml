include: "/data_product/**/*.view"
include: "//central-models-dbr/businesses/businesses.view"
include: "data_products_aggregate_awareness.lkml"
include: "/growth/*.view.lkml"

include: "./_tests.lkml"

explore: iluma_businesses {
  group_label: "Data Product Business"
  view_label: "1. Business details"
  join: businesses {
    view_label: "1. Business details"
    sql_on: ${iluma_businesses.xendit_business_ids} = ${businesses.business_id};;
    fields: [businesses.account_manager_name, businesses.sales_rep_name, businesses.technical_rep_name]
    relationship: many_to_one
  }
  join: iluma_businesses_permissions {
    view_label: "2. API key permissions"
    sql_on: ${iluma_businesses.business_id} = ${iluma_businesses_permissions.business_id} ;;
    relationship: one_to_one
  }
  join: iluma_business_facts {
    view_label: "3. Business facts"
    sql_on: ${iluma_businesses.business_id} = ${iluma_business_facts.business_id} ;;
    relationship: one_to_one
  }
  join: data_product_billing_rates {
    from: data_product_billing_rate_history
    view_label: "4. Billing details"
    sql_on: ${iluma_businesses.business_id} = ${data_product_billing_rates.iluma_business_id} ;;
    relationship: one_to_many
  }
  join: data_product_hierarchy {
    view_label: "4. Billing details"
    sql_on: ${data_product_billing_rates.product} = ${data_product_hierarchy.product_name} ;;
    relationship: many_to_one
  }
  join: data_product_business_rate {
    view_label: "4. Billing details"
    sql_on: ${iluma_businesses.business_id} = ${data_product_business_rate.iluma_business_id} and ${data_product_business_rate.product} = ${data_product_hierarchy.product_name};;
    relationship: one_to_one
  }
}

explore: iluma_requests {
  group_label: "Data Product Business"
  from: allrequestsbackbone
  view_label: "2. Summary request data"
  fields: [
    ALL_FIELDS*
    , -bank_name_validator.durations_pxx*
    , -npwp_validator.durations_pxx*
  ]
  sql_always_where: not ${iluma_requests.billing_product} = 'CUSTOMER_OBJECT' ;;
  # always_filter: {
  #   filters: [
  #     data_product_hierarchy.product_name: "-CUSTOMER_OBJECT"
  #   ]
  # }
  join: data_product_hierarchy {
    view_label: "2. Summary request data"
    sql_on: ${iluma_requests.billing_product} = ${data_product_hierarchy.product_name} ;;
    relationship: many_to_one
  }
  join: iluma_businesses {
    view_label: "1. Business details"
    sql_on: ${iluma_requests.user_id} = ${iluma_businesses.business_id};;
    relationship: many_to_one
  }
  join: businesses {
    view_label: "1. Business details"
    sql_on: ${iluma_businesses.xendit_business_ids} = ${businesses.business_id} ;;
    fields: [businesses.account_manager_name, businesses.sales_rep_name, businesses.technical_rep_name]
    relationship: many_to_one
  }
  join: bank_name_validator {
    view_label: "3a. Bank NV request details"
    sql_on: ${iluma_requests.id} = ${bank_name_validator.id} ;;
    relationship: many_to_one
  }
  join: bankaccountvalidations {
    sql_on: ${bank_name_validator.id} = ${bankaccountvalidations.id} ;;
    fields: []
    relationship: many_to_one # to account for non-iluma requests in the table
  }
  join: ewallet_validator {
    view_label: "3b. eWallet request details"
    sql_on: ${iluma_requests.id} = ${ewallet_validator.id} ;;
    relationship: many_to_one
  }
  join: npwp_validator {
    from: consolidated_npwp_validator
    view_label: "3c. NPWP validator request details"
    sql_on: ${iluma_requests.id} = ${npwp_validator.id} ;;
    relationship: many_to_one
  }
  join: idcardverificationbackbone {
    view_label: "3d. ID card verification request details"
    sql_on: ${iluma_requests.id} = ${idcardverificationbackbone.id} ;;
    relationship: many_to_one
  }
  # join: phonenumbervalidations {
  #   view_label: "3e. Phone number request details (deprecated)"
  #   sql_on: ${iluma_requests.id} = ${phonenumbervalidations.id};;
  #   relationship: many_to_one
  # }
  join: id_cards_recognitions {
    view_label: "3f. ID OCR request details"
    sql_on: ${iluma_requests.id} = ${id_cards_recognitions.task_id};;
    relationship: many_to_one
  }
  join: face_comparisons {
    from: face_comparison_backbone
    view_label: "3g. Face comparison request details"
    sql_on: ${iluma_requests.id} = ${face_comparisons.task_id};;
    relationship: many_to_one
  }
   join: credit_enrichment_requests {
    view_label: "3h. Credit enrichment request details"
    sql_on: ${iluma_requests.id} = ${credit_enrichment_requests.id};;
    relationship: many_to_one
  }
  join: bundled_identity_requests {
    view_label: "3i. Bundled identity affirmation request details"
    sql_on: ${iluma_requests.id} = ${bundled_identity_requests.id};;
    relationship: many_to_one
  }
  # @TODO build an aggregate table in databricks and import
  join: cft_entity_data_requests {
    view_label: "3h. CFT entity request details"
    sql_on: ${iluma_requests.id} = ${cft_entity_data_requests.id};;
    relationship: many_to_one
  }
  join: cft_individual_data_requests {
    view_label: "3i. CFT individual request details"
    sql_on: ${iluma_requests.id} = ${cft_individual_data_requests.id};;
    relationship: many_to_one
  }
  # join: identity_service {
  #   view_label: "4a. Customer objects"
  #   sql_on: ${iluma_requests.id} = ${identity_service.end_customer_id};;
  #   relationship: many_to_one
  # }
  # join: ip_data_requests {
  #   view_label: "4a. IP address validation details"
  #   sql_on: ${iluma_requests.id} = ${ip_data_requests.id};;
  #   relationship: many_to_one
  # }
  join: iluma_callbacks {
    view_label: "5. Callbacks"
    sql_on: ${iluma_requests.id} = ${iluma_callbacks.request_id};;
    relationship: many_to_one
  }
  join: data_product_billing_rates {
    view_label: "6. Billing rates"
    sql_on: ${iluma_requests.id} = ${data_product_billing_rates.id};;
    relationship: one_to_one
  }
  join: sales_attribution {
    view_label: "Sales Attribution Live"
    sql_on: ${iluma_businesses.xendit_business_ids} = ${sales_attribution.business_id};;
    type: left_outer
    relationship: one_to_one
  }
}

explore: bank_channel_usage {
  group_label: "Data Product Business"
}

explore: bank_name_validator {
  group_label: "Data Product Business"
  description: "All bank NV requests including batch disbursement, payouts, etc"
  join: iluma_businesses {
    sql_on: ${bank_name_validator.business_id} = ${iluma_businesses.business_id};;
    relationship: many_to_one
  }
  join: bankaccountvalidations {
    sql_on: ${bank_name_validator.id} = ${bankaccountvalidations.id} ;;
    fields: []
    relationship: many_to_one # to account for non-iluma requests in the table
  }
}

explore: failure_rate_analysis {
  group_label: "Data Product Business"
  view_name: lagged_failure_rate_validation
  view_label: "Past 5 min Behavior by Created Time"
  description: "Request Volume and Failure Rate for Trailing 5 min by time of request. Showing Past 30 days only."
  join: iluma_businesses {
    sql_on: ${lagged_failure_rate_validation.user_id} = ${iluma_businesses.business_id};;
    relationship: many_to_one
  }
}

explore: npwp_validator {
  group_label: "Data Product Business"
  description: "Full raw data for NPWP validator including all source tables"
  view_name: npwpvalidationbackbone
  join: npwp_validator_v1 {
    sql_on: ${npwpvalidationbackbone.id} = ${npwp_validator_v1.id} ;;
    relationship: many_to_one
  }
  join: npwpdatarequests {
    sql_on: ${npwpvalidationbackbone.id} = ${npwpdatarequests.id} ;;
    relationship: many_to_one
  }
}
