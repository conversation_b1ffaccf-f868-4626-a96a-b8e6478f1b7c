view: ewallet_validator {
  sql_table_name: clean__iluma_ewallet_account_validation_service.ewallet_account_validation_requests ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    hidden: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: dt {
    type: string
    sql: ${TABLE}.dt ;;
  }

  dimension: events {
    type: string
    sql: ${TABLE}.events ;;
    required_access_grants: [global__can_view_end_user_pii]
  }

  dimension: ewallet_account_number {
    type: string
    sql: ${TABLE}.ewallet_account_number ;;
  }

  dimension: ewallet_type {
    type: string
    sql: ${TABLE}.ewallet_type ;;
  }

  dimension: failure_reason {
    type: string
    sql: ${TABLE}.failure_reason ;;
  }

  dimension: internal_reference {
    type: string
    sql: ${TABLE}.internal_reference ;;
  }

  dimension: is_cached {
    type: yesno
    sql: ${TABLE}.is_cached ;;
  }

  dimension: is_found {
    type: yesno
    sql: ${TABLE}.is_found ;;
  }

  dimension: request_body {
    type: string
    sql: ${TABLE}.request_body ;;
    required_access_grants: [global__can_view_end_user_pii]
  }

  dimension: request_count {
    type: number
    sql: ${TABLE}.request_count ;;
  }

  dimension: result {
    type: string
    sql: ${TABLE}.result ;;
    required_access_grants: [global__can_view_end_user_pii]
  }

  dimension: source_channel {
    type: string
    sql: ${TABLE}.source_channel ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }

  measure: count_distinct {
    type: count_distinct
    sql: ${ewallet_type} || ${ewallet_account_number} ;;
  }
}
