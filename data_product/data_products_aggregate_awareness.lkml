include: "data_products.explore"

explore: +iluma_requests {
  aggregate_table: _rollup_requests_by_day {
    materialization: {
      sql_trigger_value: select count(*) from ${allrequestsbackbone.SQL_TABLE_NAME};;
    }
    query: {
      dimensions: [
        iluma_requests.dt
        , iluma_requests.created_date
        , iluma_requests.product
        , data_product_hierarchy.product_group
        , data_product_hierarchy.product_display_name
        , data_product_hierarchy.product_name
        , iluma_requests.status
      ]
      measures: [
        iluma_requests.request_count
      ]
      timezone: "Asia/Jakarta"
    }
  }
  aggregate_table: _rollup_requests_by_day_product_business {
    materialization: {
      sql_trigger_value: select count(*) from ${allrequestsbackbone.SQL_TABLE_NAME};;
    }
    query: {
      dimensions: [
        iluma_requests.dt
        , iluma_requests.created_date
        , iluma_requests.product
        , data_product_hierarchy.product_group
        , data_product_hierarchy.product_display_name
        , data_product_hierarchy.product_name
        , iluma_businesses.business_id
        , iluma_businesses.business_name
        , iluma_requests.status
      ]
      measures: [
        iluma_requests.request_count
      ]
      timezone: "Asia/Jakarta"
    }
  }
}
