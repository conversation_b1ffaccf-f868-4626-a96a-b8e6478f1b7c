view: iluma_callbacks {
  derived_table: {
    sql_trigger_value: select count(*) from clean__callback_service_prod_live.callbackrequests ;;
    sql: select
        id as callback_id
        , created
        , updated
        , status_updated
        , payload.id as request_id
        , payload.updated as updated_in_payload
        , payload.status as status_in_payload
        , json_array_length(events) as number_of_callback_attempts --get the number of times we tried to send a callback
        , 999 as status_code -- , element_at(responses,-1)['statusCode'] as status_code-- get the terminal status code
        , status as callback_status
      from clean__callback_service_prod_live.callbackrequests

      -- NOTES
      -- select max(created) from clean__callback_service_prod_live.callbackrequests 2021-08-18
      -- created timestamp on callback == updated timestamp on the request

      -- @TODO:
      -- in some cases there is a massive difference in status_updated vs updated.
      -- in many cases the cardinality events != cardinality responses
      -- in some cases cardinality responses > 1 when all status codes are 200
       ;;
  }
  suggestions: no

  dimension: callback_id {
    primary_key: yes
    type: string
    sql: ${TABLE}.callback_id ;;
  }
  dimension_group: created {
    description: "When the callback was created, i.e. after the product service had finished processing"
    type: time
    timeframes: [year,month,date,hour,minute,second,time,raw]
    sql: ${TABLE}.created ;;
  }
  dimension_group: updated {
    type: time
    timeframes: [year,month,date,hour,minute,second,time,raw]
    sql: ${TABLE}.updated ;;
  }
  dimension_group: status_updated {
    type: time
    timeframes: [year,month,date,hour,minute,second,time,raw]
    sql: ${TABLE}.status_updated ;;
  }
  dimension: request_id {
    description: "The request id as extracted from the callback payload"
    type: string
    sql: ${TABLE}.request_id ;;
  }
  dimension_group: updated_in_payload {
    description: "The request updated timestamp as extracted from the callback payload"
    type: time
    timeframes: [year,month,date,hour,minute,second,time,raw]
    sql: coalesce(
      try(from_iso8601_timestamp(${TABLE}.updated_in_payload))
      , try(parse_datetime(${TABLE}.updated_in_payload, 'YYYY-MM-dd HH:mm:ss.SSSSSSZ'))
    ) ;;
  }
  dimension: status_in_payload {
    description: "The request status as extracted from the callback payload"
    type: string
    sql: ${TABLE}.status_in_payload ;;
    suggestable: yes
  }
  dimension: number_of_callback_attempts {
    description: "The cardinality of the events array within the callback record"
    type: number
    sql: ${TABLE}.number_of_callback_attempts ;;
  }
  dimension: status_code {
    label: "Status Code (temporarily disabled)"
    description: "The status code returned from the client server"
    type: number
    sql: ${TABLE}.status_code ;;
  }
  dimension: callback_status {
    description: "Our status for the callback. Will be COMPLETED if a 200 status is returned from the client server"
    type: string
    sql: ${TABLE}.callback_status ;;
    suggestable: yes
  }
  dimension_group: create_to_delivery {
    group_label: "Callback Speed"
    description: "Time taken from callback creation to callback delivery, i.e., speed of this service alone"
    type: duration
    intervals: [second,minute]
    sql_start: ${created_raw} ;;
    sql_end: ${status_updated_raw} ;;
  }
  dimension: seconds_create_to_delivery_tier {
    group_label: "Callback Speed"
    group_item_label: "Seconds - Tiered"
    type: tier
    tiers: [1,2,5,10,15,30,60,120,300]
    style: relational
    sql: ${seconds_create_to_delivery} ;;
  }

  measure: count {
    type: count
    drill_fields: [detail*]
  }

  set: detail {
    fields: [
      callback_id,
      created_time,
      updated_time,
      status_updated_time,
      request_id,
      updated_in_payload_time,
      status_in_payload,
      number_of_callback_attempts,
      status_code,
      callback_status
    ]
  }
}
