view: ewallet_accounts {
  sql_table_name: clean__iluma_ewallet_account_validation_service.ewallet_accounts ;;
  drill_fields: [id]
  suggestions: no

  dimension: id {
    primary_key: yes
    type: string
    sql: ${TABLE}.id ;;
  }

  dimension_group: created {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.created ;;
  }

  dimension: dt {
    type: string
    sql: ${TABLE}.dt ;;
  }

  dimension: ewallet_account_number {
    type: string
    sql: ${TABLE}.ewallet_account_number ;;
  }

  dimension: ewallet_type {
    type: string
    sql: ${TABLE}.ewallet_type ;;
  }

  dimension: is_found {
    type: yesno
    sql: ${TABLE}.is_found ;;
  }

  dimension: result {
    type: string
    sql: ${TABLE}.result ;;
    required_access_grants: [global__can_view_end_user_pii]
  }

  dimension: source_channel {
    type: string
    sql: ${TABLE}.source_channel ;;
  }

  dimension: status {
    type: string
    sql: ${TABLE}.status ;;
  }

  dimension: ttl {
    type: number
    sql: ${TABLE}.ttl ;;
  }

  dimension_group: updated {
    type: time
    timeframes: [
      raw,
      time,
      date,
      week,
      month,
      quarter,
      year
    ]
    sql: ${TABLE}.updated ;;
  }

  measure: count {
    type: count
    drill_fields: [id]
  }
}