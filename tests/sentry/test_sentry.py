from xenpy_core.hooks.saas.sentry import SentryH<PERSON>
from xenpy_core import constants
import os

sentry = SentryHook()

if os.getenv(constants.CONFIG_ID_XENPY_TESTING_ENVIRONMENT) == "True":
    assert sentry.client == None

if os.getenv(constants.CONFIG_ID_XENPY_TESTING_ENVIRONMENT) == "False":
    assert sentry.client != None

if os.getenv(constants.CONFIG_ID_XENPY_TESTING_ENVIRONMENT) == None:
    assert sentry.client == None


@sentry.send_error_to_sentry
def divide_by_zero():
    try:
        return 1 / 0
    except ZeroDivisionError as e:
        print(e)
        raise ZeroDivisionError


divide_by_zero()
