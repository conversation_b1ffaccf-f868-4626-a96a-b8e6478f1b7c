import json
import xenpy


class MockGrabSheets:

    # mock json() method always returns a specific testing dictionary
    @staticmethod
    def grab_sheets():
        with open("tests/google_sheets/data/google_sheet_test_data.json", "r") as f:
            sheet = json.load(f)
        return sheet


class TestUtil:
    def test_grab_sheet_dataframe(self, monkeypatch):
        def mock_get(*args, **kwargs):
            return MockGrabSheets()

        monkeypatch.setattr(xenpy.google_sheets.util, "grab_sheets", mock_get)
        target_sheets = xenpy.google_sheets.util.grab_sheets(None, [None], None)
        assert "Products" in target_sheets.grab_sheets()
